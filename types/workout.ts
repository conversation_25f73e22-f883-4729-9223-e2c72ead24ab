// /types/workout.ts
export type WorkoutType = 'amrap' | 'emom' | 'tabata' | 'forTime' | 'quickRest';

export interface WorkoutPreset {
  id: string;
  name: string;
  type: WorkoutType;
  settings: Record<string, number>;
}

export interface WorkoutSettings {
  amrap: {
    minutes: number;
  };
  emom: {
    rounds: number;
    intervalSeconds: number;
  };
  tabata: {
    rounds: number;
    workSeconds: number;
    restSeconds: number;
  };
  forTime: {
    timeCap: number;
  };
  quickRest: {
    seconds: number;
  };
}