export const GOAL_CATEGORIES = [
  { value: 'monthly_checkins', label: 'Μηνιαίοι Έλεγχοι' },
  { value: 'weight_lifted', label: 'Συνολικό Βάρος' },
  { value: 'personal_records', label: 'Προσωπικά Ρεκόρ' },
  { value: 'attendance_streak', label: 'Σερί Παρουσιών' },
  { value: 'exercise_variety', label: 'Ποικιλία Ασκήσεων' },
] as const;

export type GoalCategory = typeof GOAL_CATEGORIES[number]['value'];

export type GoalStatus = 'pending' | 'accepted' | 'declined' | 'completed';

export interface GoalFormData {
  title: string;
  description: string;
  category: GoalCategory | '';
  target_value: number;
  current_value: number;
  due_date: string;
}

export interface Goal {
  id: string;
  pelatis_id: string;
  assigned_by: string;
  title: string;
  description: string;
  category: GoalCategory;
  target_value: number;
  current_value: number;
  start_date: string;
  due_date: string | null;
  status: GoalStatus;
  created_at: string;
  updated_at: string;
}