// types/company.ts
import type { Database } from '@/types/supabase';

export type CompanySettings = Database['public']['Tables']['company_settings']['Row'];
export type CompanySettingsInsert = Database['public']['Tables']['company_settings']['Insert'];
export type CompanySettingsUpdate = Database['public']['Tables']['company_settings']['Update'];

export interface MyDataEnvironmentInfo {
  environment: 'development' | 'production';
  hasCredentials: boolean;
  currentEnvironment: string;
}