// types/dashboard.ts - Core types for our dashboard
export type PeriodOption = '30' | '90' | '180' | '365';

export interface MetricWithChange {
  current: number;
  previous: number;
  percentChange: number;
}

export interface ChartDataPoint {
  [key: string]: string | number | null;
}

export interface PeriodRange {
  startDate: string;
  endDate: string;
  previousStartDate: string;
  previousEndDate: string;
}

export interface MemberSegment {
  name: string;
  value: number;
  color: string;
}

export interface DashboardMetrics {
  activeMembers: MetricWithChange;
  mrr: MetricWithChange;
  newMembers: MetricWithChange;
  retentionRate: MetricWithChange;
  churnRate?: MetricWithChange;
  avgCheckins?: MetricWithChange;
  totalRevenue?: MetricWithChange;
  totalExpenses?: MetricWithChange;
  profitMargin?: MetricWithChange;
  classUtilization?: MetricWithChange;
}