import type { J<PERSON> } from './supabase';

export interface NotificationMetadata extends Record<string, Json | undefined> {
  // Badge-related fields
  badge_id?: string;
  badge_name?: string;
  badge_level?: number;
  
  // Transaction-related fields
  transactionId?: string;
  amount?: number;
  
  // Event type field
  eventType: 
    | 'badge_earned' 
    | 'payment_success' 
    | 'payment_failed'
    | 'refund_processed'
    | 'subscription_expiring' 
    | 'goal_achieved';
}
