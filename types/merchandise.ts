// types/merchandise.ts

export interface Color {
  id: string
  color_name: string
  color_hex: string
  display_order: number
  active: boolean
  created_at: string
}

export interface Size {
  id: string
  size_name: string
  display_order: number
  active: boolean
  created_at: string
}

export interface Stock {
  id: string
  color_id: string
  size_id: string
  quantity: number
  price: number
  created_at: string
  updated_at: string
}

export interface Sale {
  id: string
  pelatis_id: string
  item_type: string
  item_size: string | null
  item_color: string | null
  price: number
  payment_method: string
  sale_date: string
  comments: string | null
  created_at: string
  client_name?: string
  client_last_name?: string
}

export interface SalesFilter {
  size?: string
  color?: string
  startDate?: Date | null
  endDate?: Date | null
}

export interface ColorFormData {
  id?: string
  color_name: string
  color_hex: string
  display_order: number
  active: boolean
}

export interface SizeFormData {
  id?: string
  size_name: string
  display_order: number
  active: boolean
}

export interface StockFormData {
  id?: string
  color_id: string
  size_id: string
  quantity: number
  price: number
}

export interface SaleFormData {
  item_type: string
  item_size: string
  item_color: string
  price: string | number
  payment_method: string
  sale_date: Date
  comments: string
}

export interface Client {
  id: string
  name: string
  last_name: string
  email?: string | null
}

export interface ClientOption {
  value: string
  label: string
  pelatis_id: string
  session_id: string
}