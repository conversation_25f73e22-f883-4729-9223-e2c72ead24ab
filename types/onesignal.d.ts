export interface OneSignalType {
  init: (config: any) => Promise<void>;
  getNotificationPermission: () => Promise<string>;
  getUserId: () => Promise<string>;
  showNativePrompt: () => Promise<boolean>;
  on: (event: string, callback: (value: any) => void) => void;
  off?: (event: string) => void;
  initialized?: boolean;
}

declare global {
  interface Window {
    OneSignal: OneSignalType[] & {
      push: (callback: () => void) => void;
    };
  }
}
