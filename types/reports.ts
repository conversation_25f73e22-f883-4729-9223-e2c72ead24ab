import type { Database } from './supabase'

type Tables = Database['public']['Tables']
type Pliromes = Tables['pliromes']['Row']
type Pelates = Tables['pelates']['Row']

export interface MonthlyRevenueView {
  month: string
  mrr: number
}

export interface MonthlyAttendanceView {
  month: string
  unique_sessions: number
  total_checkins: number
}

export interface MonthlyRetentionView {
  month: string
  total_members: number
  retained_members: number
}

export interface MonthlyChurnMetrics {
  month: string
  churn_rate: number
  churned_members: number
}

export interface ClassCapacityMetrics {
  month: string
  avg_capacity: number
}

export interface PliromeWithPelati extends Pliromes {
  pelati: Pick<Pelates, 'id' | 'client_name'> | null
}