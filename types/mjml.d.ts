declare module 'mjml' {
  interface MjmlOptions {
    keepComments?: boolean;
    beautify?: boolean;
    minify?: boolean;
    validationLevel?: 'strict' | 'soft' | 'skip';
    filePath?: string;
    fonts?: Record<string, string>;
    juicePreserveTags?: Record<string, string>;
    juiceOptions?: Record<string, any>;
    minifyOptions?: Record<string, any>;
    ignoreIncludes?: boolean;
  }

  interface MjmlResult {
    html: string;
    errors: Array<{
      line: number;
      message: string;
      tagName: string;
      formattedMessage: string;
    }>;
  }

  function mjml2html(mjmlContent: string, options?: MjmlOptions): MjmlResult;

  export default mjml2html;
}
