export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      account_balances: {
        Row: {
          account_name: Database["public"]["Enums"]["payment_method"]
          created_at: string | null
          current_balance: number
          id: string
          initial_balance: number
          last_reconciled: string | null
          last_updated: string | null
        }
        Insert: {
          account_name: Database["public"]["Enums"]["payment_method"]
          created_at?: string | null
          current_balance: number
          id?: string
          initial_balance: number
          last_reconciled?: string | null
          last_updated?: string | null
        }
        Update: {
          account_name?: Database["public"]["Enums"]["payment_method"]
          created_at?: string | null
          current_balance?: number
          id?: string
          initial_balance?: number
          last_reconciled?: string | null
          last_updated?: string | null
        }
        Relationships: []
      }
      account_transfers: {
        Row: {
          amount: number
          completed_date: string | null
          created_at: string | null
          from_account: Database["public"]["Enums"]["payment_method"]
          id: string
          initiated_date: string | null
          notes: string | null
          reference_number: string | null
          status: string
          to_account: Database["public"]["Enums"]["payment_method"]
        }
        Insert: {
          amount: number
          completed_date?: string | null
          created_at?: string | null
          from_account: Database["public"]["Enums"]["payment_method"]
          id?: string
          initiated_date?: string | null
          notes?: string | null
          reference_number?: string | null
          status: string
          to_account: Database["public"]["Enums"]["payment_method"]
        }
        Update: {
          amount?: number
          completed_date?: string | null
          created_at?: string | null
          from_account?: Database["public"]["Enums"]["payment_method"]
          id?: string
          initiated_date?: string | null
          notes?: string | null
          reference_number?: string | null
          status?: string
          to_account?: Database["public"]["Enums"]["payment_method"]
        }
        Relationships: []
      }
      admin_expenses: {
        Row: {
          amount: number
          category: string
          category_id: number | null
          created_at: string | null
          date: string
          description: string | null
          id: string
          payment_method: string
          recurring: boolean | null
          recurring_period: string | null
          reference_number: string | null
          updated_at: string | null
          vendor_id: number | null
        }
        Insert: {
          amount: number
          category: string
          category_id?: number | null
          created_at?: string | null
          date: string
          description?: string | null
          id?: string
          payment_method: string
          recurring?: boolean | null
          recurring_period?: string | null
          reference_number?: string | null
          updated_at?: string | null
          vendor_id?: number | null
        }
        Update: {
          amount?: number
          category?: string
          category_id?: number | null
          created_at?: string | null
          date?: string
          description?: string | null
          id?: string
          payment_method?: string
          recurring?: boolean | null
          recurring_period?: string | null
          reference_number?: string | null
          updated_at?: string | null
          vendor_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_expenses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "expense_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "admin_expenses_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_onesignal_subscriptions: {
        Row: {
          auth_user_id: string
          created_at: string | null
          enabled: boolean | null
          id: string
          player_id: string
          updated_at: string | null
        }
        Insert: {
          auth_user_id: string
          created_at?: string | null
          enabled?: boolean | null
          id?: string
          player_id: string
          updated_at?: string | null
        }
        Update: {
          auth_user_id?: string
          created_at?: string | null
          enabled?: boolean | null
          id?: string
          player_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_onesignal_subscriptions_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "admin_onesignal_subscriptions_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "admin_onesignal_subscriptions_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "admin_onesignal_subscriptions_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      api_logs: {
        Row: {
          created_at: string | null
          error_message: string | null
          id: string
          invoice_id: string | null
          request_body: string | null
          request_type: string
          response_body: string | null
          status: string
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          id?: string
          invoice_id?: string | null
          request_body?: string | null
          request_type: string
          response_body?: string | null
          status: string
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          id?: string
          invoice_id?: string | null
          request_body?: string | null
          request_type?: string
          response_body?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "api_logs_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      assigned_goals: {
        Row: {
          assigned_by: string
          category: string
          created_at: string
          current_value: number
          description: string | null
          due_date: string | null
          id: string
          pelatis_id: string
          start_date: string
          status: Database["public"]["Enums"]["goal_status"]
          target_value: number
          template_id: string | null
          title: string
          updated_at: string
        }
        Insert: {
          assigned_by: string
          category: string
          created_at?: string
          current_value?: number
          description?: string | null
          due_date?: string | null
          id?: string
          pelatis_id: string
          start_date?: string
          status?: Database["public"]["Enums"]["goal_status"]
          target_value: number
          template_id?: string | null
          title: string
          updated_at?: string
        }
        Update: {
          assigned_by?: string
          category?: string
          created_at?: string
          current_value?: number
          description?: string | null
          due_date?: string | null
          id?: string
          pelatis_id?: string
          start_date?: string
          status?: Database["public"]["Enums"]["goal_status"]
          target_value?: number
          template_id?: string | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "assigned_goals_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "assigned_goals_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "assigned_goals_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "assigned_goals_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "assigned_goals_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "assigned_goals_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "goal_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      badges: {
        Row: {
          category: Database["public"]["Enums"]["badge_category"]
          created_at: string
          description: string
          icon_content: string | null
          icon_type: string | null
          icon_url: string | null
          id: string
          level: Database["public"]["Enums"]["badge_level"]
          name: string
          requirements: Json
        }
        Insert: {
          category: Database["public"]["Enums"]["badge_category"]
          created_at?: string
          description: string
          icon_content?: string | null
          icon_type?: string | null
          icon_url?: string | null
          id?: string
          level: Database["public"]["Enums"]["badge_level"]
          name: string
          requirements: Json
        }
        Update: {
          category?: Database["public"]["Enums"]["badge_category"]
          created_at?: string
          description?: string
          icon_content?: string | null
          icon_type?: string | null
          icon_url?: string | null
          id?: string
          level?: Database["public"]["Enums"]["badge_level"]
          name?: string
          requirements?: Json
        }
        Relationships: []
      }
      bookings: {
        Row: {
          booked_session_id: string | null
          created_at: string | null
          id: string
          pelatis_id: string | null
        }
        Insert: {
          booked_session_id?: string | null
          created_at?: string | null
          id?: string
          pelatis_id?: string | null
        }
        Update: {
          booked_session_id?: string | null
          created_at?: string | null
          id?: string
          pelatis_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["session_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      check_ins: {
        Row: {
          check_in_time: string | null
          id: string
          pelatis_id: string | null
          session_id: string | null
        }
        Insert: {
          check_in_time?: string | null
          id?: string
          pelatis_id?: string | null
          session_id?: string | null
        }
        Update: {
          check_in_time?: string | null
          id?: string
          pelatis_id?: string | null
          session_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "check_ins_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["session_id"]
          },
        ]
      }
      company_settings: {
        Row: {
          address: string | null
          branch: string
          city: string | null
          companyName: string
          country: string
          created_at: string | null
          defaultClassificationCategory: string | null
          defaultClassificationType: string | null
          id: string
          postalCode: string | null
          updated_at: string | null
          vatNumber: string
        }
        Insert: {
          address?: string | null
          branch?: string
          city?: string | null
          companyName: string
          country?: string
          created_at?: string | null
          defaultClassificationCategory?: string | null
          defaultClassificationType?: string | null
          id?: string
          postalCode?: string | null
          updated_at?: string | null
          vatNumber: string
        }
        Update: {
          address?: string | null
          branch?: string
          city?: string | null
          companyName?: string
          country?: string
          created_at?: string | null
          defaultClassificationCategory?: string | null
          defaultClassificationType?: string | null
          id?: string
          postalCode?: string | null
          updated_at?: string | null
          vatNumber?: string
        }
        Relationships: []
      }
      course_durations: {
        Row: {
          days: number
          duration_name: string
          id: number
        }
        Insert: {
          days: number
          duration_name: string
          id?: number
        }
        Update: {
          days?: number
          duration_name?: string
          id?: number
        }
        Relationships: []
      }
      customer_lifetime_metrics: {
        Row: {
          avg_lifespan_months: number
          churn_rate: number
          created_at: string | null
          id: string
          month: string
          total_customers: number
        }
        Insert: {
          avg_lifespan_months: number
          churn_rate: number
          created_at?: string | null
          id?: string
          month: string
          total_customers: number
        }
        Update: {
          avg_lifespan_months?: number
          churn_rate?: number
          created_at?: string | null
          id?: string
          month?: string
          total_customers?: number
        }
        Relationships: []
      }
      email_templates: {
        Row: {
          created_at: string
          html: string
          id: string
          key: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          html: string
          id?: string
          key: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          html?: string
          id?: string
          key?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      error_logs: {
        Row: {
          context: string | null
          created_at: string | null
          error_message: string
          id: string
          metadata: Json | null
          severity: string | null
        }
        Insert: {
          context?: string | null
          created_at?: string | null
          error_message: string
          id?: string
          metadata?: Json | null
          severity?: string | null
        }
        Update: {
          context?: string | null
          created_at?: string | null
          error_message?: string
          id?: string
          metadata?: Json | null
          severity?: string | null
        }
        Relationships: []
      }
      exercise_categories: {
        Row: {
          category_type: string
          category_value: string
          created_at: string | null
          exercise_id: string
          id: string
          is_primary: boolean | null
        }
        Insert: {
          category_type: string
          category_value: string
          created_at?: string | null
          exercise_id: string
          id?: string
          is_primary?: boolean | null
        }
        Update: {
          category_type?: string
          category_value?: string
          created_at?: string | null
          exercise_id?: string
          id?: string
          is_primary?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "exercise_categories_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercise_movements"
            referencedColumns: ["id"]
          },
        ]
      }
      exercise_images: {
        Row: {
          created_at: string | null
          exercise_id: string
          id: string
          image_id: string
          is_primary: boolean | null
        }
        Insert: {
          created_at?: string | null
          exercise_id: string
          id?: string
          image_id: string
          is_primary?: boolean | null
        }
        Update: {
          created_at?: string | null
          exercise_id?: string
          id?: string
          image_id?: string
          is_primary?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "exercise_images_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercise_movements"
            referencedColumns: ["id"]
          },
        ]
      }
      exercise_mapping: {
        Row: {
          exercise_name: string | null
          new_id: string | null
          old_id: string | null
        }
        Insert: {
          exercise_name?: string | null
          new_id?: string | null
          old_id?: string | null
        }
        Update: {
          exercise_name?: string | null
          new_id?: string | null
          old_id?: string | null
        }
        Relationships: []
      }
      exercise_movements: {
        Row: {
          body_part: string
          categories_text: string | null
          created_at: string | null
          description: string | null
          equipment: string
          exercise_name: string
          expertise_level: string
          fts: unknown | null
          id: string
          image_id: string | null
          movement_category: string
          movement_pattern: string[] | null
          video_source: string | null
          video_url: string | null
        }
        Insert: {
          body_part: string
          categories_text?: string | null
          created_at?: string | null
          description?: string | null
          equipment: string
          exercise_name: string
          expertise_level: string
          fts?: unknown | null
          id?: string
          image_id?: string | null
          movement_category: string
          movement_pattern?: string[] | null
          video_source?: string | null
          video_url?: string | null
        }
        Update: {
          body_part?: string
          categories_text?: string | null
          created_at?: string | null
          description?: string | null
          equipment?: string
          exercise_name?: string
          expertise_level?: string
          fts?: unknown | null
          id?: string
          image_id?: string | null
          movement_category?: string
          movement_pattern?: string[] | null
          video_source?: string | null
          video_url?: string | null
        }
        Relationships: []
      }
      exercise_records: {
        Row: {
          calories: number | null
          created_at: string | null
          date_achieved: string | null
          exercise_id: string
          id: string
          is_pr: boolean | null
          notes: string | null
          pelatis_id: string
          reps: number | null
          sets: number | null
          time: string | null
          weight: number | null
          wod_id: string | null
        }
        Insert: {
          calories?: number | null
          created_at?: string | null
          date_achieved?: string | null
          exercise_id: string
          id?: string
          is_pr?: boolean | null
          notes?: string | null
          pelatis_id: string
          reps?: number | null
          sets?: number | null
          time?: string | null
          weight?: number | null
          wod_id?: string | null
        }
        Update: {
          calories?: number | null
          created_at?: string | null
          date_achieved?: string | null
          exercise_id?: string
          id?: string
          is_pr?: boolean | null
          notes?: string | null
          pelatis_id?: string
          reps?: number | null
          sets?: number | null
          time?: string | null
          weight?: number | null
          wod_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "exercise_records_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercise_movements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "exercise_records_wod_id_fkey"
            columns: ["wod_id"]
            isOneToOne: false
            referencedRelation: "wod"
            referencedColumns: ["id"]
          },
        ]
      }
      exercise_videos: {
        Row: {
          created_at: string | null
          exercise_id: string
          id: string
          is_primary: boolean | null
          video_source: string | null
          video_url: string
        }
        Insert: {
          created_at?: string | null
          exercise_id: string
          id?: string
          is_primary?: boolean | null
          video_source?: string | null
          video_url: string
        }
        Update: {
          created_at?: string | null
          exercise_id?: string
          id?: string
          is_primary?: boolean | null
          video_source?: string | null
          video_url?: string
        }
        Relationships: [
          {
            foreignKeyName: "exercise_videos_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercise_movements"
            referencedColumns: ["id"]
          },
        ]
      }
      exercises: {
        Row: {
          category: string
          created_at: string
          description: string | null
          exercise_name: string
          id: number
          image_id: string
          video_source: string | null
          video_url: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          exercise_name: string
          id?: number
          image_id: string
          video_source?: string | null
          video_url: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          exercise_name?: string
          id?: number
          image_id?: string
          video_source?: string | null
          video_url?: string
        }
        Relationships: []
      }
      expense_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          name: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          name: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          name?: string
        }
        Relationships: []
      }
      fitness_measurements: {
        Row: {
          created_at: string | null
          id: string
          measurement_date: string | null
          pelatis_id: string | null
          resting_heart_rate: number
          waist_circumference: number
          weight: number
        }
        Insert: {
          created_at?: string | null
          id?: string
          measurement_date?: string | null
          pelatis_id?: string | null
          resting_heart_rate: number
          waist_circumference: number
          weight: number
        }
        Update: {
          created_at?: string | null
          id?: string
          measurement_date?: string | null
          pelatis_id?: string | null
          resting_heart_rate?: number
          waist_circumference?: number
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fitness_measurements_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      goal_templates: {
        Row: {
          category: string
          created_at: string
          created_by: string
          description: string | null
          duration_days: number | null
          id: string
          is_active: boolean | null
          target_value: number
          title: string
        }
        Insert: {
          category: string
          created_at?: string
          created_by: string
          description?: string | null
          duration_days?: number | null
          id?: string
          is_active?: boolean | null
          target_value: number
          title: string
        }
        Update: {
          category?: string
          created_at?: string
          created_by?: string
          description?: string | null
          duration_days?: number | null
          id?: string
          is_active?: boolean | null
          target_value?: number
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "goal_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "goal_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "goal_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "goal_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      images_rename: {
        Row: {
          created_at: string
          id: number
          newname: string | null
          oldname: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          newname?: string | null
          oldname?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          newname?: string | null
          oldname?: string | null
        }
        Relationships: []
      }
      imported_parousies: {
        Row: {
          Date: string | null
          Day: string | null
          "Days of month": string | null
          Hour: string | null
          Month: string | null
          payments: string | null
          pelates: string | null
          Program: string | null
          trainers: string | null
        }
        Insert: {
          Date?: string | null
          Day?: string | null
          "Days of month"?: string | null
          Hour?: string | null
          Month?: string | null
          payments?: string | null
          pelates?: string | null
          Program?: string | null
          trainers?: string | null
        }
        Update: {
          Date?: string | null
          Day?: string | null
          "Days of month"?: string | null
          Hour?: string | null
          Month?: string | null
          payments?: string | null
          pelates?: string | null
          Program?: string | null
          trainers?: string | null
        }
        Relationships: []
      }
      invoice_lines: {
        Row: {
          created_at: string | null
          description: string
          id: string
          income_classification_category: string
          income_classification_type: string
          invoice_id: string
          line_number: number
          net_value: number
          quantity: number
          unit_price: number
          vat_amount: number
          vat_category: number
        }
        Insert: {
          created_at?: string | null
          description: string
          id?: string
          income_classification_category: string
          income_classification_type: string
          invoice_id: string
          line_number: number
          net_value: number
          quantity: number
          unit_price: number
          vat_amount: number
          vat_category: number
        }
        Update: {
          created_at?: string | null
          description?: string
          id?: string
          income_classification_category?: string
          income_classification_type?: string
          invoice_id?: string
          line_number?: number
          net_value?: number
          quantity?: number
          unit_price?: number
          vat_amount?: number
          vat_category?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_lines_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_payment_methods: {
        Row: {
          amount: number
          created_at: string | null
          id: string
          invoice_id: string
          payment_info: string | null
          payment_type: number
        }
        Insert: {
          amount: number
          created_at?: string | null
          id?: string
          invoice_id: string
          payment_info?: string | null
          payment_type: number
        }
        Update: {
          amount?: number
          created_at?: string | null
          id?: string
          invoice_id?: string
          payment_info?: string | null
          payment_type?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_payment_methods_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          client_country: string | null
          client_id: string | null
          client_name: string
          client_vat: string
          created_at: string | null
          currency: string | null
          id: string
          invoice_number: string
          invoice_series: string
          invoice_type: string
          issue_date: string
          mark: string | null
          qr_url: string | null
          status: string
          total_gross: number
          total_net: number
          total_vat: number
          updated_at: string | null
        }
        Insert: {
          client_country?: string | null
          client_id?: string | null
          client_name: string
          client_vat: string
          created_at?: string | null
          currency?: string | null
          id?: string
          invoice_number: string
          invoice_series: string
          invoice_type?: string
          issue_date: string
          mark?: string | null
          qr_url?: string | null
          status?: string
          total_gross: number
          total_net: number
          total_vat: number
          updated_at?: string | null
        }
        Update: {
          client_country?: string | null
          client_id?: string | null
          client_name?: string
          client_vat?: string
          created_at?: string | null
          currency?: string | null
          id?: string
          invoice_number?: string
          invoice_series?: string
          invoice_type?: string
          issue_date?: string
          mark?: string | null
          qr_url?: string | null
          status?: string
          total_gross?: number
          total_net?: number
          total_vat?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      marketing_costs: {
        Row: {
          channel: string | null
          created_at: string | null
          id: string
          month: string
          new_customers: number
          total_costs: number
        }
        Insert: {
          channel?: string | null
          created_at?: string | null
          id?: string
          month: string
          new_customers: number
          total_costs: number
        }
        Update: {
          channel?: string | null
          created_at?: string | null
          id?: string
          month?: string
          new_customers?: number
          total_costs?: number
        }
        Relationships: []
      }
      merchandise_colors: {
        Row: {
          active: boolean
          color_hex: string | null
          color_name: string
          created_at: string | null
          display_order: number
          id: string
        }
        Insert: {
          active?: boolean
          color_hex?: string | null
          color_name: string
          created_at?: string | null
          display_order?: number
          id?: string
        }
        Update: {
          active?: boolean
          color_hex?: string | null
          color_name?: string
          created_at?: string | null
          display_order?: number
          id?: string
        }
        Relationships: []
      }
      merchandise_inventory: {
        Row: {
          created_at: string | null
          id: string
          item_color: string | null
          item_size: string | null
          item_type: string
          last_updated: string | null
          quantity: number
          reorder_level: number | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          item_color?: string | null
          item_size?: string | null
          item_type: string
          last_updated?: string | null
          quantity?: number
          reorder_level?: number | null
        }
        Update: {
          created_at?: string | null
          id?: string
          item_color?: string | null
          item_size?: string | null
          item_type?: string
          last_updated?: string | null
          quantity?: number
          reorder_level?: number | null
        }
        Relationships: []
      }
      merchandise_sales: {
        Row: {
          comments: string | null
          created_at: string | null
          id: string
          item_color: string | null
          item_size: string | null
          item_type: string
          payment_method: string
          pelatis_id: string
          price: number
          sale_date: string | null
        }
        Insert: {
          comments?: string | null
          created_at?: string | null
          id?: string
          item_color?: string | null
          item_size?: string | null
          item_type: string
          payment_method: string
          pelatis_id: string
          price: number
          sale_date?: string | null
        }
        Update: {
          comments?: string | null
          created_at?: string | null
          id?: string
          item_color?: string | null
          item_size?: string | null
          item_type?: string
          payment_method?: string
          pelatis_id?: string
          price?: number
          sale_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "merchandise_sales_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      merchandise_sizes: {
        Row: {
          active: boolean
          created_at: string | null
          display_order: number
          id: string
          size_name: string
        }
        Insert: {
          active?: boolean
          created_at?: string | null
          display_order?: number
          id?: string
          size_name: string
        }
        Update: {
          active?: boolean
          created_at?: string | null
          display_order?: number
          id?: string
          size_name?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          admin_impersonation: boolean | null
          client_id: string | null
          created_at: string
          expires_at: string | null
          id: string
          link: string | null
          message: string
          metadata: Json | null
          read: boolean
          type: string | null
        }
        Insert: {
          admin_impersonation?: boolean | null
          client_id?: string | null
          created_at?: string
          expires_at?: string | null
          id?: string
          link?: string | null
          message: string
          metadata?: Json | null
          read?: boolean
          type?: string | null
        }
        Update: {
          admin_impersonation?: boolean | null
          client_id?: string | null
          created_at?: string
          expires_at?: string | null
          id?: string
          link?: string | null
          message?: string
          metadata?: Json | null
          read?: boolean
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "notifications_client_id_fkey1"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      notifications_old: {
        Row: {
          client_id: string | null
          created_at: string | null
          id: string
          message: string | null
          metadata: Json | null
          read: boolean | null
          type: string | null
        }
        Insert: {
          client_id?: string | null
          created_at?: string | null
          id?: string
          message?: string | null
          metadata?: Json | null
          read?: boolean | null
          type?: string | null
        }
        Update: {
          client_id?: string | null
          created_at?: string | null
          id?: string
          message?: string | null
          metadata?: Json | null
          read?: boolean | null
          type?: string | null
        }
        Relationships: []
      }
      onesignal_subscriptions: {
        Row: {
          created_at: string
          enabled: boolean
          id: string
          pelatis_id: string
          player_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          enabled?: boolean
          id?: string
          pelatis_id: string
          player_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          enabled?: boolean
          id?: string
          pelatis_id?: string
          player_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "onesignal_subscriptions_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      parousies: {
        Row: {
          client_name: string | null
          client_names: string[] | null
          date: string | null
          id: string
          program: string | null
          trainer: string | null
          trainers: string | null
        }
        Insert: {
          client_name?: string | null
          client_names?: string[] | null
          date?: string | null
          id?: string
          program?: string | null
          trainer?: string | null
          trainers?: string | null
        }
        Update: {
          client_name?: string | null
          client_names?: string[] | null
          date?: string | null
          id?: string
          program?: string | null
          trainer?: string | null
          trainers?: string | null
        }
        Relationships: []
      }
      payment_webhooks: {
        Row: {
          amount: number
          created_at: string
          event_type_id: number
          id: string
          metadata: Json | null
          order_code: string
          processed: boolean | null
          status_id: number
          transaction_id: string
        }
        Insert: {
          amount: number
          created_at?: string
          event_type_id: number
          id?: string
          metadata?: Json | null
          order_code: string
          processed?: boolean | null
          status_id: number
          transaction_id: string
        }
        Update: {
          amount?: number
          created_at?: string
          event_type_id?: number
          id?: string
          metadata?: Json | null
          order_code?: string
          processed?: boolean | null
          status_id?: number
          transaction_id?: string
        }
        Relationships: []
      }
      pelates: {
        Row: {
          address: string | null
          afm: string | null
          auth_user_id: string | null
          client_name: string | null
          created_at: string | null
          date_birth: string | null
          email: string | null
          email_consent: boolean | null
          find_us: string | null
          height: number | null
          id: string
          instagram: string | null
          last_name: string | null
          name: string | null
          notification_consent: boolean | null
          phone: string | null
          sex: string | null
          time_added: string | null
          updated_at: string | null
          waiver_signature: string | null
          waiver_signed: boolean | null
          waiver_signed_date: string | null
        }
        Insert: {
          address?: string | null
          afm?: string | null
          auth_user_id?: string | null
          client_name?: string | null
          created_at?: string | null
          date_birth?: string | null
          email?: string | null
          email_consent?: boolean | null
          find_us?: string | null
          height?: number | null
          id?: string
          instagram?: string | null
          last_name?: string | null
          name?: string | null
          notification_consent?: boolean | null
          phone?: string | null
          sex?: string | null
          time_added?: string | null
          updated_at?: string | null
          waiver_signature?: string | null
          waiver_signed?: boolean | null
          waiver_signed_date?: string | null
        }
        Update: {
          address?: string | null
          afm?: string | null
          auth_user_id?: string | null
          client_name?: string | null
          created_at?: string | null
          date_birth?: string | null
          email?: string | null
          email_consent?: boolean | null
          find_us?: string | null
          height?: number | null
          id?: string
          instagram?: string | null
          last_name?: string | null
          name?: string | null
          notification_consent?: boolean | null
          phone?: string | null
          sex?: string | null
          time_added?: string | null
          updated_at?: string | null
          waiver_signature?: string | null
          waiver_signed?: boolean | null
          waiver_signed_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pelates_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: true
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "pelates_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: true
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "pelates_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: true
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "pelates_auth_user_id_fkey"
            columns: ["auth_user_id"]
            isOneToOne: true
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      pelates_parousies: {
        Row: {
          parousies_id: string
          pelates_id: string
        }
        Insert: {
          parousies_id: string
          pelates_id: string
        }
        Update: {
          parousies_id?: string
          pelates_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_parousies"
            columns: ["parousies_id"]
            isOneToOne: false
            referencedRelation: "parousies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_parousies_parousies_id_fkey"
            columns: ["parousies_id"]
            isOneToOne: false
            referencedRelation: "parousies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_parousies_pelates_id_fkey"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      pelates_tags: {
        Row: {
          pelatis_id: string
          tag_id: string
        }
        Insert: {
          pelatis_id: string
          tag_id: string
        }
        Update: {
          pelatis_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_tags_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pelates_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      pliromes: {
        Row: {
          checkins_allowed: Database["public"]["Enums"]["checkins_limit"]
          comments: string | null
          course: string | null
          course_duration_id: number | null
          date_money_gave: string | null
          debt: number | null
          end_date: string | null
          grace_period: number | null
          id: string
          money_gave: number | null
          nodebt: boolean | null
          pelates_id: string | null
          pliromes_created: string | null
          price_program: number | null
          receipt: string | null
          start_program: string | null
          way_of_payment: string | null
        }
        Insert: {
          checkins_allowed?: Database["public"]["Enums"]["checkins_limit"]
          comments?: string | null
          course?: string | null
          course_duration_id?: number | null
          date_money_gave?: string | null
          debt?: number | null
          end_date?: string | null
          grace_period?: number | null
          id?: string
          money_gave?: number | null
          nodebt?: boolean | null
          pelates_id?: string | null
          pliromes_created?: string | null
          price_program?: number | null
          receipt?: string | null
          start_program?: string | null
          way_of_payment?: string | null
        }
        Update: {
          checkins_allowed?: Database["public"]["Enums"]["checkins_limit"]
          comments?: string | null
          course?: string | null
          course_duration_id?: number | null
          date_money_gave?: string | null
          debt?: number | null
          end_date?: string | null
          grace_period?: number | null
          id?: string
          money_gave?: number | null
          nodebt?: boolean | null
          pelates_id?: string | null
          pliromes_created?: string | null
          price_program?: number | null
          receipt?: string | null
          start_program?: string | null
          way_of_payment?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_course_duration"
            columns: ["course_duration_id"]
            isOneToOne: false
            referencedRelation: "course_durations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pliromes_course_fkey"
            columns: ["course"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      programs: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      recipes: {
        Row: {
          calories: number | null
          carbohydrates: number | null
          cooking_time: string | null
          difficulty: string | null
          fats: number | null
          fiber: number | null
          id: number
          image_name: string | null
          image_url: string | null
          main_ingredient: string[] | null
          name: string | null
          protein: number | null
          salt: number | null
          saturated_fats: number | null
          servings: string | null
          sugars: number | null
          url: string | null
        }
        Insert: {
          calories?: number | null
          carbohydrates?: number | null
          cooking_time?: string | null
          difficulty?: string | null
          fats?: number | null
          fiber?: number | null
          id?: number
          image_name?: string | null
          image_url?: string | null
          main_ingredient?: string[] | null
          name?: string | null
          protein?: number | null
          salt?: number | null
          saturated_fats?: number | null
          servings?: string | null
          sugars?: number | null
          url?: string | null
        }
        Update: {
          calories?: number | null
          carbohydrates?: number | null
          cooking_time?: string | null
          difficulty?: string | null
          fats?: number | null
          fiber?: number | null
          id?: number
          image_name?: string | null
          image_url?: string | null
          main_ingredient?: string[] | null
          name?: string | null
          protein?: number | null
          salt?: number | null
          saturated_fats?: number | null
          servings?: string | null
          sugars?: number | null
          url?: string | null
        }
        Relationships: []
      }
      reconciliation_records: {
        Row: {
          account_name: Database["public"]["Enums"]["payment_method"]
          actual_balance: number
          adjusted: boolean | null
          created_at: string | null
          created_by: string
          difference: number
          id: string
          notes: string | null
          reconciliation_date: string
          system_balance: number
        }
        Insert: {
          account_name: Database["public"]["Enums"]["payment_method"]
          actual_balance: number
          adjusted?: boolean | null
          created_at?: string | null
          created_by: string
          difference: number
          id?: string
          notes?: string | null
          reconciliation_date: string
          system_balance: number
        }
        Update: {
          account_name?: Database["public"]["Enums"]["payment_method"]
          actual_balance?: number
          adjusted?: boolean | null
          created_at?: string | null
          created_by?: string
          difference?: number
          id?: string
          notes?: string | null
          reconciliation_date?: string
          system_balance?: number
        }
        Relationships: [
          {
            foreignKeyName: "reconciliation_records_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "reconciliation_records_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "reconciliation_records_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "reconciliation_records_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      roles: {
        Row: {
          id: number
          name: string
        }
        Insert: {
          id?: number
          name: string
        }
        Update: {
          id?: number
          name?: string
        }
        Relationships: []
      }
      sessions: {
        Row: {
          created_at: string | null
          created_by: string | null
          duration: number
          id: string
          max_participants: number
          program_id: string
          start_time: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          duration: number
          id?: string
          max_participants: number
          program_id: string
          start_time: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          duration?: number
          id?: string
          max_participants?: number
          program_id?: string
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_sessions_program"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sessions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "sessions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "sessions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["auth_user_id"]
          },
          {
            foreignKeyName: "sessions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["auth_user_id"]
          },
        ]
      }
      strength_exercise_records: {
        Row: {
          date_performed: string
          exercise_id: string
          id: string
          is_pr: boolean
          is_warmup: boolean
          notes: string | null
          pelatis_id: string
          reps: number
          sets: number
          weight: number
          workout_id: string
        }
        Insert: {
          date_performed?: string
          exercise_id: string
          id?: string
          is_pr?: boolean
          is_warmup?: boolean
          notes?: string | null
          pelatis_id: string
          reps: number
          sets: number
          weight: number
          workout_id: string
        }
        Update: {
          date_performed?: string
          exercise_id?: string
          id?: string
          is_pr?: boolean
          is_warmup?: boolean
          notes?: string | null
          pelatis_id?: string
          reps?: number
          sets?: number
          weight?: number
          workout_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "strength_exercise_records_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercise_movements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_exercise_records_workout_id_fkey"
            columns: ["workout_id"]
            isOneToOne: false
            referencedRelation: "strength_workouts"
            referencedColumns: ["id"]
          },
        ]
      }
      strength_training_plans: {
        Row: {
          created_at: string
          id: string
          increment_settings: Json
          is_active: boolean
          pelatis_id: string
          program_id: string
          start_date: string
          starting_weights: Json
        }
        Insert: {
          created_at?: string
          id?: string
          increment_settings: Json
          is_active?: boolean
          pelatis_id: string
          program_id: string
          start_date: string
          starting_weights: Json
        }
        Update: {
          created_at?: string
          id?: string
          increment_settings?: Json
          is_active?: boolean
          pelatis_id?: string
          program_id?: string
          start_date?: string
          starting_weights?: Json
        }
        Relationships: [
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_training_plans_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "strength_training_plans_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      strength_workouts: {
        Row: {
          completed_date: string | null
          day_of_week: string
          id: string
          plan_id: string
          scheduled_date: string
          status: Database["public"]["Enums"]["workout_status"]
          week_number: number
          workout_type: string
        }
        Insert: {
          completed_date?: string | null
          day_of_week: string
          id?: string
          plan_id: string
          scheduled_date: string
          status?: Database["public"]["Enums"]["workout_status"]
          week_number: number
          workout_type: string
        }
        Update: {
          completed_date?: string | null
          day_of_week?: string
          id?: string
          plan_id?: string
          scheduled_date?: string
          status?: Database["public"]["Enums"]["workout_status"]
          week_number?: number
          workout_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "strength_workouts_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "strength_training_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_reminders: {
        Row: {
          client_id: string
          id: string
          reminder_type: string
          sent_at: string | null
          subscription_id: string
        }
        Insert: {
          client_id: string
          id?: string
          reminder_type: string
          sent_at?: string | null
          subscription_id: string
        }
        Update: {
          client_id?: string
          id?: string
          reminder_type?: string
          sent_at?: string | null
          subscription_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "subscription_reminders_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "subscription_reminders_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "active_subscriptions"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_reminders_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "latest_client_subscriptions"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_reminders_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "pliromes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_reminders_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["subscription_id"]
          },
        ]
      }
      support_messages: {
        Row: {
          attachments: Json | null
          content: string
          created_at: string | null
          id: string
          is_coach_reply: boolean | null
          sender_id: string | null
          thread_id: string | null
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          content: string
          created_at?: string | null
          id?: string
          is_coach_reply?: boolean | null
          sender_id?: string | null
          thread_id?: string | null
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          content?: string
          created_at?: string | null
          id?: string
          is_coach_reply?: boolean | null
          sender_id?: string | null
          thread_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_messages_thread_id_fkey"
            columns: ["thread_id"]
            isOneToOne: false
            referencedRelation: "support_threads"
            referencedColumns: ["id"]
          },
        ]
      }
      support_threads: {
        Row: {
          category: Database["public"]["Enums"]["message_category"]
          client_id: string
          created_at: string | null
          id: string
          last_reply_at: string | null
          metadata: Json | null
          priority: Database["public"]["Enums"]["message_priority"] | null
          resolved_at: string | null
          status: Database["public"]["Enums"]["message_status"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          category: Database["public"]["Enums"]["message_category"]
          client_id: string
          created_at?: string | null
          id?: string
          last_reply_at?: string | null
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["message_priority"] | null
          resolved_at?: string | null
          status?: Database["public"]["Enums"]["message_status"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          category?: Database["public"]["Enums"]["message_category"]
          client_id?: string
          created_at?: string | null
          id?: string
          last_reply_at?: string | null
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["message_priority"] | null
          resolved_at?: string | null
          status?: Database["public"]["Enums"]["message_status"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "support_threads_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      tags: {
        Row: {
          color: string
          created_at: string | null
          id: string
          name: string
        }
        Insert: {
          color: string
          created_at?: string | null
          id?: string
          name: string
        }
        Update: {
          color?: string
          created_at?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      timer_rooms: {
        Row: {
          active_users: number | null
          created_at: string | null
          id: string
        }
        Insert: {
          active_users?: number | null
          created_at?: string | null
          id: string
        }
        Update: {
          active_users?: number | null
          created_at?: string | null
          id?: string
        }
        Relationships: []
      }
      user_badges: {
        Row: {
          achieved_at: string
          badge_id: string
          id: string
          pelatis_id: string
          progress: Json
        }
        Insert: {
          achieved_at?: string
          badge_id: string
          id?: string
          pelatis_id: string
          progress?: Json
        }
        Update: {
          achieved_at?: string
          badge_id?: string
          id?: string
          pelatis_id?: string
          progress?: Json
        }
        Relationships: [
          {
            foreignKeyName: "user_badges_badge_id_fkey"
            columns: ["badge_id"]
            isOneToOne: false
            referencedRelation: "badges"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "user_badges_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      user_goals: {
        Row: {
          category: string
          completed_at: string | null
          created_at: string
          current_value: number
          description: string | null
          id: string
          pelatis_id: string
          start_date: string
          target_date: string | null
          target_value: number
          title: string
          updated_at: string
        }
        Insert: {
          category: string
          completed_at?: string | null
          created_at?: string
          current_value?: number
          description?: string | null
          id?: string
          pelatis_id: string
          start_date?: string
          target_date?: string | null
          target_value: number
          title: string
          updated_at?: string
        }
        Update: {
          category?: string
          completed_at?: string | null
          created_at?: string
          current_value?: number
          description?: string | null
          id?: string
          pelatis_id?: string
          start_date?: string
          target_date?: string | null
          target_value?: number
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          auth_user_id: string
          role_id: number
        }
        Insert: {
          auth_user_id: string
          role_id: number
        }
        Update: {
          auth_user_id?: string
          role_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_wods: {
        Row: {
          check_in_id: string | null
          created_at: string | null
          id: string
          wod_id: string | null
        }
        Insert: {
          check_in_id?: string | null
          created_at?: string | null
          id?: string
          wod_id?: string | null
        }
        Update: {
          check_in_id?: string | null
          created_at?: string | null
          id?: string
          wod_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_wods_check_in_id_fkey"
            columns: ["check_in_id"]
            isOneToOne: false
            referencedRelation: "check_ins"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_wods_check_in_id_fkey"
            columns: ["check_in_id"]
            isOneToOne: false
            referencedRelation: "latest_member_checkins"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_wods_wod_id_fkey"
            columns: ["wod_id"]
            isOneToOne: false
            referencedRelation: "wod"
            referencedColumns: ["id"]
          },
        ]
      }
      vendors: {
        Row: {
          category_id: number | null
          contact_info: string | null
          created_at: string | null
          id: number
          name: string
        }
        Insert: {
          category_id?: number | null
          contact_info?: string | null
          created_at?: string | null
          id?: number
          name: string
        }
        Update: {
          category_id?: number | null
          contact_info?: string | null
          created_at?: string | null
          id?: number
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "vendors_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "expense_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      winbank_balances: {
        Row: {
          balance: number
          created_at: string | null
          description: string | null
          id: string
          transaction_date: string
        }
        Insert: {
          balance: number
          created_at?: string | null
          description?: string | null
          id?: string
          transaction_date: string
        }
        Update: {
          balance?: number
          created_at?: string | null
          description?: string | null
          id?: string
          transaction_date?: string
        }
        Relationships: []
      }
      wod: {
        Row: {
          content: string
          created_at: string | null
          date: string
          exercises: string[] | null
          id: string
          is_published: boolean | null
          published_at: string | null
          warmup: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          date: string
          exercises?: string[] | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          warmup?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          date?: string
          exercises?: string[] | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          warmup?: string | null
        }
        Relationships: []
      }
      wods_details: {
        Row: {
          date: string
          day: string | null
          main_lift: string | null
          workout_b: string | null
          workout_c: string | null
        }
        Insert: {
          date: string
          day?: string | null
          main_lift?: string | null
          workout_b?: string | null
          workout_c?: string | null
        }
        Update: {
          date?: string
          day?: string | null
          main_lift?: string | null
          workout_b?: string | null
          workout_c?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      active_subscriptions: {
        Row: {
          client_id: string | null
          days_until_expiration: number | null
          end_date: string | null
          grace_period_days: number | null
          last_name: string | null
          name: string | null
          price_program: number | null
          program_duration: number | null
          program_name: string | null
          program_name_display: string | null
          start_date: string | null
          subscription_id: string | null
          subscription_status: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pliromes_course_fkey"
            columns: ["program_name"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      auth_debug_audit_summary: {
        Row: {
          created_at: string | null
          ip_address: string | null
        }
        Relationships: []
      }
      auth_debug_email_mismatches_view: {
        Row: {
          auth_email: string | null
          auth_user_id: string | null
          last_name: string | null
          name: string | null
          pelates_email: string | null
          pelatis_id: string | null
        }
        Relationships: []
      }
      auth_debug_orphaned_users_view: {
        Row: {
          auth_email: string | null
          auth_user_id: string | null
          created_at: string | null
          last_sign_in_at: string | null
          pelates_email: string | null
          pelatis_id_by_email: string | null
        }
        Relationships: []
      }
      auth_debug_pelates_missing_auth_view: {
        Row: {
          created_at: string | null
          email: string | null
          id: string | null
          last_name: string | null
          name: string | null
        }
        Relationships: []
      }
      auth_debug_users_without_roles_view: {
        Row: {
          auth_user_id: string | null
          created_at: string | null
          email: string | null
          last_sign_in_at: string | null
          pelates_email: string | null
          pelatis_id: string | null
        }
        Relationships: []
      }
      auth_user_status_view: {
        Row: {
          auth_email: string | null
          auth_user_id: string | null
          confirmed_at: string | null
          created_at: string | null
          email_confirmed_at: string | null
          has_pelates_record: boolean | null
          has_role: boolean | null
          is_super_admin: boolean | null
          last_name: string | null
          last_sign_in_at: string | null
          name: string | null
          pelates_email: string | null
          pelatis_id: string | null
          roles: string | null
        }
        Relationships: []
      }
      avg_attendance_by_weektime: {
        Row: {
          attendance_count: number | null
          avg_attendance: number | null
          session_count: number | null
          start_time: string | null
          weekday: string | null
        }
        Relationships: []
      }
      badge_achievement_stats: {
        Row: {
          avg_days_to_achieve: number | null
          category: Database["public"]["Enums"]["badge_category"] | null
          level: Database["public"]["Enums"]["badge_level"] | null
          times_awarded: number | null
          unique_users: number | null
        }
        Relationships: []
      }
      bookings_view: {
        Row: {
          booked_session_id: string | null
          created_at: string | null
          duration: number | null
          id: string | null
          pelatis_id: string | null
          start_time: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["session_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "bookings_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      class_capacity_metrics: {
        Row: {
          capacity_rate: number | null
          month: string | null
          total_attendance: number | null
          total_capacity: number | null
        }
        Relationships: []
      }
      customer_acquisition_metrics: {
        Row: {
          cac: number | null
          month: string | null
          new_customers: number | null
          total_costs: number | null
        }
        Insert: {
          cac?: never
          month?: string | null
          new_customers?: number | null
          total_costs?: number | null
        }
        Update: {
          cac?: never
          month?: string | null
          new_customers?: number | null
          total_costs?: number | null
        }
        Relationships: []
      }
      customer_lifetime_metrics_view: {
        Row: {
          avg_lifespan_months: number | null
          churn_rate: number | null
          month: string | null
          total_customers: number | null
        }
        Insert: {
          avg_lifespan_months?: number | null
          churn_rate?: number | null
          month?: string | null
          total_customers?: number | null
        }
        Update: {
          avg_lifespan_months?: number | null
          churn_rate?: number | null
          month?: string | null
          total_customers?: number | null
        }
        Relationships: []
      }
      latest_client_subscriptions: {
        Row: {
          client_id: string | null
          days_until_expiration: number | null
          end_date: string | null
          grace_period_days: number | null
          last_name: string | null
          name: string | null
          price_program: number | null
          program_duration: number | null
          program_name: string | null
          program_name_display: string | null
          start_date: string | null
          subscription_id: string | null
          subscription_status: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pliromes_course_fkey"
            columns: ["program_name"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      latest_member_checkins: {
        Row: {
          check_in_time: string | null
          id: string | null
          last_name: string | null
          name: string | null
          pelatis_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "check_ins_pelatis_id_fkey"
            columns: ["pelatis_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      monthly_attendance_view: {
        Row: {
          month: string | null
          total_capacity: number | null
          total_checkins: number | null
          unique_sessions: number | null
        }
        Relationships: []
      }
      monthly_churn_metrics: {
        Row: {
          churn_rate: number | null
          churned_members: number | null
          month: string | null
          total_members: number | null
        }
        Relationships: []
      }
      monthly_kpi_metrics: {
        Row: {
          month: string | null
          mrr: number | null
          total_checkins: number | null
          total_members: number | null
          unique_sessions: number | null
        }
        Relationships: []
      }
      monthly_members_view: {
        Row: {
          month: string | null
          total_members: number | null
        }
        Relationships: []
      }
      monthly_retention_view: {
        Row: {
          churned_members: number | null
          month: string | null
          retained_members: number | null
          total_members: number | null
        }
        Relationships: []
      }
      monthly_revenue_view: {
        Row: {
          month: string | null
          mrr: number | null
        }
        Relationships: []
      }
      monthly_subscriptions: {
        Row: {
          client_id: string | null
          end_date: string | null
          money_gave: number | null
          month: string | null
          price_program: number | null
          program_name: string | null
          start_program: string | null
        }
        Insert: {
          client_id?: string | null
          end_date?: string | null
          money_gave?: number | null
          month?: never
          price_program?: number | null
          program_name?: string | null
          start_program?: string | null
        }
        Update: {
          client_id?: string | null
          end_date?: string | null
          money_gave?: number | null
          month?: never
          price_program?: number | null
          program_name?: string | null
          start_program?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "pliromes_course_fkey"
            columns: ["program_name"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      recent_errors: {
        Row: {
          context: string | null
          created_at: string | null
          error_message: string | null
          id: string | null
        }
        Insert: {
          context?: string | null
          created_at?: string | null
          error_message?: string | null
          id?: string | null
        }
        Update: {
          context?: string | null
          created_at?: string | null
          error_message?: string | null
          id?: string | null
        }
        Relationships: []
      }
      session_booking_counts: {
        Row: {
          booked_session_id: string | null
          total_bookings: number | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_booked_session_id_fkey"
            columns: ["booked_session_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["session_id"]
          },
        ]
      }
      subscription_metrics: {
        Row: {
          end_date: string | null
          money_gave: number | null
          pelates_id: string | null
          price_program: number | null
          start_program: string | null
          total_checkins: number | null
          unique_sessions_attended: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_email_mismatches_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_orphaned_users_view"
            referencedColumns: ["pelatis_id_by_email"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_pelates_missing_auth_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_debug_users_without_roles_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "auth_user_status_view"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "pelates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_achievement_timeline"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "user_checkin_stats"
            referencedColumns: ["pelatis_id"]
          },
          {
            foreignKeyName: "fk_pliromes_pelates"
            columns: ["pelates_id"]
            isOneToOne: false
            referencedRelation: "weekly_booking_forecast"
            referencedColumns: ["pelatis_id"]
          },
        ]
      }
      user_achievement_timeline: {
        Row: {
          achieved_at: string | null
          badge_name: string | null
          category: Database["public"]["Enums"]["badge_category"] | null
          last_name: string | null
          level: Database["public"]["Enums"]["badge_level"] | null
          name: string | null
          user_id: string | null
        }
        Relationships: []
      }
      user_checkin_stats: {
        Row: {
          last_name: string | null
          monthly_breakdown: Json | null
          name: string | null
          pelatis_id: string | null
          total_checkins: number | null
        }
        Relationships: []
      }
      weekly_booking_forecast: {
        Row: {
          booking_created_at: string | null
          booking_id: string | null
          days_until_expiration: number | null
          duration: number | null
          end_date: string | null
          last_name: string | null
          max_participants: number | null
          name: string | null
          pelatis_id: string | null
          program_id: string | null
          program_name_display: string | null
          session_id: string | null
          session_program_name: string | null
          start_time: string | null
          subscription_id: string | null
          subscription_program_id: string | null
          subscription_status: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_sessions_program"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pliromes_course_fkey"
            columns: ["subscription_program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      calculate_end_time: {
        Args: { start_time: string; duration: number }
        Returns: string
      }
      calculate_next_badge_progress: {
        Args: { user_id_param: string }
        Returns: {
          category: string
          next_badge_name: string
          current_progress: number
          target_value: number
          percentage: number
        }[]
      }
      calculate_streak: {
        Args: { user_id: string }
        Returns: number
      }
      check_badge_requirements: {
        Args: { badge_requirements: Json; user_progress: Json }
        Returns: boolean
      }
      classify_video_source: {
        Args: { url: string }
        Returns: string
      }
      complete_strength_workout: {
        Args: { p_workout_id: string }
        Returns: undefined
      }
      create_auth_user_for_pelati: {
        Args: { admin_user_id: string; p_pelati_id: string; p_email: string }
        Returns: Json
      }
      create_booking: {
        Args: { p_booked_session_id: string; p_pelatis_id: string }
        Returns: undefined
      }
      create_check_in: {
        Args: { p_user_id: string; p_session_id: string }
        Returns: string
      }
      create_subscription_reminders_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_user_for_pelati: {
        Args: { admin_user_id: string; p_pelati_id: string; p_email: string }
        Returns: Json
      }
      debug_check_ins_insert: {
        Args: { p_pelatis_id: string; p_session_id: string }
        Returns: Json
      }
      decrement_balance: {
        Args: { account: string; amount: number }
        Returns: number
      }
      decrement_users: {
        Args: { room_id: string }
        Returns: number
      }
      delete_exercise_with_relations: {
        Args: { exercise_id: string }
        Returns: undefined
      }
      execute_sql: {
        Args: { sql_query: string }
        Returns: undefined
      }
      fix_uuid_columns: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_daily_bookings_summary: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_active_subscriptions: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          last_name: string
          program_name: string
          start_date: string
          end_date: string
          days_until_expiration: number
        }[]
      }
      get_average_class_attendance: {
        Args: { start_date: string; end_date: string }
        Returns: number
      }
      get_average_member_lifetime: {
        Args: Record<PropertyKey, never>
        Returns: unknown
      }
      get_class_capacity_percentage: {
        Args: { session_id_param: string }
        Returns: number
      }
      get_client_statistics: {
        Args: Record<PropertyKey, never>
        Returns: {
          client_id: number
          client_name: string
          total_attendances: number
          total_payments: number
        }[]
      }
      get_duplicate_notifications_count: {
        Args: Record<PropertyKey, never>
        Returns: {
          count: number
        }[]
      }
      get_exercise_related_data: {
        Args: { exercise_ids: string[] }
        Returns: {
          exercise_id: string
          data_type: string
          data: Json
        }[]
      }
      get_exercise_related_data_grouped: {
        Args: { exercise_ids: string[] }
        Returns: {
          exercise_id: string
          videos: Json
          images: Json
          categories: Json
        }[]
      }
      get_expenses_summary: {
        Args: { start_date: string; end_date: string; category_filter?: string }
        Returns: {
          total_amount: number
          category: string
          count: number
        }[]
      }
      get_latest_check_ins: {
        Args: Record<PropertyKey, never>
        Returns: {
          check_in_id: string
          pelatis_id: string
          check_in_time: string
          session_id: string
        }[]
      }
      get_member_retention_rate: {
        Args: { start_date: string; end_date: string }
        Returns: number
      }
      get_monthly_churn_rate: {
        Args: { month_date: string }
        Returns: number
      }
      get_monthly_income: {
        Args: Record<PropertyKey, never>
        Returns: {
          month: string
          total: number
        }[]
      }
      get_monthly_payment_report: {
        Args: Record<PropertyKey, never>
        Returns: {
          month: string
          cash_total: number
          pos_total: number
          total: number
          payments: Json
        }[]
      }
      get_monthly_recurring_revenue: {
        Args: { month_date: string }
        Returns: number
      }
      get_notification_hourly_volume: {
        Args: Record<PropertyKey, never>
        Returns: {
          hour: string
          count: number
        }[]
      }
      get_notification_type_distribution: {
        Args: Record<PropertyKey, never>
        Returns: {
          type: string
          count: number
        }[]
      }
      get_revenue_per_member: {
        Args: { month_date: string }
        Returns: number
      }
      get_sales_by_color: {
        Args: Record<PropertyKey, never>
        Returns: {
          item_color: string
          count: number
          revenue: number
        }[]
      }
      get_sales_by_size: {
        Args: Record<PropertyKey, never>
        Returns: {
          item_size: string
          count: number
          revenue: number
        }[]
      }
      get_subscription_details: {
        Args: { subscription_id: string }
        Returns: {
          effective_end_date: string
          total_extension_days: number
          grace_period_days: number
          is_in_grace_period: boolean
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
        }[]
      }
      get_todays_strength_workout: {
        Args: { p_user_id: string }
        Returns: {
          workout_id: string
          week_number: number
          workout_type: string
          exercises: Json
        }[]
      }
      get_unread_notifications_by_client: {
        Args: Record<PropertyKey, never>
        Returns: {
          client_id: string
          client_name: string
          unread_count: number
        }[]
      }
      get_user_achievement_stats: {
        Args: { user_id_param: string }
        Returns: {
          total_badges: number
          badges_by_level: Json
          badges_by_category: Json
          recent_achievements: Json
          current_streak: number
          longest_streak: number
        }[]
      }
      get_user_wods: {
        Args: { user_id: string }
        Returns: {
          id: number
          date: string
          wod_content: string
        }[]
      }
      getUserRoles: {
        Args: { p_user_id: string }
        Returns: string[]
      }
      handle_user_role_assignment: {
        Args: {
          p_auth_user_id: string
          p_pelatis_id: string
          p_role_id: number
        }
        Returns: undefined
      }
      increment_balance: {
        Args: { account: string; amount: number }
        Returns: number
      }
      is_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_session_full: {
        Args: { session_id: string }
        Returns: boolean
      }
      link_auth_user_to_pelati: {
        Args: { p_auth_user_id: string; p_pelati_id: string }
        Returns: undefined
      }
      record_daily_subscription_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      search_exercises: {
        Args: { search_query: string }
        Returns: {
          body_part: string
          categories_text: string | null
          created_at: string | null
          description: string | null
          equipment: string
          exercise_name: string
          expertise_level: string
          fts: unknown | null
          id: string
          image_id: string | null
          movement_category: string
          movement_pattern: string[] | null
          video_source: string | null
          video_url: string | null
        }[]
      }
      send_admin_booking_notification: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      send_push_notification: {
        Args: { p_title: string; p_message: string; p_user_id: string }
        Returns: undefined
      }
      show_table_details: {
        Args: { p_table_name: string }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: string
          column_default: string
        }[]
      }
      split_string: {
        Args: { p_string: string; p_delimiter: string }
        Returns: {
          value: string
        }[]
      }
      test_notification_triggers: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      test_uuid_handling: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      upsert_exercise_related_data: {
        Args: {
          p_exercise_id: string
          p_videos: Json
          p_images: Json
          p_categories: Json
        }
        Returns: undefined
      }
    }
    Enums: {
      badge_category:
        | "attendance"
        | "exercise"
        | "progression"
        | "consistency"
        | "milestone"
      badge_level: "bronze" | "silver" | "gold" | "platinum"
      checkins_limit: "8" | "12" | "unlimited" | "1" | "5"
      expense_category:
        | "IRS"
        | "SOCIAL_SECURITY"
        | "BANK_DEPOSIT"
        | "VIVA_FEES"
        | "OTHER"
      goal_status: "pending" | "accepted" | "declined" | "completed"
      message_category: "injury" | "nutrition" | "training" | "general"
      message_priority: "low" | "medium" | "high" | "urgent"
      message_status: "open" | "in_progress" | "resolved" | "closed"
      notification_type: "info" | "warning" | "error"
      payment_method: "BANK_TRANSFER" | "VIVA" | "CASH" | "OTHER" | "WINBANK"
      t_shirt_color: "Red" | "Blue" | "Black"
      t_shirt_size: "S" | "M" | "L" | "XL" | "XXL"
      workout_status: "scheduled" | "completed" | "skipped"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      badge_category: [
        "attendance",
        "exercise",
        "progression",
        "consistency",
        "milestone",
      ],
      badge_level: ["bronze", "silver", "gold", "platinum"],
      checkins_limit: ["8", "12", "unlimited", "1", "5"],
      expense_category: [
        "IRS",
        "SOCIAL_SECURITY",
        "BANK_DEPOSIT",
        "VIVA_FEES",
        "OTHER",
      ],
      goal_status: ["pending", "accepted", "declined", "completed"],
      message_category: ["injury", "nutrition", "training", "general"],
      message_priority: ["low", "medium", "high", "urgent"],
      message_status: ["open", "in_progress", "resolved", "closed"],
      notification_type: ["info", "warning", "error"],
      payment_method: ["BANK_TRANSFER", "VIVA", "CASH", "OTHER", "WINBANK"],
      t_shirt_color: ["Red", "Blue", "Black"],
      t_shirt_size: ["S", "M", "L", "XL", "XXL"],
      workout_status: ["scheduled", "completed", "skipped"],
    },
  },
} as const
