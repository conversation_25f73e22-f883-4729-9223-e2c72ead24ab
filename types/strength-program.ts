// types/strength-program.ts  

export type PlanData = {
  id: string;
  pelatis_id: string;
  program_id: string;
  starting_weights: Record<string, number> | null; // Allow null
  increment_settings: Record<string, number> | null; // Allow null
  start_date: string;
  is_active: boolean;
  created_at: string;
};

export type Exercise = {
  exercise_id: string;
  exercise_name: string;
  weight: number;
  sets: number;
  reps: number;
  is_warmup: boolean;
};

export interface TodaysWorkout {
  workout_id: string;
  week_number: number;
  workout_type: string;
  exercises: Exercise[];
  scheduled_date: string; // Add this property
}

export type WorkoutStatus = 'scheduled' | 'completed' | 'skipped';

export type StrengthWorkout = {
  id: string;
  plan_id: string;
  workout_type: string;
  scheduled_date: string;
  day_of_week: string;
  week_number: number;
  status: WorkoutStatus;
  completed_date?: string;
};

export type WarmupSet = {
  weight: number;
  reps: number;
  percentage: number;
  description: string;
  isWarmup: boolean;
};

export type ExerciseRecord = {
  id: string;
  workout_id: string;
  pelatis_id: string;
  exercise_id: string;
  weight: number;
  sets: number;
  reps: number;
  date_performed: string;
  is_pr: boolean;
  is_warmup: boolean;
};

// Add this to your types/strength-program.ts file
export type CompletedExercise = {
  exercise_id: string;
  weight: number;
  sets: number;
  reps: number;
  is_pr: boolean;
  notes?: string;
};