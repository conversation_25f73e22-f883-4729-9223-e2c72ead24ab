export interface VivaCustomer {
  email: string;
  fullName: string;
  phone?: string;
  countryCode: string;
  requestLang: string;
}

export interface VivaPaymentOrderRequest {
  amount: number;
  customerTrns: string;
  customer: VivaCustomer;
  paymentTimeout?: number;
  preauth?: boolean;
  allowRecurring?: boolean;
  maxInstallments?: number;
  paymentNotification?: boolean;
  tipAmount?: number;
  disableExactAmount?: boolean;
  disableCash?: boolean;
  disableWallet?: boolean;
  sourceCode: string;
  merchantTrns: string;
  tags?: string[];
  cardTokens?: string[];
  redirectFromMerchant?: boolean;
}

export interface VivaPaymentOrderResponse {
  orderCode: string;
  // Other fields that might be returned in the API response
}

export interface PaymentDetails {
  amount: number;
  description: string;
  successUrl?: string;
  failureUrl?: string;
}

export interface CreatePaymentOrderRequest {
  amount: number;
  description: string;
  successUrl?: string;
  failureUrl?: string;
}

export interface PaymentOrderError {
  message: string;
  details?: string;
}

export interface CheckoutPageProps extends PaymentDetails {
  // Extends PaymentDetails to inherit all its properties
  // Add any additional props specific to the CheckoutPage component if needed
}
