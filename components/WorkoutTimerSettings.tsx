import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { WorkoutSettings, WorkoutType } from '@/types/workout';

interface WorkoutTimerSettingsProps {
  workoutType: WorkoutType;
  settings: WorkoutSettings;
  updateSettings: (settings: WorkoutSettings) => void;
}

export const WorkoutTimerSettings: React.FC<WorkoutTimerSettingsProps> = ({
  workoutType,
  settings,
  updateSettings,
}) => {
  const updateSetting = (key: string, value: number) => {
    updateSettings({
      ...settings,
      [workoutType]: {
        ...settings[workoutType],
        [key]: value,
      },
    });
  };

  const renderSettings = () => {
    switch (workoutType) {
      case 'amrap':
        return (
          <div className="space-y-4">
            <div className="flex flex-col items-center">
              <Label htmlFor="minutes">Minutes</Label>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('minutes', Math.max(1, settings.amrap.minutes - 1))}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Input
                  id="minutes"
                  type="number"
                  min="1"
                  value={settings.amrap.minutes}
                  onChange={(e) => updateSetting('minutes', parseInt(e.target.value) || 1)}
                  className="w-20 text-center"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('minutes', settings.amrap.minutes + 1)}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        );

      case 'emom':
        return (
          <div className="space-y-4">
            <div className="flex flex-col items-center">
              <Label htmlFor="rounds">Rounds</Label>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('rounds', Math.max(1, settings.emom.rounds - 1))}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Input
                  id="rounds"
                  type="number"
                  min="1"
                  value={settings.emom.rounds}
                  onChange={(e) => updateSetting('rounds', parseInt(e.target.value) || 1)}
                  className="w-20 text-center"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('rounds', settings.emom.rounds + 1)}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-col items-center">
              <Label htmlFor="intervalSeconds">Interval (seconds)</Label>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('intervalSeconds', Math.max(10, settings.emom.intervalSeconds - 5))}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Input
                  id="intervalSeconds"
                  type="number"
                  min="10"
                  step="5"
                  value={settings.emom.intervalSeconds}
                  onChange={(e) => updateSetting('intervalSeconds', parseInt(e.target.value) || 60)}
                  className="w-20 text-center"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSetting('intervalSeconds', settings.emom.intervalSeconds + 5)}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        );

      // Add other workout type settings as needed
      default:
        return null;
    }
  };

  return <div className="space-y-4">{renderSettings()}</div>;
};