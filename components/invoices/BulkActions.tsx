// components/invoices/BulkActions.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  Send, 
  Download, 
  CheckCircle,
  Loader2,
  AlertTriangle,
  X,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface BulkActionsProps {
  selectedInvoices: Invoice[];
  onClearSelection: () => void;
  onActionComplete: () => void;
}

export default function BulkActions({
  selectedInvoices,
  onClearSelection,
  onActionComplete
}: BulkActionsProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingAction, setProcessingAction] = useState<string>('');

  // Filter invoices by status for different actions
  const draftInvoices = selectedInvoices.filter(inv => inv.status === 'draft' || inv.status === 'error');
  const cancelableInvoices = selectedInvoices.filter(inv => 
    inv.status === 'submitted' && inv.mark
  );
  const deletableInvoices = selectedInvoices.filter(inv => 
    inv.status === 'draft' || inv.status === 'error' || inv.status === 'canceled'
  );
  
  const handleBulkSubmit = async () => {
    if (draftInvoices.length === 0) {
      toast.error('No draft invoices selected for submission');
      return;
    }

    if (!confirm(`Submit ${draftInvoices.length} invoice(s) to myDATA?`)) {
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Submitting');

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      // Process invoices sequentially to avoid rate limiting
      for (const invoice of draftInvoices) {
        try {
          const response = await fetch('/api/mydata/send-invoice', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceId: invoice.id }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            successCount++;
          } else {
            errorCount++;
            errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: ${data.error || 'Unknown error'}`);
          }

          // Add delay between requests to avoid rate limiting
          if (draftInvoices.indexOf(invoice) < draftInvoices.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          errorCount++;
          errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: Network error`);
        }
      }

      // Show results
      if (successCount > 0) {
        toast.success(`Successfully submitted ${successCount} invoice(s) to myDATA`);
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to submit ${errorCount} invoice(s). Check individual invoice errors.`);
        console.error('Bulk submission errors:', errors);
      }

      onActionComplete();
      onClearSelection();
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleBulkCancel = async () => {
    if (cancelableInvoices.length === 0) {
      toast.error('No submitted invoices selected for cancellation');
      return;
    }

    if (!confirm(
      `Cancel ${cancelableInvoices.length} invoice(s) in myDATA?\n\n` +
      'This will cancel the selected invoices in the AADE myDATA system. This action cannot be undone.\n\n' +
      'Invoices to cancel:\n' +
      cancelableInvoices.map(inv => `• ${inv.invoice_series}-${inv.invoice_number}`).join('\n')
    )) {
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Canceling');

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      // Process cancellations sequentially to avoid rate limiting
      for (const invoice of cancelableInvoices) {
        try {
          const response = await fetch('/api/mydata/cancel-invoice', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceId: invoice.id }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            successCount++;
          } else {
            errorCount++;
            errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: ${data.error || 'Unknown error'}`);
          }

          // Add delay between requests to avoid rate limiting
          if (cancelableInvoices.indexOf(invoice) < cancelableInvoices.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1500));
          }
        } catch (error) {
          errorCount++;
          errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: Network error`);
        }
      }

      // Show results
      if (successCount > 0) {
        toast.success(`Successfully canceled ${successCount} invoice(s) in myDATA`);
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to cancel ${errorCount} invoice(s). Check individual invoice errors.`);
        console.error('Bulk cancellation errors:', errors);
      }

      onActionComplete();
      onClearSelection();
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleBulkDelete = async () => {
    if (deletableInvoices.length === 0) {
      toast.error('No deletable invoices selected');
      return;
    }

    if (!confirm(
      `Permanently delete ${deletableInvoices.length} invoice(s)?\n\n` +
      'This will permanently remove the selected invoices and all related data from the database. This action cannot be undone.\n\n' +
      'Invoices to delete:\n' +
      deletableInvoices.map(inv => `• ${inv.invoice_series}-${inv.invoice_number} (${inv.status})`).join('\n')
    )) {
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Deleting');

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      // Process deletions sequentially
      for (const invoice of deletableInvoices) {
        try {
          const response = await fetch('/api/mydata/delete-invoice', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceId: invoice.id }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            successCount++;
          } else {
            errorCount++;
            errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: ${data.error || 'Unknown error'}`);
          }
        } catch (error) {
          errorCount++;
          errors.push(`${invoice.invoice_series}-${invoice.invoice_number}: Network error`);
        }
      }

      // Show results
      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} invoice(s)`);
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to delete ${errorCount} invoice(s). Check console for details.`);
        console.error('Bulk deletion errors:', errors);
      }

      onActionComplete();
      onClearSelection();
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleBulkPdfGeneration = async () => {
    if (selectedInvoices.length === 0) {
      toast.error('No invoices selected');
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Generating PDFs');

    let successCount = 0;
    let errorCount = 0;

    try {
      // Process PDFs sequentially
      for (const invoice of selectedInvoices) {
        try {
          const response = await fetch('/api/mydata/generate-pdf', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceId: invoice.id }),
          });

          if (response.ok) {
            // Create blob and download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `invoice_${invoice.invoice_series}-${invoice.invoice_number}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            successCount++;

            // Small delay between downloads
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            errorCount++;
          }
        } catch (error) {
          errorCount++;
        }
      }

      if (successCount > 0) {
        toast.success(`Generated ${successCount} PDF(s) successfully`);
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to generate ${errorCount} PDF(s)`);
      }

      onClearSelection();
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleBulkCheck = async () => {
    if (selectedInvoices.length === 0) {
      toast.error('No invoices selected');
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Checking');

    let readyCount = 0;
    let notReadyCount = 0;
    const issues: string[] = [];

    try {
      for (const invoice of selectedInvoices) {
        try {
          const response = await fetch(`/api/mydata/check-invoice?invoiceId=${invoice.id}`);
          
          if (response.ok) {
            const data = await response.json();
            if (data.issues.length === 0) {
              readyCount++;
            } else {
              notReadyCount++;
              issues.push(`${invoice.invoice_series}-${invoice.invoice_number}: ${data.issues.length} issue(s)`);
            }
          } else {
            notReadyCount++;
            issues.push(`${invoice.invoice_series}-${invoice.invoice_number}: Check failed`);
          }
        } catch (error) {
          notReadyCount++;
          issues.push(`${invoice.invoice_series}-${invoice.invoice_number}: Network error`);
        }
      }

      // Show summary
      toast.info(`Check complete: ${readyCount} ready, ${notReadyCount} need attention`);
      
      if (issues.length > 0) {
        console.log('Invoice issues:', issues);
      }

      onClearSelection();
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  // Don't render if no invoices selected
  if (selectedInvoices.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center gap-2">
        <Badge variant="secondary">
          {selectedInvoices.length} selected
        </Badge>
        
        {draftInvoices.length > 0 && (
          <Badge variant="outline">
            {draftInvoices.length} draft
          </Badge>
        )}
        
        {cancelableInvoices.length > 0 && (
          <Badge variant="outline">
            {cancelableInvoices.length} cancelable
          </Badge>
        )}
        
        {deletableInvoices.length > 0 && (
          <Badge variant="outline">
            {deletableInvoices.length} deletable
          </Badge>
        )}
      </div>

      <div className="flex items-center gap-2 ml-auto">
        {isProcessing && (
          <div className="flex items-center gap-2 text-blue-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">{processingAction}...</span>
          </div>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" disabled={isProcessing}>
              Bulk Actions
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem 
              onClick={handleBulkCheck}
              disabled={isProcessing}
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Check All Readiness
            </DropdownMenuItem>

            <DropdownMenuItem 
              onClick={handleBulkPdfGeneration}
              disabled={isProcessing}
            >
              <Download className="mr-2 h-4 w-4" />
              Generate All PDFs
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem 
              onClick={handleBulkSubmit}
              disabled={isProcessing || draftInvoices.length === 0}
            >
              <Send className="mr-2 h-4 w-4" />
              Submit to myDATA ({draftInvoices.length})
            </DropdownMenuItem>

            {draftInvoices.length === 0 && selectedInvoices.length > 0 && (
              <div className="px-2 py-1 text-xs text-gray-500">
                <AlertTriangle className="inline h-3 w-3 mr-1" />
                Only draft invoices can be submitted
              </div>
            )}

            <DropdownMenuSeparator />

            <DropdownMenuItem 
              onClick={handleBulkCancel}
              disabled={isProcessing || cancelableInvoices.length === 0}
              className="text-red-600 focus:text-red-600"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel in myDATA ({cancelableInvoices.length})
            </DropdownMenuItem>

            <DropdownMenuItem 
              onClick={handleBulkDelete}
              disabled={isProcessing || deletableInvoices.length === 0}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Invoices ({deletableInvoices.length})
            </DropdownMenuItem>

            {(cancelableInvoices.length === 0 && deletableInvoices.length === 0) && selectedInvoices.length > 0 && (
              <div className="px-2 py-1 text-xs text-gray-500">
                <AlertTriangle className="inline h-3 w-3 mr-1" />
                No invoices can be canceled or deleted
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          disabled={isProcessing}
        >
          Clear Selection
        </Button>
      </div>
    </div>
  );
}