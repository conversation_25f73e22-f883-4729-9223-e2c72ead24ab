// components/invoices/InvoiceTable.tsx
'use client';

import { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  QrCode,
  Loader2,
  AlertTriangle 
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import InvoiceActionsDropdown from './InvoiceActionsDropdown';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface InvoiceTableProps {
  invoices: Invoice[];
  selectedInvoices: Invoice[];
  onSelectionChange: (invoices: Invoice[]) => void;
  onActionComplete: () => void;
  onShowChecker: (invoice: Invoice) => void;
  onShowAddLine: (invoice: Invoice) => void;
  onShowAddPayment: (invoice: Invoice) => void;
}

export default function InvoiceTable({
  invoices,
  selectedInvoices,
  onSelectionChange,
  onActionComplete,
  onShowChecker,
  onShowAddLine,
  onShowAddPayment
}: InvoiceTableProps) {
  const [selectAll, setSelectAll] = useState(false);
  
  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('el-GR');
  };

  // Get status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'submitted':
        return {
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          variant: 'default' as const,
          label: 'Submitted',
          color: 'text-green-600'
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4 text-red-500" />,
          variant: 'destructive' as const,
          label: 'Error',
          color: 'text-red-600'
        };
      case 'pending':
        return {
          icon: <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />,
          variant: 'secondary' as const,
          label: 'Pending',
          color: 'text-blue-600'
        };
      case 'rate_limited':
        return {
          icon: <AlertTriangle className="h-4 w-4 text-orange-500" />,
          variant: 'secondary' as const,
          label: 'Rate Limited',
          color: 'text-orange-600'
        };
      case 'canceled':
        return {
          icon: <AlertCircle className="h-4 w-4 text-gray-500" />,
          variant: 'outline' as const,
          label: 'Canceled',
          color: 'text-gray-600'
        };
      default: // draft
        return {
          icon: <Clock className="h-4 w-4 text-gray-500" />,
          variant: 'outline' as const,
          label: 'Draft',
          color: 'text-gray-600'
        };
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (invoice: Invoice, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedInvoices, invoice]);
    } else {
      onSelectionChange(selectedInvoices.filter(inv => inv.id !== invoice.id));
      setSelectAll(false);
    }
  };

  // Handle select all checkbox
  const handleSelectAllChange = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      onSelectionChange(invoices);
    } else {
      onSelectionChange([]);
    }
  };

  // Check if invoice is selected
  const isSelected = (invoice: Invoice) => {
    return selectedInvoices.some(inv => inv.id === invoice.id);
  };

  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={selectAll}
                onCheckedChange={handleSelectAllChange}
                aria-label="Select all invoices"
              />
            </TableHead>
            <TableHead>Invoice</TableHead>
            <TableHead>Issue Date</TableHead>
            <TableHead>Client</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>myDATA</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invoices.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                No invoices found. Create a test invoice to get started.
              </TableCell>
            </TableRow>
          ) : (
            invoices.map((invoice) => {
              const statusConfig = getStatusConfig(invoice.status);
              const selected = isSelected(invoice);
              
              return (
                <TableRow 
                  key={invoice.id}
                  className={selected ? 'bg-blue-50' : ''}
                >
                  <TableCell>
                    <Checkbox
                      checked={selected}
                      onCheckedChange={(checked) => handleCheckboxChange(invoice, !!checked)}
                      aria-label={`Select invoice ${invoice.invoice_series}-${invoice.invoice_number}`}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <div className="font-medium">
                      {invoice.invoice_series}-{invoice.invoice_number}
                    </div>
                    <div className="text-sm text-gray-500">
                      Type: {invoice.invoice_type}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {formatDate(invoice.issue_date)}
                  </TableCell>
                  
                  <TableCell>
                    <div className="max-w-[200px]">
                      <div className="font-medium truncate">
                        {invoice.client_name || 'Anonymous'}
                      </div>
                      {invoice.client_vat && (
                        <div className="text-sm text-gray-500">
                          VAT: {invoice.client_vat}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <div className="font-medium">
                      {formatCurrency(invoice.total_gross)}
                    </div>
                    <div className="text-sm text-gray-500">
                      Net: {formatCurrency(invoice.total_net)}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center">
                      {statusConfig.icon}
                      <Badge variant={statusConfig.variant} className="ml-2">
                        {statusConfig.label}
                      </Badge>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {invoice.mark ? (
                        <>
                          <div className="flex items-center text-green-600">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            <span className="text-xs">Registered</span>
                          </div>
                          <div className="text-xs font-mono text-gray-500">
                            {invoice.mark.substring(0, 12)}...
                          </div>
                          {invoice.qr_url && (
                            <div className="flex items-center text-blue-600">
                              <QrCode className="h-3 w-3 mr-1" />
                              <span className="text-xs">QR Available</span>
                            </div>
                          )}
                        </>
                      ) : (
                        <span className="text-xs text-gray-400">Not submitted</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <InvoiceActionsDropdown
                      invoice={invoice}
                      onActionComplete={onActionComplete}
                      onShowChecker={onShowChecker}
                      onShowAddLine={onShowAddLine}
                      onShowAddPayment={onShowAddPayment}
                    />
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
}