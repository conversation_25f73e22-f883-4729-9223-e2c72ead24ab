// components/invoices/InvoiceManager.tsx
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { createClient } from '@supabase/supabase-js';
import { toast } from 'react-hot-toast';
import type { Database } from '@/types/supabase';
import Link from 'next/link';

type Invoice = Database['public']['Tables']['invoices']['Row'];
type InvoiceLine = Database['public']['Tables']['invoice_lines']['Row'];
type PaymentMethod = Database['public']['Tables']['invoice_payment_methods']['Row'];

export default function InvoiceManager({ invoice }: { invoice?: Invoice }) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmittingToMyData, setIsSubmittingToMyData] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [lines, setLines] = useState<Partial<InvoiceLine>[]>([{
    line_number: 1,
    description: '',
    quantity: 1,
    unit_price: 0,
    net_value: 0,
    vat_category: 1,
    vat_amount: 0
  }]);
  const [paymentMethods, setPaymentMethods] = useState<Partial<PaymentMethod>[]>([{
    payment_type: 3,
    amount: 0,
    payment_info: ''
  }]);

  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm({
    defaultValues: invoice || {
      invoice_series: 'A',
      invoice_type: '11.2', // Default to receipt
      currency: 'EUR',
      status: 'draft'
    }
  });

  // Watch form values
  const formValues = watch();

  // Load invoice data if editing
  useEffect(() => {
    if (invoice?.id) {
      // Load invoice lines
      supabase
        .from('invoice_lines')
        .select('*')
        .eq('invoice_id', invoice.id)
        .order('line_number')
        .then(({ data, error }) => {
          if (!error && data) {
            setLines(data);
          }
        });

      // Load payment methods
      supabase
        .from('invoice_payment_methods')
        .select('*')
        .eq('invoice_id', invoice.id)
        .then(({ data, error }) => {
          if (!error && data) {
            setPaymentMethods(data);
          }
        });
    }
  }, [invoice, supabase]);

  // Update totals when lines change
  useEffect(() => {
    const totalNet = lines.reduce((sum, line) =>
      sum + (line.net_value || 0), 0);

    const totalVat = lines.reduce((sum, line) =>
      sum + (line.vat_amount || 0), 0);

    setValue('total_net', totalNet);
    setValue('total_vat', totalVat);
    setValue('total_gross', totalNet + totalVat);

    // Update payment method amount to match total
    if (paymentMethods.length === 1) {
      setPaymentMethods(prevMethods => [{
        ...prevMethods[0],
        amount: totalNet + totalVat
      }]);
    }
  }, [lines, setValue, paymentMethods.length]);

  // Add invoice line
  const addLine = () => {
    setLines([
      ...lines,
      {
        line_number: lines.length + 1,
        description: '',
        quantity: 1,
        unit_price: 0,
        net_value: 0,
        vat_category: 1,
        vat_amount: 0,
        income_classification_type: 'E3_561_007',
        income_classification_category: 'category1_3'
      }
    ]);
  };

  // Remove invoice line
  const removeLine = (index: number) => {
    const newLines = [...lines];
    newLines.splice(index, 1);
    // Update line numbers
    newLines.forEach((line, i) => {
      line.line_number = i + 1;
    });
    setLines(newLines);
  };

  // Update line values
  const updateLine = (index: number, field: keyof InvoiceLine, value: string | number) => {
    const newLines = [...lines];
    newLines[index] = { ...newLines[index], [field]: value };

    // Calculate net_value and vat_amount if changing quantity or unit_price
    if (field === 'quantity' || field === 'unit_price') {
      const quantity = field === 'quantity' ? Number(value) : Number(newLines[index].quantity) || 0;
      const unitPrice = field === 'unit_price' ? Number(value) : Number(newLines[index].unit_price) || 0;
      const netValue = quantity * unitPrice;

      newLines[index].net_value = netValue;

      // Calculate VAT based on category
      const vatCategory = newLines[index].vat_category || 1;
      let vatRate = 0;

      switch (vatCategory) {
        case 1: vatRate = 0.24; break; // 24%
        case 2: vatRate = 0.13; break; // 13%
        case 3: vatRate = 0.06; break; // 6%
        case 4: vatRate = 0; break;    // 0%
        case 5: vatRate = 0.17; break; // 17%
        case 6: vatRate = 0.09; break; // 9%
        case 7: vatRate = 0.04; break; // 4%
        default: vatRate = 0.24;       // 24% default
      }

      newLines[index].vat_amount = netValue * vatRate;
    }

    setLines(newLines);
  };

  // Add payment method
  const addPaymentMethod = () => {
    setPaymentMethods([
      ...paymentMethods,
      {
        payment_type: 3,
        amount: 0,
        payment_info: ''
      }
    ]);
  };

  // Remove payment method
  const removePaymentMethod = (index: number) => {
    const newMethods = [...paymentMethods];
    newMethods.splice(index, 1);
    setPaymentMethods(newMethods);
  };

  // Update payment method
  const updatePaymentMethod = (index: number, field: keyof PaymentMethod, value: string | number) => {
    const newMethods = [...paymentMethods];
    newMethods[index] = { ...newMethods[index], [field]: value };
    setPaymentMethods(newMethods);
  };

  // Save invoice (locally only)
  const saveInvoice = async (formData: Partial<Invoice>) => {
    setIsSubmitting(true);

    try {
      // Calculate next invoice number if new
      if (!invoice?.id) {
        // Ensure invoice_series is a string
        const invoiceSeries = formData.invoice_series || 'A';

        const { data: lastInvoice } = await supabase
          .from('invoices')
          .select('invoice_number')
          .eq('invoice_series', invoiceSeries)
          .order('invoice_number', { ascending: false })
          .limit(1);

        const nextNumber = lastInvoice && lastInvoice.length > 0
          ? parseInt(lastInvoice[0].invoice_number) + 1
          : 1;

        formData.invoice_number = nextNumber.toString().padStart(3, '0');
      }

      // Save or update invoice
      const invoiceData = {
        ...formData,
        status: 'draft',
        issue_date: formData.issue_date || new Date().toISOString().split('T')[0],
        // Ensure required fields have values
        client_name: formData.client_name || 'Anonymous',
        client_vat: formData.client_vat || '000000000',
        invoice_series: formData.invoice_series || 'A',
        invoice_number: formData.invoice_number || '001',
        total_net: formData.total_net || 0,
        total_vat: formData.total_vat || 0,
        total_gross: formData.total_gross || 0
      };

      let invoiceId = invoice?.id;

      if (!invoiceId) {
        // Create new invoice
        const { data: newInvoice, error } = await supabase
          .from('invoices')
          .insert([invoiceData])
          .select()
          .single();

        if (error) throw error;
        invoiceId = newInvoice.id;
      } else {
        // Update existing invoice
        const { error } = await supabase
          .from('invoices')
          .update(invoiceData)
          .eq('id', invoiceId);

        if (error) throw error;
      }

      // Save invoice lines
      const linesData = lines.map(line => ({
        ...line,
        invoice_id: invoiceId,
        // Ensure required fields have values
        description: line.description || 'Service',
        quantity: line.quantity || 1,
        unit_price: line.unit_price || 0,
        net_value: line.net_value || 0,
        vat_amount: line.vat_amount || 0,
        vat_category: line.vat_category || 1,
        income_classification_type: line.income_classification_type || 'E3_561_007',
        income_classification_category: line.income_classification_category || 'category1_3',
        line_number: line.line_number || 1
      }));

      // Delete existing lines first if updating
      if (invoice?.id) {
        await supabase
          .from('invoice_lines')
          .delete()
          .eq('invoice_id', invoiceId);
      }

// Insert lines
const { error: linesError } = await supabase
.from('invoice_lines')
.insert(linesData);

if (linesError) throw linesError;

// Save payment methods
const paymentData = paymentMethods.map(method => ({
  ...method,
  invoice_id: invoiceId,
  // Ensure required fields have values
  amount: method.amount || 0,
  payment_type: method.payment_type || 3
}));

// Delete existing payment methods first if updating
if (invoice?.id) {
await supabase
  .from('invoice_payment_methods')
  .delete()
  .eq('invoice_id', invoiceId);
}

// Insert payment methods
const { error: paymentsError } = await supabase
.from('invoice_payment_methods')
.insert(paymentData);

if (paymentsError) throw paymentsError;

toast.success('Invoice saved successfully');
router.push(`/admin/invoices/view/${invoiceId}`);
} catch (error) {
console.error('Error saving invoice:', error);
toast.error('Failed to save invoice');
} finally {
setIsSubmitting(false);
}
};

// Submit invoice to myDATA
const submitToMyData = async () => {
if (!invoice?.id) {
toast.error('Save the invoice before submitting to myDATA');
return;
}

setIsSubmittingToMyData(true);

try {
const response = await fetch('/api/mydata/send-invoice', {
method: 'POST',
headers: {
  'Content-Type': 'application/json',
},
body: JSON.stringify({ invoiceId: invoice.id }),
});

const data = await response.json();

if (response.ok && data.success) {
toast.success('Invoice submitted to myDATA successfully');
router.refresh();
} else {
const errorMessage = data.errors?.map((e: { message: string }) => e.message).join('\n') || 'Unknown error';
toast.error(`Failed to submit to myDATA: ${errorMessage}`);
}
} catch (error) {
console.error('Error submitting to myDATA:', error);
toast.error('Failed to submit to myDATA');
} finally {
setIsSubmittingToMyData(false);
}
};

// Cancel invoice in myDATA
const cancelInvoice = async () => {
if (!invoice?.id || !invoice.mark) {
toast.error('Cannot cancel: Invoice has no myDATA MARK');
return;
}

if (!confirm('Are you sure you want to cancel this invoice in myDATA? This action cannot be undone.')) {
return;
}

setIsCanceling(true);

try {
const response = await fetch('/api/mydata/cancel-invoice', {
method: 'POST',
headers: {
  'Content-Type': 'application/json',
},
body: JSON.stringify({ invoiceId: invoice.id }),
});

const data = await response.json();

if (response.ok && data.success) {
toast.success('Invoice canceled in myDATA successfully');
router.refresh();
} else {
const errorMessage = data.errors?.map((e: { message: string }) => e.message).join('\n') || 'Unknown error';
toast.error(`Failed to cancel invoice: ${errorMessage}`);
}
} catch (error) {
console.error('Error canceling invoice:', error);
toast.error('Failed to cancel invoice');
} finally {
setIsCanceling(false);
}
};

return (
<div className="space-y-6">
<div className="flex justify-between items-center">
<h1 className="text-2xl font-bold">
  {invoice ? 'Edit Invoice' : 'Create New Invoice'}
</h1>

{invoice?.id && (
  <div className="flex space-x-2">
    {invoice.mark && (
      <div className="flex items-center space-x-2 text-sm">
        <span className="font-semibold">MARK:</span>
        <span>{invoice.mark}</span>
      </div>
    )}

    <div className="flex space-x-2">
      {invoice.status === 'draft' && (
        <button
          onClick={submitToMyData}
          disabled={isSubmittingToMyData}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isSubmittingToMyData ? 'Submitting...' : 'Submit to myDATA'}
        </button>
      )}

      {invoice.status === 'submitted' && (
        <button
          onClick={cancelInvoice}
          disabled={isCanceling}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {isCanceling ? 'Canceling...' : 'Cancel Invoice'}
        </button>
      )}
    </div>
  </div>
)}
</div>

<form onSubmit={handleSubmit(saveInvoice)} className="space-y-6">
<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
  {/* Invoice details */}
  <div className="space-y-4 p-4 border rounded-md">
    <h2 className="text-lg font-semibold">Invoice Details</h2>

    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">
          Series
        </label>
        <input
          type="text"
          {...register('invoice_series', { required: true })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
        {errors.invoice_series && (
          <p className="mt-1 text-sm text-red-600">Series is required</p>
        )}
      </div>

      {invoice?.id && (
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Number
          </label>
          <input
            type="text"
            {...register('invoice_number', { required: true })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            readOnly
          />
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700">
          Issue Date
        </label>
        <input
          type="date"
          {...register('issue_date', { required: true })}
          defaultValue={new Date().toISOString().split('T')[0]}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
        {errors.issue_date && (
          <p className="mt-1 text-sm text-red-600">Issue date is required</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          Invoice Type
        </label>
        <select
          {...register('invoice_type', { required: true })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="11.1">11.1 - Παροχή Υπηρεσιών</option>
          <option value="11.2">11.2 - Απόδειξη Λιανικής</option>
        </select>
      </div>
    </div>
  </div>

  {/* Client details */}
  <div className="space-y-4 p-4 border rounded-md">
    <h2 className="text-lg font-semibold">Client Details</h2>

    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">
          Client VAT (ΑΦΜ)
        </label>
        <input
          type="text"
          {...register('client_vat')}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          Client Name
        </label>
        <input
          type="text"
          {...register('client_name')}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          Country
        </label>
        <input
          type="text"
          {...register('client_country')}
          defaultValue="GR"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>
    </div>
  </div>
</div>

{/* Invoice lines */}
<div className="space-y-4 p-4 border rounded-md">
  <div className="flex justify-between items-center">
    <h2 className="text-lg font-semibold">Invoice Lines</h2>
    <button
      type="button"
      onClick={addLine}
      className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700"
    >
      Add Line
    </button>
  </div>

  <div className="overflow-x-auto">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Line #
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Description
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Qty
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Unit Price
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Net Value
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            VAT Cat
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            VAT Amount
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {lines.map((line, index) => (
          <tr key={index}>
            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
              {line.line_number}
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="text"
                value={line.description || ''}
                onChange={(e) => updateLine(index, 'description', e.target.value)}
                className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="number"
                value={line.quantity || 0}
                onChange={(e) => updateLine(index, 'quantity', parseFloat(e.target.value))}
                className="w-20 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="number"
                step="0.01"
                value={line.unit_price || 0}
                onChange={(e) => updateLine(index, 'unit_price', parseFloat(e.target.value))}
                className="w-24 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="number"
                step="0.01"
                value={line.net_value || 0}
                readOnly
                className="w-24 bg-gray-100 border-gray-300 rounded-md shadow-sm"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <select
                value={line.vat_category || 1}
                onChange={(e) => updateLine(index, 'vat_category', parseInt(e.target.value))}
                className="w-20 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value={1}>24%</option>
                <option value={2}>13%</option>
                <option value={3}>6%</option>
                <option value={4}>0%</option>
              </select>
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="number"
                step="0.01"
                value={line.vat_amount || 0}
                readOnly
                className="w-24 bg-gray-100 border-gray-300 rounded-md shadow-sm"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <button
                type="button"
                onClick={() => removeLine(index)}
                className="text-red-600 hover:text-red-900"
              >
                Remove
              </button>
            </td>
          </tr>
        ))}
      </tbody>
      <tfoot className="bg-gray-50">
        <tr>
          <td className="px-3 py-2" colSpan={4}>
            <span className="text-sm font-semibold">Totals</span>
          </td>
          <td className="px-3 py-2">
            <span className="text-sm font-semibold">
              {formValues.total_net?.toFixed(2) || '0.00'}
            </span>
          </td>
          <td className="px-3 py-2">
          </td>
          <td className="px-3 py-2">
            <span className="text-sm font-semibold">
              {formValues.total_vat?.toFixed(2) || '0.00'}
            </span>
          </td>
          <td className="px-3 py-2"></td>
        </tr>
      </tfoot>
    </table>
  </div>
</div>

{/* Payment methods */}
<div className="space-y-4 p-4 border rounded-md">
  <div className="flex justify-between items-center">
    <h2 className="text-lg font-semibold">Payment Methods</h2>
    <button
      type="button"
      onClick={addPaymentMethod}
      className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700"
    >
      Add Method
    </button>
  </div>

  <div className="overflow-x-auto">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Type
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Amount
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Info
          </th>
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {paymentMethods.map((method, index) => (
          <tr key={index}>
            <td className="px-3 py-2 whitespace-nowrap">
              <select
                value={method.payment_type || 3}
                onChange={(e) => updatePaymentMethod(index, 'payment_type', parseInt(e.target.value))}
                className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value={1}>1 - Μετρητά</option>
                <option value={2}>2 - Επιταγή</option>
                <option value={3}>3 - Τραπεζικό έμβασμα</option>
                <option value={4}>4 - Πιστωτική κάρτα</option>
              </select>
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="number"
                step="0.01"
                value={method.amount || 0}
                onChange={(e) => updatePaymentMethod(index, 'amount', parseFloat(e.target.value))}
                className="w-24 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              <input
                type="text"
                value={method.payment_info || ''}
                onChange={(e) => updatePaymentMethod(index, 'payment_info', e.target.value)}
                className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </td>
            <td className="px-3 py-2 whitespace-nowrap">
              {paymentMethods.length > 1 && (
                <button
                  type="button"
                  onClick={() => removePaymentMethod(index)}
                  className="text-red-600 hover:text-red-900"
                >
                  Remove
                </button>
              )}
            </td>
          </tr>
        ))}
      </tbody>
      <tfoot className="bg-gray-50">
        <tr>
          <td className="px-3 py-2">
            <span className="text-sm font-semibold">Total</span>
          </td>
          <td className="px-3 py-2">
            <span className="text-sm font-semibold">
              {paymentMethods.reduce((sum, method) => sum + (method.amount || 0), 0).toFixed(2)}
            </span>
          </td>
          <td className="px-3 py-2" colSpan={2}></td>
        </tr>
      </tfoot>
    </table>
  </div>

  {/* Warning for payment amount mismatch */}
  {Math.abs(
    paymentMethods.reduce((sum, method) => sum + (method.amount || 0), 0) -
    (formValues.total_gross || 0)
  ) > 0.01 && (
    <div className="text-red-600 text-sm">
      Warning: The total payment amount does not match the invoice total.
    </div>
  )}
</div>

{/* Submit buttons */}
<div className="flex justify-end space-x-3">
  <Link
    href="/admin/invoices"
    className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
  >
    Cancel
  </Link>
  <button
    type="submit"
    disabled={isSubmitting}
    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
  >
    {isSubmitting ? 'Saving...' : 'Save Invoice'}
  </button>
</div>
</form>
</div>
);
}