// components/invoices/DebugActions.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Settings, 
  Wifi, 
  FileText, 
  Database,
  Zap,
  RefreshCw,
  Bug,
  Loader2,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface DebugActionsProps {
  className?: string;
}

export default function DebugActions({ className = '' }: DebugActionsProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingAction, setProcessingAction] = useState<string>('');
  const router = useRouter();

  const handleTestConnection = async () => {
    setIsProcessing(true);
    setProcessingAction('Testing Connection');

    try {
      const response = await fetch('/api/mydata/test-connection', {
        method: 'POST'
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast.success('myDATA connection test successful');
      } else {
        toast.error(`Connection test failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Connection test error:', error);
      toast.error('Connection test failed');
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleTestRateLimit = async () => {
    setIsProcessing(true);
    setProcessingAction('Testing Rate Limit');

    try {
      const response = await fetch('/api/mydata/test-rate-limit');
      const data = await response.json();

      if (data.success) {
        toast.success('Rate limit test completed - check console for details');
        console.log('Rate limit test response:', data);
      } else {
        toast.error(`Rate limit test failed: ${data.error || 'Unknown error'}`);
        console.error('Rate limit test error:', data);
      }
    } catch (error) {
      console.error('Rate limit test error:', error);
      toast.error('Rate limit test failed');
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleGenerateTestXML = async () => {
    setIsProcessing(true);
    setProcessingAction('Generating Test XML');

    try {
      const response = await fetch('/api/mydata/test-xml?debug=true');
      
      if (response.ok) {
        const xmlText = await response.text();
        
        // Create a blob and download the XML file
        const blob = new Blob([xmlText], { type: 'text/xml' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `test-invoice-${Date.now()}.xml`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success('Test XML generated and downloaded');
      } else {
        const errorData = await response.json();
        toast.error(`XML generation failed: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('XML generation error:', error);
      toast.error('XML generation failed');
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleViewApiLogs = () => {
    router.push('/admin/mydata/logs');
  };

  const handleOpenSettings = () => {
    router.push('/admin/mydata/settings');
  };

  const handleRefreshAllStatuses = async () => {
    setIsProcessing(true);
    setProcessingAction('Refreshing Statuses');

    try {
      // This would be a custom API endpoint to refresh all invoice statuses
      const response = await fetch('/api/mydata/refresh-statuses', {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Refreshed ${data.count || 0} invoice statuses`);
        // Trigger page refresh or data reload
        window.location.reload();
      } else {
        const errorData = await response.json();
        toast.error(`Status refresh failed: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Status refresh error:', error);
      toast.error('Status refresh failed');
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  const handleClearApiLogs = async () => {
    if (!confirm('Are you sure you want to clear all API logs? This action cannot be undone.')) {
      return;
    }

    setIsProcessing(true);
    setProcessingAction('Clearing Logs');

    try {
      const response = await fetch('/api/mydata/clear-logs', {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('API logs cleared successfully');
      } else {
        const errorData = await response.json();
        toast.error(`Failed to clear logs: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Clear logs error:', error);
      toast.error('Failed to clear logs');
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  };

  // Only show in development or for admin users
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (!isDevelopment) {
    return null; // Hide in production unless user has admin flag
  }

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            size="sm"
            disabled={isProcessing}
            className="border-orange-200 text-orange-600 hover:bg-orange-50"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {processingAction}...
              </>
            ) : (
              <>
                <Bug className="h-4 w-4 mr-2" />
                Debug
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          {/* Connection Tests */}
          <DropdownMenuItem 
            onClick={handleTestConnection}
            disabled={isProcessing}
          >
            <Wifi className="mr-2 h-4 w-4" />
            Test API Connection
          </DropdownMenuItem>

          <DropdownMenuItem 
            onClick={handleTestRateLimit}
            disabled={isProcessing}
          >
            <Zap className="mr-2 h-4 w-4" />
            Test Rate Limiting
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Data Operations */}
          <DropdownMenuItem 
            onClick={handleGenerateTestXML}
            disabled={isProcessing}
          >
            <FileText className="mr-2 h-4 w-4" />
            Generate Test XML
          </DropdownMenuItem>

          <DropdownMenuItem 
            onClick={handleRefreshAllStatuses}
            disabled={isProcessing}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh All Statuses
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Navigation */}
          <DropdownMenuItem onClick={handleViewApiLogs}>
            <Database className="mr-2 h-4 w-4" />
            View API Logs
            <ExternalLink className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleOpenSettings}>
            <Settings className="mr-2 h-4 w-4" />
            myDATA Settings
            <ExternalLink className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Dangerous Operations */}
          <DropdownMenuItem 
            onClick={handleClearApiLogs}
            disabled={isProcessing}
            className="text-red-600 focus:text-red-600"
          >
            <Database className="mr-2 h-4 w-4" />
            Clear API Logs
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}