// components/invoices/InvoiceCheckerModal.tsx
'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertCircle, CheckCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import AddInvoiceLineForm from '@/components/mydata/AddInvoiceLineForm';
import AddPaymentMethodForm from '@/components/mydata/AddPaymentMethodForm';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface CheckResult {
  invoice: {
    id: string;
    series: string;
    number: string;
    status: string;
    hasLines: boolean;
    lineCount: number;
    hasPaymentMethods: boolean;
    paymentMethodCount: number;
    hasCompanySettings: boolean;
  };
  issues: string[];
}

interface InvoiceCheckerModalProps {
  invoice: Invoice | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onActionComplete?: () => void;
}

export default function InvoiceCheckerModal({
  invoice,
  open,
  onOpenChange,
  onActionComplete
}: InvoiceCheckerModalProps) {
  const [isChecking, setIsChecking] = useState(false);
  const [checkResult, setCheckResult] = useState<CheckResult | null>(null);
  const [showAddLine, setShowAddLine] = useState(false);
  const [showAddPayment, setShowAddPayment] = useState(false);

  const checkInvoice = async () => {
    if (!invoice?.id) return;

    setIsChecking(true);

    try {
      const response = await fetch(`/api/mydata/check-invoice?invoiceId=${invoice.id}`);

      if (!response.ok) {
        throw new Error('Failed to check invoice');
      }

      const data = await response.json();
      setCheckResult(data);
    } catch (error) {
      console.error('Error checking invoice:', error);
      toast.error('Failed to check invoice');
    } finally {
      setIsChecking(false);
    }
  };

  // Check invoice when modal opens and invoice changes
  useEffect(() => {
    if (open && invoice?.id) {
      checkInvoice();
    }
  }, [open, invoice?.id]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setCheckResult(null);
      setShowAddLine(false);
      setShowAddPayment(false);
    }
  }, [open]);

  const handleAddLineSuccess = () => {
    setShowAddLine(false);
    checkInvoice();
    onActionComplete?.();
  };

  const handleAddPaymentSuccess = () => {
    setShowAddPayment(false);
    checkInvoice();
    onActionComplete?.();
  };

  const handleOpenSettings = () => {
    window.open('/admin/mydata/settings', '_blank');
  };

  if (!invoice) return null;

  const isReady = checkResult && checkResult.issues.length === 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Check Invoice {invoice.invoice_series}-{invoice.invoice_number}
          </DialogTitle>
          <DialogDescription>
            Verify that this invoice has all required information for myDATA submission.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Loading State */}
          {isChecking && !checkResult && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              <p className="ml-3 text-gray-500">Checking invoice...</p>
            </div>
          )}

          {/* Check Results */}
          {checkResult && (
            <>
              {/* Status Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center">
                    {checkResult.invoice.hasLines ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    )}
                    <h3 className="font-medium">Line Items</h3>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {checkResult.invoice.lineCount} line{checkResult.invoice.lineCount !== 1 ? 's' : ''} found
                  </p>
                  {!checkResult.invoice.hasLines && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="mt-2"
                      onClick={() => setShowAddLine(true)}
                    >
                      Add Line Item
                    </Button>
                  )}
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center">
                    {checkResult.invoice.hasPaymentMethods ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    )}
                    <h3 className="font-medium">Payment Methods</h3>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {checkResult.invoice.paymentMethodCount} method{checkResult.invoice.paymentMethodCount !== 1 ? 's' : ''} found
                  </p>
                  {!checkResult.invoice.hasPaymentMethods && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="mt-2"
                      onClick={() => setShowAddPayment(true)}
                    >
                      Add Payment Method
                    </Button>
                  )}
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center">
                    {checkResult.invoice.hasCompanySettings ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    )}
                    <h3 className="font-medium">Company Settings</h3>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {checkResult.invoice.hasCompanySettings ? 'Configured' : 'Not configured'}
                  </p>
                  {!checkResult.invoice.hasCompanySettings && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="mt-2"
                      onClick={handleOpenSettings}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Configure Settings
                    </Button>
                  )}
                </div>
              </div>

              {/* Issues */}
              {checkResult.issues.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Issues Found</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      {checkResult.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Success State */}
              {isReady && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertTitle className="text-green-800">Ready to Submit</AlertTitle>
                  <AlertDescription className="text-green-700">
                    This invoice is ready to be submitted to myDATA. All required information is present.
                  </AlertDescription>
                </Alert>
              )}

              {/* Current Status */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Current Status:</span>
                <Badge variant={
                  checkResult.invoice.status === 'submitted' ? 'default' :
                  checkResult.invoice.status === 'error' ? 'destructive' :
                  'secondary'
                }>
                  {checkResult.invoice.status}
                </Badge>
              </div>
            </>
          )}

          {/* Add Line Form */}
          {showAddLine && (
            <div className="border-t pt-4">
              <AddInvoiceLineForm
                invoiceId={invoice.id}
                onSuccess={handleAddLineSuccess}
              />
            </div>
          )}

          {/* Add Payment Form */}
          {showAddPayment && (
            <div className="border-t pt-4">
              <AddPaymentMethodForm
                invoiceId={invoice.id}
                invoiceTotal={invoice.total_gross}
                onSuccess={handleAddPaymentSuccess}
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={checkInvoice}
              disabled={isChecking}
            >
              {isChecking ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Check
                </>
              )}
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
              {isReady && checkResult?.invoice.status === 'draft' && (
                <Button
                  onClick={() => {
                    onOpenChange(false);
                    toast.info('Invoice is ready - use the Submit action from the main list');
                  }}
                >
                  Ready to Submit
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}