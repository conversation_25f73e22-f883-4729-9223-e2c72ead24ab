'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/supabase'  // Fixed import path

// Proper type definitions based on your Supabase schema
type Wod = Database['public']['Tables']['wod']['Row']
type ExerciseMovement = Database['public']['Tables']['exercise_movements']['Row']

interface ExerciseRecordFormProps {
  exercises: ExerciseMovement[]  // Use the proper type from schema
}

export default function WodRecordForm({ exercises }: ExerciseRecordFormProps) {
  const [loading, setLoading] = useState(false)
  const [selectedDate, setSelectedDate] = useState<string>('')
  const [wod, setWod] = useState<Wod | null>(null)
  const [selectedExercises, setSelectedExercises] = useState<ExerciseMovement[]>([])

  const supabase = createClientComponentClient<Database>()

  // Rest of your code stays the same, but update setExercises to setSelectedExercises
  useEffect(() => {
    if (!selectedDate) return

    const fetchWodAndExercises = async () => {
      const { data: wodData } = await supabase
        .from('wod')
        .select('*')
        .eq('date', selectedDate)
        .single()

      if (wodData) {
        setWod(wodData)
        
        if (wodData.exercises && wodData.exercises.length > 0) {
          const { data: exercisesData } = await supabase
            .from('exercise_movements')
            .select('*')
            .in('id', wodData.exercises)

          setSelectedExercises(exercisesData || [])  // Updated this line
        }
      }
    }

    fetchWodAndExercises()
  }, [selectedDate, supabase])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      const formData = new FormData(e.currentTarget)
      const records = selectedExercises.map(exercise => {  // Updated to use selectedExercises
        const exerciseId = exercise.id
        const weight = formData.get(`weight-${exerciseId}`) ? 
          parseFloat(formData.get(`weight-${exerciseId}`) as string) : null
        const reps = formData.get(`reps-${exerciseId}`) ? 
          parseInt(formData.get(`reps-${exerciseId}`) as string) : null
        const sets = formData.get(`sets-${exerciseId}`) ? 
          parseInt(formData.get(`sets-${exerciseId}`) as string) : null
        const notes = formData.get(`notes-${exerciseId}`) as string

        return {
          pelatis_id: user.id,
          exercise_id: exerciseId,
          weight,
          reps,
          sets,
          notes,
          wod_id: wod?.id,
          date_achieved: selectedDate  // Add date_achieved from your form
        }
      })

      const { error } = await supabase
        .from('exercise_records')
        .insert(records)

      if (error) throw error

      // Clear form
      e.currentTarget.reset()
      setSelectedDate('')
      setWod(null)
      setSelectedExercises([])  // Updated this line
      
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="date" className="block text-sm font-medium">
          Select Date
        </label>
        <input
          type="date"
          id="date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          required
        />
      </div>

      {wod && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h3 className="font-medium">WOD for {selectedDate}</h3>
          {wod.warmup && (
            <div className="mt-2">
              <h4 className="text-sm font-medium">Warmup</h4>
              <p className="text-sm text-gray-600 whitespace-pre-wrap">{wod.warmup}</p>
            </div>
          )}
          <div className="mt-2">
            <h4 className="text-sm font-medium">Workout</h4>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">{wod.content}</p>
          </div>
        </div>
      )}

      {exercises.length > 0 && (
        <div className="space-y-6">
          <h3 className="font-medium">Record Your Performance</h3>
          {exercises.map((exercise) => (
            <div key={exercise.id} className="rounded-lg border p-4">
              <h4 className="font-medium">{exercise.exercise_name}</h4>
              <p className="text-sm text-gray-500 mb-4">
                {exercise.equipment} - {exercise.movement_category}
              </p>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label htmlFor={`weight-${exercise.id}`} className="block text-sm font-medium">
                    Weight (kg)
                  </label>
                  <input
                    type="number"
                    id={`weight-${exercise.id}`}
                    name={`weight-${exercise.id}`}
                    step="0.5"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                  />
                </div>

                <div>
                  <label htmlFor={`reps-${exercise.id}`} className="block text-sm font-medium">
                    Reps
                  </label>
                  <input
                    type="number"
                    id={`reps-${exercise.id}`}
                    name={`reps-${exercise.id}`}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                  />
                </div>

                <div>
                  <label htmlFor={`sets-${exercise.id}`} className="block text-sm font-medium">
                    Sets
                  </label>
                  <input
                    type="number"
                    id={`sets-${exercise.id}`}
                    name={`sets-${exercise.id}`}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                  />
                </div>
              </div>

              <div className="mt-4">
                <label htmlFor={`notes-${exercise.id}`} className="block text-sm font-medium">
                  Notes
                </label>
                <textarea
                  id={`notes-${exercise.id}`}
                  name={`notes-${exercise.id}`}
                  rows={2}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {exercises.length > 0 && (
        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {loading ? 'Saving...' : 'Save Records'}
        </button>
      )}
    </form>
  )
}