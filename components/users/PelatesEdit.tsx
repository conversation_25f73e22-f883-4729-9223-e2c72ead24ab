// components/PelatesEdit.tsx
'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { X } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from 'react-hot-toast';
import type { Database } from '@/types/supabase';

// Define base types from the schema
type Pelates = Database['public']['Tables']['pelates']['Row']
type Tags = Database['public']['Tables']['tags']['Row']

// Create the combined interface
interface PelatesWithTags extends Pelates {
  tags?: Tags[]
}

interface PelatesEditProps {
  pelatis: PelatesWithTags;
  onClose: () => void;
  onSuccess?: () => void; // Made optional with '?'
}

// At the top of the file, after the existing type definitions



export const PelatesEdit = ({ pelatis, onClose, onSuccess }: PelatesEditProps) => {
  // Initialize form data more explicitly with empty strings to avoid undefined
  const [formData, setFormData] = useState({
    name: pelatis.name || '',
    last_name: pelatis.last_name || '',
    client_name: pelatis.client_name || '',
    email: pelatis.email || '',
    phone: pelatis.phone || '',
    instagram: pelatis.instagram || '',
    created_at: pelatis.created_at || '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Add validation state
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    last_name?: string;
    email?: string;
  }>({});
  
  const supabase = createClientComponentClient<Database>();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      
      if (name === 'name' || name === 'last_name') {
        newData.client_name = `${newData.name} ${newData.last_name}`.trim();
      }
      
      return newData;
    });
  };

// components/PelatesEdit.tsx
const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  console.log("Form submitted");
  
  if (isSubmitting) return;
  
  // Reset validation errors
  setValidationErrors({});
  
  // Validate fields and collect errors
  const errors: {
    name?: string;
    last_name?: string;
    email?: string;
  } = {};
  
  if (!formData.name.trim()) {
    errors.name = 'First name is required';
  }
  
  if (!formData.last_name.trim()) {
    errors.last_name = 'Last name is required';
  }
  
  if (!formData.email.trim()) {
    errors.email = 'Email is required';
  } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  // If we have validation errors, show them and stop submission
  if (Object.keys(errors).length > 0) {
    setValidationErrors(errors);
    console.log("Validation failed", errors);
    toast.error('Please correct the errors in the form');
    return;
  }
  
  try {
    setIsSubmitting(true);
    console.log("Preparing data for update:", formData);
    
    // Your update code...
    const updateData: Database['public']['Tables']['pelates']['Update'] = {
      name: formData.name.trim(),
      last_name: formData.last_name.trim(),
      client_name: `${formData.name.trim()} ${formData.last_name.trim()}`.trim(),
      email: formData.email.trim(),
      phone: formData.phone?.trim() || null,
      instagram: formData.instagram?.trim() || null,
      updated_at: new Date().toISOString()
    };

    console.log("Sending update to Supabase for ID:", pelatis.id);
    
    const { data, error: pelatesError } = await supabase
      .from('pelates')
      .update(updateData)
      .eq('id', pelatis.id)
      .select();

    console.log("Supabase response:", { data, error: pelatesError });

    if (pelatesError) throw pelatesError;

    toast.success('Profile updated successfully!');
    console.log("Update successful, calling onSuccess and onClose");
    onSuccess?.();
    onClose();
  } catch (error) {
    console.error('Error updating pelatis:', error);
    toast.error(error instanceof Error ? error.message : 'Failed to update pelatis');
  } finally {
    setIsSubmitting(false);
  }
};

return (
  <div className="fixed inset-0 bg-black/50 flex justify-center items-center">
    <div className="bg-white p-6 rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Edit Pelatis</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="hover:bg-gray-100 rounded-full"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="name" className="flex">
            First Name <span className="text-red-500 ml-1">*</span>
          </Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={validationErrors.name ? "border-red-500" : ""}
          />
          {validationErrors.name && (
            <p className="text-red-500 text-sm mt-1">{validationErrors.name}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="last_name" className="flex">
            Last Name <span className="text-red-500 ml-1">*</span>
          </Label>
          <Input
            id="last_name"
            name="last_name"
            value={formData.last_name}
            onChange={handleInputChange}
            className={validationErrors.last_name ? "border-red-500" : ""}
          />
          {validationErrors.last_name && (
            <p className="text-red-500 text-sm mt-1">{validationErrors.last_name}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="client_name">Client Name</Label>
          <Input
            id="client_name"
            name="client_name"
            value={formData.client_name}
            onChange={handleInputChange}
            placeholder="Will be auto-filled if left empty"
          />
        </div>
        
        <div>
          <Label htmlFor="email" className="flex">
            Email <span className="text-red-500 ml-1">*</span>
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            className={validationErrors.email ? "border-red-500" : ""}
          />
          {validationErrors.email && (
            <p className="text-red-500 text-sm mt-1">{validationErrors.email}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
          />
        </div>
        
        <div>
          <Label htmlFor="instagram">Instagram</Label>
          <Input
            id="instagram"
            name="instagram"
            value={formData.instagram}
            onChange={handleInputChange}
            placeholder="@username"
          />
        </div>

        <div className="flex items-center text-sm text-gray-500 mt-2 mb-2">
          <span className="text-red-500 mr-1">*</span> Required fields
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  </div>
);
};

export default PelatesEdit;