// Αρχείο: src/components/PCSGreek.tsx
import React, { useState } from 'react';

interface PatientData {
  name: string;
  date: string;
  age: string;
  assessor: string;
}

interface PCSGreekProps {
  patientData: PatientData;
}

const PCSGreek: React.FC<PCSGreekProps> = ({  }) => {
  const [scores, setScores] = useState<Record<number, number>>({});
  const [totalScore, setTotalScore] = useState<number>(0);
  const [rumination, setRumination] = useState<number>(0);
  const [magnification, setMagnification] = useState<number>(0);
  const [helplessness, setHelplessness] = useState<number>(0);
  
  const statements = [
    "Ανησυχώ συνέχεια για το πότε θα τελειώσει ο πόνος",
    "Νιώθω ότι δεν μπορώ να συνεχίσω",
    "Είναι τρομερό και νομίζω ότι δεν πρόκειται να καλυτερέψει ποτέ",
    "Είναι φοβερό και νιώθω ότι με καταβάλλει",
    "Νιώθω ότι δεν μπορώ να το αντέξω άλλο",
    "Αρχίζω να φοβάμαι ότι ο πόνος θα χειροτερέψει",
    "Σκέφτομαι συνεχώς άλλες επώδυνες εμπειρίες",
    "Επιθυμώ με αγωνία να φύγει ο πόνος",
    "Δεν μπορώ να το βγάλω από το μυαλό μου",
    "Σκέφτομαι συνεχώς πόσο πονάει",
    "Σκέφτομαι συνεχώς πόσο πολύ θέλω να σταματήσει ο πόνος",
    "Δεν υπάρχει τίποτα που μπορώ να κάνω για να μειώσω την ένταση του πόνου",
    "Αναρωτιέμαι μήπως συμβεί κάτι σοβαρό"
  ];
  
  // Subscale configuration 
  const subScales = {
    rumination: [8, 9, 10, 11], // Items 9, 10, 11, 12 (0-indexed)
    magnification: [5, 6, 12],  // Items 6, 7, 13 (0-indexed)
    helplessness: [0, 1, 2, 3, 4, 7]   // Items 1, 2, 3, 4, 5, 8 (0-indexed)
  };
  
  const handleScoreChange = (index: number, value: number): void => {
    const newScores = { ...scores, [index]: value };
    setScores(newScores);
    calculateScores(newScores);
  };
  
  const calculateScores = (newScores: Record<number, number>): void => {
    // Calculate total score
    let total = 0;
    let validCount = 0;
    
    // Fix: Use values directly instead of entries
    Object.values(newScores).forEach((value: number) => {
      total += value;
      validCount++;
    });
    
    setTotalScore(validCount === 13 ? total : 0);
    
    // Calculate subscales
    let ruminationScore = 0;
    let magnificationScore = 0;
    let helplessnessScore = 0;
    
    // Rumination
    subScales.rumination.forEach((index: number) => {
      if (newScores[index] !== undefined) {
        ruminationScore += newScores[index];
      }
    });
    
    // Magnification
    subScales.magnification.forEach((index: number) => {
      if (newScores[index] !== undefined) {
        magnificationScore += newScores[index];
      }
    });
    
    // Helplessness
    subScales.helplessness.forEach((index: number) => {
      if (newScores[index] !== undefined) {
        helplessnessScore += newScores[index];
      }
    });
    
    setRumination(ruminationScore);
    setMagnification(magnificationScore);
    setHelplessness(helplessnessScore);
  };
  
  const handlePrint = () => {
    window.print();
  };
  
  // Interpretation function
  const getInterpretation = (score: number) => {
    if (score <= 20) return "Χαμηλή έως μέτρια καταστροφολογική σκέψη";
    if (score <= 30) return "Αυξημένη καταστροφολογική σκέψη";
    return "Κλινικά σημαντική καταστροφολογική σκέψη";
  };

  return (
    <div>
      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-bold mb-2">Οδηγίες:</h3>
        <p>Όλοι βιώνουμε επώδυνες καταστάσεις κάποια στιγμή στη ζωή μας. Τέτοιες εμπειρίες μπορεί να περιλαμβάνουν πονοκεφάλους, πόνο στα δόντια, πόνο στις αρθρώσεις ή στους μύες. Συχνά εκτιθέμεθα σε καταστάσεις που μπορεί να προκαλέσουν πόνο, όπως ασθένειες, τραυματισμούς, οδοντιατρικές επεμβάσεις ή χειρουργεία.</p>
        <p className="mt-2">Ενδιαφερόμαστε για τους τύπους σκέψεων και συναισθημάτων που έχετε όταν πονάτε. Παρακάτω υπάρχουν δεκατρείς δηλώσεις που περιγράφουν διαφορετικές σκέψεις και συναισθήματα που μπορεί να σχετίζονται με τον πόνο. Χρησιμοποιώντας την κλίμακα, παρακαλώ υποδείξτε σε ποιο βαθμό έχετε αυτές τις σκέψεις και τα συναισθήματα όταν βιώνετε πόνο.</p>
      </div>
      
      {/* Form */}
      <div className="mb-8">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse mb-4">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-2 border text-left w-1/2">Δήλωση</th>
                <th className="p-2 border text-center">Καθόλου</th>
                <th className="p-2 border text-center">Σε μικρό βαθμό</th>
                <th className="p-2 border text-center">Σε μέτριο βαθμό</th>
                <th className="p-2 border text-center">Σε μεγάλο βαθμό</th>
                <th className="p-2 border text-center">Συνεχώς</th>
              </tr>
            </thead>
            <tbody>
              {statements.map((statement, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="p-2 border">{index + 1}. {statement}</td>
                  {[0, 1, 2, 3, 4].map((value) => (
                    <td key={value} className="p-2 border text-center">
                      <input 
                        type="radio" 
                        name={`pcs-q${index}`}
                        value={value}
                        onChange={() => handleScoreChange(index, value)}
                        checked={scores[index] === value}
                        className="form-radio h-4 w-4 text-blue-600"
                      />
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-bold mb-3">Αποτελέσματα PCS:</h3>
          
          <div className="mb-3">
            <p className="font-semibold">Συνολική Βαθμολογία:</p>
            <div className="text-xl font-bold mt-1">{totalScore} / 52</div>
            <div className={`mt-1 p-2 rounded-lg text-sm font-semibold ${
              totalScore <= 20 ? 'bg-green-100 text-green-800' : 
              totalScore <= 30 ? 'bg-yellow-100 text-yellow-800' : 
              'bg-red-100 text-red-800'
            }`}>
              {getInterpretation(totalScore)}
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mt-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <p className="font-semibold text-sm">Μηρυκασμός:</p>
              <div className="text-lg font-bold">{rumination} / 16</div>
              <p className="text-xs text-gray-500 mt-1">Ερωτήσεις 9, 10, 11, 12</p>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <p className="font-semibold text-sm">Μεγέθυνση:</p>
              <div className="text-lg font-bold">{magnification} / 12</div>
              <p className="text-xs text-gray-500 mt-1">Ερωτήσεις 6, 7, 13</p>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <p className="font-semibold text-sm">Αίσθημα Αβοηθητότητας:</p>
              <div className="text-lg font-bold">{helplessness} / 24</div>
              <p className="text-xs text-gray-500 mt-1">Ερωτήσεις 1, 2, 3, 4, 5, 8</p>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold mb-2">Ερμηνεία:</h3>
          <p className="mb-2">Η βαθμολογία PCS υποδεικνύει το επίπεδο καταστροφολογικών σκέψεων σχετικά με τον πόνο.</p>
          
          <div className="mt-3">
            <p className="font-semibold">Κλινικές Ερμηνείες:</p>
            <ul className="list-disc pl-5 space-y-1 mt-1 text-sm">
              <li><span className="font-semibold text-green-600">0-20:</span> Χαμηλή έως μέτρια καταστροφολογική σκέψη</li>
              <li><span className="font-semibold text-yellow-600">21-30:</span> Αυξημένη καταστροφολογική σκέψη, συνιστάται παρακολούθηση</li>
              <li><span className="font-semibold text-red-600">31-52:</span> Κλινικά σημαντική καταστροφολογική σκέψη, ίσως απαιτείται παρέμβαση</li>
            </ul>
          </div>
          
          <div className="mt-3">
            <p className="font-semibold">Υποκλίμακες:</p>
            <ul className="list-disc pl-5 space-y-1 mt-1 text-sm">
              <li><span className="font-semibold">Μηρυκασμός:</span> Επαναλαμβανόμενη ενασχόληση με σκέψεις σχετικά με τον πόνο</li>
              <li><span className="font-semibold">Μεγέθυνση:</span> Υπερβολική εκτίμηση της απειλής του πόνου</li>
              <li><span className="font-semibold">Αίσθημα Αβοηθητότητας:</span> Αίσθημα ανικανότητας αντιμετώπισης του πόνου</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Clinical Implications */}
      <div className="bg-yellow-50 p-4 rounded-lg mb-6">
        <h3 className="font-bold mb-2">Κλινικές Επιπτώσεις:</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>Υψηλές βαθμολογίες PCS συσχετίζονται με αυξημένη ένταση πόνου, ανικανότητα και συναισθηματική δυσφορία</li>
          <li>Η καταστροφολογική σκέψη μπορεί να επηρεάσει την ανταπόκριση στη θεραπεία και το ρυθμό ανάρρωσης</li>
          <li>Οι παρεμβάσεις μπορεί να περιλαμβάνουν γνωσιακή-συμπεριφορική προσέγγιση, εκπαίδευση για τον πόνο και ασκήσεις αντιμετώπισης</li>
        </ul>
      </div>
      
      {/* Notes */}
      <div className="mb-6">
        <label className="block font-bold mb-2">Κλινικές Παρατηρήσεις:</label>
        <textarea 
          className="w-full h-32 p-3 border border-gray-300 rounded-md" 
          placeholder="Καταγράψτε επιπλέον παρατηρήσεις ή σημειώσεις εδώ..."
        ></textarea>
      </div>
      
      {/* Actions */}
      <div className="flex justify-end">
        <button 
          onClick={handlePrint} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Εκτύπωση Φόρμας
        </button>
      </div>
    </div>
  );
};

export default PCSGreek;
