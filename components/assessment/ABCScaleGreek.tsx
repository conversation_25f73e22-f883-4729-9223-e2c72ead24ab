// Αρχείο: src/components/ABCScaleGreek.tsx
// Σημείωση: Αυτή είναι μια τροποποιημένη έκδοση του component για λειτουργία με τα κοινά δεδομένα ασθενή

import React, { useState } from 'react';

interface PatientData {
  name: string;
  date: string;
  age: string;
  assessor: string;
}

interface ABCScaleGreekProps {
  patientData: PatientData;
}

const ABCScaleGreek: React.FC<ABCScaleGreekProps> = ({  }) => {
  const [scores, setScores] = useState<Record<number, number>>({});
  const [totalScore, setTotalScore] = useState(0);
  const [functioningLevel, setFunctioningLevel] = useState("");
  
  const activities = [
    "Περπάτημα μέσα στο σπίτι",
    "Ανέβασμα ή κατέβασμα σκάλας",
    "Σκύψιμο για να πιάσετε μια παντόφλα από το πάτωμα της ντουλάπας",
    "Προσπάθεια να πιάσετε ένα μικρό κουτάκι από ράφι στο ύψος των ματιών σας",
    "Στάση στις μύτες των ποδιών για να φτάσετε κάτι πάνω από το κεφάλι σας",
    "Στάση σε καρέκλα για να φτάσετε κάτι ψηλά",
    "Σκούπισμα του πατώματος",
    "Περπάτημα έξω από το σπίτι προς το αυτοκίνητο που είναι παρκαρισμένο στην αυλή",
    "Είσοδος ή έξοδος από αυτοκίνητο",
    "Περπάτημα σε χώρο στάθμευσης ή στην αγορά",
    "Περπάτημα σε ανηφόρα ή κατηφόρα",
    "Περπάτημα σε πολυσύχναστο εμπορικό κέντρο όπου περνούν γρήγορα πολλά άτομα",
    "Σύγκρουση με άλλα άτομα ενώ περπατάτε σε εμπορικό κέντρο",
    "Ανέβασμα ή κατέβασμα από κυλιόμενη σκάλα ενώ κρατιέστε από την κουπαστή",
    "Ανέβασμα ή κατέβασμα από κυλιόμενη σκάλα ενώ κρατάτε πακέτα (και δεν μπορείτε να κρατηθείτε από την κουπαστή)",
    "Περπάτημα έξω σε παγωμένο πεζοδρόμιο"
  ];
  
  const handleScoreChange = (index: number, value: string) => {
    const newScores = { ...scores, [index]: parseInt(value) };
    setScores(newScores);
    calculateTotal(newScores);
  };
  
  const calculateTotal = (newScores: Record<number, number>) => {
    const values = Object.values(newScores);
    if (values.length === 0) return;
    
    const sum = values.reduce((acc, curr) => acc + curr, 0);
    const avg = sum / 16;
    setTotalScore(avg);
    
    // Set functioning level
    if (avg < 50) {
      setFunctioningLevel("Χαμηλό επίπεδο φυσικής λειτουργικότητας");
    } else if (avg >= 50 && avg <= 80) {
      setFunctioningLevel("Μέτριο επίπεδο φυσικής λειτουργικότητας");
    } else {
      setFunctioningLevel("Υψηλό επίπεδο φυσικής λειτουργικότητας");
    }
  };
  
  const handlePrint = () => {
    window.print();
  };

  return (
    <div>
      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-bold mb-2">Οδηγίες:</h3>
        <p>Για κάθε μία από τις παρακάτω δραστηριότητες, παρακαλώ αξιολογήστε το επίπεδο αυτοπεποίθησής σας για την εκτέλεση της δραστηριότητας χωρίς να χάσετε την ισορροπία σας ή να νιώσετε αστάθεια, επιλέγοντας ένα ποσοστό από την παρακάτω κλίμακα.</p>
        <p className="mt-2">Εάν δεν εκτελείτε τη συγκεκριμένη δραστηριότητα στην καθημερινότητά σας, φανταστείτε πόσο σίγουροι/ες θα νιώθατε αν έπρεπε να την εκτελέσετε. Εάν συνήθως χρησιμοποιείτε βοήθημα βάδισης ή στηρίζεστε σε κάποιον όταν εκτελείτε αυτή τη δραστηριότητα, αξιολογήστε την αυτοπεποίθησή σας σαν να χρησιμοποιούσατε αυτά τα βοηθήματα.</p>
      </div>
      
      {/* Scale */}
      <div className="mb-6">
        <div className="flex justify-between mb-1">
          <span className="font-bold">0%</span>
          <span className="font-bold">100%</span>
        </div>
        <div className="flex justify-between mb-2">
          <span>Καμία αυτοπεποίθηση</span>
          <span>Απόλυτη αυτοπεποίθηση</span>
        </div>
        <div className="h-4 w-full bg-gray-200 rounded-full relative mb-2">
          {[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((val) => (
            <div key={val} className="absolute h-6 w-1 bg-gray-400" style={{ left: `${val}%`, top: '-4px' }}></div>
          ))}
        </div>
        <div className="flex justify-between text-xs text-gray-600">
          {[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((val) => (
            <div key={val} className="text-center" style={{ width: '20px', marginLeft: val === 0 ? '0' : '-10px' }}>
              {val}%
            </div>
          ))}
        </div>
      </div>
      
      {/* Form */}
      <div className="mb-6">
        <div className="bg-gray-100 p-3 mb-2 rounded-t flex font-bold">
          <div className="w-2/3">Δραστηριότητα</div>
          <div className="w-1/3">Αυτοπεποίθηση (0-100%)</div>
        </div>
        {activities.map((activity, index) => (
          <div key={index} className={`flex p-3 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} border-b`}>
            <div className="w-2/3">{index + 1}. {activity}</div>
            <div className="w-1/3">
              <select 
                value={scores[index] || ''} 
                onChange={(e) => handleScoreChange(index, e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Επιλέξτε επίπεδο αυτοπεποίθησης</option>
                {[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((val) => (
                  <option key={val} value={val}>{val}%</option>
                ))}
              </select>
            </div>
          </div>
        ))}
      </div>
      
      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-bold mb-2">Συνολική Βαθμολογία ABC:</h3>
          <div className="text-2xl font-bold">{totalScore.toFixed(1)}%</div>
          <div className={`mt-2 p-2 rounded font-semibold ${
            totalScore < 50 ? 'bg-red-100 text-red-800' : 
            totalScore <= 80 ? 'bg-yellow-100 text-yellow-800' : 
            'bg-green-100 text-green-800'
          }`}>
            {functioningLevel}
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold mb-2">Ερμηνεία:</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li><span className="font-semibold text-red-600">Κάτω από 50%:</span> Χαμηλό επίπεδο φυσικής λειτουργικότητας</li>
            <li><span className="font-semibold text-yellow-600">50-80%:</span> Μέτριο επίπεδο φυσικής λειτουργικότητας</li>
            <li><span className="font-semibold text-green-600">Πάνω από 80%:</span> Υψηλό επίπεδο φυσικής λειτουργικότητας</li>
          </ul>
          <p className="mt-2 text-sm text-gray-600">Σύμφωνα με Myers et al. (1998)</p>
        </div>
      </div>
      
      {/* Notes */}
      <div className="mb-6">
        <label className="block font-bold mb-2">Κλινικές Παρατηρήσεις:</label>
        <textarea 
          className="w-full h-32 p-3 border border-gray-300 rounded-md" 
          placeholder="Καταγράψτε επιπλέον παρατηρήσεις ή σημειώσεις εδώ..."
        ></textarea>
      </div>
      
      {/* Actions */}
      <div className="flex justify-end">
        <button 
          onClick={handlePrint} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Εκτύπωση Φόρμας
        </button>
      </div>
    </div>
  );
};

export default ABCScaleGreek;

