// Αρχείο: src/components/FABQGreek.tsx
import React, { useState } from 'react';

interface PatientData {
  name: string;
  date: string;
  age: string;
  assessor: string;
}

interface FABQGreekProps {
  patientData: PatientData;
}

const FABQGreek: React.FC<FABQGreekProps> = ({  }) => {
  const [scores, setScores] = useState<Record<string, number>>({});
  const [physicalActivityScore, setPhysicalActivityScore] = useState(0);
  const [workScore, setWorkScore] = useState(0);
  
  const questions = [
    {
      section: "Για κάθε δήλωση παρακαλώ κυκλώστε έναν αριθμό από το 0 έως το 6 για να δείξετε πόσο οι σωματικές δραστηριότητες όπως το σκύψιμο, η ανύψωση, το περπάτημα ή η οδήγηση επηρεάζουν ή θα επηρέαζαν τον πόνο στη μέση σας.",
      statements: [
        "Ο πόνος μου προκλήθηκε από σωματική δραστηριότητα",
        "Η σωματική δραστηριότητα χειροτερεύει τον πόνο μου",
        "Η σωματική δραστηριότητα μπορεί να βλάψει τη μέση μου",
        "Δεν θα πρέπει να κάνω σωματικές δραστηριότητες που (ίσως) χειροτερεύουν τον πόνο μου",
        "Δεν μπορώ να κάνω σωματικές δραστηριότητες που (ίσως) χειροτερεύουν τον πόνο μου"
      ],
      scoreIndices: [0, 1, 2, 3, 4] // Indices for FABQ-PA calculation (excluding item 1)
    },
    {
      section: "Οι παρακάτω δηλώσεις αφορούν το πώς η κανονική εργασία σας επηρεάζει ή θα επηρέαζε τον πόνο στη μέση σας",
      statements: [
        "Ο πόνος μου προκλήθηκε από την εργασία μου ή από ατύχημα στην εργασία",
        "Η εργασία μου επιδείνωσε τον πόνο μου",
        "Έχω αξίωση αποζημίωσης για τον πόνο μου",
        "Η εργασία μου είναι πολύ βαριά για εμένα",
        "Η εργασία μου χειροτερεύει ή θα χειροτέρευε τον πόνο μου",
        "Η εργασία μου μπορεί να βλάψει τη μέση μου",
        "Δεν θα πρέπει να κάνω την κανονική μου εργασία με τον τωρινό πόνο",
        "Δεν μπορώ να κάνω την κανονική μου εργασία με τον τωρινό πόνο",
        "Δεν μπορώ να κάνω την κανονική μου εργασία μέχρι να θεραπευτεί ο πόνος μου",
        "Δεν πιστεύω ότι θα επιστρέψω στην κανονική μου εργασία μέσα σε 3 μήνες",
        "Δεν πιστεύω ότι θα μπορέσω ποτέ να επιστρέψω σε αυτή την εργασία"
      ],
      scoreIndices: [1, 2, 3, 4, 5, 6, 9, 10, 11, 14, 15] // Indices for FABQ-W calculation (items 6, 7, 9-12, 15)
    }
  ];
  
  const handleScoreChange = (sectionIndex: number, questionIndex: number, value: number) => {
    const key = `${sectionIndex}-${questionIndex}`;
    const newScores = { ...scores, [key]: value };
    setScores(newScores);
    calculateScores(newScores);
  };
  
  const calculateScores = (newScores: Record<string, number>) => {
    // Calculate Physical Activity subscale (items 2, 3, 4, 5)
    const paItems = [1, 2, 3, 4]; // 0-indexed array positions for PA items
    let paScore = 0;
    let validPaCount = 0;
    
    paItems.forEach(index => {
      const key = `0-${index}`;
      if (newScores[key] !== undefined) {
        paScore += newScores[key];
        validPaCount++;
      }
    });
    
    setPhysicalActivityScore(validPaCount ? paScore : 0);
    
    // Calculate Work subscale (items 6, 7, 9, 10, 11, 12, 15)
    const workItems = [0, 1, 3, 4, 5, 6, 9]; // 0-indexed array positions for work items
    let workScore = 0;
    let validWorkCount = 0;
    
    workItems.forEach(index => {
      const key = `1-${index}`;
      if (newScores[key] !== undefined) {
        workScore += newScores[key];
        validWorkCount++;
      }
    });
    
    setWorkScore(validWorkCount ? workScore : 0);
  };
  
  const handlePrint = () => {
    window.print();
  };

  return (
    <div>
      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-bold mb-2">Οδηγίες:</h3>
        <p>Παρακάτω είναι μερικά από τα πράγματα που μας έχουν πει άλλοι ασθενείς σχετικά με τον πόνο τους. Για κάθε δήλωση, παρακαλώ επιλέξτε έναν αριθμό από το 0 έως το 6 για να δείξετε πόσο οι σωματικές δραστηριότητες όπως το σκύψιμο, η ανύψωση, το περπάτημα ή η οδήγηση επηρεάζουν ή θα επηρέαζαν τον πόνο στη μέση σας.</p>
      </div>
      
      {/* Form */}
      {questions.map((section, sectionIndex) => (
        <div key={sectionIndex} className="mb-8">
          <h3 className="font-bold mb-3">{section.section}</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse mb-4">
              <thead>
                <tr className="bg-gray-100">
                  <th className="p-2 border text-left w-1/2">Δήλωση</th>
                  <th className="p-2 border text-center" colSpan={7}>
                    <div className="flex justify-between px-6">
                      <span>Διαφωνώ απόλυτα</span>
                      <span>Δεν είμαι σίγουρος/η</span>
                      <span>Συμφωνώ απόλυτα</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {section.statements.map((statement, statementIndex) => (
                  <tr key={statementIndex} className={statementIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="p-2 border">{statementIndex + 1}. {statement}</td>
                    {[0, 1, 2, 3, 4, 5, 6].map((value) => (
                      <td key={value} className="p-2 border text-center w-10">
                        <input 
                          type="radio" 
                          name={`sec${sectionIndex}q${statementIndex}`}
                          value={value}
                          onChange={() => handleScoreChange(sectionIndex, statementIndex, value)}
                          checked={scores[`${sectionIndex}-${statementIndex}`] === value}
                          className="form-radio h-4 w-4 text-blue-600"
                        />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}
      
      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-bold mb-2">Αποτελέσματα FABQ:</h3>
          
          <div className="mb-3">
            <p className="font-semibold">Υποκλίμακα Σωματικής Δραστηριότητας (FABQ-PA):</p>
            <div className="text-xl font-bold mt-1">{physicalActivityScore} / 24</div>
            <p className="text-sm text-gray-600 mt-1">Ερωτήσεις 2, 3, 4, 5</p>
          </div>
          
          <div>
            <p className="font-semibold">Υποκλίμακα Εργασίας (FABQ-W):</p>
            <div className="text-xl font-bold mt-1">{workScore} / 42</div>
            <p className="text-sm text-gray-600 mt-1">Ερωτήσεις 6, 7, 9, 10, 11, 12, 15</p>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold mb-2">Ερμηνεία:</h3>
          <p className="mb-2">Υψηλότερες βαθμολογίες υποδεικνύουν μεγαλύτερο φόβο-αποφυγή πεποιθήσεις.</p>
          
          <div className="mt-3">
            <p className="font-semibold">Κλινικά Σημεία Αναφοράς:</p>
            <ul className="list-disc pl-5 space-y-1 mt-1">
              <li>FABQ-PA &gt; 15: Υψηλός φόβος-αποφυγή για σωματικές δραστηριότητες</li>
              <li>FABQ-W &gt; 34: Υψηλός φόβος-αποφυγή σχετικά με την εργασία</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Notes */}
      <div className="mb-6">
        <label className="block font-bold mb-2">Κλινικές Παρατηρήσεις:</label>
        <textarea 
          className="w-full h-32 p-3 border border-gray-300 rounded-md" 
          placeholder="Καταγράψτε επιπλέον παρατηρήσεις ή σημειώσεις εδώ..."
        ></textarea>
      </div>
      
      {/* Actions */}
      <div className="flex justify-end">
        <button 
          onClick={handlePrint} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Εκτύπωση Φόρμας
        </button>
      </div>
    </div>
  );
};

export default FABQGreek;