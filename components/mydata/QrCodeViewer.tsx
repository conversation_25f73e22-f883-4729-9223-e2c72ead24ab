// components/mydata/QrCodeViewer.tsx
'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { QrCode, ExternalLink, Copy } from 'lucide-react';
import { toast } from 'sonner';

interface QrCodeViewerProps {
  qrUrl: string;
  mark: string;
  className?: string;
}

export default function QrCodeViewer({ qrUrl, mark, className = '' }: QrCodeViewerProps) {
  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(qrUrl);
      toast.success('QR URL copied to clipboard');
    } catch (error) {
      console.error('Failed to copy URL:', error);
      toast.error('Failed to copy URL');
    }
  };

  const isValidUrl = qrUrl && qrUrl.startsWith('http');

  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2 flex items-center">
          <QrCode className="h-5 w-5 mr-2" />
          myDATA QR Code
        </h3>

        <Tabs defaultValue="view" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view">View QR</TabsTrigger>
            <TabsTrigger value="info">Information</TabsTrigger>
          </TabsList>

          <TabsContent value="view" className="py-4">
            <div className="flex flex-col items-center space-y-4">
              {!isValidUrl ? (
                <div className="flex flex-col items-center justify-center h-48 text-center">
                  <QrCode className="h-12 w-12 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-500">No QR code available</span>
                  <p className="text-xs text-gray-400 mt-1">
                    QR code will be available after successful submission to myDATA
                  </p>
                </div>
              ) : (
                <>
                  {/* QR Code Preview - Using iframe to embed the official myDATA QR page */}
                  <div className="w-full max-w-sm">
                    <div className="bg-gray-50 border rounded-lg p-4 text-center">
                      <QrCode className="h-16 w-16 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-3">
                        Official myDATA QR Code
                      </p>
                      <p className="text-xs text-gray-500 mb-3">
                        MARK: <span className="font-mono">{mark}</span>
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-wrap justify-center gap-2">
                    <Button variant="default" size="sm" asChild>
                      <a href={qrUrl} target="_blank" rel="noopener noreferrer">
                        <QrCode className="h-4 w-4 mr-2" />
                        View QR Code
                      </a>
                    </Button>

                    <Button variant="outline" size="sm" onClick={handleCopyUrl}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy URL
                    </Button>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Full Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh]">
                        <DialogHeader>
                          <DialogTitle>myDATA QR Code - MARK: {mark}</DialogTitle>
                        </DialogHeader>
                        <div className="overflow-hidden">
                          <iframe
                            src={qrUrl}
                            width="100%"
                            height="500"
                            frameBorder="0"
                            title="myDATA QR Code"
                            className="border rounded"
                          />
                          <div className="mt-4 p-3 bg-gray-50 rounded text-sm">
                            <p className="font-medium mb-1">QR Code URL:</p>
                            <p className="text-xs text-gray-600 break-all font-mono">
                              {qrUrl}
                            </p>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="info" className="py-4">
            <div className="text-sm space-y-3">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-blue-900 mb-2">About myDATA QR Codes</h4>
                <p className="text-blue-800">
                  The myDATA QR code is an official digital signature provided by AADE 
                  (Independent Authority for Public Revenue) that verifies this invoice 
                  has been properly registered in the Greek tax system.
                </p>
              </div>

              <div>
                <p className="font-medium mb-2">This QR code contains:</p>
                <ul className="list-disc list-inside space-y-1 ml-2 text-gray-700">
                  <li>Invoice MARK number: <span className="font-mono bg-gray-100 px-1 rounded">{mark}</span></li>
                  <li>Issuer and recipient information</li>
                  <li>Invoice date and details</li>
                  <li>Total amount and tax information</li>
                  <li>Digital signature for authenticity</li>
                </ul>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <h4 className="font-medium text-amber-900 mb-1">Important Notes:</h4>
                <ul className="text-amber-800 text-xs space-y-1">
                  <li>• This QR code should be included on all printed invoices</li>
                  <li>• Customers can scan it to verify invoice authenticity</li>
                  <li>• The QR code links directly to the official AADE system</li>
                  <li>• Never modify or recreate this QR code - always use the original</li>
                </ul>
              </div>

              {isValidUrl && (
                <div className="border-t pt-3">
                  <p className="text-xs text-gray-500">
                    <strong>QR URL:</strong> 
                    <br />
                    <span className="font-mono text-gray-600 break-all">
                      {qrUrl}
                    </span>
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
}