'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { Loader2, Plus } from 'lucide-react';
import { createClient } from '@supabase/supabase-js';

interface AddPaymentMethodFormProps {
  invoiceId: string;
  invoiceTotal: number;
  onSuccess?: () => void;
}

export default function AddPaymentMethodForm({ 
  invoiceId, 
  invoiceTotal, 
  onSuccess 
}: AddPaymentMethodFormProps) {
  const [paymentType, setPaymentType] = useState('3'); // Default to Bank Payment
  const [amount, setAmount] = useState(invoiceTotal.toString());
  const [paymentInfo, setPaymentInfo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount) {
      toast.error('Please enter the payment amount');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Insert the payment method
      const { error } = await supabase
        .from('invoice_payment_methods')
        .insert({
          invoice_id: invoiceId,
          payment_type: parseInt(paymentType),
          amount: parseFloat(amount),
          payment_info: paymentInfo || null
        });
      
      if (error) {
        throw error;
      }
      
      toast.success('Payment method added successfully');
      
      // Reset form
      setPaymentInfo('');
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast.error('Failed to add payment method');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 border p-4 rounded-md">
      <h3 className="text-lg font-medium">Add Payment Method</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="paymentType">Payment Type</Label>
          <Select value={paymentType} onValueChange={setPaymentType}>
            <SelectTrigger id="paymentType">
              <SelectValue placeholder="Select payment type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3">Bank Payment</SelectItem>
              <SelectItem value="4">Cash Payment</SelectItem>
              <SelectItem value="5">Check Payment</SelectItem>
              <SelectItem value="7">POS Payment</SelectItem>
              <SelectItem value="1">Domestic Payment</SelectItem>
              <SelectItem value="2">International Payment</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="amount">Amount (€)</Label>
          <Input
            id="amount"
            type="number"
            min="0.01"
            step="0.01"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            required
          />
        </div>
        
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="paymentInfo">Payment Information (Optional)</Label>
          <Input
            id="paymentInfo"
            value={paymentInfo}
            onChange={(e) => setPaymentInfo(e.target.value)}
            placeholder="e.g., Bank account, check number, etc."
          />
        </div>
      </div>
      
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Adding...
          </>
        ) : (
          <>
            <Plus className="h-4 w-4 mr-2" />
            Add Payment Method
          </>
        )}
      </Button>
    </form>
  );
}
