// components/mydata/MyDataNavigation.tsx (updated)
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function MyDataNavigation() {
  const pathname = usePathname();
  
  const isActive = (path: string) => {
    return pathname === path;
  };
  
  return (
    <nav className="mb-6">
      <ul className="flex space-x-4 border-b">
        <li>
          <Link
            href="/admin/mydata/settings"
            className={`px-3 py-2 inline-block ${
              isActive('/admin/mydata/settings') 
                ? 'border-b-2 border-blue-500 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Settings
          </Link>
        </li>
        <li>
          <Link
            href="/admin/invoices"
            className={`px-3 py-2 inline-block ${
              pathname.startsWith('/admin/invoices') 
                ? 'border-b-2 border-blue-500 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Invoices
          </Link>
        </li>
        <li>
          <Link
            href="/admin/mydata/logs"
            className={`px-3 py-2 inline-block ${
              isActive('/admin/mydata/logs') 
                ? 'border-b-2 border-blue-500 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            API Logs
          </Link>
        </li>
        <li>
          <Link
            href="/admin/mydata/test"
            className={`px-3 py-2 inline-block ${
              isActive('/admin/mydata/test') 
                ? 'border-b-2 border-blue-500 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Test
          </Link>
        </li>
      </ul>
    </nav>
  );
}