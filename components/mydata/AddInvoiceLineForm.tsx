'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { Loader2, Plus } from 'lucide-react';
import { createClient } from '@supabase/supabase-js';

interface AddInvoiceLineFormProps {
  invoiceId: string;
  onSuccess?: () => void;
}

export default function AddInvoiceLineForm({ invoiceId, onSuccess }: AddInvoiceLineFormProps) {
  const [description, setDescription] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [unitPrice, setUnitPrice] = useState('');
  const [vatCategory, setVatCategory] = useState('1'); // Default to 24%
  const [classificationType, setClassificationType] = useState('E3_561_007');
  const [classificationCategory, setClassificationCategory] = useState('category1_3');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!description || !quantity || !unitPrice) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate values
      const qty = parseFloat(quantity);
      const price = parseFloat(unitPrice);
      const netValue = qty * price;

      // Calculate VAT amount based on category
      const vatRates: Record<string, number> = {
        '1': 0.24, // 24%
        '2': 0.13, // 13%
        '3': 0.06, // 6%
        '4': 0.17, // 17%
        '5': 0.09, // 9%
        '6': 0.04  // 4%
      };

      const vatRate = vatRates[vatCategory] || 0.24;
      const vatAmount = netValue * vatRate;

      // Get the next line number
      const { data: existingLines } = await supabase
        .from('invoice_lines')
        .select('line_number')
        .eq('invoice_id', invoiceId)
        .order('line_number', { ascending: false })
        .limit(1);

      const nextLineNumber = existingLines && existingLines.length > 0
        ? existingLines[0].line_number + 1
        : 1;

      // Insert the new line
      const { error } = await supabase
        .from('invoice_lines')
        .insert({
          invoice_id: invoiceId,
          line_number: nextLineNumber,
          description,
          quantity: qty,
          unit_price: price,
          net_value: netValue,
          vat_category: parseInt(vatCategory),
          vat_amount: vatAmount,
          income_classification_type: classificationType,
          income_classification_category: classificationCategory
        });

      if (error) {
        throw error;
      }

      // Update invoice totals
      const { data: invoice } = await supabase
        .from('invoices')
        .select('total_net, total_vat, total_gross')
        .eq('id', invoiceId)
        .single();

      if (invoice) {
        await supabase
          .from('invoices')
          .update({
            total_net: invoice.total_net + netValue,
            total_vat: invoice.total_vat + vatAmount,
            total_gross: invoice.total_gross + netValue + vatAmount,
            updated_at: new Date().toISOString()
          })
          .eq('id', invoiceId);
      }

      toast.success('Invoice line added successfully');

      // Reset form
      setDescription('');
      setQuantity('1');
      setUnitPrice('');

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error adding invoice line:', error);
      toast.error('Failed to add invoice line');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 border p-4 rounded-md">
      <h3 className="text-lg font-medium">Add Invoice Line</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="description">Description *</Label>
          <Input
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Service description"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <Input
              id="quantity"
              type="number"
              min="0.01"
              step="0.01"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="unitPrice">Unit Price (€) *</Label>
            <Input
              id="unitPrice"
              type="number"
              min="0.01"
              step="0.01"
              value={unitPrice}
              onChange={(e) => setUnitPrice(e.target.value)}
              placeholder="0.00"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="vatCategory">VAT Rate</Label>
          <Select value={vatCategory} onValueChange={setVatCategory}>
            <SelectTrigger id="vatCategory">
              <SelectValue placeholder="Select VAT rate" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">24% - Standard</SelectItem>
              <SelectItem value="2">13% - Reduced</SelectItem>
              <SelectItem value="3">6% - Super Reduced</SelectItem>
              <SelectItem value="4">17% - Special Rate</SelectItem>
              <SelectItem value="5">9% - Special Reduced</SelectItem>
              <SelectItem value="6">4% - Special Super Reduced</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="classificationType">Classification Type</Label>
          <Select value={classificationType} onValueChange={setClassificationType}>
            <SelectTrigger id="classificationType">
              <SelectValue placeholder="Select classification type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="E3_561_007">E3_561_007 - Retail Sales</SelectItem>
              <SelectItem value="E3_561_001">E3_561_001 - Wholesale Sales</SelectItem>
              <SelectItem value="E3_561_003">E3_561_003 - Service Provision</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="classificationCategory">Classification Category</Label>
          <Select value={classificationCategory} onValueChange={setClassificationCategory}>
            <SelectTrigger id="classificationCategory">
              <SelectValue placeholder="Select classification category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="category1_3">Category 1.3</SelectItem>
              <SelectItem value="category1_2">Category 1.2</SelectItem>
              <SelectItem value="category1_1">Category 1.1</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Adding...
          </>
        ) : (
          <>
            <Plus className="h-4 w-4 mr-2" />
            Add Line Item
          </>
        )}
      </Button>
    </form>
  );
}
