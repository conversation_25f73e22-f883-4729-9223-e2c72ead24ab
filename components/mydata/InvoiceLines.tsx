// components/mydata/InvoiceLines.tsx
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';

interface InvoiceLinesProps {
  lines: {
    id: string;
    line_number: number;
    description: string;
    quantity: number;
    unit_price: number;
    net_value: number;
    vat_category: number;
    vat_amount: number;
  }[];
  total: {
    netValue: number;
    vatAmount: number;
    grossValue: number;
  };
}

export default function InvoiceLines({ lines, total }: InvoiceLinesProps) {
  // Map VAT category codes to percentages
  const vatRates: Record<number, string> = {
    1: '24%',
    2: '13%',
    3: '6%',
    4: '17%',
    5: '9%',
    6: '4%'
  };
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <h3 className="text-lg font-semibold p-4 border-b">Invoice Lines</h3>
      
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">#</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-right">Qty</TableHead>
              <TableHead className="text-right">Unit Price</TableHead>
              <TableHead className="text-right">VAT</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lines.map((line) => (
              <TableRow key={line.id}>
                <TableCell>{line.line_number}</TableCell>
                <TableCell>{line.description}</TableCell>
                <TableCell className="text-right">{line.quantity}</TableCell>
                <TableCell className="text-right">{formatCurrency(line.unit_price)}</TableCell>
                <TableCell className="text-right">
                  {vatRates[line.vat_category] || `${line.vat_category}%`}
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(line.net_value + line.vat_amount)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      <div className="p-4 border-t space-y-2">
        <div className="flex justify-between">
          <span>Net Total:</span>
          <span>{formatCurrency(total.netValue)}</span>
        </div>
        <div className="flex justify-between">
          <span>VAT Total:</span>
          <span>{formatCurrency(total.vatAmount)}</span>
        </div>
        <div className="flex justify-between font-bold">
          <span>Grand Total:</span>
          <span>{formatCurrency(total.grossValue)}</span>
        </div>
      </div>
    </div>
  );
}