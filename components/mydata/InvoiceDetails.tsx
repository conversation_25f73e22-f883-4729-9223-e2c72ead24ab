// components/mydata/InvoiceDetails.tsx
import { format } from 'date-fns';
import { el } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { formatCurrency } from '@/lib/utils';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface InvoiceDetailsProps {
  invoice: Invoice;
  className?: string;
}

export default function InvoiceDetails({ invoice, className = '' }: InvoiceDetailsProps) {
  // Add defensive check for invoice
  if (!invoice || typeof invoice !== 'object') {
    return (
      <Card className={className}>
        <CardContent>
          <p className="text-center py-4 text-gray-500">No invoice data available</p>
        </CardContent>
      </Card>
    );
  }

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return format(new Date(dateString), 'PPP', { locale: el });
  };

  // Get status badge variant and icon
  const getStatusBadge = () => {
    // Add defensive check for invoice.status
    const status = invoice?.status || 'draft';

    switch (status) {
      case 'submitted':
        return {
          variant: 'secondary' as const, // Changed from 'success' to 'secondary'
          icon: <CheckCircle className="h-3.5 w-3.5 mr-1" />,
          tooltip: 'Successfully submitted to myDATA'
        };
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: <AlertCircle className="h-3.5 w-3.5 mr-1" />,
          tooltip: 'Error submitting to myDATA'
        };
      case 'draft':
      default:
        return {
          variant: 'outline' as const,
          icon: <Clock className="h-3.5 w-3.5 mr-1" />,
          tooltip: 'Not yet submitted to myDATA'
        };
    }
  };

  const statusBadge = getStatusBadge();

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl">
            Invoice {invoice.invoice_series || '-'}-{invoice.invoice_number || '-'}
          </CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Badge variant={statusBadge.variant} className="ml-2">
                  <span className="flex items-center">
                    {statusBadge.icon}
                    {invoice.status || 'draft'}
                  </span>
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>{statusBadge.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Client Information</h3>
            <div className="mt-2 space-y-1">
              <p className="font-medium">{invoice.client_name || '-'}</p>
              <p>VAT: {invoice.client_vat || '-'}</p>
              <p>Country: {invoice.client_country || 'GR'}</p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Invoice Information</h3>
            <div className="mt-2 space-y-1">
              <p>Series: <span className="font-medium">{invoice.invoice_series}</span></p>
              <p>Number: <span className="font-medium">{invoice.invoice_number}</span></p>
              <p>Issue Date: <span className="font-medium">{formatDate(invoice.issue_date)}</span></p>
              <p>Type: <span className="font-medium">{invoice.invoice_type}</span></p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Totals</h3>
            <div className="mt-2 space-y-1">
              <p>Net Amount: <span className="font-medium">{formatCurrency(invoice.total_net)}</span></p>
              <p>VAT Amount: <span className="font-medium">{formatCurrency(invoice.total_vat)}</span></p>
              <p className="text-lg font-bold">
                Total: {formatCurrency(invoice.total_gross)}
              </p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">myDATA Information</h3>
            <div className="mt-2 space-y-1">
              {invoice.mark ? (
                <>
                  <p>MARK: <span className="font-mono text-sm">{invoice.mark}</span></p>
                  {invoice.qr_url && (
                    <p>
                      <a
                        href={invoice.qr_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-blue-600 hover:underline"
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        Open QR Code
                      </a>
                    </p>
                  )}
                </>
              ) : (
                <p className="text-gray-500 italic">
                  Not yet submitted to myDATA
                </p>
              )}
              <p>Created: <span className="font-medium">{formatDate(invoice.created_at)}</span></p>
              {invoice.updated_at !== invoice.created_at && (
                <p>Last Updated: <span className="font-medium">{formatDate(invoice.updated_at)}</span></p>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      {invoice.status === 'error' && (
        <CardFooter className="pt-2">
          <div className="w-full p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-red-800">Submission Error</h4>
                <p className="mt-1 text-sm text-red-700">
                  There was an error submitting this invoice to myDATA. Check the API logs for more details.
                </p>
              </div>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}