// components/mydata/MyDataSettingsClient.tsx
'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import type { Database } from '@/types/supabase';
import type { CompanySettings, CompanySettingsUpdate, MyDataEnvironmentInfo } from '@/types/company';

interface MyDataSettingsClientProps {
  initialCompanySettings: CompanySettings | null;
  environmentInfo: MyDataEnvironmentInfo;
}

// Income classification types based on the XML schema
const INCOME_CLASSIFICATION_TYPES = [
  { value: 'E3_106', label: 'E3_106 - Πωλήσεις εμπορευμάτων' },
  { value: 'E3_205', label: 'E3_205 - Πωλήσεις προϊόντων' },
  { value: 'E3_210', label: 'E3_210 - Πωλήσεις παραπροϊόντων' },
  { value: 'E3_305', label: 'E3_305 - Παροχή υπηρεσιών' },
  { value: 'E3_310', label: 'E3_310 - Παροχή υπηρεσιών' },
  { value: 'E3_318', label: 'E3_318 - Παροχή υπηρεσιών' },
  { value: 'E3_561_001', label: 'E3_561_001 - Έσοδα από πώληση αγαθών' },
  { value: 'E3_561_002', label: 'E3_561_002 - Έσοδα από πώληση υπηρεσιών' },
  { value: 'E3_561_003', label: 'E3_561_003 - Έσοδα από χρήση περιουσιακών στοιχείων' },
  { value: 'E3_561_004', label: 'E3_561_004 - Έσοδα από τόκους' },
  { value: 'E3_561_005', label: 'E3_561_005 - Έσοδα από μερίσματα' },
  { value: 'E3_561_006', label: 'E3_561_006 - Έσοδα από δικαιώματα' },
  { value: 'E3_561_007', label: 'E3_561_007 - Λοιπά έσοδα' },
  { value: 'E3_562', label: 'E3_562 - Έσοδα από εκποίηση παγίων' },
  { value: 'E3_563', label: 'E3_563 - Έσοδα από εκποίηση συμμετοχών' },
  { value: 'E3_564', label: 'E3_564 - Έσοδα από υπεραξία μεταβίβασης' },
  { value: 'E3_565', label: 'E3_565 - Έσοδα από αναστροφή προβλέψεων' },
  { value: 'E3_566', label: 'E3_566 - Έσοδα από επιχορηγήσεις' },
  { value: 'E3_567', label: 'E3_567 - Έσοδα από ασφαλιστικές αποζημιώσεις' },
  { value: 'E3_568', label: 'E3_568 - Έσοδα από πρόστιμα - ποινές' },
  { value: 'E3_570', label: 'E3_570 - Έσοδα από διαγραφή υποχρεώσεων' },
  { value: 'E3_595', label: 'E3_595 - Έσοδα από διάλυση επιχείρησης' },
  { value: 'E3_596', label: 'E3_596 - Έσοδα από συγχώνευση επιχείρησης' },
  { value: 'E3_597', label: 'E3_597 - Έσοδα από διασπορά επιχείρησης' }
];

const INCOME_CLASSIFICATION_CATEGORIES = [
  { value: 'category1_1', label: 'Category 1.1 - Έσοδα από πώληση αγαθών (+)' },
  { value: 'category1_2', label: 'Category 1.2 - Έσοδα από πώληση αγαθών (-)' },
  { value: 'category1_3', label: 'Category 1.3 - Έσοδα από παροχή υπηρεσιών (+)' },
  { value: 'category1_4', label: 'Category 1.4 - Έσοδα από παροχή υπηρεσιών (-)' },
  { value: 'category1_5', label: 'Category 1.5 - Λοιπά έσοδα (+)' },
  { value: 'category1_6', label: 'Category 1.6 - Λοιπά έσοδα (-)' },
  { value: 'category1_7', label: 'Category 1.7 - Έσοδα για λ/σμό τρίτων (+)' },
  { value: 'category1_8', label: 'Category 1.8 - Έσοδα για λ/σμό τρίτων (-)' },
  { value: 'category1_9', label: 'Category 1.9 - Έσοδα από ΦΠΑ (+)' },
  { value: 'category1_10', label: 'Category 1.10 - Έσοδα από ΦΠΑ (-)' },
  { value: 'category1_95', label: 'Category 1.95 - Λοιπά έσοδα για κατανομή κ.λπ.α. (+)' }
];

const COUNTRIES = [
  { value: 'GR', label: 'Greece' },
  { value: 'CY', label: 'Cyprus' },
  { value: 'BG', label: 'Bulgaria' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
  { value: 'IT', label: 'Italy' },
  { value: 'ES', label: 'Spain' },
  // Add more countries as needed
];

export default function MyDataSettingsClient({ 
  initialCompanySettings, 
  environmentInfo 
}: MyDataSettingsClientProps) {
  const [companySettings, setCompanySettings] = useState<Partial<CompanySettings>>({
    companyName: initialCompanySettings?.companyName || '',
    vatNumber: initialCompanySettings?.vatNumber || '',
    country: initialCompanySettings?.country || 'GR',
    branch: initialCompanySettings?.branch || '0',
    address: initialCompanySettings?.address || '',
    postalCode: initialCompanySettings?.postalCode || '',
    city: initialCompanySettings?.city || '',
    defaultClassificationType: initialCompanySettings?.defaultClassificationType || 'E3_561_007',
    defaultClassificationCategory: initialCompanySettings?.defaultClassificationCategory || 'category1_3'
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  
  const supabase = createClientComponentClient<Database>();

  const handleInputChange = (field: keyof CompanySettings, value: string) => {
    setCompanySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    // Validate required fields
    if (!companySettings.companyName || !companySettings.vatNumber) {
      toast.error('Company name and VAT number are required');
      return;
    }

    setIsSaving(true);
    try {
      const updateData: CompanySettingsUpdate = {
        companyName: companySettings.companyName,
        vatNumber: companySettings.vatNumber,
        country: companySettings.country,
        branch: companySettings.branch,
        address: companySettings.address,
        postalCode: companySettings.postalCode,
        city: companySettings.city,
        defaultClassificationType: companySettings.defaultClassificationType,
        defaultClassificationCategory: companySettings.defaultClassificationCategory
      };

      const { error } = initialCompanySettings
        ? await supabase
            .from('company_settings')
            .update(updateData)
            .eq('id', initialCompanySettings.id)
        : await supabase
            .from('company_settings')
            .insert([updateData]);

      if (error) throw error;

      toast.success('Company settings saved successfully');
      
      // If this was an insert, we should refresh to get the new record
      if (!initialCompanySettings) {
        window.location.reload();
      }
    } catch (error) {
      console.error('Error saving company settings:', error);
      toast.error('Failed to save company settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      const response = await fetch('/api/mydata/test-connection', {
        method: 'POST'
      });

      if (response.ok) {
        toast.success('myDATA connection test successful');
      } else {
        const error = await response.text();
        toast.error(`Connection test failed: ${error}`);
      }
    } catch (error) {
      console.error('Connection test error:', error);
      toast.error('Connection test failed');
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* myDATA API Status */}
      <Card>
        <CardHeader>
          <CardTitle>myDATA API Credentials</CardTitle>
          <CardDescription>
            API credentials are configured using environment variables
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div>
              <Label className="text-sm font-medium">Environment:</Label>
              <Badge variant="secondary" className="ml-2">
                {environmentInfo.environment}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium">Credentials Status:</Label>
              <Badge 
                variant={environmentInfo.hasCredentials ? "default" : "destructive"}
                className="ml-2"
              >
                {environmentInfo.hasCredentials ? "Configured" : "Not Configured"}
              </Badge>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground">
            The system will automatically use the development environment (mydataapidev.aade.gr) 
            when running in development mode, and the production environment (mydatapi.aade.gr) 
            when running in production mode.
          </p>
          
          <p className="text-sm text-muted-foreground">
            <strong>Current environment:</strong> {environmentInfo.currentEnvironment}
          </p>
          
          <Button 
            onClick={handleTestConnection}
            disabled={!environmentInfo.hasCredentials || isTestingConnection}
            className="bg-green-600 hover:bg-green-700"
          >
            {isTestingConnection ? 'Testing...' : 'Test Connection'}
          </Button>
        </CardContent>
      </Card>

      {/* Company Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Company Settings</CardTitle>
          <CardDescription>
            Configure your company information for myDATA integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={companySettings.companyName || ''}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  placeholder="Enter company name"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="vatNumber">VAT Number *</Label>
                <Input
                  id="vatNumber"
                  value={companySettings.vatNumber || ''}
                  onChange={(e) => handleInputChange('vatNumber', e.target.value)}
                  placeholder="Enter VAT number (without country prefix)"
                  required
                />
              </div>

              <div>
                <Label htmlFor="country">Country *</Label>
                <Select 
                  value={companySettings.country || 'GR'} 
                  onValueChange={(value) => handleInputChange('country', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {COUNTRIES.map(country => (
                      <SelectItem key={country.value} value={country.value}>
                        {country.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="branch">Branch Number *</Label>
                <Input
                  id="branch"
                  value={companySettings.branch || '0'}
                  onChange={(e) => handleInputChange('branch', e.target.value)}
                  placeholder="Enter branch number (0 for headquarters)"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Use 0 for headquarters or main location
                </p>
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Address Information</h3>
            
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={companySettings.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter company address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  value={companySettings.postalCode || ''}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  placeholder="Enter postal code"
                />
              </div>

              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={companySettings.city || ''}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Enter city"
                />
              </div>
            </div>
          </div>

          {/* myDATA Classification Defaults */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Default Income Classifications</h3>
            <p className="text-sm text-muted-foreground">
              These defaults will be used for new invoices. You can override them for individual invoices.
            </p>
            
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="defaultClassificationType">Default Classification Type</Label>
                <Select 
                  value={companySettings.defaultClassificationType || 'E3_561_007'} 
                  onValueChange={(value) => handleInputChange('defaultClassificationType', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {INCOME_CLASSIFICATION_TYPES.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="defaultClassificationCategory">Default Classification Category</Label>
                <Select 
                  value={companySettings.defaultClassificationCategory || 'category1_3'} 
                  onValueChange={(value) => handleInputChange('defaultClassificationCategory', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {INCOME_CLASSIFICATION_CATEGORIES.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <Button 
              onClick={handleSave}
              disabled={isSaving || !companySettings.companyName || !companySettings.vatNumber}
              className="min-w-32"
            >
              {isSaving ? 'Saving...' : initialCompanySettings ? 'Update Settings' : 'Create Settings'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}