// components/mydata/TestInvoiceForm.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Minus, Loader2 } from 'lucide-react';
import type { Database } from '@/types/supabase';

export default function TestInvoiceForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [invoiceLineItems, setInvoiceLineItems] = useState([{
    description: 'Test product',
    quantity: 1,
    unit_price: 100,
    vat_category: 1 // 24% VAT
  }]);

  const supabase = createClientComponentClient<Database>();

  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      client_name: 'Test Client',
      client_vat: '*********',
      client_country: 'GR',
      invoice_series: 'TEST',
      invoice_number: `T${new Date().getTime().toString().slice(-6)}`,
      issue_date: new Date().toISOString().split('T')[0],
      invoice_type: '11.1', // Retail invoice
      currency: 'EUR',
      payment_type: '3', // Bank payment
    }
  });

  // Add a new invoice line
  const addLine = () => {
    setInvoiceLineItems([
      ...invoiceLineItems,
      { description: '', quantity: 1, unit_price: 0, vat_category: 1 }
    ]);
  };

  // Remove an invoice line
  const removeLine = (index: number) => {
    if (invoiceLineItems.length > 1) {
      const newLines = [...invoiceLineItems];
      newLines.splice(index, 1);
      setInvoiceLineItems(newLines);
    }
  };

  // Update an invoice line
  const updateLine = (index: number, field: string, value: string | number) => {
    const newLines = [...invoiceLineItems];
    newLines[index] = { ...newLines[index], [field]: value };
    setInvoiceLineItems(newLines);
  };

  // Calculate line totals
  const calculateLineTotals = (line: { quantity: number; unit_price: number; vat_category: number }) => {
    const netValue = line.quantity * line.unit_price;
    const vatRates = { 1: 0.24, 2: 0.13, 3: 0.06, 4: 0.17, 5: 0.09, 6: 0.04 };
    const vatAmount = netValue * (vatRates[line.vat_category as keyof typeof vatRates] || 0);
    return { netValue, vatAmount };
  };

  // Calculate invoice totals
  const calculateTotals = () => {
    let totalNet = 0;
    let totalVat = 0;

    invoiceLineItems.forEach(line => {
      const { netValue, vatAmount } = calculateLineTotals(line);
      totalNet += netValue;
      totalVat += vatAmount;
    });

    const totalGross = totalNet + totalVat;

    return {
      totalNet: totalNet.toFixed(2),
      totalVat: totalVat.toFixed(2),
      totalGross: totalGross.toFixed(2)
    };
  };

// Modified part of TestInvoiceForm.tsx
const onSubmit = async (data: {
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  invoice_type: string;
  currency: string;
  payment_type: string;
}) => {
    setIsSubmitting(true);

    try {
      // Generate a more unique invoice number with timestamp and random string
      const timestamp = new Date().getTime().toString().slice(-6);
      const randomSuffix = Math.random().toString(36).substring(2, 6);
      const uniqueInvoiceNumber = `T${timestamp}-${randomSuffix}`;

      // Override the invoice number with our generated one
      data.invoice_number = uniqueInvoiceNumber;

      const totals = calculateTotals();

      console.log('Creating invoice with data:', {
        client_name: data.client_name,
        client_vat: data.client_vat,
        client_country: data.client_country,
        invoice_series: data.invoice_series,
        invoice_number: data.invoice_number, // Using our unique number
        issue_date: data.issue_date,
        invoice_type: data.invoice_type,
        currency: data.currency,
        total_net: parseFloat(totals.totalNet),
        total_vat: parseFloat(totals.totalVat),
        total_gross: parseFloat(totals.totalGross),
        status: 'draft'
      });

      // Insert the invoice into the database
      const { data: newInvoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert([{
          client_name: data.client_name,
          client_vat: data.client_vat,
          client_country: data.client_country,
          invoice_series: data.invoice_series,
          invoice_number: data.invoice_number, // Using our unique number
          issue_date: data.issue_date,
          invoice_type: data.invoice_type,
          currency: data.currency,
          total_net: parseFloat(totals.totalNet),
          total_vat: parseFloat(totals.totalVat),
          total_gross: parseFloat(totals.totalGross),
          status: 'draft'
        }])
        .select()
        .single();

      if (invoiceError || !newInvoice) {
        throw new Error(invoiceError?.message || 'Failed to create invoice');
      }

      // Insert invoice lines from the state
      const linesToInsert = [];

      // Process each line from the state
      for (let i = 0; i < invoiceLineItems.length; i++) {
        const stateLine = invoiceLineItems[i];
        const { netValue, vatAmount } = calculateLineTotals(stateLine);

        linesToInsert.push({
          invoice_id: newInvoice.id,
          line_number: i + 1,
          description: stateLine.description || 'Test product',
          quantity: stateLine.quantity || 1,
          unit_price: stateLine.unit_price || 0,
          net_value: netValue,
          vat_category: stateLine.vat_category || 1,
          vat_amount: vatAmount,
          income_classification_type: 'E3_561_007',
          income_classification_category: 'category1_3'
        });
      }

      // Insert all invoice lines
      if (linesToInsert.length > 0) {
        const { error: linesError } = await supabase
          .from('invoice_lines')
          .insert(linesToInsert);

        if (linesError) {
          console.error('Error inserting invoice lines:', linesError);
          toast.error('Invoice created but failed to add lines');
        }
      } else {
        // If no lines were created (due to a bug), create at least one default line
        const testLine = {
          quantity: 1,
          unit_price: 100,
          vat_category: 1
        };
        const { netValue, vatAmount } = calculateLineTotals(testLine);

        const { error: defaultLineError } = await supabase
          .from('invoice_lines')
          .insert({
            invoice_id: newInvoice.id,
            line_number: 1,
            description: 'Test product',
            quantity: 1,
            unit_price: 100,
            net_value: netValue,
            vat_category: 1,
            vat_amount: vatAmount,
            income_classification_type: 'E3_561_007',
            income_classification_category: 'category1_3'
          });

        if (defaultLineError) {
          console.error('Error inserting default invoice line:', defaultLineError);
          toast.error('Invoice created but failed to add default line');
        }
      }

      // Add payment method
      const { error: paymentError } = await supabase
        .from('invoice_payment_methods')
        .insert({
          invoice_id: newInvoice.id,
          payment_type: parseInt(data.payment_type),
          amount: parseFloat(totals.totalGross),
          payment_info: 'Test payment'
        });

      if (paymentError) {
        console.error('Error inserting payment method:', paymentError);
        toast.error('Invoice created but failed to add payment method');
      }

      toast.success('Test invoice created successfully!');

    } catch (error) {
      console.error('Error creating test invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create test invoice');
    } finally {
      setIsSubmitting(false);
    }
  };

return (
  <Card className="w-full">
    <CardHeader>
      <CardTitle>Create Test Invoice</CardTitle>
    </CardHeader>
    <CardContent>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Client Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Client Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="client_name" className="text-sm font-medium">Client Name</label>
              <Input
                id="client_name"
                {...register('client_name', { required: 'Client name is required' })}
                className={errors.client_name ? 'border-red-500' : ''}
              />
              {errors.client_name && (
                <p className="text-red-500 text-xs">{errors.client_name.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="client_vat" className="text-sm font-medium">VAT Number</label>
              <Input
                id="client_vat"
                {...register('client_vat', { required: 'VAT number is required' })}
                className={errors.client_vat ? 'border-red-500' : ''}
              />
              {errors.client_vat && (
                <p className="text-red-500 text-xs">{errors.client_vat.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="client_country" className="text-sm font-medium">Country</label>
              <Input
                id="client_country"
                {...register('client_country', { required: 'Country is required' })}
                className={errors.client_country ? 'border-red-500' : ''}
              />
              {errors.client_country && (
                <p className="text-red-500 text-xs">{errors.client_country.message as string}</p>
              )}
            </div>
          </div>
        </div>

        {/* Invoice Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Invoice Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label htmlFor="invoice_series" className="text-sm font-medium">Series</label>
              <Input
                id="invoice_series"
                {...register('invoice_series', { required: 'Series is required' })}
                className={errors.invoice_series ? 'border-red-500' : ''}
              />
              {errors.invoice_series && (
                <p className="text-red-500 text-xs">{errors.invoice_series.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="invoice_number" className="text-sm font-medium">Number</label>
              <Input
                id="invoice_number"
                {...register('invoice_number', { required: 'Number is required' })}
                className={errors.invoice_number ? 'border-red-500' : ''}
              />
              {errors.invoice_number && (
                <p className="text-red-500 text-xs">{errors.invoice_number.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="issue_date" className="text-sm font-medium">Issue Date</label>
              <Input
                id="issue_date"
                type="date"
                {...register('issue_date', { required: 'Issue date is required' })}
                className={errors.issue_date ? 'border-red-500' : ''}
              />
              {errors.issue_date && (
                <p className="text-red-500 text-xs">{errors.issue_date.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="invoice_type" className="text-sm font-medium">Invoice Type</label>
              <Select
                defaultValue="11.1"
                onValueChange={(value) => {
                  register('invoice_type').onChange({ target: { value, name: 'invoice_type' } });
                }}
              >
                <SelectTrigger className={errors.invoice_type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select invoice type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="11.1">11.1 - Retail Receipt</SelectItem>
                  <SelectItem value="11.2">11.2 - Simplified Invoice</SelectItem>
                  <SelectItem value="1.1">1.1 - Sales Invoice</SelectItem>
                  <SelectItem value="2.1">2.1 - Credit Note</SelectItem>
                </SelectContent>
              </Select>
              {errors.invoice_type && (
                <p className="text-red-500 text-xs">{errors.invoice_type.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="currency" className="text-sm font-medium">Currency</label>
              <Input
                id="currency"
                {...register('currency', { required: 'Currency is required' })}
                className={errors.currency ? 'border-red-500' : ''}
              />
              {errors.currency && (
                <p className="text-red-500 text-xs">{errors.currency.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="payment_type" className="text-sm font-medium">Payment Method</label>
              <Select
                defaultValue="3"
                onValueChange={(value) => {
                  register('payment_type').onChange({ target: { value, name: 'payment_type' } });
                }}
              >
                <SelectTrigger className={errors.payment_type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 - Domestic Payment</SelectItem>
                  <SelectItem value="2">2 - International Payment</SelectItem>
                  <SelectItem value="3">3 - Bank Payment</SelectItem>
                  <SelectItem value="4">4 - Cash Payment</SelectItem>
                  <SelectItem value="5">5 - Check Payment</SelectItem>
                  <SelectItem value="7">7 - POS Payment</SelectItem>
                </SelectContent>
              </Select>
              {errors.payment_type && (
                <p className="text-red-500 text-xs">{errors.payment_type.message as string}</p>
              )}
            </div>
          </div>
        </div>

        {/* Invoice Lines */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Invoice Lines</h3>
            <Button type="button" variant="outline" size="sm" onClick={addLine}>
              <Plus className="h-4 w-4 mr-2" />
              Add Line
            </Button>
          </div>

          <div className="space-y-4">
            {invoiceLineItems.map((line, index) => (
              <div key={index} className="grid grid-cols-12 gap-3 items-end p-3 border rounded-md">
                <div className="col-span-12 md:col-span-4 space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Input
                    value={line.description}
                    onChange={(e) => updateLine(index, 'description', e.target.value)}
                    placeholder="Description"
                  />
                </div>

                <div className="col-span-4 md:col-span-2 space-y-2">
                  <label className="text-sm font-medium">Quantity</label>
                  <Input
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={line.quantity}
                    onChange={(e) => updateLine(index, 'quantity', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="col-span-4 md:col-span-2 space-y-2">
                  <label className="text-sm font-medium">Unit Price</label>
                  <Input
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={line.unit_price}
                    onChange={(e) => updateLine(index, 'unit_price', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="col-span-4 md:col-span-3 space-y-2">
                  <label className="text-sm font-medium">VAT Category</label>
                  <Select
                    value={line.vat_category.toString()}
                    onValueChange={(value) => updateLine(index, 'vat_category', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="VAT Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 - 24%</SelectItem>
                      <SelectItem value="2">2 - 13%</SelectItem>
                      <SelectItem value="3">3 - 6%</SelectItem>
                      <SelectItem value="4">4 - 17%</SelectItem>
                      <SelectItem value="5">5 - 9%</SelectItem>
                      <SelectItem value="6">6 - 4%</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-12 md:col-span-1 flex justify-end">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    disabled={invoiceLineItems.length <= 1}
                    onClick={() => removeLine(index)}
                  >
                    <Minus className="h-4 w-4 text-red-500" />
                  </Button>
                </div>

                {/* Display line totals */}
                <div className="col-span-12 pt-2 border-t text-sm text-right">
                  <p>
                    Net: €{calculateLineTotals(line).netValue.toFixed(2)} |
                    VAT: €{calculateLineTotals(line).vatAmount.toFixed(2)} |
                    Total: €{(calculateLineTotals(line).netValue + calculateLineTotals(line).vatAmount).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Totals */}
        <div className="border-t pt-4">
          <div className="text-right space-y-1">
            <p>Total Net: €{calculateTotals().totalNet}</p>
            <p>Total VAT: €{calculateTotals().totalVat}</p>
            <p className="font-bold">Total Gross: €{calculateTotals().totalGross}</p>
          </div>
        </div>

        {/* Submit Button */}
        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Invoice...
            </>
          ) : (
            'Create and Send Test Invoice'
          )}
        </Button>
      </form>
    </CardContent>
  </Card>
);
}