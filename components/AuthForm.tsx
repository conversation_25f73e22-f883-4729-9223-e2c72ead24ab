// components/AuthForm.tsx
'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

interface AuthFormProps {
  redirectTo?: string
}

export default function AuthForm({ redirectTo }: AuthFormProps) {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [deviceInfo, setDeviceInfo] = useState<{
    isAndroid: boolean;
    isIOS: boolean;
    isMobile: boolean;
  }>({ isAndroid: false, isIOS: false, isMobile: false })
  const supabase = createClientComponentClient()
  
  // Detect device type on component mount
  useEffect(() => {
    const ua = window.navigator.userAgent
    const isAndroid = /Android/i.test(ua)
    // Fix the TypeScript error by not using window.MSStream
    const isIOS = /iPad|iPhone|iPod/i.test(ua) 
    const isMobile = isAndroid || isIOS || /Mobi|Mobile/i.test(ua)
    
    setDeviceInfo({ isAndroid, isIOS, isMobile })
  }, [])

  const handleSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    try {
      const origin = window.location.origin
      const callbackUrl = new URL('/auth/callback', origin)
      
      // Add redirectTo to callback URL if provided
      if (redirectTo) {
        callbackUrl.searchParams.set('redirect_to', redirectTo)
      }
      
      // Add device information and timestamp to create unique flow state
      if (deviceInfo.isMobile) {
        const deviceType = deviceInfo.isAndroid ? 'android' : deviceInfo.isIOS ? 'ios' : 'mobile'
        callbackUrl.searchParams.set('device', deviceType)
        callbackUrl.searchParams.set('t', Date.now().toString())
      }
      
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: callbackUrl.toString(),
          shouldCreateUser: true,
          // Add device metadata to help with debugging
          data: {
            device_type: deviceInfo.isAndroid 
              ? 'android' 
              : deviceInfo.isIOS 
                ? 'ios' 
                : 'browser',
            timestamp: Date.now(),
            redirect_to: redirectTo || null
          }
        },
      })

      if (error) throw error

      setEmail('')
      
      if (deviceInfo.isAndroid) {
        setMessage(
          "Magic link sent! For Android devices: Open the link in Chrome or your default browser, not in your email app's built-in browser."
        )
      } else if (deviceInfo.isIOS) {
        setMessage(
          "Magic link sent! For iOS devices: Open the email in Mail app, press and hold the magic link, then choose \"Open in Safari\"."
        )
      } else {
        setMessage('Check your email for the magic link!')
      }
    } catch (error) {
      console.error('Sign in error:', error)
      setMessage(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <form onSubmit={handleSignIn} className="space-y-4">
        <input
          type="email"
          placeholder="Your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-3 py-2 border rounded-md"
          required
          disabled={loading}
        />
        <button 
          type="submit" 
          className="w-full px-3 py-2 text-white bg-blue-600 rounded-md disabled:opacity-50"
          disabled={loading}
        >
          {loading ? 'Sending...' : 'Sign In'}
        </button>
        {message && (
          <p className={`text-sm text-center ${
            message.includes('error') ? 'text-red-600' : 'text-green-600'
          }`}>
            {message}
          </p>
        )}
      </form>
      
      {/* Mobile device guidance */}
      {deviceInfo.isMobile && (
        <div className="mt-4 p-3 bg-yellow-50 rounded-md text-sm">
          <p className="font-medium">
            ⚠️ {deviceInfo.isAndroid ? 'Android' : deviceInfo.isIOS ? 'iOS' : 'Mobile'} device detected
          </p>
          
          {deviceInfo.isAndroid && (
            <ol className="mt-1 pl-5 list-decimal space-y-1">
              <li>When you receive the email, tap and <b>hold</b> on the magic link</li>
              <li>Select <b>&quot;Open in Chrome&quot;</b> or your default browser (avoid in-app browsers)</li>
              <li>If prompted, choose to <b>continue in the same browser session</b></li>
              <li>Make sure you complete the sign-in process in one browser</li>
            </ol>
          )}
          
          {deviceInfo.isIOS && (
            <ol className="mt-1 pl-5 list-decimal space-y-1">
              <li>When you receive the email, open it in the <b>Mail app</b></li>
              <li><b>Press and hold</b> the magic link (don&quot;t just tap it)</li>
              <li>Select <b>&quot;Open in Safari&quot;</b> from the menu</li>
              <li>Ensure you <b>complete</b> the sign-in process in Safari</li>
            </ol>
          )}
          
          <p className="mt-2 text-xs">
            Having trouble? Try opening the link on a desktop computer, or request a new magic link.
          </p>
        </div>
      )}
    </div>
  )
}