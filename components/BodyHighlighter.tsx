import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bodyFront, bodyBack } from '@/data/body-parts';
import type { BodyPartName, BodySide, Gender, IntensityLevel, PainType, SelectedBodyPart } from '@/data/body-parts/types';

// Define the proper interface for body part data structure
interface BodyPartData {
  slug: string;
  color: string;
  path: {
    left?: string[];
    right?: string[];
    common?: string[];
  };
}

interface BodyHighlighterProps {
  onSelectPart: (part: SelectedBodyPart) => void;
  selectedParts: SelectedBodyPart[];
  gender?: Gender;
}

const PAIN_TYPES: PainType[] = [
  'sharp',
  'dull',
  'burning',
  'tingling',
  'numbness',
  'stiffness',
  'soreness',
  'throbbing'
];

// Mapping function for anatomical names to UI-friendly names
function mapToBodyPartName(anatomicalName: string): BodyPartName {
  const mapping: { [key: string]: BodyPartName } = {
    'trapezius': 'shoulders',
    'deltoids': 'shoulders',
    'abs': 'abdomen',
    'obliques': 'abdomen',
    'tibialis': 'calves',
    'triceps': 'biceps',
    'head': 'neck',
    'adductors': 'quadriceps',
  };
  
  return (mapping[anatomicalName] || anatomicalName) as BodyPartName;
}

// Type guard function to check if a string is a valid BodyPartName
function isValidBodyPartName(part: string): part is BodyPartName {
  return [
    'neck', 'shoulders', 'chest', 'abdomen', 'biceps', 
    'forearms', 'wrists', 'hands', 'quadriceps', 
    'knees', 'calves', 'ankles', 'feet'
  ].includes(part as BodyPartName);
}

export function BodyHighlighter({ 
  onSelectPart, 
  selectedParts
}: BodyHighlighterProps) {
  const [currentSide, setCurrentSide] = useState<BodySide>('front');
  const [selectedPart, setSelectedPart] = useState<BodyPartName | null>(null);
  const [intensity, setIntensity] = useState<IntensityLevel>(1);
  const [painType, setPainType] = useState<PainType>('dull');
  const [notes, setNotes] = useState('');
  const [activeSide, setActiveSide] = useState<'left' | 'right' | undefined>(undefined);

  // Use the imported data directly, but we need to type cast it
  const bodyPartsData = (currentSide === 'front' ? bodyFront : bodyBack) as unknown as BodyPartData[];

  if (!bodyPartsData || !Array.isArray(bodyPartsData)) {
    console.error('Body parts data is not properly loaded');
    return (
      <div className="text-center text-red-500">
        Error loading body visualization
      </div>
    );
  }
  
  const handleSubmitPart = () => {
    if (!selectedPart) return;
  
    onSelectPart({
      slug: selectedPart,
      intensity,
      side: activeSide,
      painType,
      notes: notes.trim() || undefined
    });
  
    setSelectedPart(null);
    setIntensity(1);
    setPainType('dull');
    setNotes('');
    setActiveSide(undefined);
  };

  const handlePartClick = (part: string, side?: 'left' | 'right') => {
    const mappedPart = mapToBodyPartName(part);
    if (isValidBodyPartName(mappedPart)) {
      setSelectedPart(mappedPart);
      setActiveSide(side);
    } else {
      console.error(`Invalid body part name: ${part}`);
    }
  };

  const getPartColor = (part: string, side?: 'left' | 'right') => {
    const mappedPart = mapToBodyPartName(part);
    
    if (selectedPart === mappedPart && activeSide === side) {
      return '#fbbf24';
    }
    
    if (!isValidBodyPartName(mappedPart)) return '#e2e8f0';
    
    const selected = selectedParts.find(p => 
      p.slug === mappedPart && (!p.side || p.side === side)
    );
    
    if (!selected) return '#e2e8f0';
    return selected.intensity === 1 ? '#93c5fd' : '#2563eb';
  };

  const getPartTitle = (part: BodyPartName) => {
    // Convert slug to title case (e.g., "upper-back" -> "Upper Back")
    return part
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <Tabs defaultValue={currentSide} onValueChange={(value) => setCurrentSide(value as BodySide)}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="front">Front View</TabsTrigger>
              <TabsTrigger value="back">Back View</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          <svg 
            viewBox={currentSide === 'front' ? "0 0 700 1500" : "800 0 700 1500"}
            className="w-full h-auto"
            preserveAspectRatio="xMidYMid meet"
          >
            {bodyPartsData.map((part) => (
              <g key={part.slug}>
                {part.path.left && (
                  <path
                    d={part.path.left.join(' ')}
                    fill={getPartColor(part.slug, 'left')}
                    stroke="#475569"
                    strokeWidth="1"
                    className="cursor-pointer transition-colors hover:brightness-90"
                    onClick={() => handlePartClick(part.slug, 'left')}
                  />
                )}
                {part.path.right && (
                  <path
                    d={part.path.right.join(' ')}
                    fill={getPartColor(part.slug, 'right')}
                    stroke="#475569"
                    strokeWidth="1"
                    className="cursor-pointer transition-colors hover:brightness-90"
                    onClick={() => handlePartClick(part.slug, 'right')}
                  />
                )}
                {part.path.common && (
                  <path
                    d={part.path.common.join(' ')}
                    fill={getPartColor(part.slug)}
                    stroke="#475569"
                    strokeWidth="1"
                    className="cursor-pointer transition-colors hover:brightness-90"
                    onClick={() => handlePartClick(part.slug)}
                  />
                )}
              </g>
            ))}
          </svg>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Pain Details</CardTitle>
          {selectedPart && (
            <p className="text-sm text-muted-foreground">
              {getPartTitle(selectedPart)}
              {activeSide && ` (${activeSide} side)`}
            </p>
          )}
        </CardHeader>
        <CardContent>
          {selectedPart ? (
            <div className="space-y-6">
              <div>
                <Label>Pain Intensity</Label>
                <div className="flex gap-4 mt-2">
                  <Button
                    type="button"
                    variant={intensity === 1 ? "default" : "outline"}
                    onClick={() => setIntensity(1)}
                    className="flex-1"
                  >
                    Low
                  </Button>
                  <Button
                    type="button"
                    variant={intensity === 2 ? "default" : "outline"}
                    onClick={() => setIntensity(2)}
                    className="flex-1"
                  >
                    High
                  </Button>
                </div>
              </div>

              <div>
                <Label>Pain Type</Label>
                <Select value={painType} onValueChange={(val) => setPainType(val as PainType)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PAIN_TYPES.map(type => (
                      <SelectItem key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Additional Notes</Label>
                <Textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Describe when and how the pain occurs..."
                  className="mt-2"
                />
              </div>

              <Button onClick={handleSubmitPart} className="w-full">
                Add Pain Point
              </Button>
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              Click on a body part to add pain details
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}