"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface Program {
  id: string;
  name: string;
}
interface Session {
  program_id: string;
  start_time: string;
  duration: number;
  max_participants: number;
}

const SessionCreationForm: React.FC = () => {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<string>('');
  const [sessionDuration, setSessionDuration] = useState<number>(60);
  const [maxParticipants, setMaxParticipants] = useState<number>(10);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [weekFrequency, setWeekFrequency] = useState<number>(1);
  const [selectedDays, setSelectedDays] = useState<Record<string, boolean>>({});
  const [timeslots, setTimeslots] = useState<Record<string, string[]>>({});
  const [messages, setMessages] = useState<Array<{ text: string; isError: boolean }>>([]);
  const [showCopyModal, setShowCopyModal] = useState<boolean>(false);
  const [sourceDay, setSourceDay] = useState<string>('');

  const supabase = createClientComponentClient();
  const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  const fetchPrograms = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('programs').select('id, name');
      if (error) throw error;
      setPrograms(data || []);
    } catch (error) {
      addMessage('Failed to fetch programs: ' + (error instanceof Error ? error.message : 'Unknown error'), true);
    }
  }, [supabase]);

  useEffect(() => {
    fetchPrograms();
  }, [fetchPrograms]);

  const addMessage = (text: string, isError = false) => {
    setMessages(prev => [...prev, { text, isError }]);
  };

  const handleDayToggle = (day: string) => {
    setSelectedDays(prev => ({ ...prev, [day]: !prev[day] }));
    if (!timeslots[day]) {
      setTimeslots(prev => ({ ...prev, [day]: [''] }));
    }
  };

  const handleTimeChange = (day: string, index: number, value: string) => {
    setTimeslots(prev => ({
      ...prev,
      [day]: prev[day].map((time, i) => i === index ? value : time)
    }));
  };

  const addTimeSlot = (day: string) => {
    setTimeslots(prev => ({
      ...prev,
      [day]: [...prev[day], '']
    }));
  };

  const removeTimeSlot = (day: string, index: number) => {
    setTimeslots(prev => ({
      ...prev,
      [day]: prev[day].filter((_, i) => i !== index)
    }));
  };

  const handleCopy = (day: string) => {
    setSourceDay(day);
    setShowCopyModal(true);
  };

  const confirmCopy = (targetDays: string[]) => {
    targetDays.forEach(targetDay => {
      if (targetDay !== sourceDay) {
        setSelectedDays(prev => ({ ...prev, [targetDay]: true }));
        setTimeslots(prev => ({ ...prev, [targetDay]: [...timeslots[sourceDay]] }));
      }
    });
    setShowCopyModal(false);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!selectedProgram || !startDate || !endDate) {
      addMessage('Please fill all required fields.', true);
      return;
    }
  
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const sessions: Session[] = [];
  
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new Error('Invalid date format. Please use the date picker.');
      }
  
      if (start > end) {
        throw new Error('Start date must be before or equal to end date.');
      }
  
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
      for (let i = 0; i <= diffDays; i++) {
        const currentDate = new Date(start);
        currentDate.setDate(currentDate.getDate() + i);
        const dayName = weekdays[currentDate.getDay()].toLowerCase();
  
        if (selectedDays[dayName]) {
          const dayTimeslots = timeslots[dayName] || [];
  
          dayTimeslots.forEach(time => {
            if (time) {
              const [hours, minutes] = time.split(':');
              const sessionDate = new Date(currentDate);
              sessionDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
              
              if (sessionDate >= start && sessionDate <= end) {
                sessions.push({
                  program_id: selectedProgram,
                  start_time: sessionDate.toISOString(),
                  duration: sessionDuration,
                  max_participants: maxParticipants,
                });
              }
            }
          });
        }
      }
  
      if (sessions.length === 0) {
        addMessage('No sessions created. Please check your selected days and date range.', true);
        return;
      }
  
      // Insert sessions into the database
      const { error } = await supabase
        .from('sessions')
        .insert(sessions);
  
      if (error) throw error;
  
      addMessage(`Successfully created ${sessions.length} sessions.`);
      // Reset form or navigate to a different page if needed
    } catch (error) {
      console.error('Error creating sessions:', error);
      addMessage('Failed to create sessions: ' + (error instanceof Error ? error.message : 'Unknown error'), true);
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Sessions</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="program">Program</Label>
            <Select value={selectedProgram} onValueChange={setSelectedProgram}>
              <SelectTrigger>
                <SelectValue placeholder="Select a program" />
              </SelectTrigger>
              <SelectContent>
                {programs.map(program => (
                  <SelectItem key={program.id} value={program.id}>{program.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="sessionDuration">Session Duration (minutes)</Label>
            <Input
              id="sessionDuration"
              type="number"
              value={sessionDuration}
              onChange={(e) => setSessionDuration(Number(e.target.value))}
              min={1}
            />
          </div>

          <div>
            <Label htmlFor="maxParticipants">Max Participants</Label>
            <Input
              id="maxParticipants"
              type="number"
              value={maxParticipants}
              onChange={(e) => setMaxParticipants(Number(e.target.value))}
              min={1}
            />
          </div>

          <div>
            <Label htmlFor="startDate">Start Date</Label>
            <Input
              id="startDate"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="endDate">End Date</Label>
            <Input
              id="endDate"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="weekFrequency">Week Frequency</Label>
            <Input
              id="weekFrequency"
              type="number"
              value={weekFrequency}
              onChange={(e) => setWeekFrequency(Number(e.target.value))}
              min={1}
              max={52}
            />
          </div>

          <div className="space-y-2">
            <Label>Select Days</Label>
            {weekdays.map(day => (
              <div key={day} className="flex items-center space-x-2">
                <Checkbox
                  id={`day-${day.toLowerCase()}`}
                  checked={selectedDays[day.toLowerCase()]}
                  onCheckedChange={() => handleDayToggle(day.toLowerCase())}
                />
                <Label htmlFor={`day-${day.toLowerCase()}`}>{day}</Label>
                {selectedDays[day.toLowerCase()] && (
                  <Button type="button" onClick={() => handleCopy(day.toLowerCase())}>Copy</Button>
                )}
              </div>
            ))}
          </div>

          {Object.entries(timeslots).map(([day, slots]) => (
            selectedDays[day] && (
              <div key={day} className="space-y-2">
                <h3>{day.charAt(0).toUpperCase() + day.slice(1)} Time Slots</h3>
                {slots.map((slot, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      type="time"
                      value={slot}
                      onChange={(e) => handleTimeChange(day, index, e.target.value)}
                      step="60"
                    />
                    <Button type="button" onClick={() => removeTimeSlot(day, index)}>Remove</Button>
                  </div>
                ))}
                <Button type="button" onClick={() => addTimeSlot(day)}>Add Time Slot</Button>
              </div>
            )
          ))}

          <Button type="submit">Create Sessions</Button>
        </form>

        <div className="mt-4">
          {messages.map((msg, index) => (
            <div key={index} className={`p-2 ${msg.isError ? 'text-red-500' : 'text-green-500'}`}>
              {msg.text}
            </div>
          ))}
        </div>
      </CardContent>

      <Dialog open={showCopyModal} onOpenChange={setShowCopyModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Copy Time Slots</DialogTitle>
          </DialogHeader>
          <div className="space-y-2">
            {weekdays.map(day => (
              <div key={day} className="flex items-center space-x-2">
                <Checkbox
                  id={`copy-${day.toLowerCase()}`}
                  disabled={day.toLowerCase() === sourceDay}
                />
                <Label htmlFor={`copy-${day.toLowerCase()}`}>{day}</Label>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={() => confirmCopy(weekdays.filter(day => 
              (document.getElementById(`copy-${day.toLowerCase()}`) as HTMLInputElement)?.checked
            ).map(day => day.toLowerCase()))}>
              Confirm
            </Button>
            <Button variant="outline" onClick={() => setShowCopyModal(false)}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default SessionCreationForm;