import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { forwardRef, type ButtonHTMLAttributes } from 'react'
import { DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { type Database } from '@/types/supabase'

interface SignOutButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string
  inDropdown?: boolean
}

export const SignOutButton = forwardRef<HTMLButtonElement, SignOutButtonProps>(
  ({ className, inDropdown = false, ...props }, ref) => {
    const [isSigningOut, setIsSigningOut] = useState(false)
    const supabase = createClientComponentClient<Database>()

    const handleSignOut = async () => {
      if (isSigningOut) return // Prevent multiple clicks
      
      setIsSigningOut(true)
      
      try {
        // First try the API route for server-side sign out
        const response = await fetch('/auth/signout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        })

        // If server-side sign out fails, attempt client-side
        if (!response.ok) {
          const { error } = await supabase.auth.signOut()
          if (error) throw error
        }

        // Clear any client-side state/cache here if needed
        
        // Force a hard navigation to auth page
        window.location.href = '/auth'
        
      } catch (error) {
        console.error('Sign out error:', error)
        // Even if there's an error, redirect to auth page
        window.location.href = '/auth'
      } finally {
        setIsSigningOut(false)
      }
    }

    if (inDropdown) {
      return (
        <DropdownMenuItem
          onSelect={(e) => {
            e.preventDefault()
            void handleSignOut()
          }}
          disabled={isSigningOut}
        >
          {isSigningOut ? 'Signing out...' : 'Sign Out'}
        </DropdownMenuItem>
      )
    }

    return (
      <Button 
        ref={ref}
        onClick={() => void handleSignOut()}
        variant="outline"
        className={className}
        disabled={isSigningOut}
        {...props}
      >
        {isSigningOut ? 'Signing out...' : 'Sign Out'}
      </Button>
    )
  }
)

SignOutButton.displayName = 'SignOutButton'