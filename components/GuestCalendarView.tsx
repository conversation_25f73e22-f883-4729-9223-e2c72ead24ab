'use client'

import React, { useState, useEffect, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog";
import { useRouter } from 'next/navigation';
import ReactMarkdown from 'react-markdown';
import Link from 'next/link';
import type { Database } from "@/types/supabase"

// Update Session type
type Session = {
 id: string;
 start_time: string;
 program_id: string;
 programs: {
   id: string;
   name: string;
   description: string | null;
 } | null;
 program_name: string;
};

// Helper function for date handling
function getLocalDateString(date: Date): string {
  return new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
    .toISOString()
    .split('T')[0];
}


const GuestCalendarView = () => {
  const [mounted, setMounted] = useState(false);
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(() => getWeekStart(new Date()));
  const [sessions, setSessions] = useState<Session[]>([]);
  const [wods, setWods] = useState<{ [key: string]: { content: string; date: string } }>({});
  const router = useRouter();
  const cacheKey = useRef(Math.random().toString(36).slice(2, 9));
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    setMounted(true);
  }, []);


  useEffect(() => {
    return () => {
      Object.keys(sessionStorage).forEach(key => {
        if (key.includes(cacheKey.current)) {
          sessionStorage.removeItem(key);
        }
      });
    };
  }, []);

  useEffect(() => {
    async function fetchData() {
      const endDate = new Date(currentWeekStart);
      endDate.setDate(endDate.getDate() + 7);

      // Sessions
      const sessionsCache = sessionStorage.getItem(`sessions-${cacheKey.current}-${currentWeekStart}`);
      if (!sessionsCache) {
        const { data: sessionsData, error } = await supabase
          .from('sessions')
          .select(`id, start_time, program_id, programs ( id, name, description )`)
          .gte('start_time', currentWeekStart.toISOString())
          .lt('start_time', endDate.toISOString())
          .order('start_time');

        if (!error && sessionsData) {
          const formatted = sessionsData.map(s => ({...s, program_name: s.programs?.name || 'N/A'}));
          sessionStorage.setItem(`sessions-${cacheKey.current}-${currentWeekStart}`, JSON.stringify(formatted));
          setSessions(formatted);
        }
      } else {
        setSessions(JSON.parse(sessionsCache));
      }

      // WODs (similar pattern)
      const wodsCache = sessionStorage.getItem(`wods-${cacheKey.current}-${currentWeekStart}`);
      if (!wodsCache) {
        const { data: wodsData, error } = await supabase
        .from('wod')
        .select('content, date')
        .gte('date', getLocalDateString(currentWeekStart))
        .lt('date', getLocalDateString(endDate))
        .eq('is_published', true);

        if (!error && wodsData) {
          const wodsMap = wodsData.reduce((acc, wod) => ({
            ...acc,
            [wod.date]: { content: wod.content, date: wod.date }
          }), {});
          sessionStorage.setItem(`wods-${cacheKey.current}-${currentWeekStart}`, JSON.stringify(wodsMap));
          setWods(wodsMap);
        }
      } else {
        setWods(JSON.parse(wodsCache));
      }
    }

    fetchData();
  }, [currentWeekStart, supabase]);

  // Prevent hydration issues
  if (!mounted) {
    return null;
  }

  function getWeekStart(date: Date): Date {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(d.setDate(diff));
  }

  const WodPreview: React.FC<{ content: string; date: string }> = ({ content, date }) => {
    const [isOpen, setIsOpen] = useState(false);
     // Format the date in local timezone
  const formattedDate = new Date(date + 'T00:00:00').toLocaleDateString();

    return (
      <>
        <Button
          variant="outline"
          size="sm"
          className="w-full text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800"
          onClick={() => setIsOpen(true)}
        >
          View WOD
        </Button>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent>
            <DialogHeader>
            <DialogTitle>Workout of the Day - {formattedDate}</DialogTitle>
            </DialogHeader>
            <div className="mt-2">
              <ReactMarkdown className="prose max-w-none">
                {content}
              </ReactMarkdown>
            </div>
            <DialogFooter>
              <Button onClick={() => setIsOpen(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    );
  };

  function renderCalendar() {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return dayNames.map((dayName, i) => {
      const date = new Date(currentWeekStart);
      date.setDate(date.getDate() + i);

      // Get the date string in local timezone
      const dateString = getLocalDateString(date);

      const daySessions = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return getLocalDateString(sessionDate) === dateString;
      });

      return (
        <Card key={i} className="min-h-[200px]">
          <CardHeader className="p-2">
            <div className="font-bold">{dayName}</div>
            <div>{date.toLocaleDateString()}</div>
          </CardHeader>
          <CardContent className="p-2 space-y-2">
            {daySessions.map(session => (
            <div key={session.id} className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-left flex flex-col items-start bg-slate-100 "
                onClick={() => router.push('/auth?redirect_to=/user/session-book')}
              >
               <span className="text-xs font-bold">
                {new Date(session.start_time).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false // This enables 24-hour format
  })}
                </span>
                <span className="text-xs text-muted-foreground">
                  {session.program_name}
                </span>
              </Button>
            </div>
          ))}
{wods[dateString] && (
            <WodPreview
              content={wods[dateString].content}
              date={dateString}
            />
          )}
        </CardContent>
      </Card>
    );
  });
}

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Class Schedule</h1>
        <Link
          href="/auth?redirect_to=/user/sessions/calendar"
          className="text-primary hover:underline"
        >
          Sign in to book classes
        </Link>
      </div>

      <div className="flex justify-between items-center">
        <Button
          onClick={() => {
            const newDate = new Date(currentWeekStart);
            newDate.setDate(newDate.getDate() - 7);
            if (newDate >= getWeekStart(new Date())) {
              setCurrentWeekStart(newDate);
            }
          }}
          disabled={currentWeekStart <= getWeekStart(new Date())}
        >
          Previous Week
        </Button>
        <span className="text-lg font-medium">
          Week of {currentWeekStart.toLocaleDateString()}
        </span>
        <Button
          onClick={() => {
            const newDate = new Date(currentWeekStart);
            newDate.setDate(newDate.getDate() + 7);
            setCurrentWeekStart(newDate);
          }}
        >
          Next Week
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        {renderCalendar()}
      </div>
    </div>
  );
};

export default GuestCalendarView;