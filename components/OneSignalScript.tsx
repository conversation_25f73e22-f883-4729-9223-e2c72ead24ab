// components/OneSignalScript.tsx
'use client';

import Script from 'next/script';
import { useEffect, useRef } from 'react';

export default function OneSignalScript() {
  const hasLoaded = useRef(false);

  // Check if OneSignal is already available globally
  useEffect(() => {
    if (typeof window !== 'undefined' && window.OneSignal) {
      hasLoaded.current = true;
    }
  }, []);

  // If already loaded, don't render the script again
  if (hasLoaded.current) {
    return null;
  }

  return (
    <Script 
      id="onesignal-sdk"
      src="https://cdn.onesignal.com/sdks/OneSignalSDK.js"
      strategy="afterInteractive"
    />
  );
}