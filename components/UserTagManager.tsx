import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from 'react-hot-toast';

type Tag = Database['public']['Tables']['tags']['Row'];

interface UserTagManagerProps {
  userId: string;
  initialTags?: Tag[];
  onTagsChange?: (tags: Tag[]) => void;
}

const UserTagManager: React.FC<UserTagManagerProps> = ({ userId, initialTags = [], onTagsChange }) => {
  const [tags, setTags] = useState<Tag[]>(initialTags);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClientComponentClient<Database>();

  const fetchTags = useCallback(async () => {
    setIsLoading(true);
    try {
      // Fetch available tags
      const { data: allTags, error: tagsError } = await supabase.from('tags').select('*');
      if (tagsError) throw tagsError;

      // Fetch user tags from pelates_tags
      const { data: userTags, error: userTagsError } = await supabase
        .from('pelates_tags') // Use pelates_tags instead of user_tags
        .select('tags(*)')
        .eq('pelatis_id', userId); // Ensure this matches your foreign key

      if (userTagsError) throw userTagsError;

      setTags(userTags.map(tag => tag.tags));
      setAvailableTags(allTags);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast.error('Failed to fetch tags');
    } finally {
      setIsLoading(false);
    }
  }, [userId, supabase]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  const addTag = async (tag: Tag) => {
    try {
      const { error } = await supabase
        .from('pelates_tags') // Use pelates_tags instead of user_tags
        .insert({ pelatis_id: userId, tag_id: tag.id }); // Ensure this matches your foreign key

      if (error) throw error;

      setTags(prev => [...prev, tag]);
      onTagsChange?.([...tags, tag]);
      toast.success('Tag added successfully!');
    } catch (error) {
      console.error('Error adding tag:', error);
      toast.error('Failed to add tag');
    }
  };

  const removeTag = async (tagId: string) => {
    try {
      const { error } = await supabase
        .from('pelates_tags') // Use pelates_tags instead of user_tags
        .delete()
        .eq('pelatis_id', userId)
        .eq('tag_id', tagId);

      if (error) throw error;

      setTags(prev => prev.filter(tag => tag.id !== tagId));
      onTagsChange?.(tags.filter(tag => tag.id !== tagId));
      toast.success('Tag removed successfully!');
    } catch (error) {
      console.error('Error removing tag:', error);
      toast.error('Failed to remove tag');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">User Tags</h2>
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <div>
          {tags.map(tag => (
            <Badge key={tag.id} className="mr-2">
              {tag.name}
              <Button onClick={() => removeTag(tag.id)} variant="destructive" className="ml-2">Remove</Button>
            </Badge>
          ))}
          <div>
            <h3 className="text-lg mt-4">Available Tags</h3>
            {availableTags.map(tag => (
              <Button key={tag.id} onClick={() => addTag(tag)} className="mr-2">
                Add {tag.name}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserTagManager; 