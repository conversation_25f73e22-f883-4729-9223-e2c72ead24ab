'use client';

import Image from 'next/image';
import { useState } from 'react';
import { ImageIcon } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  sizes?: string;
  priority?: boolean;
  fill?: boolean;
  style?: React.CSSProperties;
}

export default function SafeImage({
  src,
  alt,
  width = 100,
  height = 100,
  className = '',
  sizes,
  priority = false,
  fill = false,
  style,
}: SafeImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fallback component for failed images
  const ImageFallback = () => (
    <div 
      className={`bg-gray-100 border border-gray-200 rounded flex items-center justify-center ${className}`}
      style={fill ? { position: 'absolute', inset: 0, ...style } : { width, height, ...style }}
    >
      <ImageIcon className="w-6 h-6 text-gray-400" />
    </div>
  );

  // Loading component
  const ImageLoading = () => (
    <div 
      className={`bg-gray-200 animate-pulse rounded ${className}`}
      style={fill ? { position: 'absolute', inset: 0, ...style } : { width, height, ...style }}
    />
  );

  // Validate src
  if (!src || typeof src !== 'string') {
    return <ImageFallback />;
  }

  // Check for valid URL
  try {
    new URL(src);
  } catch {
    console.warn(`Invalid image URL: ${src}`);
    return <ImageFallback />;
  }

  // Show fallback if image failed to load
  if (imageError) {
    return <ImageFallback />;
  }

  // Show loading state
  if (isLoading) {
    return (
      <>
        <ImageLoading />
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          className={`${className} ${isLoading ? 'hidden' : ''}`}
          sizes={sizes}
          priority={priority}
          fill={fill}
          style={style}
          onLoad={() => setIsLoading(false)}
          onError={(e) => {
            console.error(`Failed to load image: ${src}`, e);
            setImageError(true);
            setIsLoading(false);
          }}
          // Add loading optimization
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRweHw8f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrL"
        />
      </>
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={fill ? undefined : width}
      height={fill ? undefined : height}
      className={className}
      sizes={sizes}
      priority={priority}
      fill={fill}
      style={style}
      onError={(e) => {
        console.error(`Failed to load image: ${src}`, e);
        setImageError(true);
      }}
      // Add loading optimization
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRweHw8f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrL"
    />
  );
}