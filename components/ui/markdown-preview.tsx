import React, { useState, useEffect } from 'react';
import { marked } from 'marked';

interface MarkdownPreviewProps {
  content: string;
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ content }) => {
  const [htmlContent, setHtmlContent] = useState('');
  
  useEffect(() => {
    // Handle the potentially async nature of marked
    const renderMarkdown = async () => {
      try {
        const rendered = await Promise.resolve(marked(content));
        setHtmlContent(rendered);
      } catch (error) {
        console.error('Error rendering markdown:', error);
        setHtmlContent('Error rendering content');
      }
    };
    
    renderMarkdown();
  }, [content]);
  
  return (
    <div 
      className="prose max-w-none"
      dangerouslySetInnerHTML={{ __html: htmlContent }} 
    />
  );
};

export default MarkdownPreview;