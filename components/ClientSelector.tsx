import React from 'react';
import CustomMultiSelect from '@/components/CustomMultiSelect';



// Update the interface to match FullClientOption
interface FullClientOption {
  value: string;
  label: string;
  pelatis_id: string;
  session_id: string;
  subscription?: {
    days_until_expiration: number | null;
    end_date: string | null;
    subscription_status: string | null;
    program_name_display: string | null;
  };
}

interface ClientSelectorProps {
  clients: FullClientOption[];
  selectedClients: FullClientOption[];
  onChange: (selected: FullClientOption[]) => void;
}


const ClientSelector: React.FC<ClientSelectorProps> = ({ clients, selectedClients, onChange }) => {
  // Update the mapping function to preserve required fields
  const mapToSelectOption = (client: FullClientOption) => ({
    value: client.value,
    label: client.label,
    pelatis_id: client.value, // Ensure this is preserved
    session_id: client.session_id,
    subscription: client.subscription
  });

  return (
    <div className="mb-2 sm:mb-4">
      <h3 className="font-semibold mb-2 text-gray-700 text-sm sm:text-base">Select Clients:</h3>
      <CustomMultiSelect
        options={clients.map(mapToSelectOption)}
        value={selectedClients.map(mapToSelectOption)}
        onChange={(selected) => {
          // Map the selected options back to FullClientOption
          const fullSelectedClients = selected.map(option => {
            const originalClient = clients.find(c => c.value === option.value);
            if (!originalClient) {
              return {
                value: option.value,
                label: option.label,
                pelatis_id: option.value, // Use the value as pelatis_id
                session_id: selectedClients[0]?.session_id || '',
                subscription: undefined
              };
            }
            return originalClient;
          });
          onChange(fullSelectedClients);
        }}
      />
    </div>
  );
};

export default ClientSelector;