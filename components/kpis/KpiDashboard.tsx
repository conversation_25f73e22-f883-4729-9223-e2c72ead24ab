import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from 'date-fns';
import { useKpiData } from './useKpiData';
import { BusinessKpis } from './BusinessKpis';
import { MemberKpis } from './MemberKpis';
import { OperationalKpis } from './OperationalKpis';
import { KpiTrendChart } from './KpiTrendChart';
import { KpiDistributionChart } from './KpiDistributionChart';

export function KpiDashboard() {
  const [selectedMonth, setSelectedMonth] = useState<'current' | 'previous'>('current');
  const [activeTab, setActiveTab] = useState('business');

  const { metricsData, monthlyData, isLoading } = useKpiData(selectedMonth);

  // Sample data for distribution chart
  const membershipDistribution = [
    { name: 'Monthly', value: 45, color: '#3b82f6' },
    { name: 'Quarterly', value: 30, color: '#10b981' },
    { name: 'Annual', value: 15, color: '#f59e0b' },
    { name: 'Pay-as-you-go', value: 10, color: '#ef4444' }
  ];

  // Get current date for display
  const currentDate = new Date();
  const formattedDate = format(currentDate, 'MMMM d, yyyy');

  // Use activeTab to conditionally render content or apply styles
  const renderCharts = () => {
    if (isLoading || !monthlyData.length) return null;
    
    // Convert monthlyData to the format expected by KpiTrendChart
    const chartData = monthlyData.map(item => ({
      ...item,
      // Ensure all properties can be accessed with string indexing
      [item.month]: item.month,
      [item.monthDisplay]: item.monthDisplay,
      ['mrr']: item.mrr,
      ['totalMembers']: item.totalMembers,
      ['retentionRate']: item.retentionRate,
      ['capacityRate']: item.capacityRate,
      ['avgAttendance']: item.totalCheckins / (item.uniqueSessions || 1)
    }));
    
    switch (activeTab) {
      case 'business':
        return (
          <div className="grid gap-4 md:grid-cols-2">
            <KpiTrendChart
              title="Monthly Recurring Revenue Trend"
              data={chartData}
              dataKey="mrr"
              valuePrefix="€"
              color="#3b82f6"
            />
            <KpiTrendChart
              title="Total Members Trend"
              data={chartData}
              dataKey="totalMembers"
              color="#10b981"
            />
          </div>
        );
      case 'members':
        return (
          <div className="grid gap-4 md:grid-cols-2">
            <KpiTrendChart
              title="Average Attendance Trend"
              data={chartData}
              dataKey="avgAttendance"
              color="#8b5cf6"
            />
            <KpiDistributionChart
              title="Membership Type Distribution"
              data={membershipDistribution}
              valueSuffix="%"
            />
          </div>
        );
      case 'operations':
        return (
          <div className="grid gap-4 md:grid-cols-2">
            <KpiTrendChart
              title="Retention Rate Trend"
              data={chartData}
              dataKey="retentionRate"
              valueSuffix="%"
              color="#f59e0b"
            />
            <KpiTrendChart
              title="Class Capacity Trend"
              data={chartData}
              dataKey="capacityRate"
              valueSuffix="%"
              color="#06b6d4"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Key Performance Indicators</h1>
          <p className="text-muted-foreground">As of {formattedDate}</p>
        </div>

        <Select
          value={selectedMonth}
          onValueChange={(value: 'current' | 'previous') => setSelectedMonth(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="current">Current Month</SelectItem>
            <SelectItem value="previous">Previous Month</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="business" onValueChange={setActiveTab} value={activeTab}>
        <TabsList>
          <TabsTrigger value="business">Business Health</TabsTrigger>
          <TabsTrigger value="members">Member Metrics</TabsTrigger>
          <TabsTrigger value="operations">Operational Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="business" className="space-y-6">
          <BusinessKpis data={metricsData} isLoading={isLoading} />
          {!isLoading && monthlyData.length > 0 && renderCharts()}
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <MemberKpis data={metricsData} isLoading={isLoading} />
          {!isLoading && monthlyData.length > 0 && renderCharts()}
        </TabsContent>

        <TabsContent value="operations" className="space-y-6">
          <OperationalKpis data={metricsData} isLoading={isLoading} />
          {!isLoading && monthlyData.length > 0 && renderCharts()}
        </TabsContent>
      </Tabs>
    </div>
  );
}



