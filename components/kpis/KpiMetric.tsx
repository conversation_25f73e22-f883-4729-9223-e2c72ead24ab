import React from 'react';
import { TrendingUp, TrendingDown, LucideIcon } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

interface KpiMetricProps {
  title: string | React.ReactNode;
  value: string | number;
  previousValue?: string | number;
  change?: number;
  icon: LucideIcon;
  description: string;
  calculation: string;
  formula?: string;
  bestPractices: string[];
  currentMonth: string;
  previousMonth: string;
}

export function KpiMetric({
  title,
  value,
  previousValue,
  change,
  icon: Icon,
  description,
  calculation,
  formula,
  bestPractices,
  previousMonth
}: KpiMetricProps) {
  // Add safety checks for undefined values
  const safeValue = value !== undefined ? value : '0';
  const safePreviousValue = previousValue !== undefined ? previousValue : '0';
  const safeChange = change !== undefined ? change : 0;
  const safePreviousMonth = previousMonth || 'Previous';

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-primary/10 rounded-full">
              <Icon className="h-6 w-6 text-primary" />
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">{title}</div>
            </div>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Icon className="h-4 w-4" />
                <span className="sr-only">Info</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <Icon className="mr-2 h-5 w-5" />
                  {typeof title === 'string' ? title : 'New Clients'}
                </DialogTitle>
                <div className="pt-4">
                  <DialogDescription className="mb-4">
                    {description}
                  </DialogDescription>

                  <div className="space-y-4">
                    <div>
                      <div className="font-semibold text-sm mb-1">How it&apos;s calculated</div>
                      <div className="text-sm">{calculation}</div>
                      {formula && (
                        <div className="mt-1 p-2 bg-muted rounded text-sm font-mono">
                          {formula}
                        </div>
                      )}

                      {/* Example calculations for different KPIs */}
                      {title === "Lifetime Value" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Revenue Per Member: {parseInt(safeValue.toString().replace(/[^0-9]/g, ''), 10) / 24}€ per month</p>
                            <p>Average Membership Duration: 24 months</p>
                            <p>Lifetime Value = {parseInt(safeValue.toString().replace(/[^0-9]/g, ''), 10) / 24}€ × 24 months = {safeValue}</p>
                          </div>
                        </div>
                      )}

                      {title === "Monthly Recurring Revenue" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Active Members: 40 members</p>
                            <p>Average Monthly Fee: 6€ per member</p>
                            <p>MRR = 40 members × 6€ = 240€</p>
                            <p className="mt-2 text-xs text-muted-foreground">Note: Previous month had higher MRR (€2,845) due to seasonal promotions and annual membership renewals.</p>
                          </div>
                        </div>
                      )}

                      {title === "Revenue Per Member" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Monthly Revenue: 2,400€</p>
                            <p>Active Members: 40 members</p>
                            <p>Revenue Per Member = 2,400€ ÷ 40 members = 60€ per member</p>
                          </div>
                        </div>
                      )}

                      {title === "Retention Rate" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Members at Start of Period: 40 members</p>
                            <p>Members Who Renewed: 34 members</p>
                            <p>Retention Rate = (34 ÷ 40) × 100 = 85%</p>
                          </div>
                        </div>
                      )}

                      {title === "Churn Rate" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Members at Start of Period: 40 members</p>
                            <p>Members Who Cancelled: 6 members</p>
                            <p>Churn Rate = (6 ÷ 40) × 100 = 15%</p>
                            <p className="mt-2 text-xs">Alternative calculation: 100% - Retention Rate (85%) = 15%</p>
                          </div>
                        </div>
                      )}

                      {title === "Average Attendance" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Check-ins: 340 check-ins</p>
                            <p>Number of Sessions: 40 sessions</p>
                            <p>Average Attendance = 340 ÷ 40 = 8.5 members per session</p>
                          </div>
                        </div>
                      )}

                      {title === "Class Capacity" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Filled Slots: 300 slots</p>
                            <p>Total Available Slots: 400 slots</p>
                            <p>Class Capacity = (300 ÷ 400) × 100 = 75%</p>
                          </div>
                        </div>
                      )}

                      {title === "Customer Acquisition Cost" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Marketing Expenses: 600€</p>
                            <p>New Members Acquired: 5 members</p>
                            <p>CAC = 600€ ÷ 5 = 120€ per new member</p>
                          </div>
                        </div>
                      )}

                      {title === "Member Engagement" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Check-ins: 340 check-ins</p>
                            <p>Active Members: 40 members</p>
                            <p>Member Engagement = 340 ÷ 40 = 8.5 visits per member</p>
                          </div>
                        </div>
                      )}

                      {title === "Average Class Duration" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Class Time: 2,200 minutes</p>
                            <p>Number of Classes: 40 classes</p>
                            <p>Average Class Duration = 2,200 ÷ 40 = 55 minutes per class</p>
                          </div>
                        </div>
                      )}

                      {title === "Instructor Utilization" && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Teaching Hours: 85 hours</p>
                            <p>Available Hours: 100 hours</p>
                            <p>Instructor Utilization = (85 ÷ 100) × 100 = 85%</p>
                          </div>
                        </div>
                      )}

                      {(typeof title === 'string' && title === "New Clients") ||
                       (typeof title !== 'string' && React.isValidElement(title)) && (
                        <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                          <div className="font-semibold text-sm mb-1">Example Calculation:</div>
                          <div className="text-sm">
                            <p>Total Sign-ups in Current Period: {safeValue} members</p>
                            <p>Total Sign-ups in Previous Period: {safePreviousValue} members</p>
                            <p>Change = ({safeValue} - {safePreviousValue}) ÷ {safePreviousValue} × 100 = {safeChange}%</p>
                            <p className="mt-2 text-xs text-muted-foreground">Note: After the 20th of each month, this KPI shows the current month&apos;s data. Before the 20th, it shows the previous month&apos;s data to ensure complete reporting periods.</p>
                            <p className="mt-2 text-xs text-muted-foreground">New members are calculated based on the pelates (client) creation date, not the payment start date. Only unique members are counted per month to avoid duplicates.</p>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="font-semibold text-sm mb-1">Best practices</div>
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {bestPractices?.map((practice, i) => (
                          <li key={i}>{practice}</li>
                        )) || <li>No best practices available</li>}
                      </ul>
                    </div>
                  </div>
                </div>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>

        <div className="mt-3">
          <div className="text-3xl font-bold">{safeValue}</div>

          <div className="flex flex-col mt-1">
            <div className="text-sm text-muted-foreground">
              {safePreviousMonth}: {safePreviousValue}
            </div>

            {safeChange !== null && (
              <div className={`flex items-center text-sm mt-1 ${
                safeChange >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {safeChange >= 0 ? (
                  <>
                    <TrendingUp className="mr-1 h-4 w-4" />
                    +{safeChange.toFixed(1)}%
                  </>
                ) : (
                  <>
                    <TrendingDown className="mr-1 h-4 w-4" />
                    {safeChange.toFixed(1)}%
                  </>
                )}
                <span className="text-muted-foreground ml-1">
                  vs {safePreviousMonth}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
