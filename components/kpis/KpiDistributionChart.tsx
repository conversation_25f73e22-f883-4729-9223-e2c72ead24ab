import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  TooltipProps
} from 'recharts';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

interface KpiDistributionChartProps {
  title: string;
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  valuePrefix?: string;
  valueSuffix?: string;
  height?: number;
}

export function KpiDistributionChart({
  title,
  data,
  valuePrefix = "",
  valueSuffix = "",
  height = 300
}: KpiDistributionChartProps) {
  // Default colors if not provided in data
  const defaultColors = [
    "#3b82f6", "#10b981", "#f59e0b", "#ef4444",
    "#8b5cf6", "#ec4899", "#06b6d4", "#84cc16"
  ];

  // Format the value for display
  const formatValue = (value: number) => {
    if (valuePrefix === "€" || valuePrefix === "$") {
      return `${valuePrefix}${value.toLocaleString()}${valueSuffix}`;
    }
    return `${valuePrefix}${value}${valueSuffix}`;
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-primary">
            {formatValue(payload[0].value as number)}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom legend renderer
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderLegend = (props: any) => {
    const { payload } = props;

    if (!payload || payload.length === 0) {
      return null;
    }

    return (
      <ul className="flex flex-wrap justify-center gap-x-4 gap-y-2 text-sm mt-4">
        {payload.map((
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          entry: any, 
          index: number
        ) => (
          <li key={`item-${index}`} className="flex items-center">
            <div
              className="w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: entry.color }}
            />
            <span>{entry.value}</span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height: `${height}px` }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.color || defaultColors[index % defaultColors.length]}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={renderLegend} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}







