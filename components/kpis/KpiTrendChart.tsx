import {
  Line<PERSON>hart,
  Line,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface KpiTrendChartProps {
  title: string;
  data: Array<{
    month: string;
    monthDisplay: string;
    [key: string]: string | number;
  }>;
  dataKey: string;
  color?: string;
  valuePrefix?: string;
  valueSuffix?: string;
  height?: number;
}

export function KpiTrendChart({
  title,
  data,
  dataKey,
  color = "#3b82f6",
  valuePrefix = "",
  valueSuffix = "",
  height = 300
}: KpiTrendChartProps) {
  // Format the value for display
  const formatValue = (value: number) => {
    if (valuePrefix === "€" || valuePrefix === "$") {
      return `${valuePrefix}${value.toLocaleString()}${valueSuffix}`;
    }
    return `${valuePrefix}${value}${valueSuffix}`;
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">{label}</p>
          <p className="text-primary">
            {formatValue(payload[0].value as number)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height: `${height}px` }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis
                dataKey="monthDisplay"
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: '#e5e7eb' }}
                tickFormatter={(value) => {
                  if (value >= 1000) {
                    return `${value / 1000}k`;
                  }
                  return value;
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey={dataKey}
                stroke={color}
                strokeWidth={2}
                dot={{ r: 3, strokeWidth: 2, fill: "white" }}
                activeDot={{ r: 5, strokeWidth: 0 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
