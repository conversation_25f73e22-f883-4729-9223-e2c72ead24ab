import { DollarSign, Clock, Percent } from 'lucide-react';
import { KpiMetric } from './KpiMetric';
import type { KpiData } from './useKpiData';

interface OperationalKpisProps {
  data: KpiData;
  isLoading?: boolean;
}

export function OperationalKpis({ data, isLoading = false }: OperationalKpisProps) {
  if (isLoading) {
    return <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(4).fill(0).map((_, i) => (
        <div key={i} className="h-[140px] rounded-lg bg-muted animate-pulse" />
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiMetric
          title="Customer Acquisition Cost"
          value={data.acquisitionCost.value}
          previousValue={data.acquisitionCost.previousValue}
          change={data.acquisitionCost.change}
          icon={DollarSign}
          currentMonth={data.acquisitionCost.currentMonth}
          previousMonth={data.acquisitionCost.previousMonth}
          description="Customer Acquisition Cost (CAC) is the average amount spent to acquire a new member."
          calculation="Total marketing and sales expenses divided by the number of new members acquired."
          formula="CAC = (Marketing + Sales Expenses) / New Members"
          bestPractices={[
            "Aim for a CAC that is less than the lifetime value of a member",
            "Optimize marketing channels to reduce acquisition costs",
            "Track CAC over time to identify cost-saving opportunities",
            "Use CAC data to inform pricing and membership strategies"
          ]}
        />



        <KpiMetric
          title="Average Class Duration"
          value="55 min"
          previousValue="55 min"
          change={0}
          icon={Clock}
          currentMonth={data.mrr.currentMonth}
          previousMonth={data.mrr.previousMonth}
          description="Average Class Duration measures the typical length of fitness classes offered."
          calculation="Sum of all class durations divided by the number of classes."
          formula="Average Class Duration = Total Class Time / Number of Classes"
          bestPractices={[
            "Optimize class duration based on member feedback and attendance data",
            "Ensure classes are long enough to deliver value but not too long to discourage attendance",
            "Consider offering varied durations to accommodate different schedules",
            "Track correlation between class duration and attendance rates"
          ]}
        />

        <KpiMetric
          title="Instructor Utilization"
          value="85%"
          previousValue="82.5%"
          change={2.5}
          icon={Percent}
          currentMonth={data.mrr.currentMonth}
          previousMonth={data.mrr.previousMonth}
          description="Instructor Utilization measures how efficiently instructor time is being used."
          calculation="Hours instructors spend teaching divided by total available instructor hours."
          formula="Instructor Utilization = Teaching Hours / Available Hours × 100"
          bestPractices={[
            "Target 80-90% utilization for optimal efficiency without burnout",
            "Balance instructor schedules to avoid underutilization or overwork",
            "Use utilization data to inform hiring decisions",
            "Consider instructor popularity when scheduling classes"
          ]}
        />
      </div>
    </div>
  );
}
