import { Activity, Users, Calendar, ExternalLink } from 'lucide-react';
import { KpiMetric } from './KpiMetric';
import type { KpiData } from './useKpiData';
import Link from 'next/link';

interface MemberKpisProps {
  data: KpiData;
  isLoading?: boolean;
}

export function MemberKpis({ data, isLoading = false }: MemberKpisProps) {
  if (isLoading) {
    return <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(4).fill(0).map((_, i) => (
        <div key={i} className="h-[140px] rounded-lg bg-muted animate-pulse" />
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiMetric
          title="Average Attendance"
          value={data.avgAttendance.value}
          previousValue={data.avgAttendance.previousValue}
          change={data.avgAttendance.change}
          icon={Activity}
          currentMonth={data.avgAttendance.currentMonth}
          previousMonth={data.avgAttendance.previousMonth}
          description="Average Attendance shows the average number of members attending classes each month."
          calculation="Total number of check-ins divided by the number of unique sessions."
          formula="Average Attendance = Total Check-ins / Unique Sessions"
          bestPractices={[
            "Monitor attendance trends to identify peak times for classes",
            "Adjust class schedules based on attendance patterns",
            "Offer promotions for off-peak times to increase attendance",
            "Use attendance data to inform marketing strategies"
          ]}
        />

        <KpiMetric
          title="Class Capacity"
          value={data.classCapacity.value}
          previousValue={data.classCapacity.previousValue}
          change={data.classCapacity.change}
          icon={Users}
          currentMonth={data.classCapacity.currentMonth}
          previousMonth={data.classCapacity.previousMonth}
          description="Class Capacity is the percentage of available class slots filled each month."
          calculation="Number of filled slots divided by the total number of available slots."
          formula="Class Capacity = (Filled Slots / Total Slots) * 100"
          bestPractices={[
            "Target a class capacity of at least 70% to ensure classes are not overbooked",
            "Monitor class capacity trends to identify popular classes",
            "Adjust class sizes based on demand",
            "Use class capacity data to inform scheduling decisions"
          ]}
        />

        <KpiMetric
          title={
            <div className="flex items-center gap-2">
              <span>Member Engagement</span>
              <Link href="/admin/reports/checkins" className="text-blue-500 hover:text-blue-700">
                <ExternalLink className="h-4 w-4" />
              </Link>
            </div>
          }
          value={`${data.avgAttendance.value} visits/mo`}
          previousValue={`${data.avgAttendance.previousValue} visits/mo`}
          change={data.avgAttendance.change}
          icon={Calendar}
          currentMonth={data.avgAttendance.currentMonth}
          previousMonth={data.avgAttendance.previousMonth}
          description="Member Engagement measures how frequently members visit the gym on average."
          calculation="Total number of check-ins divided by the number of active members."
          formula="Member Engagement = Total Check-ins / Active Members"
          bestPractices={[
            "Aim for at least 8-12 visits per month per member",
            "Create engagement programs for less active members",
            "Recognize and reward highly engaged members",
            "Use engagement data to predict retention risk"
          ]}
        />

        <KpiMetric
          title={
            <div className="flex items-center gap-2">
              <span>New Clients</span>
              <Link href="/admin/reports/new-members" className="text-blue-500 hover:text-blue-700">
                <ExternalLink className="h-4 w-4" />
              </Link>
            </div>
          }
          value={data.newClients.value}
          previousValue={data.newClients.previousValue}
          change={data.newClients.change}
          icon={Users}
          currentMonth={data.newClients.currentMonth}
          previousMonth={data.newClients.previousMonth}
          description="New Clients shows the number of unique new members who joined during the selected period."
          calculation="Count of unique members based on their account creation date in the pelates table."
          formula="New Clients = Count(Unique new members by creation date)"
          bestPractices={[
            "Track acquisition sources to identify effective marketing channels",
            "Compare against acquisition costs to calculate ROI",
            "Set monthly targets based on business growth goals",
            "Analyze seasonal patterns to optimize marketing efforts"
          ]}
        />
      </div>
    </div>
  );
}
