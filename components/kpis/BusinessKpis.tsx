import { DollarSign, Users, PercentIcon } from 'lucide-react';
import { KpiMetric } from './KpiMetric';
import type { KpiData } from './useKpiData';

interface BusinessKpisProps {
  data: KpiData;
  isLoading?: boolean;
}

export function BusinessKpis({ data, isLoading = false }: BusinessKpisProps) {
  if (isLoading) {
    return <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(8).fill(0).map((_, i) => (
        <div key={i} className="h-[140px] rounded-lg bg-muted animate-pulse" />
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiMetric
          title="Monthly Recurring Revenue"
          value={data.mrr.value}
          previousValue={data.mrr.previousValue}
          change={data.mrr.change}
          icon={DollarSign}
          currentMonth={data.mrr.currentMonth}
          previousMonth={data.mrr.previousMonth}
          description="Monthly Recurring Revenue (MRR) is the predictable revenue the gym expects to receive each month from active memberships."
          calculation="Sum of all active membership fees normalized to a monthly basis."
          formula="MRR = Sum(Monthly price * Active members)"
          bestPractices={[
            "Track MRR growth month-over-month to gauge business health",
            "Analyze MRR by membership type to identify high-value programs",
            "Use MRR to forecast cash flow and make strategic decisions",
            "Target a minimum 5% annual growth rate"
          ]}
        />

        <KpiMetric
          title="Total Revenue (Current)"
          value={data.totalRevenue.value}
          previousValue={data.totalRevenue.previousValue}
          change={data.totalRevenue.change}
          icon={DollarSign}
          currentMonth={data.totalRevenue.currentMonth}
          previousMonth={data.totalRevenue.previousMonth}
          description="Total Revenue shows the actual revenue collected in the current period from all sources."
          calculation="Sum of all payments received in the current period."
          formula="Total Revenue = Sum(All payments)"
          bestPractices={[
            "Compare against MRR to identify revenue leakage or additional revenue sources",
            "Track month-over-month to identify seasonal trends",
            "Use to validate financial projections",
            "Analyze by revenue source to identify growth opportunities"
          ]}
        />

        <KpiMetric
          title="Revenue Per Member"
          value={data.revenuePerMember.value}
          previousValue={data.revenuePerMember.previousValue}
          change={data.revenuePerMember.change}
          icon={DollarSign}
          currentMonth={data.revenuePerMember.currentMonth}
          previousMonth={data.revenuePerMember.previousMonth}
          description="Revenue Per Member shows the average monthly revenue generated by each member, including all revenue sources."
          calculation="Total monthly revenue divided by the number of active members."
          formula="RPM = Total Monthly Revenue / Active Member Count"
          bestPractices={[
            "Compare against industry benchmarks (typically €75-150/month for premium fitness)",
            "Look for opportunities to increase through upselling additional services",
            "Monitor for seasonal fluctuations",
            "Use to evaluate pricing strategy effectiveness"
          ]}
        />

        <KpiMetric
          title="Active Members (Current)"
          value={data.activeMembers.value}
          previousValue={data.activeMembers.previousValue}
          change={data.activeMembers.change}
          icon={Users}
          currentMonth={data.activeMembers.currentMonth}
          previousMonth={data.activeMembers.previousMonth}
          description="Active Members shows the current number of members with active subscriptions."
          calculation="Count of unique members with active subscriptions as of today."
          formula="Active Members = Count(Unique members with active subscriptions)"
          bestPractices={[
            "Track daily to identify immediate membership trends",
            "Compare to historical averages to identify seasonal patterns",
            "Set growth targets based on capacity and business goals",
            "Segment by membership type to identify high-performing programs"
          ]}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiMetric
          title="Total Members"
          value={data.totalMembers.value}
          previousValue={data.totalMembers.previousValue}
          change={data.totalMembers.change}
          icon={Users}
          currentMonth={data.totalMembers.currentMonth}
          previousMonth={data.totalMembers.previousMonth}
          description="Total Members shows the number of members who had an active membership during the selected month."
          calculation="Count of unique members with an active membership at any point during the month."
          formula="Total Members = Count(Unique members with active memberships in month)"
          bestPractices={[
            "Track month-over-month to identify membership trends",
            "Compare to capacity to identify growth potential",
            "Set growth targets based on business goals",
            "Use to forecast revenue and resource needs"
          ]}
        />

        <KpiMetric
          title="Retention Rate"
          value={data.retentionRate.value}
          previousValue={data.retentionRate.previousValue}
          change={data.retentionRate.change}
          icon={PercentIcon}
          currentMonth={data.retentionRate.currentMonth}
          previousMonth={data.retentionRate.previousMonth}
          description="Retention Rate is the percentage of members who continue their subscription from one period to the next."
          calculation="Number of members who renew divided by the total number of members at the start of the period."
          formula="Retention Rate = (Renewals / Members at Start) * 100"
          bestPractices={[
            "Aim for a retention rate of at least 80% to ensure business sustainability",
            "Analyze reasons for churn to improve retention strategies",
            "Offer incentives for renewals to boost retention",
            "Regularly communicate with members to maintain engagement"
          ]}
        />

        <KpiMetric
          title="Churn Rate"
          value={data.churnRate.value}
          previousValue={data.churnRate.previousValue}
          change={data.churnRate.change}
          icon={PercentIcon}
          currentMonth={data.churnRate.currentMonth}
          previousMonth={data.churnRate.previousMonth}
          description="Churn Rate is the percentage of members who cancel their subscription within a given period."
          calculation="Number of members who cancel divided by the total number of members at the start of the period."
          formula="Churn Rate = (Cancellations / Members at Start) * 100"
          bestPractices={[
            "Aim for a churn rate of less than 10% to maintain a healthy membership base",
            "Identify and address the root causes of churn",
            "Offer retention programs to reduce churn",
            "Gather feedback from members to improve the gym experience"
          ]}
        />

        <KpiMetric
          title="Lifetime Value"
          value={data.revenuePerMember.value ? `${parseInt(data.revenuePerMember.value.toString().replace(/[^0-9]/g, '')) * 24}€` : '0€'}
          previousValue={data.revenuePerMember.previousValue ? `${parseInt(data.revenuePerMember.previousValue.toString().replace(/[^0-9]/g, '')) * 24}€` : '0€'}
          change={data.revenuePerMember.change}
          icon={DollarSign}
          currentMonth={data.revenuePerMember.currentMonth}
          previousMonth={data.revenuePerMember.previousMonth}
          description="Lifetime Value (LTV) estimates the total revenue a gym can expect from a member throughout their membership."
          calculation="Average monthly revenue per member multiplied by the average membership duration in months (24 months)."
          formula="LTV = Revenue Per Member × 24 months"
          bestPractices={[
            "Aim for an LTV at least 3 times higher than your CAC",
            "Increase LTV by extending membership duration and increasing revenue per member",
            "Use LTV to determine maximum sustainable acquisition costs",
            "Segment LTV by membership type to identify most valuable customer segments"
          ]}
        />
      </div>
    </div>
  );
}
