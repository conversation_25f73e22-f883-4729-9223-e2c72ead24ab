import React from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { WorkoutPreset } from '@/types/workout';

interface WorkoutPresetItemProps {
  preset: WorkoutPreset;
  onSelect: (preset: WorkoutPreset) => void;
  onDelete: (id: string) => void;
}

export const WorkoutPresetItem: React.FC<WorkoutPresetItemProps> = ({
  preset,
  onSelect,
  onDelete,
}) => {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
      <div className="flex-1">
        <h3 className="font-medium">{preset.name}</h3>
        <p className="text-sm text-gray-400">{preset.type.toUpperCase()}</p>
      </div>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onSelect(preset)}
        >
          Load
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(preset.id)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default WorkoutPresetItem;