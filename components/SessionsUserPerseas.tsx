'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SupabaseClient } from '@supabase/supabase-js';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON>itle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Check, X } from 'lucide-react';

type Session = {
  id: string;
  start_time: string;
  max_participants: number;
  duration: number;
  program_id: string;
  programs: {
    id: string;
    name: string;
    description: string | null;
  };
  bookings: {
    id: string;
  }[];
  available_slots: number;
};

type BookingResponse = {
  id: string;
  booked_session_id: string;
  pelatis_id: string;
  created_at: string;
  sessions: {
    start_time: string;
    programs: {
      name: string;
    }[];
  }[];
};

type Booking = {
  id: string;
  booked_session_id: string;
  pelatis_id: string;
  created_at: string;
  session: {
    start_time: string;
    program: {
      name: string;
    };
  };
};

const PROGRAM_ID = '7a1c070b-b5cc-453f-944d-39ba4a1004ad';  // PERSEAS program ID
const PROGRAM_NAME = 'PERSEAS';

const SessionsUserPerseas: React.FC = () => {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(getWeekStart(new Date()));
  const [sessions, setSessions] = useState<Session[]>([]);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [email, setEmail] = useState<string>('');
  const [isEmailValid, setIsEmailValid] = useState<boolean>(false);
  const [bookingSuccess, setBookingSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [userBookings, setUserBookings] = useState<Booking[]>([]);

  const supabase = createClientComponentClient<SupabaseClient>();

  const fetchSessionsForWeek = useCallback(async (startDate: Date) => {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 7);

    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          programs (
            id,
            name,
            description
          ),
          bookings (id)
        `)
        .eq('program_id', PROGRAM_ID)
        .gte('start_time', startDate.toISOString())
        .lt('start_time', endDate.toISOString())
        .order('start_time');

      if (error) throw error;

      const formattedSessions = data.map(session => ({
        ...session,
        available_slots: session.max_participants - (session.bookings?.length || 0)
      }));

      setSessions(formattedSessions);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setError(`Failed to fetch ${PROGRAM_NAME} sessions.`);
    }
  }, [supabase]);

  useEffect(() => {
    fetchSessionsForWeek(currentWeekStart);
  }, [currentWeekStart, fetchSessionsForWeek]);

  function getWeekStart(date: Date): Date {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() - d.getDay() + (d.getDay() === 0 ? -6 : 1));
    return d;
  }

  function showSessionDetails(session: Session) {
    setSelectedSession(session);
    setShowModal(true);
    setBookingSuccess(false);
    setError(null);
  }

  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  function handleEmailChange(e: React.ChangeEvent<HTMLInputElement>) {
    const newEmail = e.target.value;
    setEmail(newEmail);
    setIsEmailValid(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail));
  }

  async function fetchUserBookings() {
    if (!isEmailValid) return;

    try {
      const { data: user, error: userError } = await supabase
        .from('pelates')
        .select('id')
        .eq('email', email)
        .single();

      if (userError) {
        setError('User not found. Please book a session first.');
        return;
      }

      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          booked_session_id,
          pelatis_id,
          created_at,
          sessions:sessions!bookings_booked_session_id_fkey (
            start_time,
            programs:programs!fk_sessions_program (
              name
            )
          )
        `)
        .eq('pelatis_id', user.id);

      if (bookingsError) throw bookingsError;

      if (bookings) {
        const formattedBookings: Booking[] = (bookings as BookingResponse[]).map(booking => ({
          id: booking.id,
          booked_session_id: booking.booked_session_id,
          pelatis_id: booking.pelatis_id,
          created_at: booking.created_at,
          session: {
            start_time: booking.sessions[0]?.start_time || '',
            program: {
              name: booking.sessions[0]?.programs[0]?.name || 'Unknown Program'
            }
          }
        }));
        setUserBookings(formattedBookings);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setError('Failed to fetch bookings. Please try again.');
    }
  }

  // ... rest of your existing code remains the same including renderCalendar,
  // bookSession, and the return JSX ...
  
  function renderCalendar() {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const calendar = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
  
    for (let i = 0; i < 7; i++) {

      
      const date = new Date(currentWeekStart);
date.setDate(date.getDate() + i); 
      
      const daySessions = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate.toDateString() === date.toDateString();
      });
  
      calendar.push(
        <Card key={i} className="min-h-[100px]">
          <CardHeader className="p-2">
            <div className="font-bold">{dayNames[i]}</div>
            <div>{formatDate(date)}</div>
          </CardHeader>
          <CardContent className="p-2">
            {daySessions.map(session => {
              const sessionDate = new Date(session.start_time);
              const isDisabled = sessionDate < today || session.available_slots === 0;
              return (
                <Button
                  key={session.id}
                  variant={isDisabled ? "secondary" : "default"}
                  size="sm"
                  className="w-full mb-1 text-left justify-start md:flex md:flex-col md:h-12 md:items-center md:content-center justify-center md:space-y-1 space-x-1"
                  onClick={() => !isDisabled && showSessionDetails(session)}
                  disabled={isDisabled}
                >
                  <div className="truncate">
                    {sessionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                  <div>({session.available_slots} slots)</div>
                </Button>
              );
            })}
          </CardContent>
        </Card>
      );
    }
  
    return calendar;
  }

  async function bookSession() {
    if (!selectedSession || !isEmailValid) return;

    try {
      let userId: string;

      const { data: existingUser, error: userError } = await supabase
        .from('pelates')
        .select('id')
        .eq('email', email)
        .single();

      if (userError && userError.code === 'PGRST116') {
        const { data: newUser, error: newUserError } = await supabase
          .from('pelates')
          .insert({ email: email, client_name: email.split('@')[0] })
          .select('id')
          .single();

        if (newUserError) throw newUserError;
        userId = newUser.id;
      } else if (userError) {
        throw userError;
      } else {
        userId = existingUser.id;
      }

      if (selectedSession.available_slots <= 0) {
        setError('Session is full');
        return;
      }

      const { error: bookingError } = await supabase
        .from('bookings')
        .insert({
          booked_session_id: selectedSession.id,
          pelatis_id: userId
        });

      if (bookingError) throw bookingError;

      setBookingSuccess(true);
      setError(null);
      fetchSessionsForWeek(currentWeekStart);
    } catch (error) {
      console.error('Error creating booking:', error);
      setError('Failed to create booking. Please try again.');
    }
  }

  return (
    <div className="bg-gray-100 min-h-screen p-4 sm:p-8">
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-4">{PROGRAM_NAME} Program Schedule</h1>
        <div className="bg-white p-4 sm:p-6 w-full">
          <h2 className="text-lg sm:text-xl font-bold mb-4">How to Book a Session</h2>
          <div className="text-sm">
            <ol className="list-decimal pl-5">
              <li>Select the desired date and time from the calendar.</li>
              <li>Enter your email in the field that appears.</li>
              <li>Wait for the green confirmation icon to appear.</li>
              <li>Click the &quot;Book Session&quot; button.</li>
              <li>
                <p>You can view your bookings by going to the</p>
                <a className="text-slate-800 font-bold" href="/user/sessions/bookings">Bookings</a> menu
              </li>
            </ol>
          </div>
        </div>
        
        {error && (
          <div className="p-4 mb-4 rounded-md bg-red-100 text-red-700">
            {error}
          </div>
        )}
        
        <div className="mb-4 flex flex-col sm:flex-row justify-between items-center">
          <Button 
            onClick={() => {
              const newWeekStart = new Date(currentWeekStart);
              newWeekStart.setDate(newWeekStart.getDate() - 7);
              if (newWeekStart >= getWeekStart(new Date())) {
                setCurrentWeekStart(newWeekStart);
              }
            }} 
            className="mb-2 sm:mb-0"
            disabled={currentWeekStart <= getWeekStart(new Date())}
          >
            Previous Week
          </Button>
          <span className="text-xl font-semibold mb-2 sm:mb-0">
            Week of {formatDate(currentWeekStart)}
          </span>
          <Button onClick={() => setCurrentWeekStart(new Date(currentWeekStart.setDate(currentWeekStart.getDate() + 7)))}>
            Next Week
          </Button>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-7 gap-2">
          {renderCalendar()}
        </div>

        <div className="mt-6">
          <div className="flex items-center space-x-2">
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={handleEmailChange}
              className="flex-grow"
            />
            <span className="text-2xl">
              {email && (isEmailValid ? <Check className="text-green-500" /> : <X className="text-red-500" />)}
            </span>
          </div>
          <Button onClick={fetchUserBookings} disabled={!isEmailValid} className="mt-2">
            Fetch My Bookings
          </Button>
        </div>

        {userBookings.length > 0 && (
    <div className="mt-4">
      <h2 className="text-xl font-semibold mb-2">Your Bookings</h2>
      <ul>
        {userBookings.map((booking) => (
          <li key={booking.id} className="mb-2">
            {new Date(booking.session.start_time).toLocaleString()} - {booking.session.program.name}
          </li>
        ))}
      </ul>
    </div>
  )}
      </div>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Session Details</DialogTitle>
          </DialogHeader>
          {selectedSession && (
            <div className="py-4">
              <p>Date: {new Date(selectedSession.start_time).toLocaleDateString()}</p>
              <p>Time: {new Date(selectedSession.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
              <p>Program: {selectedSession.programs.name}</p>
              <p>Available Slots: {selectedSession.available_slots}</p>
            </div>
          )}
          {!bookingSuccess ? (
            <div className="mt-4">
              <div className="relative">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={handleEmailChange}
                  className="pr-10"
                />
                {email && (
                  <span className="absolute right-3 top-3">
                    {isEmailValid ? <Check className="text-green-500" /> : <X className="text-red-500" />}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Enter a valid email to proceed with booking.
              </p>
              <Button onClick={bookSession} disabled={!isEmailValid} className="mt-4">
                Book Session
              </Button>
            </div>
          ) : (
            <div className="mt-4 text-green-500 font-bold">
              Booking successful! Check your email for confirmation.
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setShowModal(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 

export default SessionsUserPerseas;