// components/dashboard/PeriodSelector.tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PeriodOption } from "@/types/dashboard";

interface PeriodSelectorProps {
  period: PeriodOption;
  onChange: (value: PeriodOption) => void;
}

export function PeriodSelector({ period, onChange }: PeriodSelectorProps) {
  return (
    <Select value={period} onValueChange={(value) => onChange(value as PeriodOption)}>
      <SelectTrigger className="w-40">
        <SelectValue placeholder="Select period" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="30">Last 30 days</SelectItem>
        <SelectItem value="90">Last 90 days</SelectItem>
        <SelectItem value="180">Last 180 days</SelectItem>
        <SelectItem value="365">Last 365 days</SelectItem>
      </SelectContent>
    </Select>
  );
}