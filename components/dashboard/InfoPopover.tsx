// components/dashboard/InfoPopover.tsx
import { useState } from 'react';
import { Info } from 'lucide-react';

interface InfoPopoverProps {
  title: string;
  calculation: string;
  description: string;
  importance: string;
}

export function InfoPopover({ title, calculation, description, importance }: InfoPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Generate a unique ID for accessibility
  const popoverId = `popover-${title.replace(/\s+/g, '-').toLowerCase()}`;
  
  return (
    <div className="relative">
      <button 
        onClick={() => setIsOpen(!isOpen)} 
        className="inline-flex items-center text-gray-500 hover:text-gray-700"
        aria-label={`Information about ${title}`}
        aria-expanded={isOpen}
        aria-controls={popoverId}
      >
        <Info className="h-4 w-4" />
      </button>
      
      {isOpen && (
        <div
          id={popoverId}
          role="dialog"
          aria-labelledby={`${popoverId}-title`}
          className="absolute z-50 w-72 p-4 bg-white border rounded-md shadow-lg right-0 mt-2"
        >
          <h4 id={`${popoverId}-title`} className="font-bold text-sm mb-2">{title}</h4>
          <div className="space-y-2 text-xs">
            <div>
              <span className="font-semibold">Calculation:</span> {calculation}
            </div>
            <div>
              <span className="font-semibold">Description:</span> {description}
            </div>
            <div>
              <span className="font-semibold">Business Importance:</span> {importance}
            </div>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="text-xs text-blue-600 mt-2"
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
}