// components/dashboard/KpiCard.tsx
import { LucideIcon } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// Remove the unused import
import { TrendingUp, TrendingDown } from 'lucide-react';

interface KpiCardProps {
  title: string;
  value: number | string;
  previousValue?: number;
  format?: 'number' | 'currency' | 'percent' | 'decimal';
  icon: LucideIcon;
  description?: string;
  changeThreshold?: number;
}

export function KpiCard({ 
  title, 
  value, 
  previousValue, 
  format = 'number',
  icon: Icon, 
  description,
  changeThreshold = 0.1
}: KpiCardProps) {
  // Calculate percent change if previousValue is provided
  const percentChange = previousValue !== undefined && previousValue !== 0 
    ? ((Number(value) - previousValue) / previousValue) * 100 
    : 0;
  
  const isPositive = percentChange >= 0;
  const isSignificant = Math.abs(percentChange) > changeThreshold;
  
  // Format the value based on type
  let formattedValue: string;
  if (typeof value === 'string') {
    formattedValue = value;
  } else {
    if (format === 'currency') {
      formattedValue = new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: 'EUR' 
      }).format(value);
    } else if (format === 'percent') {
      formattedValue = `${value.toFixed(1)}%`;
    } else if (format === 'decimal') {
      formattedValue = value.toFixed(1);
    } else {
      formattedValue = new Intl.NumberFormat('en-US').format(value);
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formattedValue}</div>
        {previousValue !== undefined && (
          <p className={`text-xs ${
            percentChange > changeThreshold 
              ? 'text-green-600' 
              : percentChange < -changeThreshold 
                ? 'text-red-600' 
                : 'text-muted-foreground'
            } flex items-center mt-1`}
          >
            {isSignificant && (
              isPositive 
                ? <TrendingUp className="h-3 w-3 mr-1" /> 
                : <TrendingDown className="h-3 w-3 mr-1" />
            )}
            {Math.abs(percentChange).toFixed(1)}% from previous period
          </p>
        )}
        {description && <CardDescription className="text-xs mt-2">{description}</CardDescription>}
      </CardContent>
    </Card>
  );
}
