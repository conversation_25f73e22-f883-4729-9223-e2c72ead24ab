'use client';

import React, { useState, FormEvent } from 'react';
import { CheckoutPageProps } from '@/types/payment';
import { Loader2, CreditCard, Info, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const CheckoutPage: React.FC<CheckoutPageProps> = ({
  amount,
  description,
  successUrl,
  failureUrl
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format the amount for display (converts cents to euros)
  const displayAmount = typeof amount === 'number'
    ? (amount / 100).toFixed(2)
    : '0.00';

  // Helper function to get program name and duration
  const getProgramDetails = () => {
    const parts = description.split(' - ');
    return {
      name: parts[0],
      duration: parts.length > 1 ? parts[1] : ''
    };
  };

  const programDetails = getProgramDetails();

  const handleCheckout = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate amount is a positive number
      const numericAmount = parseFloat(String(amount));
      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error('Invalid payment amount');
      }

      // Prepare request data
      const requestBody = {
        amount: numericAmount,
        description: description || 'Payment order',
        successUrl: successUrl || `${window.location.origin}/payment/successful`,
        failureUrl: failureUrl || `${window.location.origin}/payment/unsuccessful`
      };

      console.log('Initiating checkout request:', requestBody);

      // Send request to create payment order
      const response = await fetch('/api/create-payment-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create payment order');
      }

      const data = await response.json();
      
      if (data.checkoutUrl) {
        window.location.href = data.checkoutUrl;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Ολοκλήρωση Παραγγελίας</h2>
          <p className="text-gray-600 mt-2">Επιβεβαιώστε την παραγγελία σας</p>
        </div>

        <div className="space-y-6">
          {/* Order Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-4">Στοιχεία Παραγγελίας</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Πρόγραμμα</span>
                <span className="font-medium text-gray-900">{programDetails.name}</span>
              </div>
              {programDetails.duration && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Διάρκεια</span>
                  <span className="font-medium text-gray-900">{programDetails.duration}</span>
                </div>
              )}
              <div className="flex justify-between border-t border-gray-200 pt-3 mt-3">
                <span className="text-gray-600">Κόστος</span>
                <span className="text-2xl font-bold text-gray-900">
                  {new Intl.NumberFormat('el-GR', {
                    style: 'currency',
                    currency: 'EUR'
                  }).format(Number(displayAmount))}
                </span>
              </div>
            </div>
          </div>

          {/* Payment Method Info */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Τρόπος Πληρωμής
            </h3>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-700">
                Η πληρωμή θα γίνει με ασφάλεια μέσω της Viva Payments
              </p>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-gray-50 p-4 rounded-lg flex items-start">
            <Shield className="w-5 h-5 text-green-600 mr-3 mt-1" />
            <div>
              <h4 className="font-medium text-gray-900">Ασφαλείς Συναλλαγές</h4>
              <p className="text-sm text-gray-600 mt-1">
                Όλες οι συναλλαγές είναι κρυπτογραφημένες και ασφαλείς
              </p>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 p-4 rounded-lg flex items-center">
              <Info className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}

          {/* Checkout Button */}
          <Button
            onClick={handleCheckout}
            disabled={loading}
            className="w-full py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                Επεξεργασία...
              </span>
            ) : (
              'Πληρωμή'
            )}
          </Button>

          {/* Terms and Privacy */}
          <p className="text-xs text-center text-gray-500 mt-4">
            Προχωρώντας στην πληρωμή, αποδέχεστε τους{' '}
            <a href="/terms" className="text-blue-600 hover:underline">
              Όρους Χρήσης
            </a>{' '}
            και την{' '}
            <a href="/privacy" className="text-blue-600 hover:underline">
              Πολιτική Απορρήτου
            </a>
          </p>
        </div>
      </Card>
    </div>
  );
};

export default CheckoutPage;
