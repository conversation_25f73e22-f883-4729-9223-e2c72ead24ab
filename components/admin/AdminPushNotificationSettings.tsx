'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useOneSignal } from '@/hooks/useOneSignal';

export function AdminPushNotificationSettings() {
  const { isInitialized, isSubscribed, playerId, requestSubscription } = useOneSignal();
  const [loading, setLoading] = useState(false);

  const handleEnableNotifications = async () => {
    setLoading(true);
    try {
      await requestSubscription();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium mb-1">Push Notifications Status</h3>
        <p className="text-sm text-gray-500">
          {!isInitialized ? 'Initializing...' : 
           isSubscribed ? '✓ Push notifications are enabled' : 
           'Push notifications are disabled'}
        </p>
        {playerId && (
          <p className="text-xs text-gray-500 mt-1">Device ID: {playerId}</p>
        )}
      </div>

      {isInitialized && !isSubscribed && (
        <Button 
          onClick={handleEnableNotifications}
          disabled={loading}
        >
          {loading ? 'Enabling...' : 'Enable Push Notifications'}
        </Button>
      )}

      <Alert>
        <AlertDescription>
          Enable push notifications to receive instant updates about new bookings, 
          support requests, and other important administrative events.
        </AlertDescription>
      </Alert>
    </div>
  );
}
