// components/admin/NotificationSettings.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useOneSignal } from '@/contexts/OneSignalContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export function AdminNotificationSettings() {
  const { isInitialized, isSubscribed, playerId, requestSubscription, updateSubscriptionStatus } = useOneSignal();
  const [status, setStatus] = useState<string>('');

  const handleSubscribe = async () => {
    setStatus('Requesting permission...');
    try {
      await requestSubscription();
      setStatus('Subscription requested successfully');
    } catch (error) {
      console.error('Error requesting subscription:', error);
      setStatus('Failed to request subscription');
    }
  };

  const handleToggleNotifications = async (enabled: boolean) => {
    setStatus('Updating subscription...');
    try {
      await updateSubscriptionStatus(enabled);
      setStatus(`Notifications ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error updating subscription:', error);
      setStatus('Failed to update subscription');
    }
  };

  useEffect(() => {
    if (isSubscribed) {
      setStatus('Subscribed to push notifications');
    } else if (isInitialized) {
      setStatus('Not subscribed to push notifications');
    } else {
      setStatus('Initializing OneSignal...');
    }
  }, [isInitialized, isSubscribed]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Push Notification Settings</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm text-gray-500 mb-2">
            Status: <span className="font-medium">{status}</span>
          </p>
          {isSubscribed && playerId && (
            <p className="text-xs text-gray-400">Device ID: {playerId}</p>
          )}
        </div>
        
        {isInitialized && (
          <>
            {!isSubscribed ? (
              <Button onClick={handleSubscribe}>
                Enable Push Notifications
              </Button>
            ) : (
              <div className="flex items-center space-x-2">
                <Switch 
                  id="notifications-toggle"
                  checked={isSubscribed}
                  onCheckedChange={handleToggleNotifications}
                />
                <Label htmlFor="notifications-toggle">Push notifications are enabled</Label>
              </div>
            )}

            <Alert className="mt-4">
              <AlertDescription>
                Push notifications allow you to receive instant updates about new support tickets, 
                payment confirmations, and other important system events.
              </AlertDescription>
            </Alert>
          </>
        )}
      </CardContent>
    </Card>
  );
}