import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const AUTH_FLOW = `stateDiagram-v2
    [*] --> AuthForm
    
    state AuthForm {
        [*] --> Choice
        Choice --> MagicLink
        Choice --> Password
    }
    
    state MagicLink {
        [*] --> CheckUserExists
        CheckUserExists --> NewUser: No
        CheckUserExists --> ExistingUser: Yes
        
        state NewUser {
            [*] --> SendConfirmation
            SendConfirmation --> ClickConfirm
            ClickConfirm --> Callback
        }
        
        state ExistingUser {
            [*] --> SendMagicLink
            SendMagicLink --> ClickLink
            ClickLink --> Callback
        }
    }
    
    state Password {
        [*] --> CheckUser
        CheckUser --> SignUp: New User
        CheckUser --> Login: Existing
        
        state Login {
            [*] --> CheckHasPassword
            CheckHasPassword --> SignIn: Yes
            CheckHasPassword --> SetPassword: No
            SetPassword --> SendSetPasswordEmail
            SendSetPasswordEmail --> ClickSetPassword
            ClickSetPassword --> Callback
        }
        
        state SignUp {
            [*] --> CreateAccount
            CreateAccount --> Verify
            Verify --> Callback
        }
    }
    
    state Callback {
        [*] --> CheckRole
        CheckRole --> AdminView: Is Admin
        CheckRole --> UserView: Regular User
    }
    
    AdminView --> [*]
    UserView --> [*]`

const POLICY_FLOW = `stateDiagram-v2
    [*] --> Request
    
    state Request {
        [*] --> AuthCheck
        AuthCheck --> Authenticated: Valid Session
        AuthCheck --> Redirect: Invalid Session
        
        state Authenticated {
            [*] --> RoleCheck
            RoleCheck --> AdminPolicy: Admin Role
            RoleCheck --> UserPolicy: User Role
            
            state AdminPolicy {
                [*] --> FullAccess: All Operations
            }
            
            state UserPolicy {
                [*] --> CheckOwnership
                CheckOwnership --> Allow: Own Resource
                CheckOwnership --> Deny: Not Own Resource
            }
        }
    }
    
    Redirect --> [*]: To Auth Page
    Allow --> [*]: Process Request
    Deny --> [*]: 403 Error
    FullAccess --> [*]: Process Request`

const NOTIFICATION_FLOW = `stateDiagram-v2
    [*] --> Event
    
    state Event {
        [*] --> CheckinEvent
        [*] --> PaymentEvent
        [*] --> BookingEvent
    }
    
    state CheckinEvent {
        [*] --> ProcessCheckin
        ProcessCheckin --> UpdateStreak
        UpdateStreak --> CreateNotification
    }
    
    state PaymentEvent {
        [*] --> ProcessPayment
        ProcessPayment --> NotifyAdmin
        ProcessPayment --> NotifyUser
    }
    
    state BookingEvent {
        [*] --> ValidateBooking
        ValidateBooking --> ConfirmBooking
        ConfirmBooking --> NotifyParticipants
    }
    
    CreateNotification --> [*]
    NotifyAdmin --> [*]
    NotifyUser --> [*]
    NotifyParticipants --> [*]`

interface DiagramTabProps {
  content: string;
  title: string;
  description: string;
}

const DiagramTab: React.FC<DiagramTabProps> = ({ content, title, description }) => (
  <Card className="w-full h-full">
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="mermaid">
        {content}
      </div>
    </CardContent>
  </Card>
);

export default function DiagramsPage() {
  const diagrams = [
    {
      id: "auth",
      title: "Authentication Flow",
      description: "User authentication process including magic link and password flows",
      content: AUTH_FLOW
    },
    {
      id: "policies",
      title: "Authorization Policies",
      description: "Access control and policy enforcement flow",
      content: POLICY_FLOW
    },
    {
      id: "notifications",
      title: "Notification System",
      description: "Event-based notification processing flow",
      content: NOTIFICATION_FLOW
    }
  ];

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">System Diagrams</h1>
      <Tabs defaultValue="auth" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {diagrams.map((diagram) => (
            <TabsTrigger key={diagram.id} value={diagram.id}>
              {diagram.title}
            </TabsTrigger>
          ))}
        </TabsList>
        {diagrams.map((diagram) => (
          <TabsContent key={diagram.id} value={diagram.id}>
            <DiagramTab
              content={diagram.content}
              title={diagram.title}
              description={diagram.description}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}