// components/admin/CommandPalette.tsx
'use client'

import { useRouter } from 'next/navigation'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command'
import { adminNavigation } from './navigationData'

// Note: Flattened navigation items can be used for advanced search functionality if needed

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CommandPalette({ isOpen, onClose }: CommandPaletteProps) {
  const router = useRouter()

  // Navigate to selected page and close dialog
  const handleSelect = (href: string) => {
    router.push(href)
    onClose()
  }

  return (
    <CommandDialog open={isOpen} onOpenChange={onClose}>
      <CommandInput placeholder="Search admin functions..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>

        {adminNavigation.map((section) => (
          <CommandGroup key={section.id} heading={section.title}>
            {section.items.map((item) => (
              <CommandItem
                key={item.href}
                value={`${item.title} ${section.title}`}
                onSelect={() => handleSelect(item.href)}
              >
                {item.title}
              </CommandItem>
            ))}
          </CommandGroup>
        ))}
      </CommandList>
    </CommandDialog>
  )
}