// components/admin/navigationData.ts
import {
    Bar<PERSON>hart, Users, DollarSign, Settings,
    Home, Calendar, Package
  } from 'lucide-react';

  export interface NavigationItem {
    title: string;
    href: string;
  }

  export interface NavigationSection {
    id: string;
    title: string;
    icon: React.ElementType;
    items: NavigationItem[];
  }

  export const adminNavigation: NavigationSection[] = [
    {
      id: 'dashboard',
      title: 'Dashboard & Analytics',
      icon: Bar<PERSON>hart,
      items: [
        { title: 'Overview', href: '/admin/dashboard' },
        { title: 'KPI Dashboard', href: '/admin/reports/kpis' },
        { title: 'Monthly Reports', href: '/admin/reports/monthly' },
        { title: 'Check-ins', href: '/admin/reports/checkins' },
        { title: 'New Members', href: '/admin/reports/new-members' }
      ]
    },
    {
      id: 'members',
      title: 'Member Management',
      icon: Users,
      items: [
        { title: 'View Members', href: '/admin/users/view' },
        { title: 'Active Subscriptions', href: '/admin/subscriptions/active' },
        { title: 'Expiring Subscriptions', href: '/admin/subscriptions/expiring' },
        { title: 'Support Tickets', href: '/admin/support' },
        { title: 'Add Check-in', href: '/admin/check-ins' }
      ]
    },
    {
      id: 'schedule',
      title: 'Schedule & Programs',
      icon: Calendar,
      items: [
        { title: 'Session Calendar', href: '/admin/sessions/calendar' },
        { title: 'Add Session', href: '/admin/sessions/add' },
        { title: 'Daily Overview', href: '/admin/daily' },
        { title: 'WODs', href: '/admin/wods' },
        { title: 'Add WOD', href: '/admin/wods/add' },
        { title: 'Exercise Library', href: '/admin/wods/exercises' }
      ]
    },
    {
      id: 'financial',
      title: 'Financial Operations',
      icon: DollarSign,
      items: [
        { title: 'View Payments', href: '/admin/payments/view' },
        { title: 'New Payment', href: '/admin/payments/add' },
        { title: 'View Expenses', href: '/admin/expenses' }
      ]
    },
    {
      id: 'system',
      title: 'System & Settings',
      icon: Settings,
      items: [
        { title: 'Notifications', href: '/admin/notifications' },
        { title: 'Notification Dashboard', href: '/admin/notifications/dashboard' },
        { title: 'Auth Debug', href: '/admin/auth-debug' },
        { title: 'Role Check', href: '/admin/check-role' }
      ]
    },
    {
      id: 'tools',
      title: 'Additional Tools',
      icon: Package,
      items: [
        { title: 'CrossFit Timer', href: '/admin/crossfitimer' },
        { title: 'Meals Planning', href: '/meals/weekbyweek' },
        { title: 'Ergo Conversions', href: '/admin/wods/conversiontable' },
        { title: 'Merchandise', href: '/admin/merchandise' }
      ]
    }
  ];

  // Helper functions
  export function getBottomNavItems() {
    return [
      { icon: Home, label: 'Dashboard', href: '/admin/dashboard' },
      { icon: Users, label: 'Members', href: '/admin/users/view' },
      { icon: Calendar, label: 'Schedule', href: '/admin/sessions/calendar' },
      { icon: BarChart, label: 'Reports', href: '/admin/reports/kpis' }
    ];
  }