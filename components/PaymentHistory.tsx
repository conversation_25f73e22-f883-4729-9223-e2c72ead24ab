import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import type { Database } from '@/types/supabase';

// Define base payment type with relations
type Payment = Database['public']['Tables']['pliromes']['Row'] & {
  pelates?: {
    client_name: string | null;
  } | null;
  programs?: {
    name: string | null;
  } | null;
  course_durations?: {
    duration_name: string | null;
  } | null;
};

interface PaymentHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  clientPayments: Payment[];
  clientName: string;
}

export default function PaymentHistory({
  isOpen,
  onClose,
  clientPayments,
  clientName
}: PaymentHistoryProps) {
  // Sort payments by date
  const sortedPayments = [...clientPayments].sort((a, b) => {
    const dateA = new Date(a.date_money_gave || 0);
    const dateB = new Date(b.date_money_gave || 0);
    return dateB.getTime() - dateA.getTime();
  });

  // Calculate totals
  const totals = clientPayments.reduce((acc, payment) => ({
    totalPaid: acc.totalPaid + (payment.money_gave || 0),
    totalDebt: acc.totalDebt + (payment.debt || 0),
    totalPrice: acc.totalPrice + (payment.price_program || 0),
  }), {
    totalPaid: 0,
    totalDebt: 0,
    totalPrice: 0,
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Payment History - {clientName}
          </DialogTitle>
        </DialogHeader>

        {/* Summary Card */}
        <Card className="bg-gray-50">
          <CardContent className="pt-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Total Paid</p>
                <p className="text-lg font-semibold text-green-600">
                  €{totals.totalPaid.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Debt</p>
                <p className="text-lg font-semibold text-red-600">
                  €{totals.totalDebt.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Value</p>
                <p className="text-lg font-semibold">
                  €{totals.totalPrice.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <ScrollArea className="h-[400px] rounded-md border p-4">
          <div className="space-y-4">
            {sortedPayments.map((payment) => (
              <Card key={payment.id} className="relative">
                <CardContent className="pt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium">
                        {payment.programs?.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {payment.course_durations?.duration_name} • {payment.checkins_allowed} checkins
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        €{payment.money_gave?.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {payment.date_money_gave ? 
                          format(parseISO(payment.date_money_gave), 'dd/MM/yyyy') : 
                          'N/A'}
                      </p>
                    </div>
                    <div className="col-span-2 border-t pt-2 mt-2">
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Start: </span>
                          {payment.start_program ? 
                            format(parseISO(payment.start_program), 'dd/MM/yyyy') : 
                            'N/A'}
                        </div>
                        <div>
                          <span className="text-gray-500">End: </span>
                          {payment.end_date ? 
                            format(parseISO(payment.end_date), 'dd/MM/yyyy') : 
                            'N/A'}
                        </div>
                        <div>
                          <span className="text-gray-500">Method: </span>
                          {payment.way_of_payment || 'N/A'}
                        </div>
                      </div>
                      {payment.comments && (
                        <p className="text-sm text-gray-600 mt-2">
                          {payment.comments}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}