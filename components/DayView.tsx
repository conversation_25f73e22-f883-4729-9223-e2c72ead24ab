'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import ReactMarkdown from 'react-markdown';
import { User, Clock } from 'lucide-react';
// Base types
interface SupabasePelates {
  id: string;
  client_name: string | null;
  name: string | null;
  last_name: string | null;
}

interface SupabaseProgram {
  id: string;
  name: string;
  created_at: string | null;
  description: string | null;
}

interface SubscriptionInfo {
  days_until_expiration: number | null;
  end_date: string | null;
  start_date: string | null;
  subscription_status: string | null;
  program_name_display: string | null;
  total_check_ins?: number;
}

// Session and related types

// Processed types remain the same but update ProcessedSession




interface Client {
  id: string;
  client_name: string | null;
}
interface Session {
  id: string;
  start_time: string;
  duration: number;
  max_participants: number;
  program_id: string;
  created_at: string | null;
  created_by: string | null;
  programs: {  // Change from array to single object
    id: string;
    name: string;
    created_at: string | null;
    description: string | null;
  };
  check_ins: {
    id: string;
    pelatis_id: string;
    pelates: SupabasePelates;
    subscription: SubscriptionInfo;
  }[];
  bookings: {
    id: string;
    pelatis_id: string;
    pelates: {
      id: string;
      client_name: string | null;
    };
  }[];
}

interface Booking {
  id: string;
  pelatis_id: string;
  pelates: SupabasePelates;
}

interface SessionQueryResult {
  check_ins?: {
    pelatis_id: string;
    id: string;
    check_in_time: string;
    pelates: SupabasePelates;
  }[];
}

interface RawCheckIn {
  id: string;
  pelatis_id: string;
  check_in_time: string;
  pelates: SupabasePelates;
 }

 // Add to existing interfaces
interface Wod {
  id: string;
  content: string;
  date: string;
}

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export default function DayView({ date = new Date().toISOString().split('T')[0] }) {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [programs, setPrograms] = useState<SupabaseProgram[]>([]);
  const [selectedClient, setSelectedClient] = useState('');
  const [selectedSession, setSelectedSession] = useState('');
  const [messages, setMessages] = useState<{ type: 'success' | 'error'; content: string }[]>([]);
  const [formattedDate, setFormattedDate] = useState(formatDate(date));
  const [isQuickSessionOpen, setIsQuickSessionOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [wod, setWod] = useState<Wod | null>(null);
  const [isWodDialogOpen, setIsWodDialogOpen] = useState(false);

  const supabase = createClientComponentClient();

  const fetchData = useCallback(async () => {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);
  
      // Fetch sessions data
// In the fetchData function, update the Supabase query:
const { data: sessionsData, error } = await supabase
  .from('sessions')
  .select(`
    *,
    programs:program_id!inner (
      id,
      name,
      created_at,
      description
    ),
    check_ins (       
      id,
      pelatis_id,
      check_in_time,
      pelates:pelatis_id (*)
    ),
    bookings (
      id,
      pelatis_id,
      pelates:pelatis_id (*)
    )
  `)
  .gte('start_time', startOfDay.toISOString())
  .lte('start_time', endOfDay.toISOString())
  .order('start_time');
  
      if (error) {  // Changed here
        console.error('Error fetching sessions:', error);
        return;
      }
  
      if (!sessionsData) {
        setSessions([]);
        return;
      }
  
      // Get unique client IDs from check-ins
      const clientIds = Array.from(new Set(
        sessionsData.flatMap(session => 
          ((session as SessionQueryResult).check_ins || [])
            .map(checkIn => checkIn?.pelatis_id)
            .filter(Boolean)
        )
      ));

// Fetch active subscriptions
const { data: subscriptions, error: subscriptionsError } = await supabase
  .from('active_subscriptions')
  .select('*')
  .in('client_id', clientIds);

if (subscriptionsError) {
  console.error('Error fetching subscriptions:', subscriptionsError);
}
// Fetch check-in counts for each client
const checkInCounts = await Promise.all(
  clientIds.map(async (clientId) => {
    const subscription = subscriptions?.find(sub => sub.client_id === clientId);
    if (!subscription?.start_date) return { clientId, count: 0 };

    const { count, error: countError } = await supabase
      .from('check_ins')
      .select('id', { count: 'exact', head: true })
      .eq('pelatis_id', clientId)
      .gte('check_in_time', subscription.start_date);

    if (countError) {
      console.error('Error fetching check-in count:', countError);
      return { clientId, count: 0 };
    }

    return { clientId, count: count || 0 };
  })
);

      // Create subscription map
      const subsMap = (subscriptions || []).reduce<Record<string, SubscriptionInfo>>((acc, sub) => {
        if (sub.client_id) {
          const checkInInfo = checkInCounts.find(c => c.clientId === sub.client_id);
          acc[sub.client_id] = {
            days_until_expiration: sub.days_until_expiration,
            end_date: sub.end_date,
            start_date: sub.start_date,
            subscription_status: 'active',
            program_name_display: sub.program_name_display,
            total_check_ins: checkInInfo?.count || 0
          };
        }
        return acc;
      }, {});

 // Transform sessions data with proper type handling and null checks
// Line 1: Trying to transform sessionsData into ProcessedSession array
const transformedSessions = sessionsData.map(session => ({
 ...session,
 programs: session.programs || [{
   id: '',
   name: 'Unknown Program',
   created_at: null, 
   description: null
 }],
 check_ins: (session.check_ins || [] as RawCheckIn[])
 .filter(Boolean)
 .map((checkIn: RawCheckIn) => ({
   id: checkIn.id,
   pelatis_id: checkIn.pelatis_id,
   pelates: checkIn.pelates || {
     id: '',
     client_name: null,
     name: null,
     last_name: null
   },
   subscription: subsMap[checkIn.pelatis_id] || {
     days_until_expiration: null,
     end_date: null,
     start_date: null,
     subscription_status: 'inactive',
     program_name_display: null,
     total_check_ins: 0
   }
 })),
 bookings: (session.bookings || [])
 .filter(Boolean)
 .map((booking: Booking) => ({
   id: booking.id,
   pelatis_id: booking.pelatis_id,
   pelates: booking.pelates || {
     id: '',
     client_name: null,
     name: null,
     last_name: null
   }
 }))
}));

setSessions(transformedSessions);

// Fetch available clients with error handling
const { data: clientsData, error: clientsError } = await supabase
  .from('pelates')
  .select('id, client_name')
  .order('client_name');

if (clientsError) {
  console.error('Error fetching clients:', clientsError);
} else {
  setClients(clientsData || []);
}

// Fetch programs with error handling
const { data: programsData, error: programsError } = await supabase
  .from('programs')
  .select('id, name, created_at, description')
  .order('name');

if (programsError) {
  console.error('Error fetching programs:', programsError);
} else {
  setPrograms(programsData || []);
}
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }, [date, supabase]);

    // Add fetch function inside the component
const fetchWod = useCallback(async () => {
  try {
    const { data, error } = await supabase
      .from('wod')
      .select('*')
      .eq('date', date)
      .eq('is_published', true)
      .single();

    if (error) throw error;
    setWod(data);
  } catch (error) {
    console.error('Error fetching WOD:', error);
  }
}, [date, supabase]);




  useEffect(() => {
    setFormattedDate(formatDate(date));
    fetchWod();
    fetchData();
  }, [date, fetchWod , fetchData  ]);

  async function handleQuickSessionCreate() {
    if (!selectedProgram || !selectedTime) return;
    setIsSubmitting(true);
    
    try {
      const [hours, minutes] = selectedTime.split(':');
      const sessionDate = new Date(date);
      sessionDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      const { error } = await supabase
        .from('sessions')
        .insert({
          program_id: selectedProgram,
          start_time: sessionDate.toISOString(),
          duration: 60,
          max_participants: 10
        });

      if (error) throw error;

      setMessages(prev => [...prev, { type: 'success', content: 'Session created successfully' }]);
      setSelectedProgram('');
      setSelectedTime('');
      setIsQuickSessionOpen(false);
      fetchData();
    } catch (error) {
      console.error('Error creating session:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to create session' }]);
    } finally {
      setIsSubmitting(false);
    }
  }

  async function handleCheckIn() {
    if (!selectedClient || !selectedSession) {
      setMessages(prev => [...prev, { type: 'error', content: 'Please select both client and session' }]);
      return;
    }

    try {
      const { error } = await supabase
        .from('check_ins')
        .insert({
          session_id: selectedSession,
          pelatis_id: selectedClient,
          check_in_time: new Date().toISOString()
        });

      if (error) throw error;

      setMessages(prev => [...prev, { type: 'success', content: 'Check-in created successfully' }]);
      setSelectedClient('');
      setSelectedSession('');
      fetchData();
    } catch (error) {
      console.error('Error creating check-in:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to create check-in' }]);
    }
  }


  return (
    <div className="container mx-auto p-4">
    <div className="flex justify-between items-center mb-6">
      <h1 className="text-2xl font-bold">{formattedDate}</h1>
      <div className="flex gap-2">
    {wod && (
      <Button 
        variant="outline"
        onClick={() => setIsWodDialogOpen(true)}
        className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800"
      >
        View WOD
      </Button>
    )}
    <Button onClick={() => setIsQuickSessionOpen(true)}>Add Session</Button>
  </div>
      
    </div>

    {/* Messages */}
    {messages.map((msg, index) => (
      <div key={index} className={`mb-4 p-4 rounded ${
        msg.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
      }`}>
        {msg.content}
      </div>
    ))}

{wod && (
  <Dialog open={isWodDialogOpen} onOpenChange={setIsWodDialogOpen}>
    <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Workout of the Day - {formattedDate}</DialogTitle>
      </DialogHeader>
      <div className="mt-2 prose max-w-none">
        <ReactMarkdown>{wod.content}</ReactMarkdown>
      </div>
      <DialogFooter>
        <Button onClick={() => setIsWodDialogOpen(false)}>Close</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
)}

      {/* Quick Session Dialog */}
      <Dialog open={isQuickSessionOpen} onOpenChange={setIsQuickSessionOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Session for {formattedDate}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="program">Program</Label>
              <Select
                value={selectedProgram}
                onValueChange={setSelectedProgram}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a program" />
                </SelectTrigger>
                <SelectContent>
                  {programs.map(program => (
                    <SelectItem key={program.id} value={program.id}>
                      {program.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="time">Time</Label>
              <Input
                id="time"
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                step="60"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsQuickSessionOpen(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button 
              onClick={handleQuickSessionCreate} 
              disabled={!selectedProgram || !selectedTime || isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Session'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Check-in Form */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Add Check-in</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-3 gap-4">
          <div>
            <Label>Client</Label>
            <Select value={selectedClient} onValueChange={setSelectedClient}>
              <SelectTrigger>
                <SelectValue placeholder="Select client" />
              </SelectTrigger>
              <SelectContent>
                {clients.map(client => (
                  <SelectItem key={client.id} value={client.id}>
                    {client.client_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>Session</Label>
            <Select value={selectedSession} onValueChange={setSelectedSession}>
              <SelectTrigger>
                <SelectValue placeholder="Select session" />
              </SelectTrigger>
              <SelectContent>
              {sessions.map(session => (
  <SelectItem key={session.id} value={session.id}>
    {`${new Date(session.start_time).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })} - ${session.programs.name || 'Unknown Program'}`}
  </SelectItem>
))}
              </SelectContent>
            </Select>
          </div>
          <Button onClick={handleCheckIn} className="self-end">Add Check-in</Button>
        </CardContent>
      </Card>

      {/* Sessions List */}
      <div className="grid gap-6">
        {sessions.map(session => (
          <Card key={session.id}>
            <CardHeader>
            <CardTitle>
  {`${new Date(session.start_time).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })} - ${session.programs.name || 'Unknown Program'}`}
</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-green-600 mb-2">
                    Check-ins ({session.check_ins?.length || 0})
                  </h3>
                  <ul className="space-y-1">
                  {session.check_ins?.map((checkIn) => (
  <li key={checkIn.id} className="bg-green-50 p-2 rounded">
    <div className="flex flex-col">
    <div className="flex items-center gap-2">
        <a 
          href={`/admin/users/${checkIn.pelatis_id}`}
          className="font-medium hover:underline"
        >
          {checkIn.pelates.client_name || 
           `${checkIn.pelates.name || ''} ${checkIn.pelates.last_name || ''}`.trim() || 
           'Unknown'}
        </a>
        <User size={16} className="text-gray-500" />
      </div>
      {checkIn.subscription && (
        <div className="flex flex-col gap-1 text-xs text-gray-600">
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            {checkIn.subscription.subscription_status === 'active' ? (
              <span className={
                checkIn.subscription.days_until_expiration && 
                checkIn.subscription.days_until_expiration <= 7 ? 
                'text-amber-600' : ''
              }>
                Expires in {checkIn.subscription.days_until_expiration} days 
                {checkIn.subscription.program_name_display && 
                  ` | ${checkIn.subscription.program_name_display}`}
              </span>
            ) : (
              <span className="text-red-600">Inactive subscription</span>
            )}
          </div>
          {checkIn.subscription.start_date && (
            <div className="flex items-center gap-2">
              <span className="font-semibold text-indigo-600">
                Total Check-ins: {checkIn.subscription.total_check_ins}
              </span>
              <span className="text-gray-500">
                (since {new Date(checkIn.subscription.start_date).toLocaleDateString()})
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  </li>
))}
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-600 mb-2">
                    Bookings ({session.bookings?.length || 0})
                  </h3>
                  <ul className="space-y-1">
                  {session.bookings?.map((booking) => (
  <li key={booking.id} className="bg-blue-50 p-2 rounded">
    {booking.pelates.client_name || 'Unknown'}
  </li>
))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>


    </div>
  );
}