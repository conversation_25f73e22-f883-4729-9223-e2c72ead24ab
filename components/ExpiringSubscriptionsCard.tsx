'use client';
import React, { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import type { BadgeProps } from "@/components/ui/badge";
import { toast } from '@/hooks/use-toast';
import { Calendar, Mail, RefreshCw, CheckCircle, Settings } from 'lucide-react';
import EmailComposer from '@/components/EmailComposer';
import { useRouter } from 'next/navigation';

// Define what we get from Supabase (with possible null values)
interface SupabaseSubscription {
  subscription_id: string | null;
  client_id: string | null;
  name: string | null;
  last_name: string | null;
  program_name: string | null;
  program_name_display: string | null;
  start_date: string | null;
  end_date: string | null;
  days_until_expiration: number | null;
  price_program: number | null;
  subscription_status: string | null;
  pelates?: {
    email: string | null;
  } | null;
}

// Define our clean app model (no nulls)
interface ExpiringSubscription {
  subscription_id: string;
  client_id: string;
  name: string;
  last_name: string;
  email: string;
  program_name: string;
  program_name_display: string;
  start_date: string;
  end_date: string;
  days_until_expiration: number;
  price_program: number;
  subscription_status: string;
}

interface ExpiringSubscriptionsCardProps {
  // Either a specific day or a range of days
  daysRange: {
    min: number;
    max: number;
  };
  title?: string;
  showSendReminders?: boolean; // Only show send reminders for certain date ranges
  onlyActive?: boolean; // Whether to only show active subscriptions or include expired/grace period
  badgeColor?: BadgeProps['variant']; // Optional badge color for status
}

const ExpiringSubscriptionsCard: React.FC<ExpiringSubscriptionsCardProps> = ({ 
  daysRange,
  title,
  showSendReminders = true,
  onlyActive = true,
  badgeColor
}) => {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(true);
  const [expiringSubscriptions, setExpiringSubscriptions] = useState<ExpiringSubscription[]>([]);
  const [sendingReminders, setSendingReminders] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showEmailComposer, setShowEmailComposer] = useState(false);
  const [selectedClients, setSelectedClients] = useState<{email: string; client_name: string}[]>([]);
  const [selectedSubscriptions, setSelectedSubscriptions] = useState<Set<string>>(new Set());
  const router = useRouter();

  // Generate default title based on days range
  const getDefaultTitle = () => {
    if (daysRange.min === daysRange.max) {
      return `Subscriptions Expiring in ${daysRange.min} Days`;
    } 
    
    if (daysRange.min < 0 && daysRange.max < 0) {
      return `Subscriptions Expired in Last ${Math.abs(daysRange.min)} Days`;
    }
    
    if (daysRange.min < 0 && daysRange.max >= 0) {
      return `Recently Expired and Expiring Soon (${daysRange.min} to ${daysRange.max} days)`;
    }
    
    return `Subscriptions Expiring in ${daysRange.min} to ${daysRange.max} Days`;
  };

  const displayTitle = title || getDefaultTitle();

  // Function to convert from Supabase data to our clean app model
  const transformSubscriptionData = (data: SupabaseSubscription[]): ExpiringSubscription[] => {
    return data
      .filter(sub => 
        // Filter out items with missing required fields
        sub.subscription_id && 
        sub.client_id && 
        sub.days_until_expiration !== null
      )
      .map(sub => ({
        subscription_id: sub.subscription_id!,
        client_id: sub.client_id!,
        name: sub.name || 'Unknown',
        last_name: sub.last_name || 'Client',
        email: (sub.pelates?.email || '<EMAIL>').toLowerCase(),
        program_name: sub.program_name || 'Unknown Program',
        program_name_display: sub.program_name_display || sub.program_name || 'Unknown Program',
        start_date: sub.start_date || new Date().toISOString(),
        end_date: sub.end_date || new Date().toISOString(),
        days_until_expiration: sub.days_until_expiration!,
        price_program: sub.price_program || 0,
        subscription_status: sub.subscription_status || 'Unknown'
      }));
  };

  const fetchExpiringSubscriptions = async () => {
    const isRefreshing = refreshing;
    if (!isRefreshing) setLoading(true);
    setRefreshing(true);
    
    try {
      console.log(`Fetching subscriptions with days_until_expiration between ${daysRange.min} and ${daysRange.max}`);
      
      // Build the query
      let query = supabase
        .from('latest_client_subscriptions')
        .select(`
          *,
          pelates:client_id(
            email
          )
        `)
        .gte('days_until_expiration', daysRange.min)
        .lte('days_until_expiration', daysRange.max)
        .order('days_until_expiration', { ascending: true });
      
      // Add status filter if needed
      if (onlyActive) {
        query = query.eq('subscription_status', 'Active');
      }
      
      // Execute the query
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching expiring subscriptions:', error);
        throw error;
      }
      
      console.log(`Got ${data?.length || 0} subscriptions in range:`, data);
      
      // Transform to our clean model with proper TypeScript safety
      const processedData = transformSubscriptionData(data || []);
      
      setExpiringSubscriptions(processedData);
      
      if (isRefreshing) {
        toast({
          title: "Refreshed",
          description: "Subscription list updated",
          variant: "default"
        });
      }
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast({
        title: "Error",
        description: "Failed to load subscriptions",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch data when the component mounts and when daysRange changes
  useEffect(() => {
    fetchExpiringSubscriptions();
  }, [daysRange.min, daysRange.max]);

  const handleSendReminders = async (specificIds?: string[]) => {
    const idsToProcess = specificIds || Array.from(selectedSubscriptions);
    
    if (idsToProcess.length === 0) {
      toast({
        title: "Error",
        description: 'Please select at least one client',
        variant: "destructive"
      });
      return;
    }

    setSendingReminders(true);
    try {
      const reminderDay = Math.round((daysRange.min + daysRange.max) / 2);
      
      console.log(`Sending reminders with days parameter ${reminderDay} for ${idsToProcess.length} selected clients`);
      
      const response = await fetch(`/api/subscription-reminders?days=${reminderDay}&rangeMin=${daysRange.min}&rangeMax=${daysRange.max}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionIds: idsToProcess
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send reminders');
      }

      // Show confirmation toast with email details
      const selectedEmailDetails = expiringSubscriptions
        .filter(sub => idsToProcess.includes(sub.subscription_id))
        .map(sub => `${sub.name} ${sub.last_name} (${sub.email})`);

      toast({
        title: "Sending reminders to:",
        description: (
          <div className="mt-2 max-h-[200px] overflow-y-auto">
            {selectedEmailDetails.map((detail, i) => (
              <div key={i} className="text-sm">{detail}</div>
            ))}
          </div>
        ),
        duration: 5000,
      });
      
      const data = await response.json();
      console.log('Reminder sending response:', data);
      
      if (data.summary.sent > 0) {
        toast({
          title: "Success",
          description: `Successfully sent ${data.summary.sent} reminder(s)`,
          variant: "default"
        });
      } else if (data.summary.skipped > 0) {
        toast({
          title: "Info",
          description: `All ${data.summary.skipped} client(s) already received reminders`,
          variant: "default"
        });
      } else {
        toast({
          title: "Info",
          description: 'No reminders sent',
          variant: "default"
        });
      }
      
      // Refresh to update UI
      fetchExpiringSubscriptions();
      
    } catch (error) {
      console.error('Error sending reminders:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to send reminders',
        variant: "destructive"
      });
    } finally {
      setSendingReminders(false);
    }
  };

  const toggleSelectAll = () => {
    if (selectedSubscriptions.size === expiringSubscriptions.length) {
      setSelectedSubscriptions(new Set());
    } else {
      setSelectedSubscriptions(new Set(expiringSubscriptions.map(sub => sub.subscription_id)));
    }
  };

  const toggleSubscription = (subscriptionId: string) => {
    const newSelected = new Set(selectedSubscriptions);
    if (newSelected.has(subscriptionId)) {
      newSelected.delete(subscriptionId);
    } else {
      newSelected.add(subscriptionId);
    }
    setSelectedSubscriptions(newSelected);
  };

  const handleComposeEmail = () => {
    if (selectedSubscriptions.size === 0) {
      toast({
        title: "Error",
        description: 'Please select at least one client',
        variant: "destructive"
      });
      return;
    }
    
    const selectedClients = expiringSubscriptions
      .filter(sub => selectedSubscriptions.has(sub.subscription_id))
      .map(sub => ({
        email: sub.email,
        client_name: `${sub.name} ${sub.last_name}`
      }));
    
    setSelectedClients(selectedClients);
    setShowEmailComposer(true);
  };

  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return new Intl.DateTimeFormat('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    } catch (e) {
      return dateStr;
    }
  };

  const formatPrice = (price: number) => {
    return `€${price.toFixed(2)}`;
  };

  // Get appropriate status badge color
  const getStatusBadgeVariant = (
    status: string, 
    daysUntilExpiration: number
  ): BadgeProps['variant'] => {  // explicitly type the return value
    if (badgeColor) return badgeColor as BadgeProps['variant'];
    
    if (status === 'Active') {
      if (daysUntilExpiration < 0) return 'destructive'; // Expired but marked active
      if (daysUntilExpiration <= 7) return 'secondary';  // Expiring soon
      return 'default';                                  // Active with time left
    }
    
    if (status === 'In Grace Period') return 'secondary';
    if (status === 'Expired') return 'destructive';
    
    return 'outline'; // Default
  };

  // Add a new event handler function
  const handleSendRemindersClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    handleSendReminders();
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {displayTitle}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchExpiringSubscriptions}
              disabled={loading || refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleComposeEmail}
              disabled={loading || expiringSubscriptions.length === 0}
            >
              <Mail className="h-4 w-4 mr-1" />
              Compose Email
            </Button>
            {showSendReminders && (
              <>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSendRemindersClick}
                  disabled={loading || sendingReminders || selectedSubscriptions.size === 0}
                >
                  <Mail className="h-4 w-4 mr-1" />
                  {sendingReminders ? 'Sending...' : 'Send Selected Reminders'}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => router.push('/admin/email-templates')}
                >
                  <Settings className="h-4 w-4 mr-1" />
                  Email Templates
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : expiringSubscriptions.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mb-2" />
            <h3 className="text-lg font-medium">No Subscriptions Found</h3>
            <p className="text-sm text-gray-500">
              No subscriptions found within the specified timeframe.
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={selectedSubscriptions.size === expiringSubscriptions.length}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Program</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Days Left</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expiringSubscriptions.map((subscription) => (
                <TableRow key={subscription.subscription_id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedSubscriptions.has(subscription.subscription_id)}
                      onChange={() => toggleSubscription(subscription.subscription_id)}
                      className="rounded border-gray-300"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {subscription.name} {subscription.last_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {subscription.email}
                    </div>
                  </TableCell>
                  <TableCell>{subscription.program_name_display}</TableCell>
                  <TableCell>{formatDate(subscription.end_date)}</TableCell>
                  <TableCell>
                    {subscription.days_until_expiration < 0 
                      ? `${Math.abs(subscription.days_until_expiration)} days ago` 
                      : `${subscription.days_until_expiration} days`}
                  </TableCell>
                  <TableCell>{formatPrice(subscription.price_program)}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={getStatusBadgeVariant(subscription.subscription_status, subscription.days_until_expiration)}
                    >
                      {subscription.subscription_status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          handleSendReminders([subscription.subscription_id]);
                        }}
                        disabled={sendingReminders}
                      >
                        <Mail className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
      
      {showEmailComposer && (
        <EmailComposer
          selectedClients={selectedClients}
          onClose={() => setShowEmailComposer(false)}
          defaultSubject={`Your subscription is expiring soon`}
        />
      )}
    </Card>
  );
};

export default ExpiringSubscriptionsCard;
