'use client';
// components/UserActions.tsx
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { useRouter } from "next/navigation";

export default function UserActions({ 
  userId, 
  email,
  isActive, 
  onEdit 
}: { 
  userId: string;
  email: string | null;
  isActive: boolean;
  onEdit: () => void;
}) {
  const router = useRouter();

  const handleImpersonate = async () => {
    try {
      const res = await fetch('/api/admin/impersonate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, email })
      });
      
      if (!res.ok) throw new Error('Failed to start impersonation');
      
      const { redirectUrl } = await res.json();
      router.push(redirectUrl);
    } catch (error) {
      console.error('Impersonation failed:', error);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className={`mr-4 px-2 py-1 rounded ${isActive ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
      <Button onClick={onEdit}>
        <Edit className="mr-2 h-4 w-4" /> Edit
      </Button>
      <Button variant="secondary" onClick={handleImpersonate}>
        Impersonate User
      </Button>
    </div>
  );
}