'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Trash2 } from "lucide-react" // Remove Edit from import
import { ColorForm } from "./ColorForm"
import type { Color } from "@/types/merchandise"

interface ColorListProps {
  colors: Color[]
  loading: boolean
  onDelete: (id: string) => Promise<void>
  onEdit: (color: Color) => void
}

export function ColorList({ colors, loading, onDelete, onEdit }: ColorListProps) {
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    )
  }

  if (colors.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No colors defined yet.</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Color
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Hex
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Order
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {colors.map((color) => (
            <tr key={color.id}>
              <td className="px-4 py-3 whitespace-nowrap">
                <div className="flex items-center">
                  <div 
                    className="h-4 w-4 rounded-full mr-2" 
                    style={{ backgroundColor: color.color_hex }}
                  />
                  <span className="font-medium">{color.color_name}</span>
                </div>
              </td>
              <td className="px-4 py-3 whitespace-nowrap font-mono text-sm">
                {color.color_hex}
              </td>
              <td className="px-4 py-3 whitespace-nowrap">
                {color.display_order}
              </td>
              <td className="px-4 py-3 whitespace-nowrap">
                <Badge 
                  variant="default" 
                  className={color.active ? "bg-green-500 hover:bg-green-600" : ""}
                >
                  {color.active ? 'Active' : 'Inactive'}
                </Badge>
              </td>
              <td className="px-4 py-3 whitespace-nowrap">
                <div className="flex space-x-2">
                  <ColorForm 
                    initialData={color}
                    onSuccess={() => onEdit(color)}
                  />
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="text-red-600 hover:text-red-800"
                    onClick={() => onDelete(color.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
