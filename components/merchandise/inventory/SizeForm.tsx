'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  <PERSON>alog,
  DialogContent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { Plus, Loader2 } from "lucide-react"
import { SizeFormData } from "@/types/merchandise"

interface SizeFormProps {
  initialData?: SizeFormData
  onSuccess: () => void
  onCancel?: () => void
}

const defaultFormData: SizeFormData = {
  size_name: '',
  display_order: 0,
  active: true
}

export function SizeForm({ initialData, onSuccess, onCancel }: SizeFormProps) {
  const [formData, setFormData] = useState<SizeFormData>(initialData || defaultFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/merchandise/sizes', {
        method: initialData ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to save size')
      
      onSuccess()
    } catch (error) {
      console.error('Error saving size:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {initialData ? 'Edit Size' : 'Add New Size'}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{initialData ? 'Edit Size' : 'Add New Size'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="size_name">Size Name</Label>
            <Input 
              id="size_name"
              value={formData.size_name} 
              onChange={(e) => setFormData({...formData, size_name: e.target.value})}
              placeholder="e.g. S, M, L, XL"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="display_order">Display Order</Label>
            <Input 
              type="number"
              id="display_order"
              value={formData.display_order} 
              onChange={(e) => setFormData({...formData, display_order: parseInt(e.target.value) || 0})}
              placeholder="e.g. 1, 2, 3"
              required
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="size_active" 
              checked={formData.active} 
              onCheckedChange={(checked) => setFormData({...formData, active: checked})}
            />
            <Label htmlFor="size_active" className="cursor-pointer">Active</Label>
          </div>
          
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : initialData ? (
                'Update Size'
              ) : (
                'Add Size'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}