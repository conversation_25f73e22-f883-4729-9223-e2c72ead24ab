'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Trash2 } from "lucide-react"
import { StockForm } from "./StockForm"
import type { Stock, Color, Size } from "@/types/merchandise"
import { formatCurrency } from "@/lib/utils"

interface StockListProps {
  stock: Stock[]
  colors: Color[]
  sizes: Size[]
  loading: boolean
  onDelete: (id: string) => Promise<void>
  onEdit: (stock: Stock) => void
}

export function StockList({ stock, colors, sizes, loading, onDelete, onEdit }: StockListProps) {
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    )
  }

  if (stock.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No stock items found.</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Color
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Size
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Quantity
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Price
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {stock.map((item) => {
            const color = colors.find(c => c.id === item.color_id)
            const size = sizes.find(s => s.id === item.size_id)
            
            return (
              <tr key={item.id}>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    <div 
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: color?.color_hex }}
                    />
                    {color?.color_name}
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  {size?.size_name}
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <Badge 
                    variant="default"
                    className={item.quantity > 0 
                      ? "bg-green-500 hover:bg-green-600" 
                      : "bg-red-500 hover:bg-red-600"}
                  >
                    {item.quantity}
                  </Badge>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  {formatCurrency(item.price)}
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex space-x-2">
                    <StockForm 
                      initialData={item}
                      colors={colors}
                      sizes={sizes}
                      onSuccess={() => onEdit(item)}
                    />
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="text-red-600 hover:text-red-800"
                      onClick={() => onDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  )
}
