'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Edit2 } from "lucide-react"
import { toast } from 'react-hot-toast'
import type { Stock, Color, Size } from '@/types/merchandise'

interface StockFormProps {
  initialData?: Stock
  colors: Color[]
  sizes: Size[]
  onSuccess: () => void
}

export function StockForm({ initialData, colors, sizes, onSuccess }: StockFormProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<Partial<Stock>>(
    initialData || {
      color_id: '',
      size_id: '',
      quantity: 0,
      price: 0,
    }
  )

  const supabase = createClientComponentClient()

  const handleSubmit = async () => {
    setLoading(true)
    try {
      if (initialData) {
        // Update existing stock
        const { error } = await supabase
          .from('merchandise_stock')
          .update({
            color_id: formData.color_id,
            size_id: formData.size_id,
            quantity: formData.quantity,
            price: formData.price,
          })
          .eq('id', initialData.id)

        if (error) throw error
        toast.success('Stock updated successfully')
      } else {
        // Create new stock
        const { error } = await supabase
          .from('merchandise_stock')
          .insert({
            color_id: formData.color_id,
            size_id: formData.size_id,
            quantity: formData.quantity,
            price: formData.price,
          })

        if (error) throw error
        toast.success('Stock added successfully')
      }

      setOpen(false)
      onSuccess()
    } catch (error) {
      toast.error('Failed to save stock')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {initialData ? (
          <Button variant="ghost" size="sm">
            <Edit2 className="h-4 w-4" />
          </Button>
        ) : (
          <Button>
            Add New Stock
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {initialData ? 'Edit Stock' : 'Add New Stock'}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label>Color</label>
            <Select
              value={formData.color_id}
              onValueChange={(value) => 
                setFormData(prev => ({ ...prev, color_id: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a color" />
              </SelectTrigger>
              <SelectContent>
                {colors.map((color) => (
                  <SelectItem key={color.id} value={color.id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-4 w-4 rounded-full"
                        style={{ backgroundColor: color.color_hex }}
                      />
                      {color.color_name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label>Size</label>
            <Select
              value={formData.size_id}
              onValueChange={(value) => 
                setFormData(prev => ({ ...prev, size_id: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a size" />
              </SelectTrigger>
              <SelectContent>
                {sizes.map((size) => (
                  <SelectItem key={size.id} value={size.id}>
                    {size.size_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label>Quantity</label>
            <Input
              type="number"
              min="0"
              value={formData.quantity}
              onChange={(e) => 
                setFormData(prev => ({ 
                  ...prev, 
                  quantity: parseInt(e.target.value) || 0 
                }))
              }
            />
          </div>

          <div className="space-y-2">
            <label>Price</label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={(e) => 
                setFormData(prev => ({ 
                  ...prev, 
                  price: parseFloat(e.target.value) || 0 
                }))
              }
            />
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
          >
            {initialData ? 'Update' : 'Create'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
