'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Trash2, Plus, GripVertical } from "lucide-react"
import { toast } from 'react-hot-toast'
import type { Color } from '@/types/merchandise'
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult
} from '@hello-pangea/dnd'

interface ColorManagerProps {
  colors: Color[]
  onUpdate: () => void
}

export function ColorManager({ colors, onUpdate }: ColorManagerProps) {
  const [newColorName, setNewColorName] = useState('')
  const [newColorHex, setNewColorHex] = useState('#000000')
  const supabase = createClientComponentClient()

  const handleAdd = async () => {
    try {
      const { error } = await supabase
        .from('merchandise_colors')
        .insert({
          color_name: newColorName,
          color_hex: newColorHex,
          display_order: colors.length
        })

      if (error) throw error

      setNewColorName('')
      setNewColorHex('#000000')
      onUpdate()
      toast.success('Color added successfully')
    } catch (error) {
      toast.error('Failed to add color')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from('merchandise_colors')
        .delete()
        .eq('id', id)

      if (error) throw error

      onUpdate()
      toast.success('Color deleted successfully')
    } catch (error) {
      toast.error('Failed to delete color')
    }
  }

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return

    const items = Array.from(colors)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    // Update display order for all items
    try {
      await Promise.all(
        items.map((item, index) =>
          supabase
            .from('merchandise_colors')
            .update({ display_order: index })
            .eq('id', item.id)
        )
      )

      onUpdate()
    } catch (error) {
      toast.error('Failed to update order')
    }
  }

  return (
    <div className="space-y-4">
      <Card className="p-4">
        <div className="flex gap-4">
          <Input
            placeholder="Color name"
            value={newColorName}
            onChange={(e) => setNewColorName(e.target.value)}
          />
          <Input
            type="color"
            value={newColorHex}
            onChange={(e) => setNewColorHex(e.target.value)}
            className="w-20"
          />
          <Button onClick={handleAdd} disabled={!newColorName}>
            <Plus className="h-4 w-4 mr-2" />
            Add Color
          </Button>
        </div>
      </Card>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="colors">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-2"
            >
              {colors.map((color, index) => (
                <Draggable
                  key={color.id}
                  draggableId={color.id}
                  index={index}
                >
                  {(provided) => (
                    <Card
                      className="p-4 flex items-center justify-between"
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                    >
                      <div className="flex items-center gap-4">
                        <div {...provided.dragHandleProps}>
                          <GripVertical className="h-5 w-5 text-gray-400" />
                        </div>
                        <div
                          className="h-6 w-6 rounded-full"
                          style={{ backgroundColor: color.color_hex }}
                        />
                        <span>{color.color_name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(color.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </Card>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  )
}