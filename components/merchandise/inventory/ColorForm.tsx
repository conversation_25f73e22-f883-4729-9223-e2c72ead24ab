'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
  <PERSON>alogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { Plus, Loader2 } from "lucide-react"
import { ColorFormData } from "@/types/merchandise"

interface ColorFormProps {
  initialData?: ColorFormData
  onSuccess: () => void
  onCancel?: () => void
}

const defaultFormData: ColorFormData = {
  color_name: '',
  color_hex: '#000000',
  display_order: 0,
  active: true
}

export function ColorForm({ initialData, onSuccess, onCancel }: ColorFormProps) {
  const [formData, setFormData] = useState<ColorFormData>(initialData || defaultFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // API call to save/update color
      const response = await fetch('/api/merchandise/colors', {
        method: initialData ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to save color')
      
      onSuccess()
    } catch (error) {
      console.error('Error saving color:', error)
      // Add error handling/notification here
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {initialData ? 'Edit Color' : 'Add New Color'}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{initialData ? 'Edit Color' : 'Add New Color'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="color_name">Color Name</Label>
            <Input 
              id="color_name"
              value={formData.color_name} 
              onChange={(e) => setFormData({...formData, color_name: e.target.value})}
              placeholder="e.g. Red, Blue, Black, etc."
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="color_hex">Color Hex Code</Label>
            <div className="flex space-x-2 items-center">
              <Input 
                id="color_hex"
                type="color"
                value={formData.color_hex} 
                onChange={(e) => setFormData({...formData, color_hex: e.target.value})}
                className="w-16 h-10 p-1"
                required
              />
              <Input 
                value={formData.color_hex} 
                onChange={(e) => setFormData({...formData, color_hex: e.target.value})}
                placeholder="#000000"
                pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
                required
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="display_order">Display Order</Label>
            <Input 
              type="number"
              id="display_order"
              value={formData.display_order} 
              onChange={(e) => setFormData({...formData, display_order: parseInt(e.target.value) || 0})}
              placeholder="e.g. 1, 2, 3, etc."
              required
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="color_active" 
              checked={formData.active} 
              onCheckedChange={(checked) => setFormData({...formData, active: checked})}
            />
            <Label htmlFor="color_active" className="cursor-pointer">Active</Label>
          </div>
          
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : initialData ? (
                'Update Color'
              ) : (
                'Add Color'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}