'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Trash2, Plus, GripVertical } from "lucide-react"
import { toast } from 'react-hot-toast'
import type { Size } from '@/types/merchandise'
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult
} from '@hello-pangea/dnd'

interface SizeManagerProps {
  sizes: Size[]
  onUpdate: () => void
}

export function SizeManager({ sizes, onUpdate }: SizeManagerProps) {
  const [newSizeName, setNewSizeName] = useState('')
  const supabase = createClientComponentClient()

  const handleAdd = async () => {
    try {
      const { error } = await supabase
        .from('merchandise_sizes')
        .insert({
          size_name: newSizeName,
          display_order: sizes.length
        })

      if (error) throw error

      setNewSizeName('')
      onUpdate()
      toast.success('Size added successfully')
    } catch (error) {
      toast.error('Failed to add size')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from('merchandise_sizes')
        .delete()
        .eq('id', id)

      if (error) throw error

      onUpdate()
      toast.success('Size deleted successfully')
    } catch (error) {
      toast.error('Failed to delete size')
    }
  }

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return

    const items = Array.from(sizes)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    try {
      await Promise.all(
        items.map((item, index) =>
          supabase
            .from('merchandise_sizes')
            .update({ display_order: index })
            .eq('id', item.id)
        )
      )

      onUpdate()
    } catch (error) {
      toast.error('Failed to update order')
    }
  }

  return (
    <div className="space-y-4">
      <Card className="p-4">
        <div className="flex gap-4">
          <Input
            placeholder="Size name (e.g., S, M, L, XL)"
            value={newSizeName}
            onChange={(e) => setNewSizeName(e.target.value)}
          />
          <Button onClick={handleAdd} disabled={!newSizeName}>
            <Plus className="h-4 w-4 mr-2" />
            Add Size
          </Button>
        </div>
      </Card>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="sizes">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-2"
            >
              {sizes.map((size, index) => (
                <Draggable
                  key={size.id}
                  draggableId={size.id}
                  index={index}
                >
                  {(provided) => (
                    <Card
                      className="p-4 flex items-center justify-between"
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                    >
                      <div className="flex items-center gap-4">
                        <div {...provided.dragHandleProps}>
                          <GripVertical className="h-5 w-5 text-gray-400" />
                        </div>
                        <span>{size.size_name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(size.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </Card>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  )
}