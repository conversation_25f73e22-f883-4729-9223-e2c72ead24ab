import React from 'react';
import Select, { ClearIndicatorProps, StylesConfig } from 'react-select';

interface ClientOption {
  value: string;
  label: string;
}

interface CustomMultiSelectProps {
  options: ClientOption[];
  value: ClientOption[];
  onChange: (selected: ClientOption[]) => void;
}

const CustomClearText: React.FC = () => <>clear all</>;

const ClearIndicator = (props: ClearIndicatorProps<ClientOption, true>) => {
  const {
    children = <CustomClearText />,
    getStyles,
    innerProps: { ref, ...restInnerProps },
  } = props;
  return (
    <div
      {...restInnerProps}
      ref={ref}
      style={getStyles('clearIndicator', props) as React.CSSProperties}
    >
      <div style={{ padding: '0px 5px' }}>{children}</div>
    </div>
  );
};

const customStyles: StylesConfig<ClientOption, true> = {
  clearIndicator: (base, state) => ({
    ...base,
    cursor: 'pointer',
    color: state.isFocused ? 'blue' : 'black',
  }),
};

const CustomMultiSelect: React.FC<CustomMultiSelectProps> = ({ options, value, onChange }) => {
  return (
    <Select
      closeMenuOnSelect={false}
      components={{ ClearIndicator }}
      styles={customStyles}
      value={value}
      onChange={(selected) => onChange(selected as ClientOption[])}
      isMulti
      options={options}
    />
  );
};

export default CustomMultiSelect;