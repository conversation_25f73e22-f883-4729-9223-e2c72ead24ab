import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import type { Database } from '@/types/supabase';

// Define base payment type with relations
type Payment = Database['public']['Tables']['pliromes']['Row'] & {
  pelates?: {
    client_name: string | null;
  } | null;
  programs?: {
    name: string | null;
  } | null;
  course_durations?: {
    duration_name: string | null;
  } | null;
};

type CheckinsLimit = Database['public']['Enums']['checkins_limit'];

type PaymentEditableField = keyof Pick<Payment,
  | 'start_program'
  | 'date_money_gave'
  | 'end_date'
  | 'course'
  | 'course_duration_id'
  | 'checkins_allowed'
  | 'grace_period'
  | 'price_program'
  | 'money_gave'
  | 'debt'
  | 'way_of_payment'
  | 'nodebt'
  | 'comments'
>;

type PaymentFieldValue = string | number | boolean | null;

interface PaymentEditDialogProps {
  payment: Payment | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => Promise<void>;
  programs: Database['public']['Tables']['programs']['Row'][];
  courseDurations: Database['public']['Tables']['course_durations']['Row'][];
  handleEditingPaymentChange: (
    field: PaymentEditableField, 
    value: PaymentFieldValue
  ) => void;
}

export default function PaymentEditDialog({
  payment,
  isOpen,
  onClose,
  onUpdate,
  programs,
  courseDurations,
  handleEditingPaymentChange,
}: PaymentEditDialogProps) {
  if (!payment) return null;



// Helper function for handling number inputs with proper typing
const handleNumberChange = (field: PaymentEditableField, value: string) => {
  const numValue = value === '' ? null : Number(value);
  if (
    field === 'price_program' || 
    field === 'money_gave' || 
    field === 'debt' || 
    field === 'grace_period'
  ) {
    handleEditingPaymentChange(field, numValue);
  }
};

// Helper function for handling date changes with proper typing
const handleDateChange = (field: PaymentEditableField, value: string) => {
  if (
    field === 'start_program' || 
    field === 'date_money_gave' || 
    field === 'end_date'
  ) {
    const dateValue = value ? new Date(value).toISOString() : null;
    handleEditingPaymentChange(field, dateValue);
  }
};

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Edit Payment - {payment.pelates?.client_name}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-12 gap-4 py-4">
          {/* Program Information Section */}
          <div className="col-span-12 bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-4">Program Details</h3>
            <div className="grid grid-cols-3 gap-4">
              {/* Date fields */}
              <div>
                <Label htmlFor="start_program">Start Date</Label>
                <Input
  id="start_program"
  type="date"
  value={payment.start_program ? payment.start_program.split('T')[0] : ''}
  onChange={(e) => handleDateChange('start_program', e.target.value)}
/>
              </div>

              <div>
                <Label htmlFor="date_money_gave">Payment Date</Label>
                <Input
  id="date_money_gave"
  type="date"
  value={payment.date_money_gave ? payment.date_money_gave.split('T')[0] : ''}
  onChange={(e) => handleDateChange('date_money_gave', e.target.value)}
/>
              </div>

              <div>
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={payment.end_date ? payment.end_date.split('T')[0] : ''}
                  onChange={(e) => handleDateChange('end_date', e.target.value)}
                  disabled
                />
              </div>

              {/* Program Type Select */}
              <div>
                <Label htmlFor="course">Program Type</Label>
                <Select
  value={payment.course ?? undefined}
  onValueChange={(value) => handleEditingPaymentChange('course', value)}
>
                  <SelectTrigger id="course">
                    <SelectValue placeholder="Select program" />
                  </SelectTrigger>
                  <SelectContent>
                    {programs.map((program) => (
                      <SelectItem key={program.id} value={program.id}>
                        {program.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Duration Select */}
              <div>
                <Label htmlFor="course_duration">Duration</Label>
                <Select
  value={payment.course_duration_id?.toString() ?? undefined}
  onValueChange={(value) => 
    handleEditingPaymentChange('course_duration_id', parseInt(value))
  }
>
                  <SelectTrigger id="course_duration">
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    {courseDurations.map((duration) => (
                      <SelectItem key={duration.id} value={duration.id.toString()}>
                        {duration.duration_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Checkins Limit Select */}
              <div>
                <Label htmlFor="checkins_allowed">Checkins Limit</Label>
                <Select
  value={payment.checkins_allowed ?? undefined}
  onValueChange={(value: CheckinsLimit) => 
    handleEditingPaymentChange('checkins_allowed', value)
  }
>
                  <SelectTrigger id="checkins_allowed">
                    <SelectValue placeholder="Select checkins limit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="8">8 Checkins</SelectItem>
                    <SelectItem value="12">12 Checkins</SelectItem>
                    <SelectItem value="unlimited">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Grace Period */}
              <div>
                <Label htmlFor="grace_period">Grace Period (days)</Label>
                <Input
                  id="grace_period"
                  type="number"
                  value={payment.grace_period || ''}
                  onChange={(e) => handleNumberChange('grace_period', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Financial Information Section */}
          <div className="col-span-12 bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-4">Financial Information</h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="price_program">Program Price (€)</Label>
                <Input
  id="price_program"
  type="number"
  value={payment.price_program ?? ''}
  onChange={(e) => handleEditingPaymentChange(
    'price_program',
    Number(e.target.value)
  )}
/>
              </div>

              <div>
                <Label htmlFor="money_gave">Amount Paid (€)</Label>
                <Input
  id="money_gave"
  type="number"
  value={payment.money_gave ?? ''}
  onChange={(e) => handleEditingPaymentChange(
    'money_gave',
    Number(e.target.value)
  )}
/>
              </div>

              <div>
                <Label htmlFor="debt">Outstanding Debt (€)</Label>
                <Input
  id="debt"
  type="number"
  value={payment.debt ?? ''}
  onChange={(e) => handleEditingPaymentChange(
    'debt',
    Number(e.target.value)
  )}
/>
              </div>

              <div>
                <Label htmlFor="way_of_payment">Payment Method</Label>
                <Select
  value={payment.way_of_payment ?? undefined}
  onValueChange={(value: string) => 
    handleEditingPaymentChange('way_of_payment', value)
  }
>
  <SelectTrigger id="way_of_payment">
    <SelectValue placeholder="Select payment method" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="CASH">Cash</SelectItem>
    <SelectItem value="VIVA">Card (Viva)</SelectItem>
    <SelectItem value="WINBANK">Bank Transfer</SelectItem>
  </SelectContent>
</Select>
              </div>

              <div className="flex items-center space-x-2 mt-6">
              <Checkbox
  id="nodebt"
  checked={payment.nodebt ?? false}
  onCheckedChange={(checked: boolean) => 
    handleEditingPaymentChange('nodebt', checked)
  }
/>
                <Label htmlFor="nodebt">Mark as Fully Paid</Label>
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="col-span-12">
            <Label htmlFor="comments">Additional Notes</Label>
            <Textarea
  id="comments"
  className="h-24"
  placeholder="Add any additional notes or comments about this payment..."
  value={payment.comments ?? ''}
  onChange={(e) => handleEditingPaymentChange('comments', e.target.value)}
/>
          </div>
        </div>
  <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onUpdate}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
