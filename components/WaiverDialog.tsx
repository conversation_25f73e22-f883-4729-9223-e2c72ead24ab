// components/WaiverDialog.tsx
import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ChevronDownIcon } from "@radix-ui/react-icons"
import waiverTextGreek from '@/utils/waiverText';

interface WaiverDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (signatureData: string) => void;
  initialSignature?: string;
}

const WaiverDialog: React.FC<WaiverDialogProps> = ({ 
  open, 
  onOpenChange, 
  onSave,
  initialSignature
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [points, setPoints] = useState<{x: number, y: number}[]>([]);
  const [hasSignature, setHasSignature] = useState(false);
  const [showScrollIndicator, setShowScrollIndicator] = useState(true);
  
  // Initialize canvas when component mounts or dialog opens
  useEffect(() => {
    if (open && canvasRef.current && containerRef.current) {
      // Use setTimeout to ensure container is fully rendered
      setTimeout(() => {
        const canvas = canvasRef.current;
        if (!canvas || !containerRef.current) return;
        
        canvas.width = containerRef.current.offsetWidth - 4;
        canvas.height = Math.min(200, window.innerHeight * 0.2);
        
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          if (initialSignature) {
            const img = new Image();
            img.onload = () => {
              ctx.drawImage(img, 0, 0);
              setHasSignature(true);
            };
            img.src = initialSignature;
          } else {
            setHasSignature(false);
          }
        }
      }, 100);
    }
  }, [open, initialSignature]);

  const scrollToSignature = () => {
    if (scrollAreaRef.current) {
      const signatureSection = containerRef.current;
      if (signatureSection) {
        signatureSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setShowScrollIndicator(false);
      }
    }
  };

  // Handle scroll to show/hide scroll indicator
  useEffect(() => {
    const handleScroll = () => {
      if (!scrollAreaRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
      setShowScrollIndicator(!isNearBottom);
    };

    const scrollElement = scrollAreaRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
      handleScroll(); // Check initial state
    }

    return () => {
      if (scrollElement) {
        scrollElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (open && canvasRef.current && containerRef.current) {
        const canvas = canvasRef.current;
        
        // Store current signature
        const signatureData = hasSignature ? canvas.toDataURL('image/png') : null;
        
        // Resize canvas
        canvas.width = containerRef.current.offsetWidth - 4;
        canvas.height = Math.min(200, window.innerHeight * 0.2);
        
        // Restore signature if any
        if (signatureData && hasSignature) {
          const ctx = canvas.getContext('2d');
          if (!ctx) return;
          
          const img = new Image();
          img.onload = () => {
            ctx.drawImage(img, 0, 0);
          };
          img.src = signatureData;
        }
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [open, hasSignature]);
  
  const getCoordinates = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    
    if ('touches' in e) {
      e.preventDefault(); // Prevent scrolling while drawing
      return {
        x: e.touches[0].clientX - rect.left,
        y: e.touches[0].clientY - rect.top
      };
    } else {
      return {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
    }
  };
  
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Prevent default behavior
    setIsDrawing(true);
    const coords = getCoordinates(e);
    setPoints([coords]);
    
    // Start drawing immediately
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.beginPath();
    ctx.moveTo(coords.x, coords.y);
    ctx.lineTo(coords.x, coords.y);
    ctx.stroke();
    
    setHasSignature(true);
  };
  
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Prevent scrolling on touch devices
    if (!isDrawing) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const coords = getCoordinates(e);
    setPoints(prev => [...prev, coords]);
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.lineJoin = 'round';
    ctx.lineCap = 'round';
    ctx.lineWidth = 3;
    ctx.strokeStyle = '#000';
    
    if (points.length > 0) {
      const lastPoint = points[points.length - 1];
      ctx.beginPath();
      ctx.moveTo(lastPoint.x, lastPoint.y);
      ctx.lineTo(coords.x, coords.y);
      ctx.stroke();
    }
  };
  
  const endDrawing = () => {
    setIsDrawing(false);
  };
  
  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setPoints([]);
    setHasSignature(false);
  };
  
  const handleSave = () => {
    if (!hasSignature) {
      alert('Παρακαλώ υπογράψτε πριν συνεχίσετε.');
      return;
    }
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const signatureData = canvas.toDataURL('image/png');
    onSave(signatureData);
    onOpenChange(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col p-0 gap-0 max-w-[90%] w-[1200px] h-screen md:h-[90vh] md:max-h-[900px]">
        <DialogHeader className="p-4 md:p-6 border-b">
          <DialogTitle className="text-center text-xl">Συμφωνητικό Συμμετοχής</DialogTitle>
        </DialogHeader>
        
        <div 
          ref={scrollAreaRef}
          className="flex-1 overflow-y-auto px-4 md:px-6 pb-4"
        >
          {/* Waiver Text */}
          <div className="py-4">
            {waiverTextGreek.split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4 text-sm md:text-base">{paragraph}</p>
            ))}
          </div>

          {/* Signature Area */}
          <div ref={containerRef} className="flex flex-col gap-2 pt-4">
            <h3 className="font-semibold">Υπογραφή</h3>
            <p className="text-sm text-gray-600 mb-2">
              Υπογράψτε στο παρακάτω πλαίσιο για να αποδεχτείτε τους όρους
            </p>
            
            <div className="border-2 rounded-md bg-white p-1 relative" style={{ touchAction: 'none' }}>
              <canvas
                ref={canvasRef}
                className="w-full border cursor-crosshair bg-white"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={endDrawing}
                onTouchCancel={endDrawing}
                style={{ 
                  touchAction: 'none',
                  height: '150px'
                }}
              />
            </div>
            
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              className="self-end"
              onClick={clearCanvas}
            >
              Καθαρισμός
            </Button>
          </div>
        </div>

        {/* Floating scroll indicator */}
        {showScrollIndicator && (
          <div className="absolute left-1/2 bottom-24 -translate-x-1/2 z-10">
            <Button
              variant="secondary"
              size="sm"
              className="rounded-full shadow-lg flex items-center gap-2"
              onClick={scrollToSignature}
            >
              <ChevronDownIcon className="h-4 w-4" />
              Μετάβαση στην υπογραφή
            </Button>
          </div>
        )}
        
        <DialogFooter className="p-4 md:p-6 border-t bg-background">
          <div className="flex flex-col sm:flex-row sm:justify-between w-full gap-2">
            <Button 
              type="button"
              variant="outline"
              className="w-full sm:w-auto order-2 sm:order-1"
              onClick={() => onOpenChange(false)}
            >
              Ακύρωση
            </Button>
            <Button 
              className="w-full sm:w-auto order-1 sm:order-2"
              onClick={handleSave}
            >
              Αποδοχή και Υπογραφή
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WaiverDialog;
