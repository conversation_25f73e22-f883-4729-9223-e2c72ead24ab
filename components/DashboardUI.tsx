'use client'
import { useState } from 'react';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowDownIcon, ArrowUpIcon, Users, DollarSign, Activity, Calendar, UserPlus, Heart, TrendingUp, Scale } from 'lucide-react';
import { TooltipProvider, Tooltip as UITooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import KPIDashboard from './KPIDashboard';

const KPI_DESCRIPTIONS = {
  mrr: `Monthly Recurring Revenue (MRR) is the total revenue received from all active memberships`,
  totalMembers: `Total number of members with active memberships`,
  retentionRate: `Percentage of members who maintain their membership from the previous month`,
  avgClassAttendance: `Average number of members attending each class`
} as const;

interface KPICardProps {
  title: string;
  value: number;
  change?: number;
  icon: React.ElementType;
  format?: 'number' | 'currency' | 'percentage';
  description: string;
}

const KPICard = ({ title, value, change, icon: Icon, format = 'number', description }: KPICardProps) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium flex items-center gap-2">
        {title}
        <TooltipProvider>
          <UITooltip>
            <TooltipTrigger className="text-muted-foreground hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <circle cx="12" cy="12" r="10" />
                <path d="M12 16v-4M12 8h.01" />
              </svg>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs p-4">
              <p className="text-sm">{description}</p>
            </TooltipContent>
          </UITooltip>
        </TooltipProvider>
      </CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">
        {format === 'currency' && '€'}
        {format === 'percentage' ? `${value.toFixed(1)}%` : value.toLocaleString()}
      </div>
      {typeof change !== 'undefined' && (
        <p className="text-xs text-muted-foreground flex items-center">
          {change > 0 ? (
            <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
          ) : (
            <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
          )}
          <span>{Math.abs(change).toFixed(1)}% from last month</span>
        </p>
      )}
    </CardContent>
  </Card>
);

export default function DashboardUI() {
  const { metrics, historicalData } = KPIDashboard();
  const [activeTab, setActiveTab] = useState('overview');

  const revenueData = historicalData.map(d => ({
    month: d.month,
    amount: d.mrr
  }));

  const attendanceData = historicalData.map(d => ({
    month: d.month,
    average: d.total_checkins / d.unique_sessions
  }));
  const churnData = historicalData.map(d => ({
    month: d.month,
    rate: d.churn_rate
  }));
  
  return (
    <div className="space-y-8 p-8">
      <h1 className="text-3xl font-bold">Gym Performance Dashboard</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Historical Trends</TabsTrigger>
          <TabsTrigger value="business">Business Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <KPICard
              title="Monthly Revenue"
              value={metrics.mrr}
              change={metrics.mrrChange}
              icon={DollarSign}
              format="currency"
              description={KPI_DESCRIPTIONS.mrr}
            />
            <KPICard
              title="Members"
              value={metrics.totalMembers}
              change={metrics.memberChange}
              icon={Users}
              description={KPI_DESCRIPTIONS.totalMembers}
            />
            <KPICard
              title="Retention"
              value={metrics.retentionRate}
              change={metrics.retentionChange}
              icon={Activity}
              format="percentage"
              description={KPI_DESCRIPTIONS.retentionRate}
            />
            <KPICard
              title="Class Attendance"
              value={metrics.avgClassAttendance}
              change={metrics.attendanceChange}
              icon={Calendar}
              description={KPI_DESCRIPTIONS.avgClassAttendance}
            />
            <KPICard
  title="Churn Rate"
  value={metrics.churnRate}
  change={metrics.churnChange * -1} // Invert change since lower churn is better
  icon={Users}
  format="percentage"
  description="Percentage of members who cancelled their membership this month"
/>
          </div>
        </TabsContent>

        <TabsContent value="trends">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue History</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="amount" stroke="#8884d8" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Class Attendance Trends</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={attendanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="average" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Monthly Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr>
                        <th className="text-left p-2">Month</th>
                        <th className="text-right p-2">Revenue</th>
                        <th className="text-right p-2">Members</th>
                        <th className="text-right p-2">Retention</th>
                        <th className="text-right p-2">Avg. Attendance</th>
                        <th className="text-right p-2">Churn</th>
                      </tr>
                    </thead>
                    <tbody>
                      {historicalData.map((month) => (
                        <tr key={month.month}>
                          <td className="text-left p-2">{month.month}</td>
                          <td className="text-right p-2">€{month.mrr.toLocaleString()}</td>
                          <td className="text-right p-2">{month.total_members}</td>
                          <td className="text-right p-2">
                            {((month.retained_members / month.total_members) * 100).toFixed(1)}%
                          </td>
                          <td className="text-right p-2">
                            {(month.total_checkins / month.unique_sessions).toFixed(1)}
                          </td>
                          <td className="text-right p-2">{month.churn_rate.toFixed(1)}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
            <Card>
  <CardHeader>
    <CardTitle>Churn Rate Trends</CardTitle>
  </CardHeader>
  <CardContent className="h-[300px]">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={churnData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip />
        <Line 
          type="monotone" 
          dataKey="rate" 
          stroke="#ff4444" 
          name="Churn Rate %" 
        />
      </LineChart>
    </ResponsiveContainer>
  </CardContent>
</Card>
          </div>
        </TabsContent>

        <TabsContent value="business">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <KPICard
              title="Customer Acquisition Cost"
              value={metrics.cac}
              icon={UserPlus}
              format="currency"
              description="Average cost to acquire a new customer"
            />
            <KPICard
              title="Lifetime Value"
              value={metrics.ltv}
              icon={Heart}
              format="currency"
              description="Average revenue generated per customer over their lifetime"
            />
            <KPICard
              title="MRR Growth Rate"
              value={metrics.mrrGrowthRate}
              change={metrics.mrrGrowthRate - (historicalData[1]?.mrr_growth_rate ?? 0)}
              icon={TrendingUp}
              format="percentage"
              description="Month-over-month MRR growth rate"
            />
            <KPICard
              title="Annual Recurring Revenue"
              value={metrics.arr}
              icon={Calendar}
              format="currency"
              description="Annualized value of your MRR"
            />
            <KPICard
              title="LTV:CAC Ratio"
              value={metrics.ltvCacRatio}
              icon={Scale}
              format="number"
              description="Ratio of customer lifetime value to acquisition cost"
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
