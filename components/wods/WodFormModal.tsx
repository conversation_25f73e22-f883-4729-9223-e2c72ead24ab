import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch"; // Add this import
import { X } from 'lucide-react';
import type { Database } from '@/types/supabase';

// Type definitions from Supabase schema
type Wod = Database['public']['Tables']['wod']['Row'];
type ExerciseMovement = Database['public']['Tables']['exercise_movements']['Row'];

interface WodFormData {
  date: string;
  content: string;
  warmup: string | null;
  is_published: boolean | null;
  published_at: string | null;  // Add this
  exercises: string[] | null;
}

interface WodFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedWod: Wod | null;
  exercises: ExerciseMovement[];
  onSubmit: (data: WodFormData) => Promise<void>;
}

const WodFormModal: React.FC<WodFormModalProps> = ({
  isOpen,
  onClose,
  selectedWod,
  exercises,
  onSubmit
}) => {
  // Initialize form data with proper types
  const [formData, setFormData] = useState<WodFormData>({
    date: new Date().toISOString().split('T')[0],
    content: '',
    warmup: null,
    is_published: null,
    published_at: null,  // Add this line
    exercises: null
  });

  const [selectedExercises, setSelectedExercises] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredExercises, setFilteredExercises] = useState<ExerciseMovement[]>(exercises);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter exercises based on search term
  useEffect(() => {
    const filtered = exercises.filter(exercise =>
      exercise.exercise_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exercise.body_part.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exercise.movement_category.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredExercises(filtered);
  }, [searchTerm, exercises]);

  // Initialize or reset form when selectedWod changes
  useEffect(() => {
    if (selectedWod) {
      setFormData({
        date: selectedWod.date,
        content: selectedWod.content,
        warmup: selectedWod.warmup,
        is_published: selectedWod.is_published,
        published_at: selectedWod.published_at,  // Add this
        exercises: selectedWod.exercises
      });
      setSelectedExercises(selectedWod.exercises || []);
    } else {
      setFormData({
        date: new Date().toISOString().split('T')[0],
        content: '',
        warmup: null,
        is_published: false,  // Default to draft
        published_at: null,
        exercises: null
      });
      setSelectedExercises([]);
    }
  }, [selectedWod, isOpen]);


  const handleInputChange = <K extends keyof WodFormData>(
    field: K,
    value: WodFormData[K]
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleExercise = (exerciseId: string) => {
    setSelectedExercises(prev => {
      const newExercises = prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId];
      handleInputChange('exercises', newExercises);
      return newExercises;
    });
  };

  const removeExercise = (exerciseId: string) => {
    setSelectedExercises(prev => {
      const newExercises = prev.filter(id => id !== exerciseId);
      handleInputChange('exercises', newExercises);
      return newExercises;
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);
      await onSubmit({
        ...formData,
        exercises: selectedExercises
      });
      onClose();
    } catch (error) {
      console.error('Error submitting WOD:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderExerciseBadge = (exerciseId: string) => {
    const exercise = exercises.find(ex => ex.id === exerciseId);
    if (!exercise) return null;

    return (
      <Badge
        key={exercise.id}
        variant="secondary"
        className="flex items-center gap-1 p-1"
      >
        <span className="truncate max-w-[150px]">{exercise.exercise_name}</span>
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            removeExercise(exercise.id);
          }}
          className="ml-1 hover:text-destructive"
        >
          <X className="h-3 w-3" />
        </button>
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-7xl w-full h-[90vh] p-0">
        <DialogHeader className="px-6 py-4 border-b flex flex-row items-center justify-between">
          <DialogTitle>{selectedWod ? 'Edit WOD' : 'Add New WOD'}</DialogTitle>
          <div className="flex items-center gap-4">
  <div className="flex items-center space-x-2">
    <Switch
      id="published"
      checked={formData.is_published || false}
      onCheckedChange={(checked) => {
        setFormData(prev => ({
          ...prev,
          is_published: checked,
          published_at: checked && !prev.is_published ? new Date().toISOString() : prev.published_at
        }));
      }}
    />
    <Label htmlFor="published">
      {formData.is_published ? 'Published' : 'Draft'}
    </Label>
  </div>
  <div className="flex gap-2">
    <Button
      type="button"
      variant="outline"
      onClick={onClose}
      disabled={isSubmitting}
    >
      Cancel
    </Button>
    <Button
      type="submit"
      form="wod-form"
      disabled={isSubmitting}
    >
      {isSubmitting ? 'Saving...' : (selectedWod ? 'Update WOD' : 'Create WOD')}
    </Button>
  </div>
</div>
        </DialogHeader>

        <form id="wod-form" onSubmit={handleSubmit} className="flex h-full">
          <div className="flex flex-col h-full w-full">
            {/* Date input */}
            <div className="p-4 border-b">
              <Label htmlFor="date">Date:</Label>
              <Input
                type="date"
                id="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
                className="w-full"
              />
            </div>

            <div className="flex flex-1 min-h-0">
              {/* Warmup Column */}
              <div className="flex-1 p-4 border-r overflow-hidden flex flex-col">
                <Label htmlFor="warmup" className="mb-2">Warm-up:</Label>
                <textarea
                  id="warmup"
                  value={formData.warmup || ''}
                  onChange={(e) => handleInputChange('warmup', e.target.value)}
                  className="flex-1 p-2 border rounded-md resize-none"
                  placeholder="Enter warm-up in Markdown format..."
                />
              </div>

              {/* WOD Content Column */}
              <div className="flex-1 p-4 border-r overflow-hidden flex flex-col">
                <Label htmlFor="content" className="mb-2">WOD Content:</Label>
                <textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  className="flex-1 p-2 border rounded-md resize-none"
                  required
                  placeholder="Enter WOD content in Markdown format..."
                />
              </div>

              {/* Exercises Column */}
              <div className="w-96 flex flex-col border-l overflow-hidden">
                <div className="p-4 border-b">
                  <Input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search exercises by name, category, or body part..."
                    className="w-full"
                  />
                </div>

                {/* Selected Exercises */}
                <div className="p-4 border-b">
                  <Label className="mb-2 block">Selected Exercises:</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedExercises.map(exerciseId =>
                      renderExerciseBadge(exerciseId)
                    )}
                  </div>
                </div>

                {/* Exercise List */}
                <ScrollArea className="flex-1">
                  <div className="p-4 space-y-2">
                    {filteredExercises.map(exercise => (
                      <div
                        key={exercise.id}
                        className={`p-2 rounded cursor-pointer transition-colors ${
                          selectedExercises.includes(exercise.id)
                            ? 'bg-primary/10 hover:bg-primary/20'
                            : 'hover:bg-accent'
                        }`}
                        onClick={() => toggleExercise(exercise.id)}
                      >
                        <div className="font-medium">{exercise.exercise_name}</div>
                        <div className="text-sm text-muted-foreground">
                          {exercise.body_part} • {exercise.movement_category}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default WodFormModal;