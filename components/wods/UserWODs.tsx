import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { format, parseISO } from 'date-fns';
import ReactMarkdown from 'react-markdown';

// Types
type CheckInWithWods = {
  id: string;
  check_in_time: string | null;
  user_wods: {
    id: string;
    wod: {
      id: string;
      date: string;
      content: string;
    } | null;
  }[];
}

type WodWithCheckIn = {
  wodId: string;
  checkInId: string;
  date: string;
  content: string;
}

interface UserWODsProps {
  userId: string;
}

export function UserWODs({ userId }: UserWODsProps) {
  const [wodsHistory, setWodsHistory] = useState<WodWithCheckIn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectedWod, setSelectedWod] = useState<WodWithCheckIn | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    const fetchUserWODsHistory = async () => {
      try {
        const { data, error } = await supabase
          .from('check_ins')
          .select(`
            id,
            check_in_time,
            user_wods!inner (
              id,
              wod!inner (
                id,
                date,
                content
              )
            )
          `)
          .eq('pelatis_id', userId)
          .order('check_in_time', { ascending: false });

        if (error) throw error;

        const transformedData: WodWithCheckIn[] = data
          ?.filter((checkin): checkin is CheckInWithWods =>
            checkin.user_wods.length > 0 && checkin.user_wods[0].wod !== null)
          .map(checkin => ({
            wodId: checkin.user_wods[0].wod!.id,
            checkInId: checkin.id,
            date: checkin.user_wods[0].wod!.date,
            content: checkin.user_wods[0].wod!.content
          })) ?? [];

        setWodsHistory(transformedData);
      } catch (error) {
        console.error('Error fetching user WODs history:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserWODsHistory();
  }, [supabase, userId]);

  // Calendar navigation
  const handleMonthChange = (increment: boolean) => {
    setCurrentMonth(prev => {
      const newDate = new Date(currentYear, increment ? prev + 1 : prev - 1, 1);
      setCurrentYear(newDate.getFullYear());
      return newDate.getMonth();
    });
  };

  // Calendar rendering helpers
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
    <div key={day} className="font-bold text-center py-2 text-xs sm:text-sm">{day}</div>
  ));

  const renderCalendar = () => {
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
    const calendar = [];

    // Empty cells for days before the first of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendar.push(<div key={`empty-${i}`} className="min-h-[60px]" />);
    }

    // Calendar days
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const dayWods = wodsHistory.filter(wod => wod.date === dateString);
      const isToday = new Date(dateString).toDateString() === new Date().toDateString();

      calendar.push(
        <Card key={day} className={`min-h-[60px] ${isToday ? 'bg-blue-100' : ''} flex flex-col`}>
          <CardHeader className="p-1">
            <div className="font-bold text-xs">{day}</div>
          </CardHeader>
          <CardContent className="p-1 flex-grow flex flex-col">
            {dayWods.map(wod => (
              <Button
                key={wod.wodId}
                variant="secondary"
                size="sm"
                className="w-full mb-1 text-left justify-start text-xs"
                onClick={() => setSelectedWod(wod)}
              >
                View WOD
              </Button>
            ))}
          </CardContent>
        </Card>
      );
    }

    return calendar;
  };

  if (isLoading) return <div>Loading workout history...</div>;
  if (error) return <div>Error loading workout history: {error}</div>;
  if (wodsHistory.length === 0) return <div>No workout history available.</div>;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <Button onClick={() => handleMonthChange(false)}>Previous</Button>
        <span className="text-lg font-semibold">
          {monthNames[currentMonth]} {currentYear}
        </span>
        <Button onClick={() => handleMonthChange(true)}>Next</Button>
      </div>

      <div className="grid grid-cols-7 gap-2">
        {dayHeaders}
        {renderCalendar()}
      </div>

      <Dialog open={!!selectedWod} onOpenChange={() => setSelectedWod(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedWod && format(parseISO(selectedWod.date), 'MMMM d, yyyy')}
            </DialogTitle>
          </DialogHeader>
          <div className="prose max-w-none">
            {selectedWod && <ReactMarkdown>{selectedWod.content}</ReactMarkdown>}
          </div>
          <DialogFooter>
            <Button onClick={() => setSelectedWod(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default UserWODs;