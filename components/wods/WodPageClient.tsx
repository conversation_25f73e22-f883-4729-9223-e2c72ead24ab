'use client'

import React, { useState, useCallback, useEffect, type FC } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import ReactMarkdown from 'react-markdown';
import Link from 'next/link';
import { Badge } from "@/components/ui/badge";

type Wod = Database['public']['Tables']['wod']['Row'];
type Exercise = Database['public']['Tables']['exercise_movements']['Row'];

type Message = {
  text: string;
  isError: boolean;
};

interface WodPageClientProps {
  initialWods: Wod[];
  exercises: Exercise[];
}

const WodPageClient: FC<WodPageClientProps> = ({ initialWods, exercises }) => {
  const [wods, setWods] = useState<Wod[]>(initialWods);
  const [message, setMessage] = useState<Message>({ text: '', isError: false });
  const [isLoading, setIsLoading] = useState(false);

  const supabase = createClientComponentClient<Database>();

  const showMessage = useCallback((text: string, isError: boolean = false) => {
    setMessage({ text, isError });
    setTimeout(() => setMessage({ text: '', isError: false }), 5000);
  }, []);

  const fetchWods = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('wod')
        .select('*')
        .order('date', { ascending: false });

      if (error) throw error;

      setWods(data || []);
    } catch (error) {
      console.error('Error fetching WODs:', error);
      showMessage('Failed to fetch WODs', true);
    } finally {
      setIsLoading(false);
    }
  }, [supabase, showMessage]);

  useEffect(() => {
    const channel = supabase
      .channel('wod_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'wod'
        },
        (payload) => {
          console.log('Change received!', payload);
          fetchWods();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase, fetchWods]);

  const handleDelete = useCallback(async (wodId: string) => {
    try {
      setWods(prev => prev.filter(wod => wod.id !== wodId));

      const { error } = await supabase
        .from("wod")
        .delete()
        .eq('id', wodId);

      if (error) throw error;

      showMessage('WOD deleted successfully!');
    } catch (error) {
      await fetchWods(); // Revert optimistic update
      console.error('Error deleting WOD:', error);
      showMessage('Failed to delete WOD: ' + (error instanceof Error ? error.message : 'Unknown error'), true);
    }
  }, [supabase, showMessage, fetchWods]);

  return (
    <div className="p-0 sm:p-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 p-4 sm:p-0">
        <h1 className="text-2xl font-bold">WODs</h1>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => fetchWods()}
            disabled={isLoading}
            size="sm"
            className="text-sm"
          >
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            asChild
            size="sm"
            className="text-sm"
          >
            <Link href="/admin/wods/add">Add New WOD</Link>
          </Button>
        </div>
      </div>

      {message.text && (
        <div className={`mb-4 p-2 mx-2 sm:mx-0 rounded text-sm ${
          message.isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
        }`}>
          {message.text}
        </div>
      )}

      {isLoading && !wods.length ? (
        <div className="text-center py-4">Loading WODs...</div>
      ) : !wods.length ? (
        <div className="text-center py-4">No WODs found</div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-2 sm:px-0">
          {wods.map((wod) => (
            <Card key={wod.id} className="flex flex-col relative border-x-0 sm:border-x rounded-none sm:rounded-lg">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">
                    {new Date(wod.date).toLocaleDateString('en-GB')}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {wod.is_published ? (
                      <Badge
                        variant="default"
                        className="bg-green-500 flex items-center gap-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                        Published
                      </Badge>
                    ) : (
                      <Badge
                        variant="secondary"
                        className="bg-gray-200 text-gray-700 flex items-center gap-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/><line x1="7" y1="7" x2="7.01" y2="7"/></svg>
                        Draft
                      </Badge>
                    )}
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        asChild
                      >
                        <Link href={`/admin/wods/edit/${wod.id}`}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        </Link>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-red-500 hover:text-red-700"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the WOD.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(wod.id)}>Delete</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex-grow overflow-auto pt-2">
                {wod.warmup && (
                  <div className="mb-4">
                    <h3 className="font-semibold mb-2">Warm-up</h3>
                    <div className="prose max-w-none">
                      <ReactMarkdown>{wod.warmup}</ReactMarkdown>
                    </div>
                  </div>
                )}
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">WOD</h3>
                  <div className="prose max-w-none">
                    <ReactMarkdown>{wod.content}</ReactMarkdown>
                  </div>
                </div>
                {wod.exercises && wod.exercises.length > 0 && (
                  <div className="mb-4">
                    <h3 className="font-semibold mb-2">Exercises</h3>
                    <div className="flex flex-wrap gap-2">
                      {wod.exercises.map(exerciseId => {
                        const exercise = exercises.find(ex => ex.id === exerciseId);
                        return exercise ? (
                          <Badge key={exercise.id} variant="secondary">
                            {exercise.exercise_name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default WodPageClient;