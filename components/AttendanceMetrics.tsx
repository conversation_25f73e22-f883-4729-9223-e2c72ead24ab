import React, { useMemo } from 'react';
import { Card, CardContent } from "@/components/ui/card";


interface CheckIn {
  check_in_time: string;
}

interface Payment {
  date_money_gave: string | null;
  money_gave: number | null;
}

interface GroupedCheckins {
  [key: string]: {
    total: number;
    days: {
      [key: string]: { date: Date; times: string[] };
    };
  };
}

interface AttendanceMetricsProps {
  checkIns: CheckIn[];
  payments: Payment[];
  groupedCheckins: GroupedCheckins;
}

const AttendanceMetrics: React.FC<AttendanceMetricsProps> = ({ 
  checkIns, 
  payments,
  groupedCheckins 
}) => {
  // Calculate total check-ins
  const totalCheckins = checkIns.length;

  // Calculate total months
  const totalMonths = Object.keys(groupedCheckins).length;

  // Calculate total payments and average per month
  const paymentMetrics = useMemo(() => {
    const total = payments.reduce((sum, payment) => {
      // Use nullish coalescing to handle null values
      const amount = payment.money_gave ?? 0;
      return sum + amount;
    }, 0);

    const avgPerMonth = totalMonths > 0 ? total / totalMonths : 0;

    return {
      total: total.toFixed(2),
      average: avgPerMonth.toFixed(2)
    };
  }, [payments, totalMonths]);

  // Calculate average check-ins per month
  const avgCheckinsPerMonth = totalMonths > 0 ? (totalCheckins / totalMonths).toFixed(1) : '0.0';

  // Debug info
  console.log('AttendanceMetrics Debug:', {
    totalCheckins,
    totalMonths,
    payments: payments.map(p => ({ date: p.date_money_gave, amount: p.money_gave })),
    paymentMetrics
  });

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-sm font-medium">Total Check-ins</h3>
          <p className="text-2xl font-bold">{totalCheckins}</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <h3 className="text-sm font-medium">Total Months</h3>
          <p className="text-2xl font-bold">{totalMonths}</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <h3 className="text-sm font-medium">Avg €/Month</h3>
          <p className="text-2xl font-bold">€{paymentMetrics.average}</p>
          <p className="text-sm text-gray-500">Total: €{paymentMetrics.total}</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <h3 className="text-sm font-medium">Avg Check-ins/Month</h3>
          <p className="text-2xl font-bold">{avgCheckinsPerMonth}</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceMetrics;