import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from 'react-hot-toast';
import { Mail } from 'lucide-react';
import MarkdownPreview from '@/components/ui/markdown-preview';

interface EmailComposerProps {
  selectedClients: Array<{
    email: string;
    client_name: string;
  }>;
  onClose: () => void;
  defaultSubject?: string; // Add optional defaultSubject prop
}

const EmailComposer: React.FC<EmailComposerProps> = ({ 
  selectedClients, 
  onClose,
  defaultSubject = '' // Provide default value
}) => {
  const [subject, setSubject] = useState(defaultSubject); // Initialize with defaultSubject
  const [content, setContent] = useState('');
  const [isPreview, setIsPreview] = useState(false);
  const [isSending, setIsSending] = useState(false);

// Update EmailComposer to improve error handling

// In EmailComposer.tsx
// In EmailComposer.tsx, update the handleSend function:
const handleSend = async () => {
  if (!subject.trim() || !content.trim()) {
    toast.error('Please fill in both subject and content');
    return;
  }
  
  console.log('About to send request to API with payload:', {
    to: selectedClients.map(client => client.email),
    subject,
    content: content.substring(0, 100) + '...' // Log just the beginning for brevity
  });
  
  setIsSending(true);
  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: selectedClients.map(client => client.email),
        subject,
        content,
      }),
    });
    
    console.log('API response status:', response.status);
    
    const responseData = await response.json();
    console.log('API response data:', responseData);

    if (!response.ok) {
      // Better error handling - extract the actual error message
      const errorMessage = responseData.error?.message || 
                          responseData.message || 
                          responseData.error || 
                          'Failed to send email';
      throw new Error(errorMessage);
    }
    
    toast.success('Emails sent successfully!');
    onClose();
  } catch (error) {
    console.error('Error sending emails:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to send emails';
    toast.error(errorMessage);
  } finally {
    setIsSending(false);
  }
};

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Send Email to {selectedClients.length} Clients
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="recipients">Recipients</Label>
            <div className="text-sm text-gray-500 bg-gray-50 p-2 rounded max-h-28 overflow-y-auto">
              {selectedClients.map(client => (
                <div key={client.email}>{client.client_name} ({client.email})</div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Email subject..."
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="content">Content (Markdown supported)</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPreview(!isPreview)}
              >
                {isPreview ? 'Edit' : 'Preview'}
              </Button>
            </div>
            
            {isPreview ? (
              <div className="min-h-[200px] p-4 border rounded overflow-auto">
                <MarkdownPreview content={content} />
              </div>
            ) : (
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Write your email content in markdown..."
                className="min-h-[200px]"
              />
            )}
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSend} 
              disabled={isSending || !subject.trim() || !content.trim()}
            >
              {isSending ? 'Sending...' : 'Send Email'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EmailComposer;
