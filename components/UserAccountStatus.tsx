'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'react-hot-toast';
import { Loader2 } from 'lucide-react';

interface Props {
  pelatiId: string;
  email: string | null;
  onAccountCreated?: () => void;
}

type AccountStatus = {
  hasAuth: boolean;
  authUserId: string | null;
};

export default function UserAccountStatus({ pelatiId, email, onAccountCreated }: Props) {
  const supabase = createClientComponentClient<Database>();
  const [isLoading, setIsLoading] = useState(false);
  const [accountStatus, setAccountStatus] = useState<AccountStatus>({
    hasAuth: false,
    authUserId: null
  });

  useEffect(() => {
    checkAccountStatus();
  }, [pelatiId]);

  const checkAccountStatus = async () => {
    try {
      const { data: pelati } = await supabase
        .from('pelates')
        .select('auth_user_id')
        .eq('id', pelatiId)
        .single();
   
      const authUserId = pelati?.auth_user_id ?? null;
   
      setAccountStatus({
        hasAuth: !!authUserId,
        authUserId 
      });
    } catch (error) {
      console.error('Error checking status:', error);
    }
   };

  const handleCreateAccount = async () => {
    if (!email?.trim()) {
      toast.error('Email required');
      return;
    }
  
    setIsLoading(true);
    try {
      const { error: authError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          data: {
            pelatiId,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        }
      });
  
      if (authError) throw authError;
  
      const { error: updateError } = await supabase
        .from('pelates')
        .update({ 
          auth_user_id: email
        })
        .eq('id', pelatiId);
  
      if (updateError) throw updateError;
  
      toast.success('Magic link sent to email');
      onAccountCreated?.();
    } catch (error) {
      console.error('Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create account';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Account Setup</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {accountStatus.hasAuth ? (
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              <p className="text-green-700">Account Active</p>
            </div>
            <p className="text-sm text-gray-600">
              Login available via magic link at: {email}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <Button
              onClick={handleCreateAccount}
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending Magic Link...
                </>
              ) : (
                'Send Magic Link'
              )}
            </Button>
            {email && (
              <p className="text-sm text-gray-500">
                A magic link will be sent to: {email}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}