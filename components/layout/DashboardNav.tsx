// components/layout/DashboardNav.tsx
'use client'
import { <PERSON>ideIcon, BarChart, Users, Du<PERSON><PERSON>, DollarSign, Settings, Home } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavItem {
  title: string;
  href: string;
  icon: LucideIcon;
}

export function DashboardNav() {
  const pathname = usePathname();
  
  const navItems: NavItem[] = [
    {
      title: 'Overview',
      href: '/admin/dashboard',
      icon: Home
    },
    {
      title: 'Members',
      href: '/admin/members',
      icon: Users
    },
    {
      title: 'Programs',
      href: '/admin/programs',
      icon: Dumbbell
    },
    {
      title: 'Finances',
      href: '/admin/finances',
      icon: DollarSign
    },
    {
      title: 'Reports',
      href: '/admin/reports',
      icon: BarChart
    },
    {
      title: 'Settings',
      href: '/admin/settings',
      icon: Settings
    }
  ];
  
  return (
    <nav className="space-y-1">
      {navItems.map((item) => {
        const isActive = pathname === item.href;
        return (
          <Link
            key={item.href}
            href={item.href}
            className={`
              flex items-center px-3 py-2 text-sm rounded-md 
              ${isActive 
                ? 'bg-primary text-primary-foreground font-medium' 
                : 'text-muted-foreground hover:bg-muted hover:text-foreground'}
            `}
          >
            <item.icon className="mr-2 h-4 w-4" />
                <span>{item.title}</span>
                </Link>
              );
            })}
          </nav>
        );
       }