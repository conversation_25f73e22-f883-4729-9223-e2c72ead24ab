// components/layout/MainContent.tsx
'use client'

import { Session } from '@supabase/supabase-js'
import { useState, useEffect } from 'react'
import { MainHeader } from './MainHeader'
import { SecondaryNavigation } from './SecondaryNavigation'
import { AdminSidebar } from './AdminSidebar'
import { AdminBottomNav } from './AdminBottomNav'
import { AdminNavigationDrawer } from '@/components/admin/AdminNavigationDrawer'
import { CommandPalette } from '@/components/admin/CommandPalette'
import { useMemo } from 'react'

interface MainContentProps {
  session: Session | null
  isAdmin: boolean
  userName?: string | null
  children: React.ReactNode
}

export function MainContent({ session, isAdmin, userName, children }: MainContentProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false)
  const [sidebarExpanded, setSidebarExpanded] = useState(false)
  
  // Load sidebar state from localStorage on first render
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('adminSidebarExpanded')
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState))
      }
    }
  }, [])
  
  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('adminSidebarExpanded', JSON.stringify(sidebarExpanded))
    }
  }, [sidebarExpanded])

  const userInitial = useMemo(() => {
    if (!userName) return undefined
    // Slugify and get first character
    const initial = userName
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
      .trim()
      .charAt(0)
      .toUpperCase()
    return initial
  }, [userName])

  // Admin layout with sidebar
  if (session && isAdmin) {
    return (
      <div className="flex h-screen overflow-hidden">
        {/* Hide sidebar on mobile, show on tablet and up */}
        <div className="hidden md:block">
          <AdminSidebar 
            sidebarExpanded={sidebarExpanded}
            setSidebarExpanded={setSidebarExpanded}
          />
        </div>
        <div className="flex flex-col flex-1 overflow-hidden">
          <MainHeader
            session={session}
            isAdmin={isAdmin}
            userInitial={userInitial}
            isSidebarLayout={true}
            onOpenCommandPalette={() => setIsCommandPaletteOpen(true)}
          />
          <main className="flex-1 p-4 overflow-y-auto pb-16 md:pb-4">{children}</main>
          
          {/* Mobile bottom navigation - only visible on small screens */}
          <AdminBottomNav onOpenDrawer={() => setIsDrawerOpen(true)} />
        </div>
        
        {/* Mobile navigation drawer */}
        <AdminNavigationDrawer 
          isOpen={isDrawerOpen} 
          onClose={() => setIsDrawerOpen(false)} 
        />
        
        {/* Command palette */}
        <CommandPalette 
          isOpen={isCommandPaletteOpen}
          onClose={() => setIsCommandPaletteOpen(false)}
        />
      </div>
    )
  }

  // Regular user layout
  return (
    <div className="flex flex-col min-h-screen">
      <MainHeader
        session={session}
        isAdmin={isAdmin}
        userInitial={userInitial}
      />
      {session && <SecondaryNavigation isAdmin={false} />}
      <main className="flex-1 p-4 max-w-7xl mx-auto w-full">{children}</main>
    </div>
  )
}