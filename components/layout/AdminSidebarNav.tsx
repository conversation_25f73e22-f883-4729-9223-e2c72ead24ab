// components/layout/AdminSidebarNav.tsx
'use client'

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  BarChart, Users, Dumbbell, DollarSign, Settings,
  Home, Calendar, FileText, Bell, Clock, Package,
  ChevronDown, ChevronRight, Receipt, Database
} from 'lucide-react';

// Map group titles to appropriate icons
const groupIcons: Record<string, React.ElementType> = {
  "Reports": <PERSON><PERSON>hart,
  "Coach Tools": Clock,
  "Members": Users,
  "Finances": DollarSign,
  "Payments": Receipt,
  "Sessions": Calendar,
  "WODS": Dumbbell,
  "System": Settings,
  "Notifications": Bell,
  "My Data": Database,
  "Other": Package
};

const adminGroups = [
  {
    title: "Reports",
    items: [
      { title: "KPI Dashboard", href: "/admin/reports/kpis" },
      { title: "Monthly Reports", href: "/admin/reports/monthly" },
      { title: "Check-ins", href: "/admin/reports/checkins" },
      { title: "New Members", href: "/admin/reports/new-members" },
      { title: "Advanced Analytics", href: "/reports/analytics" },
    ]
  },
  {
    title: "Coach Tools",
    items: [
      { title: "Daily Overview", href: "/admin/daily" },
      { title: "Exercise Records", href: "/admin/exercise-records" },
      { title: "Session Calendar", href: "/admin/sessions/calendar" }
    ]
  },
  {
    title: "Members",
    items: [
      { title: "View Members", href: "/admin/users/view" },
      { title: "Subscriptions Active", href: "/admin/subscriptions/active" },
      { title: "Subscriptions Expiring", href: "/admin/subscriptions/expiring" },
      { title: "Support Tickets", href: "/admin/support" },
      { title: "Add Check-in", href: "/admin/check-ins" }
    ]
  },
  {
    title: "Finances",
    items: [
      { title: "Dashboard", href: "/admin/finances" },
      { title: "Transactions", href: "/admin/finances/transactions" },
      { title: "Cashflow", href: "/admin/finances/cashflow" },
      { title: "Transfers", href: "/admin/finances/transfers" },
      { title: "Accounts", href: "/admin/finances/accounts" },
      { title: "Reconciliation", href: "/admin/finances/reconciliation" },
      { title: "Expenses", href: "/admin/expenses" }
    ]
  },
  {
    title: "Payments",
    items: [
      { title: "View Payments", href: "/admin/payments/view" },
      { title: "New Payment", href: "/admin/payments/add" }
    ]
  },
  {
    title: "Sessions",
    items: [
      { title: "Session Calendar", href: "/admin/sessions/calendar" },
      { title: "Add Session", href: "/admin/sessions/add" },
      { title: "Daily", href: "/admin/daily" }
    ]
  },
  {
    title: "WODS",
    items: [
      { title: "WODs", href: "/admin/wods" },
      { title: "Add WOD", href: "/admin/wods/add" },
      { title: "Exercise Library", href: "/admin/wods/exercises" }
    ]
  },
  {
    title: "System",
    items: [
      { title: "Auth Debug", href: "/admin/auth-debug" },
      { title: "Role Check", href: "/admin/check-role" }
    ]
  },
  {
    title: "Notifications",
    items: [
      { title: "Dashboard", href: "/admin/notifications/dashboard" },
      { title: "Manage Notifications", href: "/admin/notifications" },
      { title: "Subscriptions", href: "/admin/notifications/subscriptions" }
    ]
  },
  {
    title: "My Data",
    items: [
      { title: "Settings", href: "/admin/mydata/settings" },
      { title: "API Logs", href: "/admin/mydata/logs" },
      { title: "Invoices", href: "/admin/invoices" }
    ]
  },
  {
    title: "Other",
    items: [
      { title: "CROSSFIT TIMER", href: "/admin/crossfitimer" },
      { title: "Meals Planning", href: "/meals/weekbyweek" },
      { title: "Ergo Conversions", href: "/admin/wods/conversiontable" },
      { title: "Exercises", href: "/admin/wods/exercises" },
      { title: "Email", href: "/admin/email-templates" },
      { title: "Goals", href: "/admin//goals" },
      { title: "marketing", href: "/admin/marketing" },
      { title: "Subscriptions Active", href: "/admin/subscriptions/active" },
      { title: "Subscriptions Expiring", href: "/admin/subscriptions/expiring" },
      { title: "Workout Tracking", href: "/admin/workout-tracking" },
      { title: "Settings", href: "/admin/settings" },
      { title: "Diagrams", href: "/admin/diagrams" },
      { title: "Business", href: "/admin/business" },
      { title: "Merchandise", href: "/admin/merchandise" }
    ]
  }
];

interface AdminSidebarNavProps {
  collapsed: boolean;
  setCollapsed?: (collapsed: boolean) => void;
}

export function AdminSidebarNav({ collapsed, setCollapsed }: AdminSidebarNavProps) {
  const pathname = usePathname();
  const [openGroups, setOpenGroups] = useState<Record<string, boolean>>({});

  const toggleGroup = (title: string) => {
    if (collapsed) {
      // If sidebar is collapsed, expand it first and then open the group
      if (setCollapsed) {
        setCollapsed(false);
        // Set this group to open after sidebar expands
        setOpenGroups(prev => ({
          ...prev,
          [title]: true
        }));
      }
      return;
    }

    // Normal toggle behavior when sidebar is expanded
    setOpenGroups(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  return (
    <nav className="space-y-1 py-2">
      <Link
        href="/admin/dashboard"
        className={cn(
          "flex items-center px-3 py-2 mx-2 rounded-md",
          pathname === "/admin/dashboard"
            ? "bg-slate-700 text-white"
            : "text-slate-300 hover:bg-slate-700 hover:text-white",
          collapsed ? "justify-center" : ""
        )}
      >
        <Home className="h-5 w-5" />
        {!collapsed && <span className="ml-3">Dashboard</span>}
      </Link>

      {adminGroups.map((group) => {
        const isOpen = openGroups[group.title];
        const GroupIcon = groupIcons[group.title] || FileText;
        const activeItem = group.items.find(item => pathname === item.href);

        return (
          <Collapsible
            key={group.title}
            open={isOpen && !collapsed}
            className={cn("mx-2", collapsed ? "items-center" : "")}
          >
            <CollapsibleTrigger
              onClick={() => toggleGroup(group.title)}
              className={cn(
                "flex items-center w-full px-3 py-2 rounded-md",
                activeItem ? "text-white" : "text-slate-300 hover:text-white",
                activeItem && !isOpen ? "bg-slate-700" : "",
                "hover:bg-slate-700",
                collapsed ? "justify-center" : "justify-between"
              )}
            >
              <div className="flex items-center">
                <GroupIcon className="h-5 w-5" />
                {!collapsed && <span className="ml-3">{group.title}</span>}
              </div>
              {!collapsed && (
                isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
              )}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-1 ml-2 space-y-1">
              {group.items.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-sm",
                    pathname === item.href
                      ? "bg-slate-700 text-white"
                      : "text-slate-300 hover:bg-slate-700 hover:text-white",
                  )}
                >
                  <span className="truncate">{item.title}</span>
                </Link>
              ))}
            </CollapsibleContent>
          </Collapsible>
        );
      })}
    </nav>
  );
}
