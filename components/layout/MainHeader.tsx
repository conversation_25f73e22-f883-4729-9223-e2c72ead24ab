// components/layout/MainHeader.tsx
'use client'
import { useEffect } from 'react';
import { User, Search, Menu } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { NotificationsPopover } from '@/components/NotificationsPopover'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { SignOutButton } from '@/components/SignOutButton'
import { Session } from '@supabase/supabase-js'

interface MainHeaderProps {
  session: Session | null
  isAdmin?: boolean
  userInitial?: string
  isSidebarLayout?: boolean
  onOpenCommandPalette?: () => void
  onOpenDrawer?: () => void
}

export function MainHeader({
  session,
  isAdmin,
  userInitial,
  isSidebarLayout,
  onOpenCommandPalette,
  onOpenDrawer
}: MainHeaderProps) {
  // Add keyboard shortcut for command palette
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (onOpenCommandPalette) {
          onOpenCommandPalette();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onOpenCommandPalette]);

  const homeLink = isAdmin
    ? '/admin/dashboard'
    : session
      ? '/user/front-page'
      : '/guest/calendar'

  // Admin header layout (clean, focused, no navigation)
  if (isAdmin && isSidebarLayout) {
    return (
      <header className="border-b border-l border-slate-700 bg-slate-800 z-10">
        <div className="flex h-16 items-center px-4 justify-between">
          {/* Mobile: Logo and menu button */}
          <div className="md:hidden flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="text-white mr-2"
              onClick={onOpenDrawer}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Open menu</span>
            </Button>
            <Link href={homeLink} className="flex items-center">
              <Image
                src="/images/logo_icon_minimal.svg"
                alt="LIFT GYM"
                width={36}
                height={36}
                className="invert"
                priority
              />
            </Link>
          </div>

          {/* Desktop/Tablet: Title */}
          <div className="hidden md:block">
            <h1 className="text-white font-medium">Admin Dashboard</h1>
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Command palette button (desktop/tablet only) */}
            {onOpenCommandPalette && (
              <button
                onClick={onOpenCommandPalette}
                className="hidden md:flex items-center text-white hover:text-gray-300 px-3 py-1 rounded border border-slate-600 hover:border-slate-500 transition-colors"
              >
                <Search className="h-4 w-4 mr-2" />
                <span className="text-sm mr-2">Search</span>
                <kbd className="flex items-center justify-center h-5 min-w-[20px] bg-slate-700 text-xs rounded px-1 text-white">
                  ⌘K
                </kbd>
              </button>
            )}

            {/* Notifications */}
            <NotificationsPopover userId={session?.user.id || ''} />

            {/* User menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:text-gray-300 font-bold w-10 h-10 flex-shrink-0"
                >
                  {userInitial ? (
                    <span className="text-lg font-bold">
                      {userInitial}
                    </span>
                  ) : (
                    <User className="h-5 w-5" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-56 z-50"
                sideOffset={8}
                alignOffset={0}
                forceMount
              >
                {session?.user?.email && (
                  <>
                    <DropdownMenuItem className="font-medium text-sm text-gray-500 px-3 py-2">
                      {session.user.email}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/user/profile" className="w-full">
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={(e) => {
                      e.preventDefault();
                      onOpenCommandPalette?.();
                    }}>
                      <div className="flex items-center justify-between w-full">
                        <span>Search</span>
                        <kbd className="bg-muted text-muted-foreground rounded px-1.5 text-xs">⌘K</kbd>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <SignOutButton inDropdown />
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
    )
  }

  // Regular user header (unchanged)
  return (
    <header className="border-b bg-slate-800">
      <div className="flex items-center justify-between px-4 h-16">
        <div className="flex items-center space-x-4 md:space-x-8 overflow-x-auto scrollbar-hide">
          <Link href={homeLink} className="flex items-center flex-shrink-0">
            <Image
              src="/images/logo_icon_minimal.svg"
              alt="LIFT GYM"
              width={48}
              height={48}
              className="invert"
              priority
            />
          </Link>

          <nav className="flex items-center space-x-4 overflow-x-auto scrollbar-hide min-w-max">
            <Link
              href="/guest/calendar"
              className="text-white hover:text-gray-300 transition-colors whitespace-nowrap"
            >
              Calendar
            </Link>
            {/* Add other client navigation links here */}
          </nav>
        </div>

        <div className="flex items-center space-x-4">
          {session ? (
            <>
              <NotificationsPopover userId={session.user.id || ''} />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white hover:text-gray-300 font-bold w-10 h-10 flex-shrink-0"
                  >
                    {userInitial ? (
                      <span className="text-lg font-bold">
                        {userInitial}
                      </span>
                    ) : (
                      <User className="h-5 w-5" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="w-64 z-50"
                  sideOffset={8}
                  alignOffset={0}
                  forceMount
                >
                  {session?.user?.email && (
                    <>
                      <DropdownMenuItem className="font-medium text-sm text-gray-500 px-3 py-2">
                        {session.user.email}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {isAdmin && (
                        <DropdownMenuItem asChild>
                          <Link href="/admin/dashboard" className="w-full">
                            Admin Dashboard
                          </Link>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem asChild>
                        <Link href="/user/profile" className="w-full">
                          Profile
                        </Link>
                      </DropdownMenuItem>
                      <SignOutButton inDropdown />
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <Button variant="ghost" className="text-white hover:text-gray-300" asChild>
              <Link href="/auth">Sign In</Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}