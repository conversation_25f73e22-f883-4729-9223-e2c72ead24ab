// components/layout/AdminSidebar.tsx
'use client'

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AdminSidebarNav } from './AdminSidebarNav';
import { Button } from '@/components/ui/button';

interface AdminSidebarProps {
  className?: string;
  sidebarExpanded: boolean;
  setSidebarExpanded: (expanded: boolean) => void;
}

export function AdminSidebar({ 
  className, 
  sidebarExpanded, 
  setSidebarExpanded 
}: AdminSidebarProps) {
  return (
    <div
      className={cn(
        "flex flex-col h-screen bg-slate-800 text-white transition-all duration-300 border-r border-slate-700",
        sidebarExpanded ? "w-[250px]" : "w-[70px]",
        className || ""
      )}
    >
      <div className="flex items-center justify-between p-4 border-b border-slate-700">
        <h2 className={cn("font-semibold", sidebarExpanded ? "block" : "hidden")}>Admin Panel</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarExpanded(!sidebarExpanded)}
          className="text-white hover:bg-slate-700"
          title={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-600">
        <AdminSidebarNav 
          collapsed={!sidebarExpanded} 
          setCollapsed={(collapsed) => setSidebarExpanded(!collapsed)} 
        />
      </div>
    </div>
  );
}