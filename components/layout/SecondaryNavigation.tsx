'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

const clientLinks = [
  { href: '/user/profile', title: 'My Profile' },
  { href: '/user/exercise-records', title: 'Training Log' },
  { href: '/user/session-book', title: 'Book Sessions' },
  { href: '/user/wods', title: "Today's WOD" },
  { href: '/user/weight-tracker', title: 'Weight Tracker' },
  { href: '/user/goals', title: 'My Goals' },
]

interface SecondaryNavigationProps {
  isAdmin?: boolean
}

export function SecondaryNavigation({ isAdmin }: SecondaryNavigationProps) {
  const pathname = usePathname();

  console.log('SecondaryNavigation rendered with:', { isAdmin });

  // No need to render for admin users as they'll use the sidebar
  if (isAdmin) {
    return null;
  }

  return (
    <nav className="border-b bg-white overflow-x-auto scrollbar-hide">
      <div className="flex items-center px-4 h-12 space-x-6 min-w-max">
        {clientLinks.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={cn(
              "text-sm font-medium transition-colors hover:text-primary whitespace-nowrap",
              pathname === link.href
                ? "text-primary"
                : "text-muted-foreground"
            )}
          >
            {link.title}
          </Link>
        ))}
      </div>
    </nav>
  );
}
