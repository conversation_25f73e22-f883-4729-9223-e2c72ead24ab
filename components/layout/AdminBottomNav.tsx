// components/layout/AdminBottomNav.tsx
'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Menu } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getBottomNavItems } from '@/components/admin/navigationData'

interface AdminBottomNavProps {
  onOpenDrawer: () => void;
}

export function AdminBottomNav({ onOpenDrawer }: AdminBottomNavProps) {
  const pathname = usePathname()
  const navItems = getBottomNavItems()
  
  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 md:hidden">
      <div className="grid grid-cols-5 h-16">
        {navItems.map((item, i) => {
          const isActive = pathname === item.href
          const Icon = item.icon
          
          return (
            <Link
              key={i}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center h-full",
                isActive ? "text-primary" : "text-muted-foreground"
              )}
            >
              <Icon className={cn("h-5 w-5 mb-1")} />
              <span className="text-xs">{item.label}</span>
            </Link>
          )
        })}
        
        <button
          onClick={onOpenDrawer}
          className="flex flex-col items-center justify-center h-full text-muted-foreground"
        >
          <Menu className="h-5 w-5 mb-1" />
          <span className="text-xs">Menu</span>
        </button>
      </div>
    </nav>
  )
}