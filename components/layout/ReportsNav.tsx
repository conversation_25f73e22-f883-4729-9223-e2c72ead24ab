'use client'

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { BarChart2, TrendingUp, Users, DollarSign, Calendar, Activity } from 'lucide-react';

const reportLinks = [
  {
    title: "Dashboard",
    href: "/admin/reports/dashboard",
    icon: BarChart2
  },
  {
    title: "Financial",
    href: "/admin/reports/financial",
    icon: DollarSign
  },
  {
    title: "Members",
    href: "/admin/reports/members",
    icon: Users
  },
  {
    title: "Programs",
    href: "/admin/reports/programs",
    icon: Activity
  },
  {
    title: "Monthly",
    href: "/admin/reports/monthly",
    icon: Calendar
  },
  {
    title: "New Members",
    href: "/admin/reports/new-members",
    icon: TrendingUp
  }
];

export function ReportsNav() {
  const pathname = usePathname();
  
  return (
    <nav className="space-y-1">
      {reportLinks.map((link) => {
        const isActive = pathname === link.href;
        const Icon = link.icon;
        
        return (
          <Link
            key={link.href}
            href={link.href}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-muted hover:text-foreground"
            )}
          >
            <Icon className="mr-3 h-5 w-5" />
            {link.title}
          </Link>
        );
      })}
    </nav>
  );
}