'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, Filter } from 'lucide-react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

type Tag = Database['public']['Tables']['tags']['Row'];

interface TagFilterProps {
  onTagsChange: (selectedTags: Tag[]) => void;
  className?: string;
}

const TagFilter: React.FC<TagFilterProps> = ({ 
  onTagsChange,
  className = ''
}) => {
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [open, setOpen] = useState(false);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    const fetchTags = async () => {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching tags:', error);
        return;
      }

      if (data) {
        setAvailableTags(data);
      }
    };

    fetchTags();
  }, [supabase]);

  const toggleTag = (tag: Tag) => {
    setSelectedTags(prev => {
      const isSelected = prev.some(t => t.id === tag.id);
      const newTags = isSelected
        ? prev.filter(t => t.id !== tag.id)
        : [...prev, tag];
      
      onTagsChange(newTags);
      return newTags;
    });
  };

  const clearFilters = () => {
    setSelectedTags([]);
    onTagsChange([]);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter by Tags
            {selectedTags.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {selectedTags.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-2" align="start">
          <div className="space-y-2">
            {availableTags.map((tag) => (
              <div
                key={tag.id}
                onClick={() => toggleTag(tag)}
                className="flex items-center justify-between px-2 py-1 rounded-md hover:bg-secondary cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: tag.color }}
                  />
                  <span>{tag.name}</span>
                </div>
                {selectedTags.some(t => t.id === tag.id) && (
                  <Check className="h-4 w-4" />
                )}
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {selectedTags.length > 0 && (
        <Button 
          variant="ghost" 
          size="sm"
          onClick={clearFilters}
        >
          Clear
        </Button>
      )}

      {selectedTags.map((tag) => (
        <Badge
          key={tag.id}
          style={{ 
            backgroundColor: tag.color,
            color: 'white'
          }}
          className="flex items-center gap-1"
        >
          {tag.name}
          <span 
            className="cursor-pointer ml-1" 
            onClick={() => toggleTag(tag)}
          >
            ×
          </span>
        </Badge>
      ))}
    </div>
  );
};

export default TagFilter;   