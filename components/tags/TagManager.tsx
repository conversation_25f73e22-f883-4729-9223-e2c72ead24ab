import React, { useState, useEffect, useCallback } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { X, Plus, Check, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';


type Tag = Database['public']['Tables']['tags']['Row'];

interface TagManagerProps {
  pelateId: string;
  onTagsChange?: () => void;
  className?: string;
  initialTags?: Tag[];
}

const TagManager = ({ 
  pelateId, 
  onTagsChange, 
  className = '',
  initialTags 
}: TagManagerProps) => {
  const [currentTags, setCurrentTags] = useState<Tag[]>([]);
  const [availableTags, setAvailableTags] = useState<Tag[]>(initialTags || []);
  const [isLoading, setIsLoading] = useState(!initialTags);
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isNewTagDialogOpen, setIsNewTagDialogOpen] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#000000');
  
  const supabase = createClientComponentClient<Database>();

  const fetchTags = useCallback(async () => {
    if (!pelateId) return;
    
    try {
      const [tagsResponse, currentTagsResponse] = await Promise.all([
        !initialTags ? supabase.from('tags').select('*').order('name') : null,
        supabase
          .from('pelates_tags')
          .select(`
            tags (
              id,
              name,
              color,
              created_at
            )
          `)
          .eq('pelatis_id', pelateId)
      ]);

      // Ensure we include created_at in the mapping
      if (currentTagsResponse.data) {
        const currentTagsList = currentTagsResponse.data
          .map(relation => relation.tags)
          .filter((tag): tag is NonNullable<Tag> => tag !== null);
        setCurrentTags(currentTagsList);
      }

      if (tagsResponse?.data) {
        setAvailableTags(tagsResponse.data);
      }

    } catch (error) {
      console.error('Error in fetchTags:', error);
    } finally {
      setIsLoading(false);
    }
  }, [pelateId, supabase, initialTags]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);


  const addTag = async (tagToAdd: Tag) => {
    try {
      // Check if the relationship already exists
      const { data: existing } = await supabase
        .from('pelates_tags')
        .select()
        .eq('pelatis_id', pelateId)
        .eq('tag_id', tagToAdd.id)
        .single();
  
      if (existing) {
        console.log('Tag already exists for this pelate');
        return;
      }
  
      const { error } = await supabase
        .from('pelates_tags')
        .insert({
          pelatis_id: pelateId,
          tag_id: tagToAdd.id
        });
  
      if (error) throw error;
  
      await fetchTags();
      onTagsChange?.();
      setOpen(false);
      setSearchValue('');
      
    } catch (error) {
      console.error('Error in addTag:', error);
    }
  };

  const removeTag = async (tagId: string) => {
    try {
      const { error } = await supabase
        .from('pelates_tags')
        .delete()
        .eq('pelatis_id', pelateId)
        .eq('tag_id', tagId);

      if (error) throw error;
      
      await fetchTags();
      onTagsChange?.();
    } catch (error) {
      console.error('Error removing tag:', error);
    }
  };

  const createNewTag = async () => {
    try {
      const { data: newTag, error } = await supabase
        .from('tags')
        .insert({
          name: newTagName,
          color: newTagColor,
          created_at: new Date().toISOString()  // Include created_at
        })
        .select()
        .single();

      if (error) throw error;

      if (newTag) {
        setAvailableTags(prev => [...prev, newTag]);
        setNewTagName('');
        setNewTagColor('#000000');
        setIsNewTagDialogOpen(false);
        await addTag(newTag);
      }
    } catch (error) {
      console.error('Error creating new tag:', error);
    }
  };
  const filteredTags = availableTags.filter(tag => 
    !currentTags.some(ct => ct.id === tag.id) && 
    tag.name.toLowerCase().includes(searchValue.toLowerCase())
  );


  return (
    <div className={`flex flex-wrap gap-2 items-center ${className}`}>
      {currentTags?.map((tag) => (
        <Badge
          key={tag.id}
          style={{ backgroundColor: tag.color }}
          className="flex items-center gap-1 text-white"
        >
          {tag.name}
          <X
            className="h-3 w-3 cursor-pointer hover:opacity-75"
            onClick={() => removeTag(tag.id)}
          />
        </Badge>
      ))}
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setOpen(true)}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Plus className="h-4 w-4 mr-1" />
            )}
            Add Tag
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Search tags..." 
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList>
              {filteredTags.length === 0 ? (
                <CommandEmpty>
                  <div className="py-2 px-4">
                    <p className="text-sm text-gray-500 mb-2">No tags found.</p>
                    <Dialog open={isNewTagDialogOpen} onOpenChange={setIsNewTagDialogOpen}>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full"
                        >
                          Create New Tag
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create New Tag</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="name" className="text-right">
                              Name
                            </Label>
                            <Input
                              id="name"
                              value={newTagName}
                              onChange={(e) => setNewTagName(e.target.value)}
                              className="col-span-3"
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="color" className="text-right">
                              Color
                            </Label>
                            <Input
                              id="color"
                              type="color"
                              value={newTagColor}
                              onChange={(e) => setNewTagColor(e.target.value)}
                              className="col-span-3"
                            />
                          </div>
                        </div>
                        <Button onClick={createNewTag}>
                          Create Tag
                        </Button>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredTags.map((tag) => (
                    <CommandItem
                      key={tag.id}
                      onSelect={() => addTag(tag)}
                    >
                      <div className="flex items-center flex-1">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: tag.color }}
                        />
                        {tag.name}
                      </div>
                      <Check
                        className="h-4 w-4 opacity-0 group-data-[state=selected]:opacity-100"
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default TagManager;