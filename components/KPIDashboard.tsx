import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

const MARCH_2024 = '2024-03-01';

type MonthlyKPIMetrics = {
  month: string;
  total_members: number;
  total_checkins: number;
  unique_sessions: number;
  mrr: number;
  churned_members: number;
  churn_rate: number;
  retained_members: number;
  mrr_growth_rate: number;
}

type KPIMetrics = {
  mrr: number;
  mrrChange: number;
  totalMembers: number;
  memberChange: number;
  retentionRate: number;
  retentionChange: number;
  avgClassAttendance: number;
  attendanceChange: number;
  classCapacity: number;
  capacityChange: number;
  revenuePerMember: number;
  churnRate: number;
  churnChange: number;
  totalChurned: number;
  cac: number;
  ltv: number;
  mrrGrowthRate: number;
  arr: number;
  ltvCacRatio: number;
}

const KPIDashboard = () => {
  const [metrics, setMetrics] = useState<KPIMetrics>({
    mrr: 0,
    mrrChange: 0,
    totalMembers: 0,
    memberChange: 0,
    retentionRate: 0,
    retentionChange: 0,
    avgClassAttendance: 0,
    attendanceChange: 0,
    classCapacity: 0,
    capacityChange: 0,
    churnRate: 0,
    churnChange: 0,
    totalChurned: 0,
    revenuePerMember: 0,
    cac: 0,
    ltv: 0,
    mrrGrowthRate: 0,
    arr: 0,
    ltvCacRatio: 0
  });
  const [historicalData, setHistoricalData] = useState<MonthlyKPIMetrics[]>([]);
  const supabase = createClientComponentClient<Database>();

  const calculateChange = (curr: number | null | undefined, prev: number | null | undefined): number => {
    if (typeof curr !== 'number' || typeof prev !== 'number' || prev === 0) return 0;
    return ((curr - prev) / prev) * 100;
  };

  const fetchHistoricalKPIs = async () => {
    try {
      const { data: monthlyRevenue } = await supabase
        .from('monthly_revenue_view')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      const { data: monthlyAttendance } = await supabase
        .from('monthly_attendance_view')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      const { data: retentionData } = await supabase
        .from('monthly_retention_view')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      const { data: capacityData } = await supabase
        .from('class_capacity_metrics')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      if (!monthlyRevenue?.length) throw new Error('No revenue data found');

  // Add churn data fetch
  const { data: churnData } = await supabase
  .from('monthly_churn_metrics')
  .select('*')
  .gte('month', MARCH_2024)
  .order('month', { ascending: false });

if (!monthlyRevenue?.length) throw new Error('No revenue data found');
      const combinedData: MonthlyKPIMetrics[] = monthlyRevenue.map((revenue, index) => {
        const prevMRR = monthlyRevenue[index + 1]?.mrr || 0;
        const currentMRR = revenue.mrr || 0;
        const mrrGrowthRate = prevMRR === 0 ? 0 : ((currentMRR - prevMRR) / prevMRR) * 100;

        return {
          month: revenue.month || '',
          mrr: revenue.mrr || 0,
          total_members: retentionData?.[index]?.total_members || 0,
          total_checkins: monthlyAttendance?.[index]?.total_checkins || 0,
          unique_sessions: monthlyAttendance?.[index]?.unique_sessions || 0,
          retained_members: retentionData?.[index]?.retained_members || 0,
          churned_members: churnData?.[index]?.churned_members || 0,
          churn_rate: churnData?.[index]?.churn_rate || 0,
          mrr_growth_rate: mrrGrowthRate
        };
      });

      setHistoricalData(combinedData);

      const current = combinedData[0];
      const previous = combinedData[1];

      const currentRetentionRate = current.total_members ? 
        (current.retained_members / current.total_members) : 0;
      const previousRetentionRate = previous?.total_members ? 
        (previous.retained_members / previous.total_members) : 0;

      const currentAttendanceRate = current.unique_sessions ? 
        current.total_checkins / current.unique_sessions : 0;
      const previousAttendanceRate = previous?.unique_sessions ? 
        previous.total_checkins / previous.unique_sessions : 0;

      // Add new query for acquisition costs
      const { data: acquisitionCosts, error: acquisitionError } = await supabase
        .from('marketing_costs')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      if (acquisitionError) throw acquisitionError;

      // Add new query for customer lifetime data
      const { data: lifetimeData } = await supabase
        .from('customer_lifetime_metrics')
        .select('*')
        .gte('month', MARCH_2024)
        .order('month', { ascending: false });

      // Calculate CAC (total marketing costs / new customers)
      const totalMarketingCosts = acquisitionCosts?.[0]?.total_costs ?? 0;
      const newCustomersCount = acquisitionCosts?.[0]?.new_customers ?? 1;
      const calculatedCAC = totalMarketingCosts / newCustomersCount;

      // Calculate LTV (average revenue per user * average customer lifespan)
      const avgMonthlyRevenue = current.mrr / current.total_members;
      const avgLifespanMonths = lifetimeData?.[0]?.avg_lifespan_months || 0;
      const calculatedLTV = avgMonthlyRevenue * avgLifespanMonths;

      // Calculate MRR Growth Rate
      const mrrGrowthRate = calculateChange(current.mrr, previous?.mrr);

      // Calculate ARR (MRR * 12)
      const calculatedARR = current.mrr * 12;

      // Calculate LTV:CAC ratio
      const ltvCacRatio = calculatedCAC > 0 ? calculatedLTV / calculatedCAC : 0;

      setMetrics({
        mrr: current.mrr,
        mrrChange: calculateChange(current.mrr, previous?.mrr),
        totalMembers: current.total_members,
        memberChange: calculateChange(current.total_members, previous?.total_members),
        retentionRate: currentRetentionRate * 100,
        retentionChange: calculateChange(currentRetentionRate, previousRetentionRate),
        avgClassAttendance: currentAttendanceRate,
        attendanceChange: calculateChange(currentAttendanceRate, previousAttendanceRate),
        classCapacity: capacityData?.[0]?.capacity_rate || 0,
        capacityChange: calculateChange(
          capacityData?.[0]?.capacity_rate || 0,
          capacityData?.[1]?.capacity_rate || 0,
        ),
        churnRate: current.churn_rate,
        churnChange: calculateChange(
          current.churn_rate,
          previous?.churn_rate
        ),
        totalChurned: current.churned_members,
        revenuePerMember: current.total_members ? 
          current.mrr / current.total_members : 0,
        cac: calculatedCAC,
        ltv: calculatedLTV,
        mrrGrowthRate: mrrGrowthRate,
        arr: calculatedARR,
        ltvCacRatio: ltvCacRatio
      });

    } catch (error) {
      console.error('Error fetching KPI data:', error);
    }
  };

  useEffect(() => {
    fetchHistoricalKPIs();
    const interval = setInterval(fetchHistoricalKPIs, 300000);
    return () => clearInterval(interval);
  }, []);

  return { metrics, historicalData };
};

export default KPIDashboard;
