import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Play,
  Settings,
  Users,
  Maximize,
  Minimize,
  Wifi
} from 'lucide-react';
import NetworkSync, { NetworkSyncHandle } from './NetworkSync';
import TimerDisplay from './TimerDisplay';
import CountdownDisplay from './CountdownDisplay';
import TimerControls from './TimerControls';
import TimerSettingsComponent from './TimerSettings';
import FullscreenTimer from './FullscreenTimer';
import FullscreenCountdown from './FullscreenCountdown';
import KeyboardShortcutsInfo from './KeyboardShortcutsInfo';

// Define types for the timer state and settings
interface TimerState {
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  currentSet: number;
  phase: 'work' | 'rest';
}

interface TimerSettingsData {
  sets: number;
  workTime: number;
  restTime: number;
}

// Interface to match NetworkSync's TimerSettings
interface TimerSettings {
  rounds: number;
  workTime: number;
  restTime: number;
  prepareTime: number;
  intervals: number;
}

const CrossfitTimer: React.FC = () => {
  // Core state variables
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [time, setTime] = useState(0);
  const [currentSet, setCurrentSet] = useState(1);
  const [phase, setPhase] = useState<'work' | 'rest'>('work');
  const [isMuted, setIsMuted] = useState(false);
  const [countdownActive, setCountdownActive] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [textColor, setTextColor] = useState('#000000');
  const [activeTab, setActiveTab] = useState<'timer' | 'settings' | 'sync'>('timer');
  const [isFullscreenSupported, setIsFullscreenSupported] = useState(true);
  const [connectedRoom, setConnectedRoom] = useState<string | null>(null);

  // Settings
  const [settings, setSettings] = useState<TimerSettingsData>({
    sets: 3,
    workTime: 60,
    restTime: 30
  });

  // Refs
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerContainerRef = useRef<HTMLDivElement>(null);
  const networkSyncRef = useRef<NetworkSyncHandle>(null);

  // Derive current timer state for sharing
  const timerState: TimerState = {
    isRunning,
    isPaused,
    time,
    currentSet,
    phase
  };

  useEffect(() => {
    // Check if fullscreen is supported using type declarations for cross-browser support
    interface FullscreenDocument extends Document {
      webkitRequestFullscreen?: () => Promise<void>;
      mozRequestFullScreen?: () => Promise<void>;
      msRequestFullscreen?: () => Promise<void>;
    }

    const doc = document as FullscreenDocument;
    const fullscreenSupported =
      doc.documentElement.requestFullscreen ||
      doc.webkitRequestFullscreen ||
      doc.mozRequestFullScreen ||
      doc.msRequestFullscreen;

    setIsFullscreenSupported(!!fullscreenSupported);
  }, []);

  const playSound = useCallback(() => {
    if (!isMuted && audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(err => console.error('Error playing sound', err));
    }
  }, [isMuted]);

  useEffect(() => {
    // Initialize audio
    audioRef.current = new Audio('/beep.mp3');

    // Try to preload the audio
    if (audioRef.current) {
      audioRef.current.preload = 'auto';
      audioRef.current.load();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isRunning && !countdownActive) {
      intervalRef.current = setInterval(() => {
        setTime(prev => {
          if (prev <= 1) {
            playSound();

            if (phase === 'work') {
              setPhase('rest');
              return settings.restTime;
            } else {
              if (currentSet >= settings.sets) {
                clearInterval(intervalRef.current!);
                setIsRunning(false);
                return 0;
              }
              setCurrentSet(prev => prev + 1);
              setPhase('work');
              return settings.workTime;
            }
          }

          if (prev === 4) {
            playSound();
          }

          return prev - 1;
        });
      }, 1000);
    } else if (countdownActive) {
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!);
            setCountdownActive(false);
            playSound();

            // Initialize the actual timer after countdown
            setIsRunning(true);
            setPhase('work');
            setTime(settings.workTime);

            return 0;
          }
          playSound();
          return prev - 1;
        });
      }, 1000);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, isPaused, countdownActive, phase, currentSet, settings, playSound]);

  // Timer control functions
  const startTimer = useCallback(() => {
    // Reset timer state
    setPhase('work');
    setCurrentSet(1);
    setTime(settings.workTime);

    // Start the countdown
    setCountdown(5);
    setCountdownActive(true);

    // If we're the master timer, broadcast start to all followers
    const room = networkSyncRef.current?.getCurrentRoom();
    if (room?.isMaster) {
      // Convert TimerSettingsData to TimerSettings
      const timerSettings: TimerSettings = {
        rounds: settings.sets,
        workTime: settings.workTime,
        restTime: settings.restTime,
        prepareTime: 5, // Default value
        intervals: 1    // Default value
      };
      networkSyncRef.current?.startTimers(timerSettings);
    }

    // Switch to timer tab when starting
    setActiveTab('timer');
  }, [settings]);

  // New handler to receive start commands from master
  const handleTimerStart = useCallback(() => {
    // This gets called when a follower receives a start command from master
    startTimer();
  }, [startTimer]);

  // Handler for network timer state changes
  const handleTimerStateChange = useCallback((networkState: {
    isRunning: boolean;
    isPaused: boolean;
    time: number;
    currentSet: number;
    phase: string;
  }) => {
    // Update local timer state when receiving updates from network
    setIsRunning(networkState.isRunning);
    setIsPaused(networkState.isPaused);
    setTime(networkState.time);
    setCurrentSet(networkState.currentSet);
    // Ensure phase is either 'work' or 'rest'
    setPhase(networkState.phase === 'work' || networkState.phase === 'rest'
      ? networkState.phase as 'work' | 'rest'
      : 'work');
  }, []);

  const pauseTimer = useCallback(() => {
    setIsRunning(false);
    setIsPaused(true);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Update network state if connected
    if (networkSyncRef.current) {
      networkSyncRef.current.broadcastTimerState({
        ...timerState,
        isRunning: false,
        isPaused: true
      });
    }
  }, [timerState]);

  const resumeTimer = useCallback(() => {
    setIsRunning(true);
    setIsPaused(false);

    // Update network state if connected
    if (networkSyncRef.current) {
      networkSyncRef.current.broadcastTimerState({
        ...timerState,
        isRunning: true,
        isPaused: false
      });
    }
  }, [timerState]);

  const resetTimer = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    setPhase('work');
    setTime(settings.workTime);
    setCurrentSet(1);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Update network state if connected
    if (networkSyncRef.current) {
      networkSyncRef.current.broadcastTimerState({
        isRunning: false,
        isPaused: false,
        time: settings.workTime,
        currentSet: 1,
        phase: 'work'
      });
    }
  }, [settings.workTime]);

  const toggleMute = useCallback(() => {
    setIsMuted(prev => !prev);
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!isFullscreenSupported) return;

    // Define interfaces for cross-browser fullscreen support
    interface FullscreenElement extends HTMLDivElement {
      webkitRequestFullscreen?: () => Promise<void>;
      mozRequestFullScreen?: () => Promise<void>;
      msRequestFullscreen?: () => Promise<void>;
    }

    interface FullscreenDocument extends Document {
      webkitExitFullscreen?: () => Promise<void>;
      mozCancelFullScreen?: () => Promise<void>;
      msExitFullscreen?: () => Promise<void>;
      webkitFullscreenElement?: Element | null;
      mozFullScreenElement?: Element | null;
      msFullscreenElement?: Element | null;
    }

    const fsElement = timerContainerRef.current as FullscreenElement | null;
    const fsDoc = document as FullscreenDocument;

    if (!isFullscreen) {
      try {
        if (fsElement?.requestFullscreen) {
          fsElement.requestFullscreen();
        } else if (fsElement?.webkitRequestFullscreen) {
          fsElement.webkitRequestFullscreen();
        } else if (fsElement?.mozRequestFullScreen) {
          fsElement.mozRequestFullScreen();
        } else if (fsElement?.msRequestFullscreen) {
          fsElement.msRequestFullscreen();
        }
        setIsFullscreen(true);
      } catch (err) {
        console.error('Could not enter fullscreen mode:', err);
      }
    } else {
      try {
        if (fsDoc.exitFullscreen) {
          fsDoc.exitFullscreen();
        } else if (fsDoc.webkitExitFullscreen) {
          fsDoc.webkitExitFullscreen();
        } else if (fsDoc.mozCancelFullScreen) {
          fsDoc.mozCancelFullScreen();
        } else if (fsDoc.msExitFullscreen) {
          fsDoc.msExitFullscreen();
        }
        setIsFullscreen(false);
      } catch (err) {
        console.error('Could not exit fullscreen mode:', err);
      }
    }
  }, [isFullscreen, isFullscreenSupported]);

  // Add keyboard event listeners
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return; // Don't trigger when typing in form elements
      }

      switch (e.key.toLowerCase()) {
        case ' ':
          if (isRunning) pauseTimer();
          else if (isPaused) resumeTimer();
          else startTimer();
          break;
        case 'r':
          resetTimer();
          break;
        case 'm':
          toggleMute();
          break;
        case 'f':
          toggleFullscreen();
          break;
        case 'escape':
          if (isFullscreen) toggleFullscreen();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isRunning, isPaused, isFullscreen, pauseTimer, resumeTimer, startTimer, resetTimer, toggleMute, toggleFullscreen]);

  // Enhanced sync handling
  useEffect(() => {
    // When connection status changes to connected, immediately send current state
    if (networkSyncRef.current && isRunning) {
      // Small delay to ensure other clients are ready to receive
      const syncTimer = setTimeout(() => {
        networkSyncRef.current?.broadcastTimerState({
          isRunning,
          isPaused,
          time,
          currentSet,
          phase
        });
      }, 500);

      return () => clearTimeout(syncTimer);
    }

    // Return empty cleanup function for code paths where we don't set a timer
    return () => {};
  }, [isRunning, isPaused, time, currentSet, phase]);

  // Handle settings changes from UI
  const handleSettingsChange = useCallback((newSettings: TimerSettingsData) => {
    setSettings(newSettings);

    // If timer is not running, update the display time based on new work time
    if (!isRunning && !isPaused) {
      setTime(newSettings.workTime);
    }

    // Update network state if connected
    if (networkSyncRef.current) {
      // Convert TimerSettingsData to TimerSettings
      const timerSettings: TimerSettings = {
        rounds: newSettings.sets,
        workTime: newSettings.workTime,
        restTime: newSettings.restTime,
        prepareTime: 5, // Default value
        intervals: 1    // Default value
      };
      networkSyncRef.current.broadcastSettings(timerSettings);
    }
  }, [isRunning, isPaused]);

  // Handle settings changes from network
  const handleNetworkSettingsChange = useCallback((networkSettings: {
    rounds?: number;
    workTime?: number;
    restTime?: number;
    prepareTime?: number;
    intervals?: number;
  }) => {
    // Convert network settings to our local format
    const localSettings: TimerSettingsData = {
      sets: networkSettings.rounds || 3,
      workTime: networkSettings.workTime || 60,
      restTime: networkSettings.restTime || 30
    };

    setSettings(localSettings);

    // If timer is not running, update the display time
    if (!isRunning && !isPaused) {
      setTime(localSettings.workTime);
    }
  }, [isRunning, isPaused]);

  const handleRoomConnection = useCallback((roomId: string | null) => {
    setConnectedRoom(roomId);
    // When joining a new room, update the tab to the timer
    if (roomId) {
      setActiveTab('timer');
    }
  }, []);

  // Listen for fullscreen change events
  useEffect(() => {
    // Define interface for cross-browser fullscreen support
    interface FullscreenDocument extends Document {
      webkitFullscreenElement?: Element | null;
      mozFullScreenElement?: Element | null;
      msFullscreenElement?: Element | null;
    }

    const handleFullscreenChange = () => {
      const doc = document as FullscreenDocument;
      setIsFullscreen(
        doc.fullscreenElement !== null ||
        doc.webkitFullscreenElement !== null ||
        doc.mozFullScreenElement !== null ||
        doc.msFullscreenElement !== null
      );
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Render fullscreen button
  const renderFullscreenButton = () => {
    if (!isFullscreenSupported) {
      return (
        <Button
          variant="ghost"
          className="absolute top-4 right-4 z-10 opacity-50"
          disabled={true}
          title="Fullscreen not supported in your browser"
        >
          <Maximize className="h-6 w-6" />
        </Button>
      );
    }

    return (
      <Button
        onClick={toggleFullscreen}
        variant="ghost"
        className="absolute top-4 right-4 z-10"
        aria-label={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
      >
        {isFullscreen ? <Minimize className="h-6 w-6" /> : <Maximize className="h-6 w-6" />}
      </Button>
    );
  };

  // Render the connected room indicator
  const renderConnectedRoomIndicator = () => {
    if (!connectedRoom) return null;

    return (
      <div className="flex items-center justify-center p-3 mb-4 bg-green-100 border border-green-200 rounded-md">
        <Wifi className="h-4 w-4 mr-2 text-green-600" />
        <span className="text-sm text-green-800">Connected to room: <strong>{connectedRoom}</strong></span>
      </div>
    );
  };

  // Render fullscreen components when in fullscreen mode
  if (isFullscreen && !countdownActive) {
    return (
      <FullscreenTimer
        time={time}
        phase={phase}
        currentSet={currentSet}
        totalSets={settings.sets}
        isRunning={isRunning}
        isPaused={isPaused}
        isMuted={isMuted}
        workTime={settings.workTime}
        restTime={settings.restTime}
        onToggleFullscreen={toggleFullscreen}
        onPlay={isRunning ? pauseTimer : isPaused ? resumeTimer : startTimer}
        onPause={pauseTimer}
        onReset={resetTimer}
        onToggleMute={toggleMute}
        connectedRoom={connectedRoom}
      />
    );
  }

  // Render fullscreen countdown
  if (isFullscreen && countdownActive) {
    return <FullscreenCountdown countdown={countdown} />;
  }

  // Add a dedicated fullscreen button for tablet users
  const renderFullscreenControls = () => {
    return (
      <div className="mt-4 flex justify-center">
        <Button
          onClick={toggleFullscreen}
          variant="outline"
          className="flex items-center gap-2"
          aria-label={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
        >
          {isFullscreen ? (
            <>
              <Minimize className="h-4 w-4" />
              <span>Exit Fullscreen</span>
            </>
          ) : (
            <>
              <Maximize className="h-4 w-4" />
              <span>Fullscreen</span>
            </>
          )}
        </Button>
      </div>
    );
  };

  return (
    <Card
      ref={timerContainerRef}
      className="w-full max-w-md mx-auto shadow-md"
      style={{
        backgroundColor,
        color: textColor,
      }}
    >
      {renderFullscreenButton()}

      <CardHeader>
        <CardTitle className="text-center">Workout Timer</CardTitle>
      </CardHeader>

      <CardContent>
        {/* Always show connected room indicator if there is a connected room */}
        {connectedRoom && renderConnectedRoomIndicator()}

        {countdownActive ? (
          <div className="flex-1 flex items-center justify-center">
            <CountdownDisplay
              countdown={countdown}
              isFullscreen={false}
            />
          </div>
        ) : (
          <>
            {!isRunning && !isPaused ? (
              <Tabs
                defaultValue="timer"
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as 'timer' | 'settings' | 'sync')}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-3 mb-4">
                  <TabsTrigger value="timer" className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    <span className="hidden sm:inline">Timer</span>
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">Settings</span>
                  </TabsTrigger>
                  <TabsTrigger value="sync" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    {connectedRoom ? (
                      <div className="flex items-center">
                        <span className="hidden sm:inline">Sync</span>
                        <span className="ml-2 flex h-2 w-2">
                          <span className="animate-ping absolute h-2 w-2 rounded-full bg-green-400 opacity-75"></span>
                          <span className="relative rounded-full h-2 w-2 bg-green-500"></span>
                        </span>
                      </div>
                    ) : (
                      <span className="hidden sm:inline">Sync</span>
                    )}
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="timer" className="mt-0">
                  <div className="flex flex-col items-center">
                    <TimerDisplay
                      time={time}
                      phase={phase}
                      currentSet={currentSet}
                      totalSets={settings.sets}
                      workTime={settings.workTime}
                      restTime={settings.restTime}
                    />
                    <TimerControls
                      isRunning={isRunning}
                      isPaused={isPaused}
                      isMuted={isMuted}
                      onStart={startTimer}
                      onPause={pauseTimer}
                      onResume={resumeTimer}
                      onReset={resetTimer}
                      onToggleMute={toggleMute}
                    />
                    <KeyboardShortcutsInfo />
                    {/* Add the fullscreen button for better tablet access */}
                    {renderFullscreenControls()}
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="mt-0">
                  <TimerSettingsComponent
                    settings={settings}
                    onSettingsChange={handleSettingsChange}
                    isMuted={isMuted}
                    onToggleMute={toggleMute}
                    onStartTimer={startTimer}
                    backgroundColor={backgroundColor}
                    textColor={textColor}
                    onBackgroundColorChange={setBackgroundColor}
                    onTextColorChange={setTextColor}
                  />
                </TabsContent>

                <TabsContent value="sync" className="mt-0">
                  <NetworkSync
                    ref={networkSyncRef}
                    onTimerStart={handleTimerStart}
                    onTimerStateChange={handleTimerStateChange}
                    onSettingsChange={handleNetworkSettingsChange}
                    onRoomConnection={handleRoomConnection}
                  />
                </TabsContent>
              </Tabs>
            ) : (
              // When timer is running or paused
              <div className="flex flex-col items-center">
                <TimerDisplay
                  time={time}
                  phase={phase}
                  currentSet={currentSet}
                  totalSets={settings.sets}
                  workTime={settings.workTime}
                  restTime={settings.restTime}
                />
                <TimerControls
                  isRunning={isRunning}
                  isPaused={isPaused}
                  isMuted={isMuted}
                  onStart={startTimer}
                  onPause={pauseTimer}
                  onResume={resumeTimer}
                  onReset={resetTimer}
                  onToggleMute={toggleMute}
                />
                <KeyboardShortcutsInfo />
                {/* Add the fullscreen button for better tablet access */}
                {renderFullscreenControls()}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default CrossfitTimer;