import React from 'react';

interface FullscreenCountdownProps {
  countdown: number;
}

const FullscreenCountdown: React.FC<FullscreenCountdownProps> = ({ countdown }) => {
  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center">
      <div className="font-light text-[25vmin] tracking-wider text-white">
        {countdown}
      </div>
      <div className="mt-4 text-[4vmin] text-gray-400">
        Get Ready
      </div>
    </div>
  );
};

export default FullscreenCountdown;