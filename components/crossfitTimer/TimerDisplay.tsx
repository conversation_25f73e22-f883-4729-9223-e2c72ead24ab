import React from 'react';

interface TimerDisplayProps {
  time: number;
  phase: 'work' | 'rest';
  currentSet: number;
  totalSets: number;
  workTime: number;
  restTime: number;
  isFullscreen?: boolean;
}

// Format time as MM:SS
const formatTime = (timeInSeconds: number): string => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = timeInSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const TimerDisplay: React.FC<TimerDisplayProps> = ({
  time,
  phase,
  currentSet,
  totalSets,
  workTime,
  restTime,
  isFullscreen = false
}) => {
  return (
    <div className={`flex flex-col items-center justify-center ${isFullscreen ? 'h-full' : ''}`}>
      <div className={`relative ${isFullscreen ? 'w-[80vmin] h-[80vmin] max-w-[800px] max-h-[800px]' : 'w-72 h-72 sm:w-96 sm:h-96'}`}>
        {/* Dotted circle background */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
          {[...Array(60)].map((_, i) => {
            const angle = (i * 6) * Math.PI / 180;
            const x = 50 + 46 * Math.cos(angle);
            const y = 50 + 46 * Math.sin(angle);
            return (
              <circle 
                key={i}
                cx={x}
                cy={y}
                r="0.8"
                fill={phase === 'work' ? '#9AE6B4' : '#F56565'}
                opacity="0.3"
              />
            );
          })}
        </svg>
        
        {/* Progress arc */}
        <svg className="absolute inset-0 w-full h-full -rotate-90" viewBox="0 0 100 100">
          <path
            d={`M 50,50 m 0,-46
               a 46,46 0 1,1 0,92
               a 46,46 0 1,1 0,-92`}
            fill="none"
            stroke={phase === 'work' ? '#9AE6B4' : '#F56565'}
            strokeWidth="2"
            strokeDasharray={`${2 * Math.PI * 46}`}
            strokeDashoffset={`${2 * Math.PI * 46 * (1 - time / (phase === 'work' ? workTime : restTime))}`}
            strokeLinecap="round"
            style={{ transition: 'stroke-dashoffset 0.5s ease' }}
          />
        </svg>
        
        {/* Time display */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
          <div className={`${isFullscreen ? 'text-[15vmin]' : 'text-6xl sm:text-7xl'} font-light tracking-wider`}>
            {formatTime(time)}
          </div>
          <div className={`${isFullscreen ? 'text-[3vmin]' : 'text-sm sm:text-base'} text-gray-400 mt-2`}>
            {phase === 'work' ? 'WORK' : 'REST'} • Set {currentSet}/{totalSets}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimerDisplay;