import React from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, RotateCcw, Volume2, VolumeX, Minimize, Wifi } from 'lucide-react';

interface FullscreenTimerProps {
  time: number;
  phase: 'work' | 'rest';
  currentSet: number;
  totalSets: number;
  isRunning: boolean;
  isPaused: boolean;
  isMuted: boolean;
  workTime: number;
  restTime: number;
  connectedRoom?: string | null;
  onToggleFullscreen: () => void;
  onPlay: () => void;
  onPause: () => void;
  onReset: () => void;
  onToggleMute: () => void;
}

const formatTime = (timeInSeconds: number): string => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = timeInSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const FullscreenTimer: React.FC<FullscreenTimerProps> = ({
  time,
  phase,
  currentSet,
  totalSets,
  isRunning,
  isPaused,
  isMuted,
  workTime,
  restTime,
  connectedRoom,
  onToggleFullscreen,
  onPlay,
  onPause,
  onReset,
  onToggleMute
}) => {
  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center">
      {/* Connected room indicator */}
      {connectedRoom && (
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 flex items-center justify-center py-2 px-6 bg-green-100 rounded-full">
          <Wifi className="h-5 w-5 mr-2 text-green-600" />
          <span className="text-sm text-green-800">Connected to room: <strong>{connectedRoom}</strong></span>
        </div>
      )}
      
      {/* Exit fullscreen button */}
      <Button
        onClick={onToggleFullscreen}
        variant="ghost"
        className="absolute top-6 right-6 text-white/80 hover:text-white hover:bg-white/10"
        aria-label="Exit Fullscreen"
      >
        <Minimize className="h-8 w-8" />
      </Button>

      {/* Main timer display */}
      <div className="relative w-[80vmin] h-[80vmin] max-w-[800px] max-h-[800px]">
        {/* Dotted circle background */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
          {[...Array(60)].map((_, i) => {
            const angle = (i * 6) * Math.PI / 180;
            const x = 50 + 46 * Math.cos(angle);
            const y = 50 + 46 * Math.sin(angle);
            return (
              <circle 
                key={i}
                cx={x}
                cy={y}
                r="0.8"
                fill={phase === 'work' ? '#9AE6B4' : '#F56565'}
                opacity="0.3"
              />
            );
          })}
        </svg>
        
        {/* Progress arc */}
        <svg className="absolute inset-0 w-full h-full -rotate-90" viewBox="0 0 100 100">
          <path
            d={`M 50,50 m 0,-46
               a 46,46 0 1,1 0,92
               a 46,46 0 1,1 0,-92`}
            fill="none"
            stroke={phase === 'work' ? '#9AE6B4' : '#F56565'}
            strokeWidth="2"
            strokeDasharray={`${2 * Math.PI * 46}`}
            strokeDashoffset={`${2 * Math.PI * 46 * (1 - time / (phase === 'work' ? workTime : restTime))}`}
            strokeLinecap="round"
            style={{ transition: 'stroke-dashoffset 0.5s ease' }}
          />
        </svg>
        
        {/* Time display */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
          <div className="text-[18vmin] font-light tracking-wider text-white">
            {formatTime(time)}
          </div>
          <div className="text-[3.5vmin] text-gray-300 mt-2">
            {phase === 'work' ? 'WORK' : 'REST'} • Set {currentSet}/{totalSets}
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="fixed bottom-16 left-0 right-0 flex justify-center gap-8">
        <Button 
          onClick={onToggleMute} 
          variant="ghost" 
          className="rounded-full hover:bg-white/10 w-16 h-16 text-white/80"
          aria-label={isMuted ? "Unmute" : "Mute"}
        >
          {isMuted ? 
            <VolumeX className="h-8 w-8" /> : 
            <Volume2 className="h-8 w-8" />
          }
        </Button>

        {!isRunning && !isPaused ? (
          <Button 
            onClick={onPlay} 
            className="rounded-full bg-primary hover:bg-primary/90 w-24 h-24"
            aria-label="Start Timer"
          >
            <Play className="h-12 w-12" />
          </Button>
        ) : isRunning ? (
          <Button 
            onClick={onPause} 
            className="rounded-full bg-primary hover:bg-primary/90 w-24 h-24"
            aria-label="Pause Timer"
          >
            <Pause className="h-12 w-12" />
          </Button>
        ) : (
          <Button 
            onClick={onPlay} 
            className="rounded-full bg-primary hover:bg-primary/90 w-24 h-24"
            aria-label="Resume Timer"
          >
            <Play className="h-12 w-12" />
          </Button>
        )}

        <Button 
          onClick={onReset} 
          variant="ghost" 
          className="rounded-full hover:bg-white/10 w-16 h-16 text-white/80"
          aria-label="Reset Timer"
        >
          <RotateCcw className="h-8 w-8" />
        </Button>
      </div>

      {/* Keyboard shortcut info */}
      <div className="fixed bottom-4 left-0 right-0 text-center text-gray-400 text-xs">
        Keyboard Shortcuts: Space (Play/Pause) • R (Reset) • M (Mute) • F (Exit Fullscreen)
      </div>
    </div>
  );
};

export default FullscreenTimer;