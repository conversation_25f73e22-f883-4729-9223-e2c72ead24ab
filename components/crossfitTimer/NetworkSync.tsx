import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Wifi, WifiOff, Crown, RefreshCw } from 'lucide-react';
import { createClient, SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TimerRoom {
  id: string;
  created_at: string;
  active_users: number;
}

interface TimerSettings {
  rounds: number;
  workTime: number;
  restTime: number;
  prepareTime: number;
  intervals: number;
}

interface TimerState {
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  currentSet: number;
  phase: string;
}

export interface NetworkSyncHandle {
  startTimers: (settings: TimerSettings) => void;
  broadcastTimerState: (state: TimerState) => void;
  broadcastSettings: (settings: TimerSettings) => void;
  connectToRoom: (roomId: string, asMaster?: boolean) => Promise<void>;
  disconnectFromRoom: () => Promise<void>;
  getCurrentRoom: () => { roomId: string | null; isMaster: boolean };
  sendTestMessage: () => void;
}

interface NetworkSyncProps {
  onTimerStart: () => void;
  onTimerStateChange?: (state: TimerState) => void;
  onSettingsChange: (settings: TimerSettings) => void;
  onRoomConnection?: (roomId: string | null) => void;
}

const NetworkSync = forwardRef<NetworkSyncHandle, NetworkSyncProps>(
  ({ onTimerStart, onTimerStateChange, onSettingsChange, onRoomConnection }, ref) => {
    const [roomId, setRoomId] = useState('');
    const [connectedRoom, setConnectedRoom] = useState<string | null>(null);
    const [isMaster, setIsMaster] = useState(false);
    const [channel, setChannel] = useState<RealtimeChannel | null>(null);
    const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
    const [availableRooms, setAvailableRooms] = useState<TimerRoom[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [testMessageStatus, setTestMessageStatus] = useState<string | null>(null);
    const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | null>(null);

    // Use a ref to track unmounting to prevent state updates after unmount
    const isMounted = useRef(true);

    // Debug ref to track initialization
    const initAttempted = useRef(false);

    // Initialize Supabase client
    useEffect(() => {
      if (initAttempted.current) return;
      initAttempted.current = true;

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      console.log('Initializing Supabase client:',
        supabaseUrl ? 'URL defined' : 'URL missing',
        supabaseKey ? 'Key defined' : 'Key missing');

      if (supabaseUrl && supabaseKey) {
        try {
          const client = createClient(supabaseUrl, supabaseKey);
          setSupabase(client);
          console.log('Supabase client initialized successfully');
          setError(null);
        } catch (err) {
          console.error('Error initializing Supabase client:', err);
          setError('Failed to initialize Supabase client. Check console for details.');
        }
      } else {
        console.error('Supabase configuration is missing:', { supabaseUrl, supabaseKey });
        setError('Supabase configuration is missing. Check your environment variables.');
      }

      // Set up the unmount flag
      return () => {
        isMounted.current = false;
      };
    }, []);

    // Fetch available rooms
    const memoizedFetchAvailableRooms = useCallback(async () => {
      if (!supabase || !isMounted.current) return;

      setIsLoading(true);
      try {
        console.log('Fetching available rooms...');
        const { data, error: fetchError } = await supabase
          .from('timer_rooms')
          .select('*')
          .gt('active_users', 0)
          .order('created_at', { ascending: false });

        if (fetchError) {
          console.error('Error fetching rooms from Supabase:', fetchError);
          throw fetchError;
        }

        console.log(`Found ${data?.length || 0} active rooms`);

        if (isMounted.current) {
          setAvailableRooms(data || []);
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching rooms:', err);
        if (isMounted.current) {
          setError(`Failed to load available rooms: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    }, [supabase]);

    // Fetch rooms on mount
    useEffect(() => {
      if (supabase) {
        memoizedFetchAvailableRooms();
      }
    }, [supabase, memoizedFetchAvailableRooms]);

    // Handle component cleanup
    useEffect(() => {
      // No need to disconnect here - we'll do it in the useEffect cleanup
      return () => {
        if (connectedRoom && channel) {
          // We need to clean up, but we shouldn't update state on unmount
          console.log('Component unmounting, cleaning up channel');

          // Just unsubscribe without state updates
          channel.unsubscribe().catch(err => {
            console.error('Error unsubscribing on unmount:', err);
          });

          // Clean up room in database if needed
          if (supabase) {
            if (isMaster) {
              supabase
                .from('timer_rooms')
                .delete()
                .eq('id', connectedRoom)
                .then(() => console.log('Room deleted on unmount'))
                .then(undefined, err => console.error('Failed to delete room on unmount:', err));
            } else {
              // Decrement active users
              supabase
                .from('timer_rooms')
                .select('active_users')
                .eq('id', connectedRoom)
                .single()
                .then(({ data }) => {
                  if (data) {
                    const newCount = Math.max(0, data.active_users - 1);
                    // Execute the update and return a void promise
                    return supabase
                      .from('timer_rooms')
                      .update({ active_users: newCount })
                      .eq('id', connectedRoom)
                      .then(() => {});
                  }
                  // Return a resolved promise to continue the chain
                  return Promise.resolve();
                })
                .then(() => console.log('Updated active users on unmount'))
                .then(undefined, (err: Error) => console.error('Failed to update active users on unmount:', err));
            }
          }
        }
      };
    }, [connectedRoom, channel, isMaster, supabase]);

    // Create a new room
    const createRoom = async (roomId: string) => {
      console.log("Starting createRoom function with roomId:", roomId);
console.log("Supabase client exists:", !!supabase);

// Removed debug logs
      if (!supabase || !roomId || !isMounted.current) return false;

      try {
        console.log(`Creating room with ID: ${roomId}`);

        // Check authentication status
        const { data: { user } } = await supabase.auth.getUser();
        console.log('Auth status:', user ? 'Authenticated' : 'Not authenticated');

        // Try to insert with detailed error capturing
        const { data, error } = await supabase
          .from('timer_rooms')
          .insert({ id: roomId, active_users: 1 })
          .select();

        if (error) {
          console.error('Error creating room:', error);
          if (isMounted.current) {
            setError(`Failed to create room: ${error.message} (Code: ${error.code})`);
          }
          return false;
        }

        console.log('Room created successfully:', data);
        return true;
      } catch (err) {
        console.error('Exception creating room:', err);
        if (isMounted.current) {
          setError(`Failed to create room: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
        return false;
      }
    };

    // Update room active users
    const updateRoomActiveUsers = async (roomId: string, increment: boolean) => {
      if (!supabase || !roomId || !isMounted.current) return;

      try {
        console.log(`Updating active users for room ${roomId}, increment: ${increment}`);

        // Get current active users
        const { data: roomData, error: fetchError } = await supabase
          .from('timer_rooms')
          .select('active_users')
          .eq('id', roomId)
          .single();

        if (fetchError) {
          console.error('Error fetching room:', fetchError);
          return;
        }

        const currentActiveUsers = roomData?.active_users || 0;
        const newActiveUsers = increment ? currentActiveUsers + 1 : Math.max(0, currentActiveUsers - 1);

        console.log(`Updating active users from ${currentActiveUsers} to ${newActiveUsers}`);

        // Update active users count
        const { error: updateError } = await supabase
          .from('timer_rooms')
          .update({ active_users: newActiveUsers })
          .eq('id', roomId);

        if (updateError) {
          console.error('Error updating room:', updateError);
          return;
        }

        // If we decreased to 0 users, delete the room
        if (newActiveUsers === 0) {
          console.log('No active users left, deleting room');
          const { error: deleteError } = await supabase
            .from('timer_rooms')
            .delete()
            .eq('id', roomId);

          if (deleteError) {
            console.error('Error deleting empty room:', deleteError);
          }
        }
      } catch (err) {
        console.error('Error updating room active users:', err);
      }
    };

    // Connect to a room - IMPROVED VERSION
    const connectToRoom = async (roomId: string, asMaster = false) => {
      if (!supabase || !roomId || !isMounted.current) return;

      setConnectionStatus('connecting');
      setError(null);

      try {
        console.log(`Connecting to room ${roomId} as ${asMaster ? 'master' : 'follower'}`);

        // If creating as master, add room to database first
        if (asMaster) {
          const success = await createRoom(roomId);
          if (!success) {
            if (isMounted.current) {
              setConnectionStatus('error');
            }
            return;
          }
        } else {
          // Check if room exists first
          const { data: roomExists, error: roomCheckError } = await supabase
            .from('timer_rooms')
            .select('id')
            .eq('id', roomId)
            .single();

          if (roomCheckError || !roomExists) {
            console.error('Room does not exist:', roomCheckError);
            if (isMounted.current) {
              setError(`Room "${roomId}" does not exist. Please check the room ID.`);
              setConnectionStatus('error');
            }
            return;
          }

          // If joining as follower, increment active users
          await updateRoomActiveUsers(roomId, true);
        }

        // Set up a basic channel
        const channelName = `timer-room-${roomId}`;
        console.log('Creating channel:', channelName);

        const newChannel = supabase.channel(channelName, {
          config: {
            broadcast: { self: true }
          }
        });
        // Add this inside the connectToRoom function
const { data: { user } } = await supabase.auth.getUser();
console.log('Current auth status:', user ? 'Authenticated' : 'Not authenticated');
if (!user && asMaster) {
  setError('Authentication required to create rooms');
  setConnectionStatus('error');
  return;
}
        // Add event handlers
        newChannel.on('broadcast', { event: 'timer-start' }, (message) => {
          console.log('Received timer-start message:', message);
          if (!isMaster && isMounted.current) {
            // Apply settings if provided
            if (message.payload.settings) {
              onSettingsChange(message.payload.settings);
            }

            // Wait a moment to ensure all clients are ready
            setTimeout(() => {
              if (isMounted.current) {
                onTimerStart();
              }
            }, 1000);
          }
        });

        newChannel.on('broadcast', { event: 'timer-state' }, (message) => {
          console.log('Received timer-state message:', message);
          if (!isMaster && onTimerStateChange && isMounted.current) {
            onTimerStateChange(message.payload);
          }
        });

        newChannel.on('broadcast', { event: 'settings-update' }, (message) => {
          console.log('Received settings-update message:', message);
          if (!isMaster && message.payload.settings && isMounted.current) {
            onSettingsChange(message.payload.settings);
          }
        });

        newChannel.on('broadcast', { event: 'test-message' }, (message) => {
          console.log('Received test message:', message);
          if (isMounted.current) {
            setTestMessageStatus(`Received: ${message.payload.message} at ${new Date().toLocaleTimeString()}`);
          }

          // If we're not the master, send an acknowledgment back
          if (!isMaster && isMounted.current) {
            newChannel.send({
              type: 'broadcast',
              event: 'test-ack',
              payload: {
                receivedAt: Date.now(),
                message: 'Message received by client'
              }
            });
          }
        });

        newChannel.on('broadcast', { event: 'test-ack' }, (message) => {
          console.log('Received test acknowledgment:', message);
          if (isMaster && isMounted.current) {
            setTestMessageStatus(
              `Acknowledgment received at ${new Date().toLocaleTimeString()}, ` +
              `latency: ${Date.now() - message.payload.receivedAt}ms`
            );
          }
        });

        // Subscribe to the channel with detailed error handling
        console.log('Subscribing to channel...');
        newChannel.subscribe(async (status) => {
          console.log('Channel subscription callback status:', status);

          if (!isMounted.current) {
            console.log('Component unmounted during subscription, ignoring callback');
            return;
          }

          if (status === 'SUBSCRIBED') {
            console.log('Successfully subscribed to channel');

            // Send a test broadcast to verify it's working
            try {
              await newChannel.send({
                type: 'broadcast',
                event: 'connection-test',
                payload: { timestamp: Date.now() }
              });
              console.log('Test broadcast sent successfully');
            } catch (broadcastErr) {
              console.error('Error sending test broadcast:', broadcastErr);
              // Continue anyway - the channel might still work for receiving
            }

            // Update local state
            setConnectionStatus('connected');
            setChannel(newChannel);
            setConnectedRoom(roomId);
            setIsMaster(asMaster);

            // Notify parent component
            if (onRoomConnection) {
              onRoomConnection(roomId);
            }

            // Refresh room list
            memoizedFetchAvailableRooms();
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Channel error occurred');
            if (isMounted.current) {
              setConnectionStatus('error');
              setError('Channel error occurred. Please check console for details.');
            }

            // Clean up
            handleFailedConnection(asMaster, roomId);
          } else if (status === 'TIMED_OUT') {
            console.error('Channel subscription timed out');
            if (isMounted.current) {
              setConnectionStatus('error');
              setError('Connection timed out. Please try again.');
            }

            // Clean up
            handleFailedConnection(asMaster, roomId);
          } else if (status === 'CLOSED') {
            console.log('Channel closed');
            // Only update if not due to unmount
            if (isMounted.current) {
              setChannel(null);
              setConnectedRoom(null);
              setIsMaster(false);
            }
          } else {
            console.log('Unhandled subscription status:', status);
          }
        });
      } catch (err) {
        console.error('Error connecting to room:', err);
        if (isMounted.current) {
          setError(`Failed to connect to room: ${err instanceof Error ? err.message : 'Unknown error'}`);
          setConnectionStatus('error');
        }

        // Clean up
        handleFailedConnection(asMaster, roomId);
      }
    };

    // Helper function to clean up after failed connection
    const handleFailedConnection = (wasMaster: boolean, roomId: string) => {
      if (!supabase) return;

      if (wasMaster) {
        // Remove the room we tried to create
        supabase
          .from('timer_rooms')
          .delete()
          .eq('id', roomId)
          .then(() => console.log('Cleaned up room after failed connection'))
          .then(undefined, (err: Error) => console.error('Failed to clean up room:', err));
      } else {
        // Decrement active users
        updateRoomActiveUsers(roomId, false);
      }
    };

    // Disconnect from room
    const disconnectFromRoom = async () => {
      if (!supabase || !channel || !connectedRoom || !isMounted.current) return;

      try {
        // Unsubscribe from channel
        console.log('Unsubscribing from channel...');
        await channel.unsubscribe();

        // Update active users
        if (!isMaster) {
          await updateRoomActiveUsers(connectedRoom, false);
        } else {
          // If master is leaving, remove the room
          await supabase
            .from('timer_rooms')
            .delete()
            .eq('id', connectedRoom);
        }

        // Only update state if component is still mounted
        if (isMounted.current) {
          // Reset local state
          setChannel(null);
          setConnectedRoom(null);
          setIsMaster(false);
          setTestMessageStatus(null);

          // Notify parent component
          if (onRoomConnection) {
            onRoomConnection(null);
          }

          // Refresh room list
          memoizedFetchAvailableRooms();
        }
      } catch (err) {
        console.error('Error disconnecting from room:', err);
        if (isMounted.current) {
          setError(`Error disconnecting: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      }
    };

    // Send test message (from master to clients)
    const sendTestMessage = () => {
      if (!channel || !isMaster || !isMounted.current) return;

      setTestMessageStatus('Sending test message...');

      channel.send({
        type: 'broadcast',
        event: 'test-message',
        payload: {
          message: `Test from master at ${Date.now()}`,
          sentAt: Date.now()
        }
      }).then(() => {
        console.log('Test message sent successfully');
      }).catch(err => {
        console.error('Error sending test message:', err);
        if (isMounted.current) {
          setTestMessageStatus(`Error sending test message: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      });
    };

    // Start timers with a delay to ensure synchronization
    const startTimers = (settings: TimerSettings) => {
      if (!channel || !isMaster || !isMounted.current) return;

      console.log('Broadcasting timer start with settings:', settings);

      channel.send({
        type: 'broadcast',
        event: 'timer-start',
        payload: {
          settings,
          startTime: Date.now() + 1000
        }
      }).then(() => {
        console.log('Timer start broadcast sent successfully');
      }).catch(err => {
        console.error('Error broadcasting timer start:', err);
      });

      // Schedule our own timer to start at the same time
      setTimeout(() => {
        if (isMounted.current) {
          onTimerStart();
        }
      }, 1000);
    };

    const broadcastTimerState = (state: TimerState) => {
      if (!channel || !isMaster || !isMounted.current) return;

      channel.send({
        type: 'broadcast',
        event: 'timer-state',
        payload: state
      }).catch(err => {
        console.error('Error broadcasting timer state:', err);
      });
    };

    const broadcastSettings = (settings: TimerSettings) => {
      if (!channel || !isMaster || !isMounted.current) return;

      channel.send({
        type: 'broadcast',
        event: 'settings-update',
        payload: { settings }
      }).catch(err => {
        console.error('Error broadcasting settings update:', err);
      });
    };

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      startTimers,
      broadcastTimerState,
      broadcastSettings,
      connectToRoom,
      disconnectFromRoom,
      getCurrentRoom: () => ({ roomId: connectedRoom, isMaster }),
      sendTestMessage
    }));

    // UI for available rooms
    const renderAvailableRooms = () => {
      if (isLoading) {
        return (
          <div className="space-y-2 mt-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        );
      }

      if (availableRooms.length === 0) {
        return (
          <div className="p-4 mt-4 bg-gray-50 text-gray-600 rounded-md text-sm text-center">
            No active rooms available.
          </div>
        );
      }

      return (
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between">
            <Label>Available Rooms</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => memoizedFetchAvailableRooms()}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          <div className="max-h-48 overflow-y-auto space-y-2">
            {availableRooms.map((room) => (
              <Button
                key={room.id}
                onClick={() => {
                  setRoomId(room.id);
                  connectToRoom(room.id, false);
                }}
                variant="outline"
                className="w-full justify-between"
              >
                <div className="flex items-center">
                  <Wifi className="h-4 w-4 mr-2" />
                  <span>{room.id}</span>
                </div>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {room.active_users} users
                </span>
              </Button>
            ))}
          </div>
        </div>
      );
    };

    // Render connection status
    const renderConnectionStatus = () => {
      if (!connectionStatus) return null;

      if (connectionStatus === 'connecting') {
        return (
          <Alert className="mb-4 bg-blue-50 border-blue-200">
            <AlertDescription className="text-blue-700 flex items-center">
              <div className="mr-2 h-4 w-4 rounded-full bg-blue-500 animate-pulse"></div>
              Connecting to room...
            </AlertDescription>
          </Alert>
        );
      }

      if (connectionStatus === 'error') {
        return (
          <Alert className="mb-4 bg-red-50 border-red-200">
            <AlertDescription className="text-red-700">
              Connection error. Please try again.
            </AlertDescription>
          </Alert>
        );
      }

      return null;
    };

    // Render test message status
    const renderTestMessage = () => {
      if (!connectedRoom || !testMessageStatus) return null;

      return (
        <div className="mt-2 p-3 text-xs bg-gray-50 border border-gray-200 rounded-md">
          <p className="font-medium">Connection Test:</p>
          <p className="mt-1">{testMessageStatus}</p>
        </div>
      );
    };

    // Render debug information in development
    const renderDebugInfo = () => {
      if (process.env.NODE_ENV !== 'development') return null;

      return (
        <div className="mt-2 p-2 text-xs bg-slate-100 border border-slate-200 rounded-md">
          <p className="font-medium">Debug Info:</p>
          <p>Supabase: {supabase ? 'Initialized' : 'Not initialized'}</p>
          <p>Connection: {connectionStatus || 'Not connected'}</p>
          <p>Room: {connectedRoom || 'None'}</p>
          <p>Role: {isMaster ? 'Master' : 'Follower'}</p>
          <p>Channel: {channel ? 'Active' : 'None'}</p>
        </div>
      );
    };

    // Main component render
    return (
      <div className="p-4 bg-white text-black rounded-md">
        <h3 className="font-medium mb-2">Timer Synchronization</h3>

        {renderConnectionStatus()}

        {error && (
          <Alert className="mb-4 bg-red-50 border-red-200">
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {!connectedRoom ? (
          <div className="space-y-3">
            <div>
              <Label>Room ID</Label>
              <Input
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                placeholder="Enter room ID"
                className="mb-2"
              />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => connectToRoom(roomId, false)}
                className="bg-green-600 hover:bg-green-700"
                disabled={!roomId.trim() || connectionStatus === 'connecting'}
              >
                <Wifi className="h-4 w-4 mr-2" />
                Join Room
              </Button>
              <Button
                onClick={() => {
                  console.log('Button clicked!');
                  // Create a unique room ID with timestamp
                  const newRoom = `timer-${Math.random().toString(36).substring(2, 8)}`;
                  setRoomId(newRoom);
                  connectToRoom(newRoom, true);
                }}
                className="bg-blue-600 hover:bg-blue-700"
                disabled={connectionStatus === 'connecting'}
              >
                <Crown className="h-4 w-4 mr-2" />
                Create Room
              </Button>
            </div>

            {renderAvailableRooms()}
            {renderDebugInfo()}
          </div>
        ) : (
          <div className="space-y-3">
            <div className="p-3 bg-green-100 border border-green-300 rounded-md">
              {isMaster ? (
                <p className="text-sm text-green-800 flex items-center">
                  <Crown className="h-4 w-4 mr-1" />
                  Main timer - Room: <strong className="ml-1">{connectedRoom}</strong>
                </p>
              ) : (
                <p className="text-sm text-green-800 flex items-center">
                  <Wifi className="h-4 w-4 mr-1" />
                  Following main timer - Room: <strong className="ml-1">{connectedRoom}</strong>
                </p>
              )}
            </div>

            {/* Test connection button for master */}
            {isMaster && (
              <Button
                onClick={sendTestMessage}
                variant="outline"
                className="w-full"
              >
                Test Connection
              </Button>
            )}

            {renderTestMessage()}
            {renderDebugInfo()}

            <Button
              onClick={disconnectFromRoom}
              variant="destructive"
              className="w-full mt-2"
            >
              <WifiOff className="h-4 w-4 mr-2" />
              Disconnect
            </Button>
          </div>
        )}
      </div>
    );
  }
);

NetworkSync.displayName = 'NetworkSync';

export default NetworkSync;
