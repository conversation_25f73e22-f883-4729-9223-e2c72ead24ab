import React from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';

interface TimerControlsProps {
  isRunning: boolean;
  isPaused: boolean;
  isMuted: boolean;
  onStart: () => void;
  onPause: () => void;
  onResume: () => void;
  onReset: () => void;
  onToggleMute: () => void;
  isFullscreen?: boolean;
}

const TimerControls: React.FC<TimerControlsProps> = ({
  isRunning,
  isPaused,
  isMuted,
  onStart,
  onPause,
  onResume,
  onReset,
  onToggleMute,
  isFullscreen = false
}) => {
  return (
    <div className={`flex justify-center gap-4 sm:gap-8 ${
      isFullscreen ? 'fixed bottom-16 left-0 right-0' : 'mt-8'
    }`}>
      <Button 
        onClick={onToggleMute} 
        variant="ghost" 
        className={`rounded-full hover:bg-gray-800/50 ${isFullscreen ? 'w-16 h-16' : 'w-12 h-12'}`}
        aria-label={isMuted ? "Unmute" : "Mute"}
      >
        {isMuted ? 
          <VolumeX className={`${isFullscreen ? 'h-8 w-8' : 'h-6 w-6'} opacity-60`} /> : 
          <Volume2 className={`${isFullscreen ? 'h-8 w-8' : 'h-6 w-6'} opacity-60`} />
        }
      </Button>

      {!isRunning && !isPaused ? (
        <Button 
          onClick={onStart} 
          className={`rounded-full bg-primary hover:bg-primary/90 ${isFullscreen ? 'w-24 h-24' : 'w-16 h-16 sm:w-20 sm:h-20'}`}
          aria-label="Start Timer"
        >
          <Play className={`${isFullscreen ? 'h-12 w-12' : 'h-8 w-8 sm:h-10 sm:w-10'}`} />
        </Button>
      ) : isRunning ? (
        <Button 
          onClick={onPause} 
          className={`rounded-full bg-primary hover:bg-primary/90 ${isFullscreen ? 'w-24 h-24' : 'w-16 h-16 sm:w-20 sm:h-20'}`}
          aria-label="Pause Timer"
        >
          <Pause className={`${isFullscreen ? 'h-12 w-12' : 'h-8 w-8 sm:h-10 sm:w-10'}`} />
        </Button>
      ) : (
        <Button 
          onClick={onResume} 
          className={`rounded-full bg-primary hover:bg-primary/90 ${isFullscreen ? 'w-24 h-24' : 'w-16 h-16 sm:w-20 sm:h-20'}`}
          aria-label="Resume Timer"
        >
          <Play className={`${isFullscreen ? 'h-12 w-12' : 'h-8 w-8 sm:h-10 sm:w-10'}`} />
        </Button>
      )}

      <Button 
        onClick={onReset} 
        variant="ghost" 
        className={`rounded-full hover:bg-gray-800/50 ${isFullscreen ? 'w-16 h-16' : 'w-12 h-12'}`}
        aria-label="Reset Timer"
      >
        <RotateCcw className={`${isFullscreen ? 'h-8 w-8' : 'h-6 w-6'} opacity-60`} />
      </Button>
    </div>
  );
};

export default TimerControls;