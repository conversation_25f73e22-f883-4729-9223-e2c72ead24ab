import React from 'react';
import { Keyboard } from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger 
} from '@/components/ui/tooltip';

const KeyboardShortcutsInfo = () => {
  return (
    <div className="mt-4 text-center">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button className="text-xs flex items-center justify-center gap-1 mx-auto text-gray-400 hover:text-gray-300">
              <Keyboard className="h-3 w-3" />
              <span>Keyboard Shortcuts</span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            <div className="space-y-2">
              <h4 className="font-medium">Keyboard Shortcuts</h4>
              <ul className="text-xs space-y-1 text-left">
                <li><kbd className="px-1 bg-gray-800 rounded text-white ">Space</kbd> - Play/Pause timer</li>
                <li><kbd className="px-1 bg-gray-800 rounded text-white">R</kbd> - Reset timer</li>
                <li><kbd className="px-1 bg-gray-800 rounded text-white">M</kbd> - Toggle mute</li>
                <li><kbd className="px-1 bg-gray-800 rounded text-white">F</kbd> - Toggle fullscreen</li>
                <li><kbd className="px-1 bg-gray-800 rounded text-white">Esc</kbd> - Exit fullscreen</li>
              </ul>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default KeyboardShortcutsInfo;