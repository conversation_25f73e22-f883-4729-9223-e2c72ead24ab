import React from 'react';

interface CountdownDisplayProps {
  countdown: number;
  isFullscreen?: boolean;
}

const CountdownDisplay: React.FC<CountdownDisplayProps> = ({ 
  countdown,
  isFullscreen = false 
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <div className={`font-light ${isFullscreen ? 'text-[25vmin]' : 'text-8xl sm:text-9xl'} tracking-wider`}>
        {countdown}
      </div>
      <div className={`mt-4 ${isFullscreen ? 'text-[4vmin]' : 'text-xl sm:text-2xl'} text-gray-400`}>
        Get Ready
      </div>
    </div>
  );
};

export default CountdownDisplay;