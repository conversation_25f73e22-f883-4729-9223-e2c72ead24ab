import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Play, Volume2, VolumeX } from 'lucide-react';
import KeyboardShortcutsInfo from './KeyboardShortcutsInfo';

interface TimerSettings {
  sets: number;
  workTime: number;
  restTime: number;
}

interface TimerSettingsProps {
  settings: TimerSettings;
  onSettingsChange: (newSettings: TimerSettings) => void;
  isMuted: boolean;
  onToggleMute: () => void;
  onStartTimer: () => void;
  backgroundColor: string;
  textColor: string;
  onBackgroundColorChange: (color: string) => void;
  onTextColorChange: (color: string) => void;
}

const TimerSettings: React.FC<TimerSettingsProps> = ({
  settings,
  onSettingsChange,
  isMuted,
  onToggleMute,
  onStartTimer,
  backgroundColor,
  textColor,
  onBackgroundColorChange,
  onTextColorChange
}) => {
  return (
    <div className="grid gap-4 sm:grid-cols-2 p-4 rounded-md bg-white text-black">
      <div className="space-y-2">
        <Label className="text-black">Number of Sets</Label>
        <Input
          type="number"
          min="1"
          value={settings.sets}
          onChange={(e) => onSettingsChange({
            ...settings, 
            sets: parseInt(e.target.value) || 1
          })}
          className="bg-white border-gray-300 text-black"
        />
      </div>
      <div className="space-y-2">
        <Label className="text-black">Work Time (seconds)</Label>
        <Input
          type="number"
          min="5"
          value={settings.workTime}
          onChange={(e) => onSettingsChange({
            ...settings, 
            workTime: parseInt(e.target.value) || 60
          })}
          className="bg-white border-gray-300 text-black"
        />
      </div>
      <div className="space-y-2">
        <Label className="text-black">Rest Time (seconds)</Label>
        <Input
          type="number"
          min="5"
          value={settings.restTime}
          onChange={(e) => onSettingsChange({
            ...settings, 
            restTime: parseInt(e.target.value) || 30
          })}
          className="bg-white border-gray-300 text-black"
        />
      </div>
      <div className="space-y-2">
        <Label className="text-black">Sound</Label>
        <div className="flex items-center space-x-2">
          <Button
            onClick={onToggleMute}
            variant={isMuted ? "outline" : "default"}
            className={`w-full ${isMuted ? 'bg-gray-100 text-gray-700 border-gray-300' : 'bg-blue-600 text-white'}`}
          >
            {isMuted ? (
              <>
                <VolumeX className="h-4 w-4 mr-2" />
                Muted
              </>
            ) : (
              <>
                <Volume2 className="h-4 w-4 mr-2" />
                On
              </>
            )}
          </Button>
        </div>
      </div>
      <div className="space-y-2 sm:col-span-2">
        <Label className="text-black">Background Color</Label>
        <div className="flex gap-2 items-center">
          <div 
            className="w-10 h-10 rounded border border-gray-300" 
            style={{ backgroundColor }}
          />
          <Input
            type="color"
            value={backgroundColor}
            onChange={(e) => onBackgroundColorChange(e.target.value)}
            className="w-full h-10 bg-white border-gray-300"
          />
        </div>
      </div>
      <div className="space-y-2 sm:col-span-2">
        <Label className="text-black">Text Color</Label>
        <div className="flex gap-2 items-center">
          <div 
            className="w-10 h-10 rounded border border-gray-300" 
            style={{ backgroundColor: textColor }}
          />
          <Input
            type="color"
            value={textColor}
            onChange={(e) => onTextColorChange(e.target.value)}
            className="w-full h-10 bg-white border-gray-300"
          />
        </div>
      </div>
      <div className="sm:col-span-2 mt-4">
        <Button 
          onClick={onStartTimer} 
          className="w-full bg-blue-600 hover:bg-blue-700 text-white h-12"
        >
          <Play className="h-5 w-5 mr-2" />
          Start Timer
        </Button>
      </div>
      
      <div className="sm:col-span-2">
        <KeyboardShortcutsInfo />
      </div>
    </div>
  );
};

export default TimerSettings;