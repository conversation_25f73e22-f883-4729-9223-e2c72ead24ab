'use client'

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import NextImage from 'next/image';
import { Play, Image as ImageIcon, Video, ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react';
import { ExerciseMovement, CATEGORY_TYPES } from './ExercisesClient';
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';

interface MediaGalleryProps {
  exercise: ExerciseMovement;
  showDescriptions?: boolean;
  showCategories?: boolean;
}

export default function MediaGallery({
  exercise,
  showDescriptions = false,
  showCategories = false
}: MediaGalleryProps) {
  const [activeVideoIndex, setActiveVideoIndex] = useState<number>(0);
  const [activeImageIndex, setActiveImageIndex] = useState<number>(0);

  // Get sorted videos (primary first)
  const sortedVideos = (exercise.videos || []).sort((a, b) => {
    if (a.is_primary && !b.is_primary) return -1;
    if (!a.is_primary && b.is_primary) return 1;
    return 0;
  });

  // Get sorted images (primary first)
  const sortedImages = (exercise.images || []).sort((a, b) => {
    if (a.is_primary && !b.is_primary) return -1;
    if (!a.is_primary && b.is_primary) return 1;
    return 0;
  });

  // Active video and image
  const activeVideo = sortedVideos[activeVideoIndex];
  const activeImage = sortedImages[activeImageIndex];

  // Helper function to extract YouTube ID
  const getYoutubeId = (url: string): string | null => {
    if (!url) return null;

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  // Get primary category value
  const getPrimaryCategoryValue = (categoryType: string) => {
    return exercise.categories?.find(cat =>
      cat.category_type === categoryType && cat.is_primary
    )?.category_value || '';
  };

  // Get all categories of a type
  const getCategoriesByType = (categoryType: string) => {
    return exercise.categories?.filter(cat =>
      cat.category_type === categoryType
    ) || [];
  };

  // Get video source badge
  const getVideoSourceBadge = (source: string | null | undefined) => {
    if (!source) return null;

    let color = "bg-gray-100";
    let icon = null;

    switch(source) {
      case 'youtube':
        color = "bg-red-100 text-red-800";
        icon = <Video className="h-3 w-3 mr-1" />;
        break;
      case 'sweat.com':
        color = "bg-purple-100 text-purple-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
        break;
      case 'trainwell.com':
        color = "bg-blue-100 text-blue-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
        break;
      default:
        color = "bg-gray-100 text-gray-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
    }

    return (
      <Badge className={`${color} flex items-center text-xs`}>
        {icon}
        {source}
      </Badge>
    );
  };

  // Handle next/prev video
  const handlePrevVideo = () => {
    setActiveVideoIndex(prev =>
      prev === 0 ? sortedVideos.length - 1 : prev - 1
    );
  };

  const handleNextVideo = () => {
    setActiveVideoIndex(prev =>
      prev === sortedVideos.length - 1 ? 0 : prev + 1
    );
  };

  // Handle next/prev image
  const handlePrevImage = () => {
    setActiveImageIndex(prev =>
      prev === 0 ? sortedImages.length - 1 : prev - 1
    );
  };

  const handleNextImage = () => {
    setActiveImageIndex(prev =>
      prev === sortedImages.length - 1 ? 0 : prev + 1
    );
  };

  return (
    <div className="space-y-6">
      {/* Exercise name and basic info */}
      <div>
        <h2 className="text-2xl font-bold">{exercise.exercise_name}</h2>

        {showCategories && (
          <div className="flex flex-wrap gap-2 mt-2">
            {getPrimaryCategoryValue('body_part') && (
              <Badge variant="outline" className="bg-blue-50">
                {getPrimaryCategoryValue('body_part')}
              </Badge>
            )}

            {getPrimaryCategoryValue('equipment') && (
              <Badge variant="outline" className="bg-green-50">
                {getPrimaryCategoryValue('equipment')}
              </Badge>
            )}

            {getPrimaryCategoryValue('expertise_level') && (
              <Badge variant="outline" className="bg-yellow-50">
                {getPrimaryCategoryValue('expertise_level')}
              </Badge>
            )}

            {getPrimaryCategoryValue('movement_category') && (
              <Badge variant="outline" className="bg-purple-50">
                {getPrimaryCategoryValue('movement_category')}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Media gallery */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Video gallery */}
        <Card>
          <CardContent className="pt-6">
            {sortedVideos.length > 0 ? (
              <div className="space-y-2">
                <div className="aspect-video relative">
                  {activeVideo.video_source === 'youtube' ||
                   activeVideo.video_url.includes('youtube.com') ||
                   activeVideo.video_url.includes('youtu.be') ? (
                    <iframe
                      width="100%"
                      height="100%"
                      src={`https://www.youtube.com/embed/${getYoutubeId(activeVideo.video_url)}`}
                      title="YouTube video player"
                      style={{ border: 0 }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    ></iframe>
                  ) : (
                    <div className="aspect-video bg-gray-100 flex items-center justify-center">
                      <div className="text-center">
                        <Play className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-500">
                          Video from {activeVideo.video_source || 'external source'}
                        </p>
                        <a
                          href={activeVideo.video_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline text-sm mt-2 inline-block"
                        >
                          Open Video Link
                        </a>
                      </div>
                    </div>
                  )}

                  {/* Navigation controls */}
                  {sortedVideos.length > 1 && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                        onClick={handlePrevVideo}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                        onClick={handleNextVideo}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>

                {/* Video counter and source */}
                <div className="flex justify-between items-center">
                  {sortedVideos.length > 1 ? (
                    <div className="text-sm text-gray-500">
                      Video {activeVideoIndex + 1} of {sortedVideos.length}
                    </div>
                  ) : (
                    <div></div>
                  )}

                  {activeVideo.video_source && getVideoSourceBadge(activeVideo.video_source)}
                </div>

                {/* Thumbnail navigation */}
                {sortedVideos.length > 1 && (
                  <div className="flex gap-2 overflow-x-auto py-2">
                    {sortedVideos.map((video, idx) => (
                      <button
                        key={video.id}
                        className={`w-16 h-12 flex-shrink-0 border-2 flex items-center justify-center ${
                          idx === activeVideoIndex ? 'border-primary' : 'border-gray-200'
                        }`}
                        onClick={() => setActiveVideoIndex(idx)}
                      >
                        <Video className="h-4 w-4" />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                <div className="text-center">
                  <Play className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-500">No video provided</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Image gallery */}
        <Card>
          <CardContent className="pt-6">
            {sortedImages.length > 0 ? (
              <div className="space-y-2">
                <div className="aspect-video relative">
                  <NextImage
                    src={getImageUrl(activeImage.image_id || '')}
                    alt={exercise.exercise_name}
                    className="object-cover rounded-md"
                    fill
                    sizes="(max-width: 768px) 100vw, 50vw"
                    onError={(e) => {
                      // Handle error by showing a placeholder
                      const imgElement = e.currentTarget as HTMLImageElement;
                      imgElement.src = getFallbackImageUrl('Image+Not+Found');
                    }}
                  />

                  {/* Navigation controls */}
                  {sortedImages.length > 1 && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                        onClick={handlePrevImage}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                        onClick={handleNextImage}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>

                {/* Image counter */}
                {sortedImages.length > 1 && (
                  <div className="text-sm text-gray-500">
                    Image {activeImageIndex + 1} of {sortedImages.length}
                  </div>
                )}

                {/* Thumbnail navigation */}
                {sortedImages.length > 1 && (
                  <div className="flex gap-2 overflow-x-auto py-2">
                    {sortedImages.map((image, idx) => (
                      <button
                        key={image.id}
                        className={`w-16 h-12 flex-shrink-0 border-2 relative ${
                          idx === activeImageIndex ? 'border-primary' : 'border-gray-200'
                        }`}
                        onClick={() => setActiveImageIndex(idx)}
                      >
                        <NextImage
                          src={getImageUrl(image.image_id || '')}
                          alt={`Thumbnail ${idx + 1}`}
                          className="object-cover"
                          fill
                          sizes="64px"
                          onError={(e) => {
                            // Handle error by showing a placeholder
                            const imgElement = e.currentTarget as HTMLImageElement;
                            imgElement.src = getFallbackImageUrl('Thumb');
                          }}
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                <div className="text-center">
                  <ImageIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-500">No image provided</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {showDescriptions && (
        <Card>
          <CardContent className="pt-6">
            <h3 className="font-medium mb-2">Description</h3>
            {exercise.description ? (
              <p className="text-gray-700">{exercise.description}</p>
            ) : (
              <p className="text-gray-400 italic">No description provided</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Additional categories */}
      {showCategories && exercise.categories && exercise.categories.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <h3 className="font-medium mb-2">Categories</h3>
            <div className="grid grid-cols-2 gap-4">
              {CATEGORY_TYPES.map(categoryType => {
                const categories = getCategoriesByType(categoryType);
                if (categories.length === 0) return null;

                return (
                  <div key={categoryType}>
                    <h4 className="text-sm font-medium mb-1 capitalize">
                      {categoryType.replace('_', ' ')}
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {categories.map(category => (
                        <Badge
                          key={category.id}
                          variant="outline"
                          className={category.is_primary ? 'bg-blue-50' : ''}
                        >
                          {category.category_value}
                        </Badge>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}