'use client'


import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Trash2, PlusCircle, ExternalLink } from 'lucide-react';
import NextImage from 'next/image';
import { v4 as uuidv4 } from 'uuid';
import { ExerciseImage as BaseExerciseImage } from './ExercisesClient';
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';

// Create a custom interface that extends the base type with the fields we need
interface ExerciseImage extends Omit<BaseExerciseImage, 'image_id'> {
  image_url: string;
  is_primary: boolean; // Override to make it non-nullable
}

interface ImageFormSectionProps {
  images: ExerciseImage[];
  onChange: (images: ExerciseImage[]) => void;
}

export default function ImageFormSection({ images, onChange }: ImageFormSectionProps) {
  // Add a new image
  const handleAddImage = () => {
    const newImage: ExerciseImage = {
      id: `temp-${uuidv4()}`, // Temporary ID for UI purposes
      exercise_id: '',
      image_url: '',
      is_primary: images.length === 0, // First image is primary by default
      created_at: new Date().toISOString()
    };

    onChange([...images, newImage]);
  };

  // Update an image
  const handleUpdateImage = (index: number, field: keyof ExerciseImage, value: string | boolean | null) => {
    const updatedImages = [...images];
    updatedImages[index] = {
      ...updatedImages[index],
      [field]: value
    };

    // If marking as primary, set others as not primary
    if (field === 'is_primary' && value === true) {
      updatedImages.forEach((image, i) => {
        if (i !== index) {
          updatedImages[i] = { ...image, is_primary: false };
        }
      });
    }

    onChange(updatedImages);
  };

  // Remove an image
  const handleRemoveImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);

    // If we removed the primary image and there are other images, make the first one primary
    if (images[index].is_primary && updatedImages.length > 0) {
      updatedImages[0] = { ...updatedImages[0], is_primary: true };
    }

    onChange(updatedImages);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {images.map((image, index) => (
          <div key={image.id} className="border rounded-md p-4 space-y-3">
            <div className="flex justify-between items-center">
              <h4 className="font-medium">Image {index + 1}</h4>
              <Button
                variant="destructive"
                size="icon"
                onClick={() => handleRemoveImage(index)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor={`image-url-${index}`}>Image URL</Label>
                <div className="flex gap-2">
                  <Input
                    id={`image-url-${index}`}
                    value={image.image_url}
                    onChange={(e) => handleUpdateImage(index, 'image_url', e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />

                  {image.image_url && (
                    <Button
                      variant="outline"
                      size="icon"
                      type="button"
                      onClick={() => window.open(getImageUrl(image.image_url), '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <Switch
                  id={`is-primary-${index}`}
                  checked={image.is_primary}
                  onCheckedChange={(checked) => handleUpdateImage(index, 'is_primary', checked)}
                />
                <Label htmlFor={`is-primary-${index}`}>Primary Image</Label>
              </div>

              {/* Image preview */}
              {image.image_url && (
                <div className="mt-2 aspect-video relative">
                  <NextImage
                    src={getImageUrl(image.image_url)}
                    alt={`Image ${index + 1}`}
                    className="object-cover rounded-md"
                    fill
                    sizes="(max-width: 768px) 100vw, 50vw"
                    onError={(e) => {
                      // Handle error by showing a placeholder
                      const imgElement = e.currentTarget as HTMLImageElement;
                      imgElement.src = getFallbackImageUrl('Image+Not+Found');
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <Button
        type="button"
        variant="outline"
        onClick={handleAddImage}
        className="w-full flex items-center"
      >
        <PlusCircle className="h-4 w-4 mr-2" />
        Add Image
      </Button>
    </div>
  );
}