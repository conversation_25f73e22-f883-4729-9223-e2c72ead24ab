'use client'

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface MediaPreviewDialogProps {
  mediaItem: {
    url: string;
    source: string;
  };
  onClose: () => void;
}

export default function MediaPreviewDialog({ mediaItem, onClose }: MediaPreviewDialogProps) {
  // Helper function to extract YouTube ID
  const getYoutubeId = (url: string): string | null => {
    if (!url) return null;

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  const isYoutubeVideo =
    mediaItem.source === 'youtube' ||
    mediaItem.url.includes('youtube.com') ||
    mediaItem.url.includes('youtu.be');

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            <span>Video Preview</span>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="aspect-video w-full">
          {isYoutubeVideo ? (
            <iframe
              width="100%"
              height="100%"
              src={`https://www.youtube.com/embed/${getYoutubeId(mediaItem.url)}`}
              title="YouTube video player"
              style={{ border: 0 }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          ) : (
            <div className="flex flex-col items-center justify-center h-full bg-gray-100 rounded-md">
              <p className="text-gray-500 mb-2">External video from {mediaItem.source || 'unknown source'}</p>
              <Button asChild variant="outline">
                <a href={mediaItem.url} target="_blank" rel="noopener noreferrer">
                  Open Video Link
                </a>
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}