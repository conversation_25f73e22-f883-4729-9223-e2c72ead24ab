'use client'

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Crown,
  Users,
  Star,
  Image,
  Video,
  FileText,
  TrendingUp,
  Award,
  Info
} from 'lucide-react';
import SafeImage from '@/components/ui/SafeImage';
import { getImageUrl } from '@/lib/utils/imageUtils';
import type { ConsolidationExercise } from './ConsolidationClient';

interface MasterSelectionPanelProps {
  exercises: ConsolidationExercise[];
  masterId: string | null;
  recommendedMasterId: string | null;
  onMasterSelect: (exerciseId: string) => void;
}

export default function MasterSelectionPanel({
  exercises,
  masterId,
  recommendedMasterId,
  onMasterSelect
}: MasterSelectionPanelProps) {

  // Helper to get primary image URL
  const getPrimaryImageUrl = (exercise: ConsolidationExercise): string | null => {
    const primaryImage = exercise.images.find(img => img.is_primary) || exercise.images[0];
    if (!primaryImage) return null;

    // Use image_id to construct the URL
    if (primaryImage.image_id) {
      return getImageUrl(primaryImage.image_id);
    }

    return null;
  };

  // Helper to get primary categories
  const getPrimaryCategories = (exercise: ConsolidationExercise) => {
    return exercise.categories.filter(cat => cat.is_primary);
  };

  // Calculate total score for display
  const getTotalScore = (exercise: ConsolidationExercise) => {
    return exercise.recordCount * 2 + exercise.completenessScore + exercise.nameQualityScore;
  };

  // Sort exercises by total score for better display
  const sortedExercises = [...exercises].sort((a, b) => getTotalScore(b) - getTotalScore(a));

  const ExerciseCard = ({ exercise, isRecommended }: { exercise: ConsolidationExercise, isRecommended: boolean }) => {
    const imageUrl = getPrimaryImageUrl(exercise);
    const primaryCategories = getPrimaryCategories(exercise);
    const totalScore = getTotalScore(exercise);
    const isSelected = masterId === exercise.id;

    return (
      <Card className={`p-6 cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
      }`}>
        <div className="flex items-start gap-4">
          {/* Radio Button */}
          <div className="pt-1">
            <RadioGroupItem value={exercise.id} id={exercise.id} />
          </div>

          {/* Exercise Image */}
          <div className="flex-shrink-0">
            {imageUrl ? (
              <SafeImage
                src={imageUrl}
                alt={exercise.exercise_name}
                width={80}
                height={80}
                className="rounded-lg object-cover"
              />
            ) : (
              <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                <Image className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>

          {/* Exercise Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <Label htmlFor={exercise.id} className="text-lg font-semibold cursor-pointer">
                  {exercise.exercise_name}
                </Label>
                {isRecommended && (
                  <Badge className="ml-2 bg-green-100 text-green-800 border-green-200">
                    <Crown className="h-3 w-3 mr-1" />
                    Recommended
                  </Badge>
                )}
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">Score: {totalScore}</div>
                <div className="flex items-center gap-1 mt-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-3 w-3 ${
                        star <= Math.ceil(exercise.completenessScore / 20)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Description */}
            {exercise.description && (
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {exercise.description}
              </p>
            )}

            {/* Categories */}
            <div className="flex flex-wrap gap-1 mb-3">
              {primaryCategories.slice(0, 4).map((category, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {category.category_type}: {category.category_value}
                </Badge>
              ))}
              {primaryCategories.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{primaryCategories.length - 4} more
                </Badge>
              )}
            </div>

            {/* Stats Row */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                <span className="text-gray-600">Records:</span>
                <span className="font-medium">{exercise.recordCount}</span>
              </div>

              <div className="flex items-center gap-2">
                <Image className="h-4 w-4 text-green-500" />
                <span className="text-gray-600">Images:</span>
                <span className="font-medium">{exercise.images.length}</span>
              </div>

              <div className="flex items-center gap-2">
                <Video className="h-4 w-4 text-purple-500" />
                <span className="text-gray-600">Videos:</span>
                <span className="font-medium">{exercise.videos.length}</span>
              </div>

              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-orange-500" />
                <span className="text-gray-600">Categories:</span>
                <span className="font-medium">{exercise.categories.length}</span>
              </div>
            </div>

            {/* Quality Indicators */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-3 w-3 text-blue-500" />
                  <span>Completeness: {exercise.completenessScore}%</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-3 w-3 text-green-500" />
                  <span>Name Quality: {exercise.nameQualityScore}%</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-3 w-3 text-purple-500" />
                  <span>Usage Weight: {exercise.recordCount * 2}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Master Exercise</h2>
        <p className="text-gray-600">
          Select which exercise will be kept as the primary record.
          All other exercises will be merged into this one.
        </p>
      </div>

      {/* Recommendation Info */}
      <Alert className="border-blue-200 bg-blue-50">
        <Info className="h-4 w-4" />
        <AlertDescription className="text-blue-800">
          <strong>Our recommendation:</strong> The exercise with the highest score is marked as recommended.
          The score is calculated based on: usage (2x record count) + data completeness + name quality.
        </AlertDescription>
      </Alert>

      {/* Master Selection */}
      <RadioGroup value={masterId || ''} onValueChange={onMasterSelect}>
        <div className="space-y-4">
          {sortedExercises.map((exercise) => (
            <ExerciseCard
              key={exercise.id}
              exercise={exercise}
              isRecommended={exercise.id === recommendedMasterId}
            />
          ))}
        </div>
      </RadioGroup>

      {/* Summary Card */}
      <Card className="p-6 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Info className="h-5 w-5 text-blue-600" />
          Consolidation Summary
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="font-medium text-gray-900 mb-1">Exercises to Merge</div>
            <div className="text-gray-600">{exercises.length} exercises selected</div>
          </div>

          <div>
            <div className="font-medium text-gray-900 mb-1">Total Records</div>
            <div className="text-gray-600">
              {exercises.reduce((sum, ex) => sum + ex.recordCount, 0)} performance records
            </div>
          </div>

          <div>
            <div className="font-medium text-gray-900 mb-1">Combined Media</div>
            <div className="text-gray-600">
              {exercises.reduce((sum, ex) => sum + ex.images.length, 0)} images, {' '}
              {exercises.reduce((sum, ex) => sum + ex.videos.length, 0)} videos
            </div>
          </div>
        </div>

        {masterId && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center gap-2 text-green-700">
              <Crown className="h-4 w-4" />
              <span className="font-medium">
                Master Exercise: {exercises.find(ex => ex.id === masterId)?.exercise_name}
              </span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              All records and media from other exercises will be transferred to this exercise.
            </div>
          </div>
        )}
      </Card>

      {/* Action Hint */}
      {!masterId && (
        <Card className="p-4 border-orange-200 bg-orange-50">
          <div className="flex items-center gap-2 text-orange-800">
            <Crown className="h-4 w-4" />
            <span className="font-medium">Select a master exercise to continue</span>
          </div>
          <div className="text-sm text-orange-700 mt-1">
            Click on any exercise card above to select it as the master.
          </div>
        </Card>
      )}
    </div>
  );
}