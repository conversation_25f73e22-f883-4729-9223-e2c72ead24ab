'use client'


import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Trash2, PlusCircle, Play, ExternalLink } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { ExerciseVideo as BaseExerciseVideo, VIDEO_SOURCES } from './ExercisesClient';

// Create a custom interface that extends the base type with the fields we need
interface ExerciseVideo extends BaseExerciseVideo {
  is_primary: boolean; // Override to make it non-nullable
}

interface VideoFormSectionProps {
  videos: ExerciseVideo[];
  onChange: (videos: ExerciseVideo[]) => void;
}

export default function VideoFormSection({ videos, onChange }: VideoFormSectionProps) {
  // Helper function to detect video source from URL
  const detectVideoSource = (url: string): string => {
    if (!url) return '';
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
    if (url.includes('sweat.com')) return 'sweat.com';
    if (url.includes('copilot-exercise')) return 'trainwell.com';
    return 'other';
  };

  // Add a new video
  const handleAddVideo = () => {
    const newVideo: ExerciseVideo = {
      id: `temp-${uuidv4()}`, // Temporary ID for UI purposes
      exercise_id: '',
      video_url: '',
      video_source: '',
      is_primary: videos.length === 0, // First video is primary by default
      created_at: new Date().toISOString()
    };

    onChange([...videos, newVideo]);
  };

  // Update a video
  const handleUpdateVideo = (index: number, field: keyof ExerciseVideo, value: string | boolean) => {
    const updatedVideos = [...videos];

    // Special handling for video_url changes to auto-detect source
    if (field === 'video_url' && typeof value === 'string') {
      const detectedSource = detectVideoSource(value);
      updatedVideos[index] = {
        ...updatedVideos[index],
        video_url: value,
        video_source: detectedSource || updatedVideos[index].video_source
      };
    } else {
      updatedVideos[index] = {
        ...updatedVideos[index],
        [field]: value
      };
    }

    // If marking as primary, set others as not primary
    if (field === 'is_primary' && value === true) {
      updatedVideos.forEach((video, i) => {
        if (i !== index) {
          updatedVideos[i] = { ...video, is_primary: false };
        }
      });
    }

    onChange(updatedVideos);
  };

  // Remove a video
  const handleRemoveVideo = (index: number) => {
    const updatedVideos = videos.filter((_, i) => i !== index);

    // If we removed the primary video and there are other videos, make the first one primary
    if (videos[index].is_primary && updatedVideos.length > 0) {
      updatedVideos[0] = { ...updatedVideos[0], is_primary: true };
    }

    onChange(updatedVideos);
  };

  // Helper function to extract YouTube ID
  const getYoutubeId = (url: string): string | null => {
    if (!url) return null;

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {videos.map((video, index) => (
          <div key={video.id} className="border rounded-md p-4 space-y-3">
            <div className="flex justify-between items-center">
              <h4 className="font-medium">Video {index + 1}</h4>
              <Button
                variant="destructive"
                size="icon"
                onClick={() => handleRemoveVideo(index)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor={`video-url-${index}`}>Video URL</Label>
                <div className="flex gap-2">
                  <Input
                    id={`video-url-${index}`}
                    value={video.video_url}
                    onChange={(e) => handleUpdateVideo(index, 'video_url', e.target.value)}
                    placeholder="https://youtube.com/watch?v=..."
                  />

                  {video.video_url && (
                    <Button
                      variant="outline"
                      size="icon"
                      type="button"
                      onClick={() => window.open(video.video_url, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor={`video-source-${index}`}>Video Source</Label>
                  <Select
                    value={video.video_source || ''}
                    onValueChange={(value) => handleUpdateVideo(index, 'video_source', value)}
                  >
                    <SelectTrigger id={`video-source-${index}`}>
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      {VIDEO_SOURCES.map((source) => (
                        <SelectItem key={source} value={source}>
                          {source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2 mt-auto">
                  <Switch
                    id={`is-primary-${index}`}
                    checked={video.is_primary}
                    onCheckedChange={(checked) => handleUpdateVideo(index, 'is_primary', checked)}
                  />
                  <Label htmlFor={`is-primary-${index}`}>Primary Video</Label>
                </div>
              </div>

              {/* Video preview */}
              {video.video_url && (
                <div className="mt-2">
                  {video.video_source === 'youtube' ||
                   video.video_url.includes('youtube.com') ||
                   video.video_url.includes('youtu.be') ? (
                    <div className="aspect-video">
                      <iframe
                        width="100%"
                        height="100%"
                        src={`https://www.youtube.com/embed/${getYoutubeId(video.video_url)}`}
                        title="YouTube video player"
                        style={{ border: 0 }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      ></iframe>
                    </div>
                  ) : (
                    <div className="aspect-video bg-gray-100 flex items-center justify-center">
                      <div className="text-center">
                        <Play className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-500">Video from {video.video_source || 'external source'}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <Button
        type="button"
        variant="outline"
        onClick={handleAddVideo}
        className="w-full flex items-center"
      >
        <PlusCircle className="h-4 w-4 mr-2" />
        Add Video
      </Button>
    </div>
  );
}