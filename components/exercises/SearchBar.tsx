'use client';

import { useState, useEffect, useCallback } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X, Loader2 } from "lucide-react";
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  onSearch: (value: string) => void;
  initialValue?: string;
  isSearching?: boolean;
  placeholder?: string;
  debounceMs?: number;
}

export default function SearchBar({
  onSearch,
  initialValue = '',
  isSearching = false,
  placeholder = 'Search...',
  debounceMs = 500
}: SearchBarProps) {
  const [inputValue, setInputValue] = useState(initialValue);
  const debouncedValue = useDebounce(inputValue, debounceMs);

  // Effect to handle debounced search
  useEffect(() => {
    if (debouncedValue !== initialValue) {
      onSearch(debouncedValue);
    }
  }, [debouncedValue, onSearch, initialValue]);

  // Update input when initialValue changes (from URL)
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  }, []);

  // Handle clear search
  const handleClear = useCallback(() => {
    setInputValue('');
    onSearch('');
  }, [onSearch]);

  // Handle form submission (immediate search)
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSearch(inputValue);
  }, [inputValue, onSearch]);

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSearch(inputValue);
    }
    if (e.key === 'Escape') {
      handleClear();
    }
  }, [inputValue, onSearch, handleClear]);

  return (
    <form onSubmit={handleSubmit} className="relative flex-1">
      <div className="relative">
        {/* Search Icon */}
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          {isSearching ? (
            <Loader2 className="h-4 w-4 text-gray-400 animate-spin" />
          ) : (
            <Search className="h-4 w-4 text-gray-400" />
          )}
        </div>

        {/* Input Field */}
        <Input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          className="pl-10 pr-10"
          disabled={isSearching}
        />

        {/* Clear Button */}
        {inputValue && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            disabled={isSearching}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Search Status */}
      {inputValue && (
        <div className="absolute top-full left-0 right-0 mt-1">
          <div className="text-xs text-gray-500 bg-white px-2 py-1 border rounded shadow-sm">
            {isSearching ? (
              <span className="flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                Searching...
              </span>
            ) : debouncedValue !== inputValue ? (
              <span>Typing...</span>
            ) : (
              <span>Press Enter to search immediately</span>
            )}
          </div>
        </div>
      )}
    </form>
  );
}