'use client'

import { useState, useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Users,
  Image,
  Video,
  Star,
  ChevronDown,
  ChevronUp,
  ChevronsUpDown
} from 'lucide-react';
import SafeImage from '@/components/ui/SafeImage';
import { getImageUrl } from '@/lib/utils/imageUtils';
import type { ConsolidationExercise } from './ConsolidationClient';

interface ExerciseSelectionTableProps {
  exercises: ConsolidationExercise[];
  selectedExerciseIds: Set<string>;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onExerciseSelect: (exerciseId: string, selected: boolean) => void;
  onSelectAll: (exerciseIds: string[]) => void;
  onClearSelection: () => void;
}

type SortField = 'name' | 'recordCount' | 'completeness' | 'nameQuality' | 'lastUsed';
type SortDirection = 'asc' | 'desc';

interface SortConfig {
  field: SortField;
  direction: SortDirection;
}

export default function ExerciseSelectionTable({
  exercises,
  selectedExerciseIds,
  searchTerm,
  onSearchChange,
  onExerciseSelect,
  onSelectAll,
  onClearSelection
}: ExerciseSelectionTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: 'name', direction: 'asc' });

  // Sorting logic
  const sortedExercises = useMemo(() => {
    const sorted = [...exercises].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortConfig.field) {
        case 'name':
          aValue = a.exercise_name.toLowerCase();
          bValue = b.exercise_name.toLowerCase();
          break;
        case 'recordCount':
          aValue = a.recordCount;
          bValue = b.recordCount;
          break;
        case 'completeness':
          aValue = a.completenessScore;
          bValue = b.completenessScore;
          break;
        case 'nameQuality':
          aValue = a.nameQualityScore;
          bValue = b.nameQualityScore;
          break;
        case 'lastUsed':
          aValue = a.lastUsed || '1900-01-01';
          bValue = b.lastUsed || '1900-01-01';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [exercises, sortConfig]);

  // Handle sort click
  const handleSort = (field: SortField) => {
    setSortConfig(current => ({
      field,
      direction: current.field === field && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return <ChevronsUpDown className="h-4 w-4 text-gray-400" />;
    }
    return sortConfig.direction === 'asc'
      ? <ChevronUp className="h-4 w-4 text-blue-600" />
      : <ChevronDown className="h-4 w-4 text-blue-600" />;
  };

  // Helper to get primary image URL
  const getPrimaryImageUrl = (exercise: ConsolidationExercise): string | null => {
    const primaryImage = exercise.images.find(img => img.is_primary) || exercise.images[0];
    if (!primaryImage) return null;

    // Use image_id since image_url doesn't exist in the type
    return getImageUrl(primaryImage.image_id || '');
  };

  // Check if all visible exercises are selected
  const allSelected = sortedExercises.length > 0 &&
    sortedExercises.every(exercise => selectedExerciseIds.has(exercise.id));

  // Check if some (but not all) visible exercises are selected
  const someSelected = sortedExercises.some(exercise => selectedExerciseIds.has(exercise.id));

  // Handle select all toggle
  const handleSelectAllToggle = () => {
    if (allSelected) {
      onClearSelection();
    } else {
      onSelectAll(sortedExercises.map(ex => ex.id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Controls */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search exercises by name, description, or category..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAllToggle}
              disabled={sortedExercises.length === 0}
            >
              {allSelected ? 'Deselect All' : 'Select All'}
            </Button>
            {selectedExerciseIds.size > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearSelection}
              >
                Clear Selection ({selectedExerciseIds.size})
              </Button>
            )}
          </div>
        </div>

        {/* Search Stats */}
        <div className="mt-3 text-sm text-gray-600">
          Showing {sortedExercises.length} exercises
          {searchTerm && ` matching "${searchTerm}"`}
          {selectedExerciseIds.size > 0 && ` • ${selectedExerciseIds.size} selected`}
        </div>
      </Card>

      {/* Exercise Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Table Header */}
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="px-4 py-3 text-left">
                  <Checkbox
                    checked={allSelected}
                    ref={(el) => {
                      if (el) {
                        const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                        if (checkbox) checkbox.indeterminate = someSelected && !allSelected;
                      }
                    }}
                    onCheckedChange={handleSelectAllToggle}
                  />
                </th>
                <th className="px-4 py-3 text-left">Image</th>
                <th
                  className="px-4 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center gap-2">
                    Exercise Name
                    {getSortIcon('name')}
                  </div>
                </th>
                <th className="px-4 py-3 text-left">Categories</th>
                <th
                  className="px-4 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('recordCount')}
                >
                  <div className="flex items-center gap-2">
                    Records
                    {getSortIcon('recordCount')}
                  </div>
                </th>
                <th className="px-4 py-3 text-left">Media</th>
                <th
                  className="px-4 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('completeness')}
                >
                  <div className="flex items-center gap-2">
                    Quality
                    {getSortIcon('completeness')}
                  </div>
                </th>
              </tr>
            </thead>

            {/* Table Body */}
            <tbody>
              {sortedExercises.map((exercise) => {
                const isSelected = selectedExerciseIds.has(exercise.id);
                const imageUrl = getPrimaryImageUrl(exercise);

                return (
                  <tr
                    key={exercise.id}
                    className={`border-b hover:bg-gray-50 transition-colors ${
                      isSelected ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    {/* Checkbox */}
                    <td className="px-4 py-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) =>
                          onExerciseSelect(exercise.id, checked as boolean)
                        }
                      />
                    </td>

                    {/* Image */}
                    <td className="px-4 py-3">
                      <div className="w-12 h-12">
                        {imageUrl ? (
                          <SafeImage
                            src={imageUrl}
                            alt={exercise.exercise_name}
                            width={48}
                            height={48}
                            className="rounded object-cover"
                          />
                        ) : (
                          <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                            <Image className="h-5 w-5 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Exercise Name */}
                    <td className="px-4 py-3">
                      <div>
                        <div className="font-medium text-gray-900">
                          {exercise.exercise_name}
                        </div>
                        {exercise.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {exercise.description}
                          </div>
                        )}
                        {/* Show "Has Records" badge exactly like working page */}
                        {exercise.recordCount > 0 && (
                          <div className="mt-1">
                            <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-medium">
                              Has Records
                            </span>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Categories */}
                    <td className="px-4 py-3">
                      <div className="flex flex-wrap gap-1">
                        {exercise.categories
                          .filter(cat => cat.is_primary)
                          .slice(0, 3)
                          .map((category, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {category.category_value}
                            </Badge>
                          ))}
                        {exercise.categories.filter(cat => cat.is_primary).length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{exercise.categories.filter(cat => cat.is_primary).length - 3}
                          </Badge>
                        )}
                      </div>
                    </td>

                    {/* Record Count */}
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className={`font-medium ${
                          exercise.recordCount > 0 ? 'text-green-600' : 'text-gray-400'
                        }`}>
                          {exercise.recordCount || 0}
                        </span>
                        {exercise.recordCount > 0 && (
                          <span className="text-xs text-green-600">(has records)</span>
                        )}
                      </div>
                    </td>

                    {/* Media */}
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        {exercise.images.length > 0 && (
                          <div className="flex items-center gap-1">
                            <Image className="h-4 w-4 text-blue-500" />
                            <span className="text-sm text-blue-600">{exercise.images.length}</span>
                          </div>
                        )}
                        {exercise.videos.length > 0 && (
                          <div className="flex items-center gap-1">
                            <Video className="h-4 w-4 text-purple-500" />
                            <span className="text-sm text-purple-600">{exercise.videos.length}</span>
                          </div>
                        )}
                        {exercise.images.length === 0 && exercise.videos.length === 0 && (
                          <span className="text-sm text-gray-400">None</span>
                        )}
                      </div>
                    </td>

                    {/* Quality Score */}
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${
                                star <= Math.ceil(exercise.completenessScore / 20)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-xs text-gray-500">
                          {exercise.completenessScore}%
                        </span>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          {/* Empty State */}
          {sortedExercises.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No exercises found</h3>
              <p className="text-gray-500">
                {searchTerm
                  ? `No exercises match "${searchTerm}". Try adjusting your search.`
                  : 'No exercises available for consolidation.'
                }
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* Selection Summary */}
      {selectedExerciseIds.size > 0 && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">
                  {selectedExerciseIds.size} exercises selected
                </span>
              </div>
              <div className="text-sm text-blue-700">
                Total records: {
                  sortedExercises
                    .filter(ex => selectedExerciseIds.has(ex.id))
                    .reduce((sum, ex) => sum + ex.recordCount, 0)
                }
              </div>
            </div>

            {selectedExerciseIds.size >= 2 && (
              <Badge className="bg-green-100 text-green-800 border-green-200">
                Ready for consolidation
              </Badge>
            )}

            {selectedExerciseIds.size === 1 && (
              <Badge variant="outline" className="text-orange-600 border-orange-200">
                Select at least 2 exercises
              </Badge>
            )}
          </div>

          {selectedExerciseIds.size >= 2 && (
            <div className="mt-3 text-sm text-blue-700">
              Selected exercises will be merged into a single exercise.
              Click &quot;Next&quot; to choose which exercise to keep as the master.
            </div>
          )}
        </Card>
      )}
    </div>
  );
}