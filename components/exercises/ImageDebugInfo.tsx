'use client'

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { getImageUrl } from '@/lib/utils/imageUtils';

// Import the base type
export type ExerciseImage = Database['public']['Tables']['exercise_images']['Row'];

// Create an extended interface for the component
interface ExtendedExerciseImage extends ExerciseImage {
  image_url?: string;
}

export default function ImageDebugInfo() {
  const [images, setImages] = useState<ExtendedExerciseImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function fetchImages() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('exercise_images')
          .select('*')
          .limit(5);

        if (error) {
          throw error;
        }

        setImages(data || []);
      } catch (err) {
        console.error('Error fetching images:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchImages();
  }, [supabase]);

  return (
    <div className="p-4 border rounded-md bg-gray-50 my-4">
      <h2 className="text-lg font-semibold mb-4">Image Debug Information</h2>

      {loading && <p className="text-gray-500">Loading image data...</p>}

      {error && (
        <div className="bg-red-100 p-3 rounded-md text-red-700 mb-4">
          Error: {error}
        </div>
      )}

      {images.length > 0 ? (
        <div className="space-y-4">
          {images.map((image) => {
            const imageUrl = getImageUrl(image.image_id || '', 'exercises');
            // Use image_id as a fallback since image_url might not exist
            const imageUrlFromImageUrl = getImageUrl(image.image_url || image.image_id || '', 'exercises');

            return (
              <div key={image.id} className="border p-3 rounded-md bg-white">
                <h3 className="font-medium">Image ID: {image.id}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <p className="text-sm text-gray-500">Raw Data:</p>
                    <pre className="bg-gray-100 p-2 rounded-md text-xs overflow-auto mt-1">
                      {JSON.stringify(image, null, 2)}
                    </pre>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium">Using image_id: {image.image_id}</p>
                      <p className="text-xs text-gray-500 break-all">{imageUrl}</p>
                      <div className="mt-2 h-24 relative">
                        <img
                          src={imageUrl}
                          alt="From image_id"
                          className="h-full object-contain"
                          onError={(e) => {
                            console.error('Error loading image from image_id:', image.image_id);
                            const imgElement = e.currentTarget;
                            imgElement.classList.add('border', 'border-red-500');
                            imgElement.title = 'Failed to load';
                          }}
                        />
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium">Using image_url: {image.image_url || 'N/A'}</p>
                      <p className="text-xs text-gray-500 break-all">{imageUrlFromImageUrl}</p>
                      <div className="mt-2 h-24 relative">
                        <img
                          src={imageUrlFromImageUrl}
                          alt="From image_url"
                          className="h-full object-contain"
                          onError={(e) => {
                            console.error('Error loading image from image_url:', image.image_url || image.image_id);
                            const imgElement = e.currentTarget;
                            imgElement.classList.add('border', 'border-red-500');
                            imgElement.title = 'Failed to load';
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        !loading && <p className="text-gray-500">No images found</p>
      )}
    </div>
  );
}
