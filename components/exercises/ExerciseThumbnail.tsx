'use client'

import { useState } from 'react';
import NextImage from 'next/image';
import { ImageIcon } from 'lucide-react';
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';

interface ExerciseThumbnailProps {
  imageUrl?: string;
  exerciseName: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function ExerciseThumbnail({
  imageUrl,
  exerciseName,
  size = 'md',
  className = ''
}: ExerciseThumbnailProps) {
  const [isError, setIsError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Determine dimensions based on size
  const getDimensions = () => {
    switch(size) {
      case 'sm': return { width: 8, height: 8 }; // 32px
      case 'lg': return { width: 16, height: 16 }; // 64px
      case 'md':
      default: return { width: 12, height: 12 }; // 48px
    }
  };

  const { width, height } = getDimensions();

  if (!imageUrl || isError) {
    return (
      <div className={`w-${width} h-${height} bg-gray-100 flex items-center justify-center rounded-md ${className}`}>
        <ImageIcon className={`w-${Math.floor(width/2)} h-${Math.floor(height/2)} text-gray-400`} />
      </div>
    );
  }

  return (
    <div className={`relative w-${width} h-${height} rounded-md overflow-hidden ${className} ${!isLoaded ? 'bg-gray-100' : ''}`}>
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-pulse w-full h-full bg-gray-200" />
        </div>
      )}
      <NextImage
        src={getImageUrl(imageUrl || '')}
        alt={exerciseName}
        className="object-cover"
        fill
        sizes={`${width * 4}px`}
        onError={(e) => {
          // Handle error by showing a placeholder
          const imgElement = e.currentTarget as HTMLImageElement;
          imgElement.src = getFallbackImageUrl('Exercise');
          setIsError(true);
        }}
        onLoad={() => setIsLoaded(true)}
      />
    </div>
  );
}