'use client'

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { X, PlusCircle } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import {
  BODY_PARTS,
  EQUIPMENT,
  EXPERTISE_LEVELS,
  MOVEMENT_CATEGORIES,
  ExerciseCategory
} from './ExercisesClient';

interface CategoryFormSectionProps {
  categories: ExerciseCategory[];
  onChange: (categories: ExerciseCategory[]) => void;
}

export default function CategoryFormSection({ categories, onChange }: CategoryFormSectionProps) {
  const [selectedCategoryType, setSelectedCategoryType] = useState<string>('');
  const [selectedCategoryValue, setSelectedCategoryValue] = useState<string>('');

  // Get categories by type
  const getCategoriesByType = (type: string) => {
    return categories.filter(cat => cat.category_type === type);
  };

  // Get primary category value for a type
  const getPrimaryCategoryValue = (type: string) => {
    return categories.find(cat =>
      cat.category_type === type && cat.is_primary
    )?.category_value || '';
  };

  // Get options for category type
  const getCategoryTypeOptions = () => {
    const options = [
      { value: 'body_part', label: 'Body Part', values: BODY_PARTS },
      { value: 'equipment', label: 'Equipment', values: EQUIPMENT },
      { value: 'expertise_level', label: 'Expertise Level', values: EXPERTISE_LEVELS },
      { value: 'movement_category', label: 'Movement Category', values: MOVEMENT_CATEGORIES },
      { value: 'movement_pattern', label: 'Movement Pattern', values: [] }
    ];

    return options;
  };

  // Get values for selected category type
  const getValuesForType = (type: string) => {
    const option = getCategoryTypeOptions().find(opt => opt.value === type);
    return option?.values || [];
  };

  // Add a category
  const handleAddCategory = () => {
    if (!selectedCategoryType || !selectedCategoryValue) return;

    // Check if category already exists
    const exists = categories.some(cat =>
      cat.category_type === selectedCategoryType &&
      cat.category_value === selectedCategoryValue
    );

    if (exists) return;

    // Create new category
    const newCategory: ExerciseCategory = {
      id: `temp-${uuidv4()}`,
      exercise_id: '',
      category_type: selectedCategoryType,
      category_value: selectedCategoryValue,
      is_primary: getCategoriesByType(selectedCategoryType).length === 0, // First category of type is primary
      created_at: new Date().toISOString()
    };

    onChange([...categories, newCategory]);

    // Reset selection
    setSelectedCategoryValue('');
  };

  // Remove a category
  const handleRemoveCategory = (categoryId: string) => {
    const categoryToRemove = categories.find(cat => cat.id === categoryId);
    if (!categoryToRemove) return;

    const updatedCategories = categories.filter(cat => cat.id !== categoryId);

    // If we removed a primary category and there are others of the same type,
    // make the first one primary
    if (categoryToRemove.is_primary) {
      const sameTypeCategories = updatedCategories.filter(
        cat => cat.category_type === categoryToRemove.category_type
      );

      if (sameTypeCategories.length > 0) {
        const indexToUpdate = updatedCategories.findIndex(
          cat => cat.id === sameTypeCategories[0].id
        );

        if (indexToUpdate !== -1) {
          updatedCategories[indexToUpdate] = {
            ...updatedCategories[indexToUpdate],
            is_primary: true
          };
        }
      }
    }

    onChange(updatedCategories);
  };

  // Set a category as primary
  const handleSetPrimary = (categoryId: string) => {
    const categoryToUpdate = categories.find(cat => cat.id === categoryId);
    if (!categoryToUpdate) return;

    const updatedCategories = categories.map(cat => {
      if (cat.category_type === categoryToUpdate.category_type) {
        return {
          ...cat,
          is_primary: cat.id === categoryId
        };
      }
      return cat;
    });

    onChange(updatedCategories);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Categories</h3>

      {/* Main Categories - Body Part, Equipment, Expertise Level, Movement Category */}
      <div className="grid grid-cols-2 gap-4">
        {/* Body Part */}
        <div>
          <Label htmlFor="body_part">Body Part</Label>
          <Select
            value={getPrimaryCategoryValue('body_part')}
            onValueChange={(value) => {
              // Remove old primary if exists
              const updatedCategories = categories.filter(
                cat => !(cat.category_type === 'body_part' && cat.is_primary)
              );

              // Add new primary
              const newCategory: ExerciseCategory = {
                id: `temp-${uuidv4()}`,
                exercise_id: '',
                category_type: 'body_part',
                category_value: value,
                is_primary: true,
                created_at: new Date().toISOString()
              };

              onChange([...updatedCategories, newCategory]);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select body part" />
            </SelectTrigger>
            <SelectContent>
              {BODY_PARTS.map((part) => (
                <SelectItem key={part} value={part}>
                  {part}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Equipment */}
        <div>
          <Label htmlFor="equipment">Equipment</Label>
          <Select
            value={getPrimaryCategoryValue('equipment')}
            onValueChange={(value) => {
              // Remove old primary if exists
              const updatedCategories = categories.filter(
                cat => !(cat.category_type === 'equipment' && cat.is_primary)
              );

              // Add new primary
              const newCategory: ExerciseCategory = {
                id: `temp-${uuidv4()}`,
                exercise_id: '',
                category_type: 'equipment',
                category_value: value,
                is_primary: true,
                created_at: new Date().toISOString()
              };

              onChange([...updatedCategories, newCategory]);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select equipment" />
            </SelectTrigger>
            <SelectContent>
              {EQUIPMENT.map((item) => (
                <SelectItem key={item} value={item}>
                  {item}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Expertise Level */}
        <div>
          <Label htmlFor="expertise_level">Expertise Level</Label>
          <Select
            value={getPrimaryCategoryValue('expertise_level')}
            onValueChange={(value) => {
              // Remove old primary if exists
              const updatedCategories = categories.filter(
                cat => !(cat.category_type === 'expertise_level' && cat.is_primary)
              );

              // Add new primary
              const newCategory: ExerciseCategory = {
                id: `temp-${uuidv4()}`,
                exercise_id: '',
                category_type: 'expertise_level',
                category_value: value,
                is_primary: true,
                created_at: new Date().toISOString()
              };

              onChange([...updatedCategories, newCategory]);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              {EXPERTISE_LEVELS.map((level) => (
                <SelectItem key={level} value={level}>
                  {level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Movement Category */}
        <div>
          <Label htmlFor="movement_category">Movement Category</Label>
          <Select
            value={getPrimaryCategoryValue('movement_category')}
            onValueChange={(value) => {
              // Remove old primary if exists
              const updatedCategories = categories.filter(
                cat => !(cat.category_type === 'movement_category' && cat.is_primary)
              );

              // Add new primary
              const newCategory: ExerciseCategory = {
                id: `temp-${uuidv4()}`,
                exercise_id: '',
                category_type: 'movement_category',
                category_value: value,
                is_primary: true,
                created_at: new Date().toISOString()
              };

              onChange([...updatedCategories, newCategory]);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {MOVEMENT_CATEGORIES.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Additional Categories */}
      <div className="border-t pt-4 mt-4">
        <h4 className="text-sm font-medium mb-3">Additional Categories</h4>

        {/* Current categories */}
        <div className="flex flex-wrap gap-2 mb-4">
          {categories.filter(cat =>
            // Skip primary categories of main types
            !(cat.is_primary &&
              ['body_part', 'equipment', 'expertise_level', 'movement_category'].includes(cat.category_type)
            )
          ).map(category => (
            <Badge
              key={category.id}
              variant="outline"
              className={`flex items-center gap-1 ${category.is_primary ? 'bg-blue-50' : ''}`}
            >
              <span className="text-xs text-gray-500">{category.category_type}:</span>
              {category.category_value}
              {!category.is_primary && (
                <Switch
                  id={`primary-${category.id}`}
                  checked={!!category.is_primary}
                  onCheckedChange={() => handleSetPrimary(category.id)}
                  className="ml-1 h-3 w-6"
                />
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 hover:bg-gray-200"
                onClick={() => handleRemoveCategory(category.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>

        {/* Add new category */}
        <div className="grid grid-cols-4 gap-2 items-end">
          <div className="col-span-1">
            <Label htmlFor="category-type" className="text-xs">Type</Label>
            <Select
              value={selectedCategoryType}
              onValueChange={setSelectedCategoryType}
            >
              <SelectTrigger id="category-type">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                {getCategoryTypeOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="col-span-2">
            <Label htmlFor="category-value" className="text-xs">Value</Label>
            {selectedCategoryType && getValuesForType(selectedCategoryType).length > 0 ? (
              <Select
                value={selectedCategoryValue}
                onValueChange={setSelectedCategoryValue}
              >
                <SelectTrigger id="category-value">
                  <SelectValue placeholder="Value" />
                </SelectTrigger>
                <SelectContent>
                  {getValuesForType(selectedCategoryType).map(value => (
                    <SelectItem key={value} value={value}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <input
                type="text"
                id="category-value"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={selectedCategoryValue}
                onChange={(e) => setSelectedCategoryValue(e.target.value)}
                placeholder="Custom value"
                disabled={!selectedCategoryType}
              />
            )}
          </div>

          <div className="col-span-1">
            <Button
              type="button"
              onClick={handleAddCategory}
              disabled={!selectedCategoryType || !selectedCategoryValue}
              className="w-full"
            >
              <PlusCircle className="h-4 w-4 mr-1" /> Add
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}