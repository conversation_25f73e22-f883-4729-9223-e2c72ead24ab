'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Image as ImageIcon, Video, ExternalLink } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ExerciseVideo, ExerciseImage } from './ExercisesClient';
import { getImageUrl } from '@/lib/utils/imageUtils';

interface MediaIndicatorProps {
  videos: ExerciseVideo[];
  images: ExerciseImage[];
  onVideoClick: (video: ExerciseVideo) => void;
}

export default function MediaIndicator({
  videos,
  images,
  onVideoClick
}: MediaIndicatorProps) {

  // Get primary video
  const primaryVideo = videos.find(video => video.is_primary) || videos[0];

  // Get primary image
  const primaryImage = images.find(image => image.is_primary) || images[0];

  // Get video source badge
  const getVideoSourceBadge = (source: string | null | undefined) => {
    if (!source) return null;

    let color = "bg-gray-100";
    let icon = null;

    switch(source) {
      case 'youtube':
        color = "bg-red-100 text-red-800";
        icon = <Video className="h-3 w-3 mr-1" />;
        break;
      case 'sweat.com':
        color = "bg-purple-100 text-purple-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
        break;
      case 'trainwell.com':
        color = "bg-blue-100 text-blue-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
        break;
      default:
        color = "bg-gray-100 text-gray-800";
        icon = <ExternalLink className="h-3 w-3 mr-1" />;
    }

    return (
      <Badge className={`${color} flex items-center text-xs`}>
        {icon}
        {source}
      </Badge>
    );
  };

  return (
    <div className="flex items-center space-x-3">
      {/* Video indicator */}
      {videos.length > 0 && (
        <div className="flex items-center">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onVideoClick(primaryVideo)}
                  className="h-8 w-8"
                >
                  <Play className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Watch primary video</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {videos.length > 1 && (
            <Badge variant="outline" className="ml-1 text-xs">
              +{videos.length - 1}
            </Badge>
          )}

          {primaryVideo?.video_source && getVideoSourceBadge(primaryVideo.video_source)}
        </div>
      )}

      {/* Image indicator */}
      {images.length > 0 && (
        <div className="flex items-center">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  asChild
                >
                  <a
                    href={getImageUrl(primaryImage.image_id || '')}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ImageIcon className="h-4 w-4" />
                  </a>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>View primary image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {images.length > 1 && (
            <Badge variant="outline" className="ml-1 text-xs">
              +{images.length - 1}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}