'use client'

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  ExerciseMovement as BaseExerciseMovement,
  ExerciseVideo as BaseExerciseVideo,
  ExerciseImage as BaseExerciseImage,
  ExerciseCategory
} from './ExercisesClient';

// Create custom interfaces for the form
interface ExerciseVideo extends BaseExerciseVideo {
  is_primary: boolean; // Override to make it non-nullable
}

interface ExerciseImage extends Omit<BaseExerciseImage, 'image_id'> {
  image_url: string;
  is_primary: boolean; // Override to make it non-nullable
}

// Custom interface for the form data
interface ExerciseFormData {
  id: string;
  exercise_name: string;
  description: string | null;
  videos: ExerciseVideo[];
  images: ExerciseImage[];
  categories: ExerciseCategory[];
  created_at: string | null;
  hasRecords?: boolean;
}
import VideoFormSection from './VideoFormSection';
import ImageFormSection from './ImageFormSection';
import CategoryFormSection from './CategoryFormSection';
import MediaGallery from './MediaGallery';

interface ExerciseFormProps {
  exercise: BaseExerciseMovement | null;
  onSave: (exercise: BaseExerciseMovement) => void;
  onCancel: () => void;
}

export default function ExerciseForm({ exercise, onSave, onCancel }: ExerciseFormProps) {
  const [currentTab, setCurrentTab] = useState('details');

  // Initialize form data from exercise or create default values
  const [formData, setFormData] = useState<ExerciseFormData>({
    id: exercise?.id || '',
    exercise_name: exercise?.exercise_name || '',
    description: exercise?.description || '',
    videos: exercise?.videos as ExerciseVideo[] || [],
    images: exercise?.images as unknown as ExerciseImage[] || [],
    categories: exercise?.categories || [],
    created_at: exercise?.created_at || '',
    hasRecords: exercise?.hasRecords || false
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Cast formData to BaseExerciseMovement for compatibility with the parent component
    onSave(formData as unknown as BaseExerciseMovement);
  };

  // Handle basic field change
  const handleChange = (field: keyof ExerciseFormData, value: string | number | boolean | null) => {
    setFormData((prev: ExerciseFormData) => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle videos change
  const handleVideosChange = (videos: ExerciseVideo[]) => {
    setFormData((prev: ExerciseFormData) => ({
      ...prev,
      videos
    }));
  };

  // Handle images change
  const handleImagesChange = (images: ExerciseImage[]) => {
    setFormData((prev: ExerciseFormData) => ({
      ...prev,
      images
    }));
  };

  // Handle categories change
  const handleCategoriesChange = (categories: ExerciseCategory[]) => {
    setFormData((prev: ExerciseFormData) => ({
      ...prev,
      categories
    }));
  };

  // Helper function to get primary category value (used in the UI)
  // This is used in the preview tab via MediaGallery

  // Get helper for videos/images count
  const getVideoCount = () => formData.videos?.length || 0;
  const getImageCount = () => formData.images?.length || 0;

  return (
    <div>
      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Basic Details</TabsTrigger>
          <TabsTrigger value="media">
            Media ({getVideoCount()} videos, {getImageCount()} images)
          </TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <form onSubmit={handleSubmit} className="mt-4">
          {/* Basic Details Tab */}
          <TabsContent value="details" className="space-y-4">
            <div>
              <Label htmlFor="exercise_name">Exercise Name</Label>
              <Input
                id="exercise_name"
                value={formData.exercise_name}
                onChange={(e) => handleChange('exercise_name', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Describe the exercise..."
                rows={3}
              />
            </div>

            <div className="border-t pt-4">
              <CategoryFormSection
                categories={formData.categories || []}
                onChange={handleCategoriesChange}
              />
            </div>
          </TabsContent>

          {/* Media Tab */}
          <TabsContent value="media" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Videos</h3>
              <VideoFormSection
                videos={formData.videos || []}
                onChange={handleVideosChange}
              />
            </div>

            <div className="border-t pt-6">
              <h3 className="text-lg font-medium mb-4">Images</h3>
              <ImageFormSection
                images={formData.images || []}
                onChange={handleImagesChange}
              />
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-4">
            <MediaGallery
              exercise={formData as unknown as BaseExerciseMovement}
              showDescriptions={true}
              showCategories={true}
            />
          </TabsContent>

          {/* Form Buttons - Always Visible */}
          <div className="flex justify-between items-center border-t pt-4 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button type="submit">
              {exercise ? 'Update Exercise' : 'Create Exercise'}
            </Button>
          </div>
        </form>
      </Tabs>
    </div>
  );
}