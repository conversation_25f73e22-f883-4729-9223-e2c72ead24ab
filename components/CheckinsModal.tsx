import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from 'react-hot-toast';

interface Checkin {
  id: string;
  pelati_id: string;
  checkin_date: string;
  notes: string;
}

interface CheckinsModalProps {
  pelatiId: string;
  pelatiName: string;
  isOpen: boolean;
  onClose: () => void;
}

const CheckinsModal: React.FC<CheckinsModalProps> = ({ pelatiId, pelatiName, isOpen, onClose }) => {
  const [checkins, setCheckins] = useState<Checkin[]>([]);
  const supabase = createClientComponentClient();

  useEffect(() => {
    if (isOpen) {
      fetchCheckins();
    }
  }, [isOpen, pelatiId]);

  const fetchCheckins = async () => {
    const { data, error } = await supabase
      .from('checkins')
      .select('*')
      .eq('pelati_id', pelatiId)
      .order('checkin_date', { ascending: false });

    if (error) {
      console.error('Error fetching checkins:', error);
      toast.error('Failed to fetch checkins data');
    } else if (data) {
      setCheckins(data);
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{`Check-ins for ${pelatiName}`}</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {checkins.map((checkin) => (
                <TableRow key={checkin.id}>
                  <TableCell>{formatDate(checkin.checkin_date)}</TableCell>
                  <TableCell>{checkin.notes}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 flex justify-end">
          <Button onClick={onClose}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CheckinsModal;