import React from 'react';
import { ExpenseAnalysisDatum } from '../../hooks/useExpenseAnalysis';

interface Props {
  data: ExpenseAnalysisDatum[];
  loading: boolean;
  error: string | null;
}

const ExpenseAnalysis: React.FC<Props> = ({ data, loading, error }) => {
  if (loading) return <div>Loading expense analysis...</div>;
  if (error) return <div style={{ color: 'red' }}>Error: {error}</div>;
  if (!data || data.length === 0) return <div>No data</div>;

  return (
    <div style={{ overflowX: 'auto' }}>
      <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
        <thead>
          <tr style={{ background: '#f5f5f5' }}>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Category</th>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Total (€)</th>
          </tr>
        </thead>
        <tbody>
          {data.map(row => (
            <tr key={row.category}>
              <td style={{ padding: 8, border: '1px solid #eee' }}>{row.category}</td>
              <td style={{ padding: 8, border: '1px solid #eee', color: 'red', fontWeight: 500 }}>{row.total.toLocaleString(undefined, { minimumFractionDigits: 2 })}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ExpenseAnalysis; 