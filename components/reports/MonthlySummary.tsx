import React from 'react';
import { MonthlySummaryDatum } from '../../hooks/useMonthlySummary';

interface Props {
  data: MonthlySummaryDatum[];
  loading: boolean;
  error: string | null;
}

const MonthlySummary: React.FC<Props> = ({ data, loading, error }) => {
  if (loading) return <div>Loading monthly summary...</div>;
  if (error) return <div style={{ color: 'red' }}>Error: {error}</div>;
  if (!data || data.length === 0) return <div>No data</div>;

  return (
    <div style={{ overflowX: 'auto' }}>
      <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
        <thead>
          <tr style={{ background: '#f5f5f5' }}>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Month</th>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Income (€)</th>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Expenses (€)</th>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Net (€)</th>
          </tr>
        </thead>
        <tbody>
          {data.map(row => (
            <tr key={row.month}>
              <td style={{ padding: 8, border: '1px solid #eee' }}>{row.month}</td>
              <td style={{ padding: 8, border: '1px solid #eee', color: 'green', fontWeight: 500 }}>{row.income.toLocaleString(undefined, { minimumFractionDigits: 2 })}</td>
              <td style={{ padding: 8, border: '1px solid #eee', color: 'red', fontWeight: 500 }}>{row.expenses.toLocaleString(undefined, { minimumFractionDigits: 2 })}</td>
              <td style={{ padding: 8, border: '1px solid #eee', color: row.income - row.expenses >= 0 ? 'green' : 'red', fontWeight: 600 }}>{(row.income - row.expenses).toLocaleString(undefined, { minimumFractionDigits: 2 })}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MonthlySummary; 