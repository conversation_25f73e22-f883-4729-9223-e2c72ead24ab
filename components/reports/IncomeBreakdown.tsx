import React from 'react';
import { IncomeBreakdownDatum } from '../../hooks/useIncomeBreakdown';

interface Props {
  data: IncomeBreakdownDatum[];
  loading: boolean;
  error: string | null;
}

const IncomeBreakdown: React.FC<Props> = ({ data, loading, error }) => {
  if (loading) return <div>Loading income breakdown...</div>;
  if (error) return <div style={{ color: 'red' }}>Error: {error}</div>;
  if (!data || data.length === 0) return <div>No data</div>;

  return (
    <div style={{ overflowX: 'auto' }}>
      <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
        <thead>
          <tr style={{ background: '#f5f5f5' }}>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Payment Method</th>
            <th style={{ padding: 8, border: '1px solid #eee' }}>Total (€)</th>
          </tr>
        </thead>
        <tbody>
          {data.map(row => (
            <tr key={row.payment_method}>
              <td style={{ padding: 8, border: '1px solid #eee' }}>{row.payment_method}</td>
              <td style={{ padding: 8, border: '1px solid #eee', color: 'green', fontWeight: 500 }}>{row.total.toLocaleString(undefined, { minimumFractionDigits: 2 })}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default IncomeBreakdown; 