import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, 
  <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';
import { <PERSON>, CardHeader, <PERSON>Title, CardContent } from '@/components/ui/card';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { useSupabase } from '@/hooks/useSupabase';

const COLORS = [
  '#0088FE', // Blue
  '#00C49F', // Teal
  '#FFBB28', // Orange
  '#FF8042', // Coral
  '#8884D8', // Purple
  '#FF6384'  // Pink
];


// Define types for our RPC function returns
type NotificationType = {
  type: string;
  count: number;
}

type HourlyVolume = {
  hour: string;
  count: number;
}

type UnreadByClient = {
  client_id: string;
  client_name: string;
  unread_count: number;
}

interface MetricsState {
  typeDistribution: NotificationType[];
  hourlyVolume: HourlyVolume[];
  errorRate: number;
  unreadByClient: UnreadByClient[];
  adminNotifications: number;
  duplicateCount: number;
}

const NotificationDashboard = () => {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<MetricsState>({
    typeDistribution: [],
    hourlyVolume: [],
    errorRate: 0,
    unreadByClient: [],
    adminNotifications: 0,
    duplicateCount: 0
  });

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Type Distribution Query
        const { data: typeData } = await supabase.rpc(
          'get_notification_type_distribution'
        ) as { data: NotificationType[] | null };
        
        // Hourly Volume Query
        const { data: volumeData } = await supabase.rpc(
          'get_notification_hourly_volume'
        ) as { data: HourlyVolume[] | null };
        
        // Error Rate Query
        const { count: errorCount } = await supabase
          .from('notifications')
          .select('*', { count: 'exact', head: true })
          .eq('type', 'error')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
        
        // Unread by Client Query
        const { data: unreadData } = await supabase.rpc(
          'get_unread_notifications_by_client'
        ) as { data: UnreadByClient[] | null };
        
        // Admin Notifications Count
        const { count: adminCount } = await supabase
          .from('notifications')
          .select('*', { count: 'exact', head: true })
          .eq('admin_impersonation', true)
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
        
        // Duplicate Notifications Count
        const { data: duplicateData } = await supabase.rpc(
          'get_duplicate_notifications_count'
        ) as { data: { count: number }[] | null };
        
        setMetrics({
          typeDistribution: typeData || [],
          hourlyVolume: volumeData || [],
          errorRate: errorCount || 0,
          unreadByClient: unreadData || [],
          adminNotifications: adminCount || 0,
          duplicateCount: duplicateData?.[0]?.count || 0
        });

      } catch (error) {
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 300000);
    return () => clearInterval(interval);
  }, [supabase]);





  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {/* Notification Type Distribution */}
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Notification Types (24h)</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={metrics.typeDistribution}
                dataKey="count"
                nameKey="type"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label
              >
                {metrics.typeDistribution.map((entry, index) => (
                  <Cell key={entry.type} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Hourly Volume */}
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Notification Volume (24h)</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={metrics.hourlyVolume}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="count" stroke="#8884d8" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Error Rate */}
      <Card>
        <CardHeader>
          <CardTitle>System Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium">Errors (24h)</p>
              <p className="text-2xl font-bold">{metrics.errorRate}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Duplicate Notifications</p>
              <p className="text-2xl font-bold">{metrics.duplicateCount}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Admin Notifications</p>
              <p className="text-2xl font-bold">{metrics.adminNotifications}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Unread by Client */}
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Unread Notifications by Client</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={metrics.unreadByClient}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="client_name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="unread_count" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationDashboard;