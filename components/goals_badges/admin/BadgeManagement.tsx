// components/admin/BadgeManagement.tsx
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "react-hot-toast";
import { BadgeGrid } from './BadgeGrid';
import type { Database } from '@/types/supabase';

type Badge = Database['public']['Tables']['badges']['Row'];
type BadgeLevel = Database['public']['Enums']['badge_level'];
type BadgeCategory = Database['public']['Enums']['badge_category'];

interface BadgeFormData {
  id?: string;
  name: string;
  description: string;
  category: BadgeCategory;
  level: BadgeLevel;
  requirements: Record<string, number>;
}

interface BadgeError {
  message: string;
}

export function BadgeManagement() {
  const { supabase } = useSupabase();
  const [badges, setBadges] = useState<Badge[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingBadge, setEditingBadge] = useState<string | null>(null);
  const [formData, setFormData] = useState<BadgeFormData>({
    name: '',
    description: '',
    category: 'attendance',
    level: 'bronze',
    requirements: {}
  });
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (data: BadgeFormData): string | null => {
    if (!data.name.trim()) return 'Name is required';
    if (!data.description.trim()) return 'Description is required';
    if (!data.category) return 'Category is required';
    if (!data.level) return 'Level is required';
    return null;
  };

  const fetchBadges = useCallback(async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('badges')
        .select('*')
        .order('name');

      if (error) throw error;
      setBadges(data || []);
    } catch (error) {
      console.error('Error fetching badges:', error);
      toast.error('Failed to fetch badges');
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchBadges();
  }, [fetchBadges]);

  const handleEdit = (badge: Badge) => {
    setEditingBadge(badge.id);
    const requirements = typeof badge.requirements === 'object' && badge.requirements !== null 
      ? badge.requirements as Record<string, number>
      : {};

    setFormData({
      id: badge.id,
      name: badge.name,
      description: badge.description,
      category: badge.category,
      level: badge.level,
      requirements
    });
    setIsDialogOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const error = validateForm(formData);
    if (error) {
      toast.error(error);
      return;
    }

    try {
      const { error } = editingBadge
        ? await supabase
            .from('badges')
            .update(formData)
            .eq('id', editingBadge)
        : await supabase
            .from('badges')
            .insert([formData]);

      if (error) throw error;

      toast.success(editingBadge ? 'Badge updated successfully' : 'Badge created successfully');
      setIsDialogOpen(false);
      resetForm();
      fetchBadges();
    } catch (error) {
      const badgeError = error as BadgeError;
      console.error('Error saving badge:', badgeError);
      toast.error(badgeError.message || 'Failed to save badge');
    }
  };

  const resetForm = () => {
    setEditingBadge(null);
    setFormData({
      name: '',
      description: '',
      category: 'attendance',
      level: 'bronze',
      requirements: {}
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">System Badges</h2>
        <Button onClick={() => {
          resetForm();
          setIsDialogOpen(true);
        }}>
          Create Badge
        </Button>
      </div>
  
      <BadgeGrid 
        badges={badges} 
        onEdit={handleEdit} 
        isLoading={isLoading} 
      />



      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingBadge ? 'Edit Badge' : 'Create Badge'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    name: e.target.value
                  }))}
                  required
                />
              </div>

              <div>
                <Label htmlFor="level">Level</Label>
                <Select
                  value={formData.level}
                  onValueChange={(value: BadgeLevel) => setFormData(prev => ({
                    ...prev,
                    level: value
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    {['bronze', 'silver', 'gold', 'platinum'].map(level => (
                      <SelectItem key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value: BadgeCategory) => setFormData(prev => ({
                  ...prev,
                  category: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {['attendance', 'exercise', 'progression', 'consistency', 'milestone'].map(category => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setIsDialogOpen(false);
                  resetForm();
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {editingBadge ? 'Update' : 'Create'} Badge
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}