'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Badge } from "@/components/ui/badge";
import type { Database } from '@/types/supabase';

// Define types from the Database type
type BadgeLevel = Database['public']['Enums']['badge_level'];
type BadgeRow = Database['public']['Tables']['badges']['Row'];
type UserBadgeRow = Database['public']['Tables']['user_badges']['Row'];
type AssignedGoalRow = Database['public']['Tables']['assigned_goals']['Row'];
type Json = Database['public']['Tables']['user_badges']['Row']['progress'];

// Define the shape of the joined user badge data
interface UserBadgeWithDetails extends Omit<UserBadgeRow, 'progress'> {
  progress: Json;
  badges: BadgeRow;
}

interface UserAssignmentsProps {
  userId: string;
}

export function UserAssignments({ userId }: UserAssignmentsProps) {
  const { supabase } = useSupabase();
  const [badges, setBadges] = useState<UserBadgeWithDetails[]>([]);
  const [goals, setGoals] = useState<AssignedGoalRow[]>([]);

  useEffect(() => {
    fetchUserAssignments();
  }, [userId]);

  const fetchUserAssignments = async () => {
    try {
      // Fetch user's badges
      const { data: badgeData, error: badgeError } = await supabase
        .from('user_badges')
        .select(`
          *,
          badges (*)
        `)
        .eq('pelatis_id', userId);

      if (badgeError) throw badgeError;
      
      // Transform the data with the correct typing
      const transformedBadges = (badgeData || []).map(badge => ({
        ...badge,
        progress: badge.progress || null
      })) as UserBadgeWithDetails[];
      
      setBadges(transformedBadges);

      // Fetch user's active goals
      const { data: goalData, error: goalError } = await supabase
        .from('assigned_goals')
        .select('*')
        .eq('user_id', userId)
        .not('status', 'eq', 'completed');

      if (goalError) throw goalError;
      setGoals(goalData || []);
    } catch (error) {
      console.error('Error fetching assignments:', error);
    }
  };

  const getLevelColor = (level: BadgeLevel): string => {
    const colors: Record<BadgeLevel, string> = {
      bronze: 'bg-orange-200 text-orange-800',
      silver: 'bg-gray-200 text-gray-800',
      gold: 'bg-yellow-200 text-yellow-800',
      platinum: 'bg-blue-200 text-blue-800'
    };
    return colors[level] || 'bg-gray-200 text-gray-800';
  };

  return (
    <div className="space-y-3">
      {badges.length > 0 && (
        <div>
          <p className="text-sm text-gray-500 mb-1">Badges:</p>
          <div className="flex flex-wrap gap-2">
            {badges.map((badge) => (
              <Badge 
                key={badge.id} 
                className={getLevelColor(badge.badges.level)}
              >
                {badge.badges.name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {goals.length > 0 && (
        <div>
          <p className="text-sm text-gray-500 mb-1">Active Goals:</p>
          <div className="space-y-1">
            {goals.map((goal) => (
              <div key={goal.id} className="flex justify-between text-sm">
                <span>{goal.title}</span>
                <Badge variant={goal.status === 'pending' ? 'outline' : 'default'}>
                  {goal.current_value}/{goal.target_value}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {badges.length === 0 && goals.length === 0 && (
        <p className="text-sm text-gray-500">No active assignments</p>
      )}
    </div>
  );
}