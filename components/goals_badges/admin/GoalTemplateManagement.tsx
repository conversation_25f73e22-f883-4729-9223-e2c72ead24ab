'use client';

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from 'react-hot-toast';

type GoalTemplate = Database['public']['Tables']['goal_templates']['Row'];

export function GoalTemplateManagement() {
  const [templates, setTemplates] = useState<GoalTemplate[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    title: '',
    description: '',
    category: '',
    target_value: 0,
    duration_days: 30,
    is_active: true
  });

  const supabase = createClientComponentClient<Database>();

  const fetchTemplates = async () => {
    const { data, error } = await supabase
      .from('goal_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      toast.error('Failed to fetch templates');
      return;
    }

    setTemplates(data);
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleCreateTemplate = async (e: React.FormEvent) => {
    e.preventDefault();

    const { data: authUser } = await supabase.auth.getUser();
    if (!authUser.user) {
      toast.error('Not authenticated');
      return;
    }

    const { error } = await supabase
      .from('goal_templates')
      .insert({
        ...newTemplate,
        created_by: authUser.user.id
      });

    if (error) {
      toast.error('Failed to create template');
      return;
    }

    toast.success('Template created successfully');
    setIsAddDialogOpen(false);
    fetchTemplates();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Goal Templates</h2>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          Create Template
        </Button>
      </div>

      <div className="grid gap-4">
        {templates.map((template) => (
          <div
            key={template.id}
            className="p-4 border rounded-lg bg-white shadow-sm"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{template.title}</h3>
                <p className="text-sm text-gray-600">{template.description}</p>
              </div>
              <div className="text-sm">
                {template.is_active ? (
                  <span className="text-green-600">Active</span>
                ) : (
                  <span className="text-red-600">Inactive</span>
                )}
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              <p>Category: {template.category}</p>
              <p>Target: {template.target_value}</p>
              <p>Duration: {template.duration_days} days</p>
            </div>
          </div>
        ))}
      </div>

      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Goal Template</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateTemplate} className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={newTemplate.title}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  title: e.target.value
                }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select
                value={newTemplate.category}
                onValueChange={(value) => setNewTemplate(prev => ({
                  ...prev,
                  category: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly_checkins">Monthly Check-ins</SelectItem>
                  <SelectItem value="attendance_streak">Attendance Streak</SelectItem>
                  <SelectItem value="weight_lifted">Weight Lifted</SelectItem>
                  <SelectItem value="personal_records">Personal Records</SelectItem>
                  <SelectItem value="exercise_variety">Exercise Variety</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="target_value">Target Value</Label>
              <Input
                id="target_value"
                type="number"
                value={newTemplate.target_value}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  target_value: parseInt(e.target.value)
                }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="duration_days">Duration (days)</Label>
              <Input
                id="duration_days"
                type="number"
                value={newTemplate.duration_days}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  duration_days: parseInt(e.target.value)
                }))}
                required
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Create</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
