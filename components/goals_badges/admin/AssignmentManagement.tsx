// components/admin/AssignmentManagement.tsx
'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "react-hot-toast";
import { UserAssignments } from './UserAssignments';
import type { Database } from '@/types/supabase';

// Define types from Database

type Badge = Database['public']['Tables']['badges']['Row'];
type GoalTemplate = Database['public']['Tables']['goal_templates']['Row'];

type AssignedGoalInsert = Database['public']['Tables']['assigned_goals']['Insert'];


// Define User interface based on the fields we need from pelates
interface User {
  id: string;
  name: string | null;
  last_name: string | null;
}

export function AssignmentManagement() {
  const { supabase } = useSupabase();
  const [users, setUsers] = useState<User[]>([]);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [goalTemplates, setGoalTemplates] = useState<GoalTemplate[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'badge' | 'goal'>('badge');
  const [selectedItem, setSelectedItem] = useState<string>('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch users (pelates)
      const { data: userData, error: userError } = await supabase
        .from('pelates')
        .select('id, name, last_name')
        .order('name');

      if (userError) throw userError;
      setUsers(userData || []);

      // Fetch badges
      const { data: badgeData, error: badgeError } = await supabase
        .from('badges')
        .select('*');

      if (badgeError) throw badgeError;
      setBadges(badgeData || []);

      // Fetch goal templates
      const { data: templateData, error: templateError } = await supabase
        .from('goal_templates')
        .select('*')
        .eq('is_active', true);

      if (templateError) throw templateError;
      setGoalTemplates(templateData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch data');
    }
  };


  const handleGoalAssignment = async (assignedGoal: AssignedGoalInsert) => {
    const { error } = await supabase
      .from('assigned_goals')
      .insert(assignedGoal);
    
    if (error) throw error;
    return { error: null };
  };
  
  // Then in your handleAssignment function:
  const handleAssignment = async () => {
    if (!selectedUser || !selectedItem) {
      toast.error('Please select both a user and an item to assign');
      return;
    }
  
    try {
      if (assignmentType === 'badge') {
        const { error } = await supabase
          .from('user_badges')
          .insert({
            pelatis_id: selectedUser,
            badge_id: selectedItem,
            achieved_at: new Date().toISOString(),
            progress: {}
          });
  
        if (error) throw error;
        toast.success('Badge assigned successfully');
      } else {
        const template = goalTemplates.find(t => t.id === selectedItem);
        if (!template) throw new Error('Goal template not found');
  
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('No authenticated user');
  
        const assignedGoal: AssignedGoalInsert = {
          assigned_by: user.id,
          pelatis_id: selectedUser,
          template_id: selectedItem,
          title: template.title,
          description: template.description,
          category: template.category,
          target_value: template.target_value,
          start_date: new Date().toISOString(),
          due_date: template.duration_days 
            ? new Date(Date.now() + template.duration_days * 24 * 60 * 60 * 1000).toISOString()
            : null,
          current_value: 0,
          status: 'pending'
        };
  
        const { error } = await handleGoalAssignment(assignedGoal);
        if (error) throw error;
        
        toast.success('Goal assigned successfully');
      }
  
      setIsAssignDialogOpen(false);
      resetForm();
      fetchData(); // Refresh the data
    } catch (error) {
      console.error('Error assigning item:', error);
      toast.error('Failed to assign item');
    }
  };

  const resetForm = () => {
    setSelectedUser('');
    setSelectedItem('');
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">User Assignments</h2>
        <Button onClick={() => setIsAssignDialogOpen(true)}>
          Assign Badge/Goal
        </Button>
      </div>

      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign to User</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Select User</label>
              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a user" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} {user.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Tabs value={assignmentType} onValueChange={(value) => {
              setAssignmentType(value as 'badge' | 'goal');
              setSelectedItem('');
            }}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="badge">Badge</TabsTrigger>
                <TabsTrigger value="goal">Goal</TabsTrigger>
              </TabsList>

              <TabsContent value="badge">
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Select Badge</label>
                  <Select value={selectedItem} onValueChange={setSelectedItem}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a badge" />
                    </SelectTrigger>
                    <SelectContent>
                      {badges.map(badge => (
                        <SelectItem key={badge.id} value={badge.id}>
                          {badge.name} ({badge.level})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="goal">
                <div className="space-y-2">
                  <label className="block text-sm font-medium mb-1">Select Goal Template</label>
                  <Select value={selectedItem} onValueChange={setSelectedItem}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a goal template" />
                    </SelectTrigger>
                    <SelectContent>
                      {goalTemplates.map(template => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAssignDialogOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleAssignment}>
              Assign {assignmentType === 'badge' ? 'Badge' : 'Goal'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Display current assignments */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {users.map(user => (
          <Card key={user.id}>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2">
                {user.name} {user.last_name}
              </h3>
              <div className="space-y-2">
                <UserAssignments userId={user.id} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}