// components/admin/BadgeGrid.tsx
import { Badge as UIBadge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import * as Icons from 'lucide-react';
import { LucideIcon } from 'lucide-react';  // Add this import
import type { Database } from '@/types/supabase';

type Badge = Database['public']['Tables']['badges']['Row'];

const levelColors = {
  bronze: 'bg-orange-200 text-orange-800',
  silver: 'bg-gray-200 text-gray-800',
  gold: 'bg-yellow-200 text-yellow-800',
  platinum: 'bg-blue-200 text-blue-800'
} as const;

interface BadgeGridProps {
  badges: Badge[];
  onEdit: (badge: Badge) => void;
  isLoading?: boolean;
}

export function BadgeGrid({ badges, onEdit, isLoading = false }: BadgeGridProps) {
  // Group badges by category
  const groupedBadges = badges.reduce((acc, badge) => {
    if (!acc[badge.category]) {
      acc[badge.category] = [];
    }
    acc[badge.category].push(badge);
    return acc;
  }, {} as Record<string, Badge[]>);

  // Sort badges within each category by level
  const levelOrder = { bronze: 0, silver: 1, gold: 2, platinum: 3 };
  Object.values(groupedBadges).forEach(group => {
    group.sort((a, b) => levelOrder[a.level] - levelOrder[b.level]);
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3].map((n) => (
          <Card key={n} className="relative">
            <CardContent className="p-4">
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-full" />
              </div>
              <div className="mt-4 flex gap-2">
                <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-5 w-20 bg-gray-200 rounded animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (badges.length === 0) {
    return (
      <Card className="p-8">
        <CardContent className="text-center text-gray-500">
          No badges found. Create your first badge to get started.
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {Object.entries(groupedBadges).map(([category, categoryBadges]) => (
        <div key={category}>
          <h3 className="text-lg font-semibold capitalize mb-3">{category}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categoryBadges.map((badge) => {
              const IconComponent: LucideIcon = badge.icon_content 
                ? Icons[badge.icon_content as keyof typeof Icons] as LucideIcon
                : Icons.Medal;

              return (
                <Card key={badge.id} className="relative group">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <IconComponent 
                        className={levelColors[badge.level]} 
                        size={24}
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold">{badge.name}</h4>
                        <p className="text-sm text-gray-600">
                          {badge.description}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 flex gap-2">
                      <UIBadge className={levelColors[badge.level]}>
                        {badge.level}
                      </UIBadge>
                    </div>
                    <button
                      onClick={() => onEdit(badge)}
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Icons.Edit2 size={16} />
                    </button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}