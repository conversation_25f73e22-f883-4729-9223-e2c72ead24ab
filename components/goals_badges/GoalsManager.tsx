// components/GoalsManager.tsx
'use client';

import React from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { CheckCircle2 } from 'lucide-react';
import type { Database } from '@/types/supabase';
import GoalCard from '@/components/goals_badges/GoalCard';

type AssignedGoal = Database["public"]["Tables"]["assigned_goals"]["Row"];


interface Goal extends AssignedGoal {
  created_at: string;
  updated_at: string;
}

type GoalCategory = typeof GOAL_CATEGORIES[number]['value'];
type GoalStatus = Database["public"]["Enums"]["goal_status"];

interface GoalFormData {
  title: string;
  description: string;
  category: GoalCategory | '';
  target_value: number;
  current_value: number;
  due_date: string;  // maps to due_date in the database
}

const GOAL_CATEGORIES = [
  { value: 'monthly_checkins', label: 'Monthly Check-ins' },
  { value: 'weight_lifted', label: 'Total Weight Lifted' },
  { value: 'personal_records', label: 'Personal Records' },
  { value: 'attendance_streak', label: 'Attendance Streak' },
  { value: 'exercise_variety', label: 'Exercise Variety' },
] as const;

export default function GoalsManager({ userId }: { userId: string }) {
  const { supabase } = useSupabase();
  const [goals, setGoals] = React.useState<Goal[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [editingGoal, setEditingGoal] = React.useState<Goal | null>(null);
  const [formData, setFormData] = React.useState<GoalFormData>({
    title: '',
    description: '',
    category: '',
    target_value: 0,
    current_value: 0,
    due_date: ''
  });

  React.useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const handleEdit = (goal: Goal) => {
    setEditingGoal(goal);
    setFormData({
      title: goal.title,
      description: goal.description || '',
      category: goal.category as GoalCategory,
      target_value: goal.target_value,
      current_value: goal.current_value,
      due_date: goal.due_date || ''
    });
    setIsDialogOpen(true);
  };

  async function fetchGoals() {
    try {
      const { data, error } = await supabase
        .from('assigned_goals')
        .select('*')
        .eq('pelatis_id', userId)  // Changed from user_id to pelatis_id
        .order('created_at', { ascending: false });
  
      if (error) throw error;
      
      setGoals(data as Goal[]);
    } catch (error) {
      console.error('Error fetching goals:', error);
      toast.error('Failed to fetch goals');
    } finally {
      setLoading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    try {
      const goalData: Database['public']['Tables']['assigned_goals']['Insert'] = {
        assigned_by: userId,
        pelatis_id: userId,
        title: formData.title,
        description: formData.description,
        category: formData.category || 'monthly_checkins',
        target_value: formData.target_value,
        current_value: formData.current_value,
        due_date: formData.due_date || null,
        start_date: editingGoal ? editingGoal.start_date : new Date().toISOString(),
        status: editingGoal ? editingGoal.status : 'accepted'
      };

      if (editingGoal) {
        const { error } = await supabase
          .from('assigned_goals')
          .update(goalData)
          .eq('id', editingGoal.id);

        if (error) throw error;
        toast.success('Goal updated successfully');
      } else {
        const { error } = await supabase
          .from('assigned_goals')
          .insert(goalData);

        if (error) throw error;
        toast.success('Goal created successfully');
      }

      setIsDialogOpen(false);
      resetForm();
      setEditingGoal(null);
      fetchGoals();
    } catch (error) {
      console.error('Error saving goal:', error);
      toast.error('Failed to save goal');
    }
  }

  async function handleProgressUpdate(goalId: string, newValue: number) {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .update({ 
          current_value: newValue,
          status: newValue >= formData.target_value ? 'completed' as GoalStatus : 'accepted' as GoalStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', goalId);

      if (error) throw error;
      toast.success('Progress updated');
      fetchGoals();
    } catch (error) {
      console.error('Error updating progress:', error);
      toast.error('Failed to update progress');
    }
  }

  async function deleteGoal(goalId: string) {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .delete()
        .eq('id', goalId);

      if (error) throw error;
      toast.success('Goal deleted successfully');
      fetchGoals();
    } catch (error) {
      console.error('Error deleting goal:', error);
      toast.error('Failed to delete goal');
    }
  }

  function resetForm() {
    setFormData({
      title: '',
      description: '',
      category: '',
      target_value: 0,
      current_value: 0,
      due_date: ''
    });
    setEditingGoal(null);
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900" />
            <p>Loading goals...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeGoals = goals.filter(g => g.status !== 'completed');
  const completedGoals = goals.filter(g => g.status === 'completed');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Your Goals</h2>
        <Button onClick={() => {
          resetForm();
          setIsDialogOpen(true);
        }}>Create Goal</Button>
      </div>

      {activeGoals.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Active Goals</h3>
          <div className="grid gap-4 md:grid-cols-2">
            {activeGoals.map((goal) => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onProgressUpdate={handleProgressUpdate}
                onDelete={() => deleteGoal(goal.id)}
                onEdit={() => handleEdit(goal)}
              />
            ))}
          </div>
        </div>
      )}

      {completedGoals.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Completed Goals</h3>
          <div className="grid gap-4 md:grid-cols-2">
            {completedGoals.map((goal) => (
              <Card key={goal.id} className="bg-gray-50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                      <h3 className="font-medium">{goal.title}</h3>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4">{goal.description}</p>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{goal.target_value}</span>
                      <span>Completed!</span>
                    </div>
                    <Progress value={100} className="h-2" />
                  </div>

                  <div className="mt-4 flex gap-2">
                  <Badge variant="secondary">  {/* Using an allowed variant */}
  Completed on {new Date(goal.updated_at).toLocaleDateString()}
</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={(open) => {
        if (!open) {
          resetForm();
        }
        setIsDialogOpen(open);
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingGoal ? 'Edit Goal' : 'Create New Goal'}</DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  title: e.target.value 
                }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  description: e.target.value 
                }))}
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select
  value={formData.category}
  onValueChange={(value: GoalCategory) => setFormData(prev => ({ 
    ...prev, 
    category: value 
  }))}
>
  <SelectTrigger>
    <SelectValue placeholder="Select a category" />
  </SelectTrigger>
  <SelectContent>
    {GOAL_CATEGORIES.map((category) => (
      <SelectItem key={category.value} value={category.value}>
        {category.label}
      </SelectItem>
    ))}
  </SelectContent>
</Select>
            </div>

            <div>
              <Label htmlFor="target_value">Target Value</Label>
              <Input
                id="target_value"
                type="number"
                value={formData.target_value}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  target_value: Number(e.target.value)
                }))}
                min={0}
                required
              />
            </div>

            <div>
      <Label htmlFor="due_date">Due Date (Optional)</Label> {/* Changed from target_date */}
      <Input
        id="due_date"
        type="date"
        value={formData.due_date} // Changed from target_date
        onChange={(e) => setFormData(prev => ({
          ...prev,
          due_date: e.target.value // Changed from target_date
        }))}
      />
    </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setIsDialogOpen(false);
                resetForm();
              }}>
                Cancel
              </Button>
              <Button type="submit">Save Goal</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {goals.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center text-gray-500">
            No goals set yet. Click &quot;Create Goal&quot; to get started!
          </CardContent>
        </Card>
      )}
    </div>
  );
}
