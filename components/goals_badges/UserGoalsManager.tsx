'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import GoalForm from './GoalForm';
import GoalCard from './GoalCard';
import { GoalsDatabase } from '@/lib/database.types';

interface Goal {
  id: string;
  title: string;
  category: string;
  description: string | null;
  assigned_by: string;
  due_date: string | null;
  start_date: string;
  target_value: number;
  current_value: number;
  status: 'pending' | 'accepted' | 'declined' | 'completed';
  template_id: string | null;
  pelatis_id: string;
  created_at: string;
  updated_at: string;
}

interface GoalFormData {
  title: string;
  category: string;
  description: string | null;
  assigned_by: string;
  due_date: string | null;
  start_date: string;
  target_value: number;
  current_value?: number;
  status?: 'pending' | 'accepted' | 'declined' | 'completed';
  template_id?: string | null;
}

interface UserGoalsManagerProps {
  pelatisId: string;
  categories: readonly {
    value: string;
    label: string;
  }[];
}

const UserGoalsManager = ({ pelatisId, categories }: UserGoalsManagerProps) => {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const supabase = createClientComponentClient<GoalsDatabase>();

  const fetchGoals = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('assigned_goals')
        .select('*')
        .eq('pelatis_id', pelatisId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setGoals(data || []);
    } catch (error) {
      console.error('Error fetching goals:', error);
    }
  }, [pelatisId, supabase]);

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const handleAddGoal = async (data: GoalFormData) => {
    try {
      const { error } = await supabase.from('assigned_goals').insert([
        {
          ...data,
          pelatis_id: pelatisId,
          assigned_by: pelatisId,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ]);

      if (error) throw error;
      fetchGoals();
      setShowForm(false);
    } catch (error) {
      console.error('Error adding goal:', error);
    }
  };

  const handleUpdateGoal = async (data: GoalFormData) => {
    if (!editingGoal) return;

    try {
      const { error } = await supabase
        .from('assigned_goals')
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingGoal.id);

      if (error) throw error;
      fetchGoals();
      setEditingGoal(null);
      setShowForm(false);
    } catch (error) {
      console.error('Error updating goal:', error);
    }
  };

  const handleDeleteGoal = async (goalId: string) => {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .delete()
        .eq('id', goalId);

      if (error) throw error;
      fetchGoals();
    } catch (error) {
      console.error('Error deleting goal:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Goals</h2>
        <Button onClick={() => setShowForm(true)} disabled={showForm}>
          Add Goal
        </Button>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingGoal ? 'Edit Goal' : 'Add Goal'}</CardTitle>
          </CardHeader>
          <CardContent>
            <GoalForm
              onSubmit={editingGoal ? handleUpdateGoal : handleAddGoal}
              initialData={editingGoal || undefined}
              categories={categories}
              isAssignment={false}
            />
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {goals.map((goal) => (
          <GoalCard
            key={goal.id}
            goal={goal}
            onEdit={() => {
              setEditingGoal(goal);
              setShowForm(true);
            }}
            onDelete={() => handleDeleteGoal(goal.id)}
          />
        ))}
      </div>
    </div>
  );
}

export { UserGoalsManager };
