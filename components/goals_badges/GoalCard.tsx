"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Edit, Trash2 } from "lucide-react";
import { Database } from "@/types/supabase";
import { Input } from "@/components/ui/input";

type Goal = Database["public"]["Tables"]["assigned_goals"]["Row"];

interface GoalCardProps {
  goal: Goal;
  onEdit: () => void;
  onDelete: () => void;
  onProgressUpdate?: (goalId: string, newValue: number) => Promise<void>;
}

const GoalCard = ({ goal, onEdit, onDelete, onProgressUpdate }: GoalCardProps) => {
  const progress = Math.min(100, (goal.current_value || 0) / goal.target_value * 100);
  const formattedStartDate = new Date(goal.start_date).toLocaleDateString();
  const formattedDueDate = goal.due_date ? new Date(goal.due_date).toLocaleDateString() : "No due date";

  const handleProgressChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value);
    if (!isNaN(newValue) && onProgressUpdate) {
      await onProgressUpdate(goal.id, newValue);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold">{goal.title}</CardTitle>
        <div className="flex gap-2">
          <Button variant="ghost" size="icon" onClick={onEdit}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onDelete}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">{goal.description}</p>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>
              {goal.current_value || 0} / {goal.target_value}
            </span>
          </div>
          <Progress value={progress} />
          {onProgressUpdate && (
            <Input
              type="number"
              min={0}
              max={goal.target_value}
              value={goal.current_value || 0}
              onChange={handleProgressChange}
              className="mt-2"
            />
          )}
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Start Date</p>
            <p>{formattedStartDate}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Due Date</p>
            <p>{formattedDueDate}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Category:</span>
          <span className="text-sm font-medium">{goal.category}</span>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Status:</span>
          <span className="text-sm font-medium">{goal.status}</span>
        </div>
      </CardContent>
    </Card>
  );
}

export default GoalCard;