'use client';

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSupabase } from '@/hooks/useSupabase';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Award } from 'lucide-react';
import { toast } from "react-hot-toast";
import type { Database } from '@/types/supabase';

type Tables = Database['public']['Tables']
type Badge = Tables['badges']['Row']
type BadgeLevel = Database['public']['Enums']['badge_level']

interface AdminBadgeManagementProps {
  userId: string;
}

export function AdminBadgeManagement({ userId }: AdminBadgeManagementProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [availableBadges, setAvailableBadges] = useState<Badge[]>([]);
  const [selectedBadgeId, setSelectedBadgeId] = useState<string>('');
  const [userBadges, setUserBadges] = useState<(Badge & { achieved_at: string })[]>([]);
  const { supabase } = useSupabase();

  const fetchData = async () => {
    try {
      // Fetch all available badges
      const { data: badges, error: badgesError } = await supabase
        .from('badges')
        .select('*')
        .order('level', { ascending: true });

      if (badgesError) throw badgesError;
      setAvailableBadges(badges);

      // Fetch user's badges using pelatis_id
      const { data: userBadgesData, error: userBadgesError } = await supabase
        .from('user_badges')
        .select(`
          badge_id,
          achieved_at,
          badges (*)
        `)
        .eq('pelatis_id', userId); // Use pelatis_id here

      if (userBadgesError) throw userBadgesError;
      
      const formattedUserBadges = userBadgesData.map(ub => ({
        ...(ub.badges as Badge),
        achieved_at: ub.achieved_at,
      }));
      
      setUserBadges(formattedUserBadges);
    } catch (error) {
      console.error('Error fetching badges:', error);
      toast.error('Failed to fetch badges');
    }
  };

  const awardBadge = async () => {
    if (!selectedBadgeId) {
      toast.error('Please select a badge');
      return;
    }

    try {
      const { error } = await supabase
        .from('user_badges')
        .insert({
          pelatis_id: userId, // Use pelatis_id here
          badge_id: selectedBadgeId,
          achieved_at: new Date().toISOString(),
          progress: {}
        });

      if (error) throw error;

      toast.success('Badge awarded successfully');
      await fetchData();
      setIsOpen(false);
      setSelectedBadgeId('');
    } catch (error) {
      console.error('Error awarding badge:', error);
      toast.error('Failed to award badge');
    }
  };

  const getLevelColor = (level: BadgeLevel) => {
    const colors: Record<BadgeLevel, string> = {
      bronze: 'bg-orange-200 text-orange-800',
      silver: 'bg-gray-200 text-gray-800',
      gold: 'bg-yellow-200 text-yellow-800',
      platinum: 'bg-blue-200 text-blue-800'
    };
    return colors[level] || 'bg-gray-200 text-gray-800';
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Badges</h2>
        <Button onClick={() => setIsOpen(true)}>Award Badge</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {userBadges.map((badge) => (
          <Card key={badge.id} className="relative">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold">{badge.name}</h3>
                  <p className="text-sm text-gray-600">{badge.description}</p>
                  <div className="flex gap-2 mt-2">
                    <Badge className={getLevelColor(badge.level)}>
                      {badge.level}
                    </Badge>
                    <Badge variant="outline">
                      {badge.category}
                    </Badge>
                  </div>
                </div>
                <Award className="h-8 w-8 text-blue-500" />
              </div>
              <div className="text-xs text-gray-500 mt-2">
                Awarded: {new Date(badge.achieved_at).toLocaleDateString()}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Award Badge</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <Select value={selectedBadgeId} onValueChange={setSelectedBadgeId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a badge" />
              </SelectTrigger>
              <SelectContent>
                {availableBadges
                  .filter(badge => !userBadges.some(ub => ub.id === badge.id))
                  .map(badge => (
                    <SelectItem key={badge.id} value={badge.id}>
                      {badge.name} ({badge.level})
                    </SelectItem>
                  ))
                }
              </SelectContent>
            </Select>

            {selectedBadgeId && (
              <div className="p-4 bg-gray-50 rounded">
                <h4 className="font-semibold">Badge Details</h4>
                {availableBadges
                  .filter(badge => badge.id === selectedBadgeId)
                  .map(badge => (
                    <div key={badge.id} className="mt-2">
                      <p className="text-sm">{badge.description}</p>
                      <div className="flex gap-2 mt-2">
                        <Badge className={getLevelColor(badge.level)}>
                          {badge.level}
                        </Badge>
                        <Badge variant="outline">
                          {badge.category}
                        </Badge>
                      </div>
                    </div>
                  ))
                }
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={awardBadge}>
              Award Badge
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default AdminBadgeManagement;