'use client';

import React from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Award, Target, Crown } from 'lucide-react';
import type { Database } from '@/types/supabase';

type BadgeCategory = Database['public']['Enums']['badge_category'];
type BadgeLevel = Database['public']['Enums']['badge_level'];

interface Achievement {
  badge_name: string;
  achieved_at: string;
  category: BadgeCategory;
  level: BadgeLevel;
}

interface BadgeStats {
  total_badges: number;
  badges_by_level: Record<BadgeLevel, number>;
  badges_by_category: Record<BadgeCategory, number>;
  recent_achievements: Achievement[];
  current_streak: number;
  longest_streak: number;
}

// Define exact database return type
interface DatabaseAchievement {
  badge_name: string;
  achieved_at: string;
  category: BadgeCategory;
  level: BadgeLevel;
}

interface DatabaseBadgeStats {
  total_badges: number;
  badges_by_level: Record<BadgeLevel, number>;
  badges_by_category: Record<BadgeCategory, number>;
  recent_achievements: DatabaseAchievement[];
  current_streak: number;
  longest_streak: number;
}

const DEFAULT_STATS: BadgeStats = {
  total_badges: 0,
  badges_by_level: {
    bronze: 0,
    silver: 0,
    gold: 0,
    platinum: 0
  },
  badges_by_category: {
    attendance: 0,
    exercise: 0,
    progression: 0,
    consistency: 0,
    milestone: 0
  },
  recent_achievements: [],
  current_streak: 0,
  longest_streak: 0
};

const CATEGORY_COLORS = {
  attendance: 'bg-blue-100 text-blue-800',
  exercise: 'bg-green-100 text-green-800',
  progression: 'bg-yellow-100 text-yellow-800',
  consistency: 'bg-purple-100 text-purple-800',
  milestone: 'bg-pink-100 text-pink-800'
} as const;

export default function BadgeAnalytics({ userId }: { userId: string }) {
  const { supabase } = useSupabase();
  const [loading, setLoading] = React.useState(true);
  const [stats, setStats] = React.useState<BadgeStats>(DEFAULT_STATS);

  React.useEffect(() => {
    async function fetchStats() {
      try {
        const { data, error } = await supabase
          .rpc('get_user_achievement_stats', {
            user_id_param: userId
          }) as { 
            data: DatabaseBadgeStats[] | null;
            error: Error | null;
          };

        if (error) throw error;

        if (data?.[0]) {
          const rawStats = data[0];
          
          setStats({
            total_badges: rawStats.total_badges,
            badges_by_level: rawStats.badges_by_level,
            badges_by_category: rawStats.badges_by_category,
            recent_achievements: rawStats.recent_achievements,
            current_streak: rawStats.current_streak,
            longest_streak: rawStats.longest_streak
          });
        }
      } catch (error) {
        console.error('Error fetching badge stats:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, [userId, supabase]);



  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900" />
            <p>Loading analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-gray-500">
          No achievement data available yet. Start earning badges to see your statistics!
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Total Badges
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-blue-500" />
              <span className="text-2xl font-bold">
                {stats.total_badges}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Current Streak
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">
                {stats.current_streak} days
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Longest Streak
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              <span className="text-2xl font-bold">
                {stats.longest_streak} days
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Badges by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(stats.badges_by_category).map(([category, count]) => (
              <div key={category}>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </span>
                  <span className="text-sm text-gray-600">
                    {count} badges
                  </span>
                </div>
                <Progress 
                  value={(count / stats.total_badges) * 100} 
                  className="h-2" 
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Achievements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.recent_achievements.map((achievement, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 rounded-lg bg-gray-50"
              >
                <div>
                  <h4 className="font-medium">{achievement.badge_name}</h4>
                  <p className="text-sm text-gray-500">
                    {new Date(achievement.achieved_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Badge className={CATEGORY_COLORS[achievement.category]}>
                    {achievement.category}
                  </Badge>
                  <Badge variant="outline">
                    {achievement.level}
                  </Badge>
                </div>
              </div>
            ))}

            {stats.recent_achievements.length === 0 && (
              <p className="text-center text-gray-500">
                No recent achievements
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}