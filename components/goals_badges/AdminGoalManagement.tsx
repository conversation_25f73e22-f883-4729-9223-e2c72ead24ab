"use client";

import { useState, useEffect, useCallback } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import GoalForm from "./GoalForm";
import GoalCard from "./GoalCard";
import type { Database } from '@/types/supabase';

type Goal = Database["public"]["Tables"]["assigned_goals"]["Row"];
type GoalInsert = Database["public"]["Tables"]["assigned_goals"]["Insert"];

interface AdminGoalManagementProps {
  userId?: string;
}

const AdminGoalManagement = ({ userId }: AdminGoalManagementProps) => {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [activeTab, setActiveTab] = useState("user-goals");
  const supabase = createClientComponentClient<Database>();

  const fetchData = useCallback(async () => {
    try {
      let query = supabase
        .from("assigned_goals")
        .select("*")
        .order("created_at", { ascending: false });

      if (userId) {
        query = query.eq("pelatis_id", userId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setGoals(data || []);
    } catch (error) {
      console.error("Error fetching goals:", error);
    }
  }, [supabase, userId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleAddGoal = async (data: Omit<GoalInsert, "id" | "pelatis_id">) => {
    if (!userId) return;

    try {
      const goalData: GoalInsert = {
        ...data,
        pelatis_id: userId,
        assigned_by: userId,
        status: "pending",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("assigned_goals")
        .insert([goalData]);

      if (error) throw error;
      fetchData();
      setShowForm(false);
    } catch (error) {
      console.error("Error adding goal:", error);
    }
  };

  const handleUpdateGoal = async (data: Omit<GoalInsert, "id" | "pelatis_id">) => {
    if (!editingGoal || !userId) return;

    try {
      const goalData: Partial<GoalInsert> = {
        ...data,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("assigned_goals")
        .update(goalData)
        .eq("id", editingGoal.id);

      if (error) throw error;
      fetchData();
      setEditingGoal(null);
      setShowForm(false);
    } catch (error) {
      console.error("Error updating goal:", error);
    }
  };

  const handleDeleteGoal = async (goalId: string) => {
    try {
      const { error } = await supabase
        .from("assigned_goals")
        .delete()
        .eq("id", goalId);

      if (error) throw error;
      fetchData();
    } catch (error) {
      console.error("Error deleting goal:", error);
    }
  };

  const categories = [
    { value: "fitness", label: "Fitness" },
    { value: "nutrition", label: "Nutrition" },
    { value: "strength", label: "Strength" },
    { value: "endurance", label: "Endurance" },
    { value: "flexibility", label: "Flexibility" },
  ];

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="user-goals">User Goals</TabsTrigger>
          <TabsTrigger value="assigned-goals">Assigned Goals</TabsTrigger>
        </TabsList>

        <TabsContent value="user-goals" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">User Goals</h2>
            <Button onClick={() => setShowForm(true)} disabled={showForm}>
              Add Goal
            </Button>
          </div>

          {showForm && (
            <Card>
              <CardHeader>
                <CardTitle>{editingGoal ? "Edit Goal" : "Add Goal"}</CardTitle>
              </CardHeader>
              <CardContent>
                <GoalForm
                  onSubmit={editingGoal ? handleUpdateGoal : handleAddGoal}
                  initialData={editingGoal || undefined}
                  categories={categories}
                />
              </CardContent>
            </Card>
          )}

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {goals.map((goal) => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onEdit={() => {
                  setEditingGoal(goal);
                  setShowForm(true);
                }}
                onDelete={() => handleDeleteGoal(goal.id)}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="assigned-goals" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Assigned Goals</h2>
            <Button onClick={() => setShowForm(true)} disabled={showForm}>
              Add Assignment
            </Button>
          </div>

          {showForm && (
            <Card>
              <CardHeader>
                <CardTitle>
                  {editingGoal ? "Edit Assignment" : "Add Assignment"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <GoalForm
                  onSubmit={editingGoal ? handleUpdateGoal : handleAddGoal}
                  initialData={editingGoal || undefined}
                  isAssignment
                  categories={categories}
                />
              </CardContent>
            </Card>
          )}

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {goals.map((goal) => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onEdit={() => {
                  setEditingGoal(goal);
                  setShowForm(true);
                }}
                onDelete={() => handleDeleteGoal(goal.id)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export { AdminGoalManagement };