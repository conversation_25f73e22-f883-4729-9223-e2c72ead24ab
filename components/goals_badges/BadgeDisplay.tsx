// components/BadgeDisplay.tsx
'use client';

import React from 'react';
import { Medal, Trophy, Target, Calendar, Dumbbell } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useSupabase } from '@/hooks/useSupabase';
import type { Database } from '@/types/supabase';

type Badge = Database['public']['Tables']['badges']['Row'];
type UserBadge = Database['public']['Tables']['user_badges']['Row'] & {
  badges: Badge;
};

type BadgeLevel = Database['public']['Enums']['badge_level'];
type BadgeCategory = Database['public']['Enums']['badge_category'];

const BADGE_LEVELS: Record<BadgeLevel, string> = {
  bronze: 'bg-orange-200 text-orange-800',
  silver: 'bg-gray-200 text-gray-800',
  gold: 'bg-yellow-200 text-yellow-800',
  platinum: 'bg-blue-200 text-blue-800'
};

const BADGE_ICONS = {
  attendance: Calendar,
  exercise: Dumbbell,
  progression: Target,
  consistency: Medal,
  milestone: Trophy
} as const;

export default function BadgeDisplay({ userId }: { userId: string }) {
  const { supabase } = useSupabase();
  const [badges, setBadges] = React.useState<UserBadge[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    async function fetchBadges() {
      try {
        const { data, error } = await supabase
          .from('user_badges')
          .select('*, badges (*)')
          .eq('pelatis_id', userId);

        if (error) throw error;
        setBadges(data as UserBadge[] || []);
      } catch (error) {
        console.error('Error fetching badges:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchBadges();
  }, [userId, supabase]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900" />
            <p>Loading badges...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const groupedBadges: Record<BadgeCategory, UserBadge[]> = badges.reduce((acc, badge) => {
    const category = badge.badges.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(badge);
    return acc;
  }, {} as Record<BadgeCategory, UserBadge[]>);

  return (
    <div className="space-y-6">
      {Object.entries(groupedBadges).map(([category, categoryBadges]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {React.createElement(
                BADGE_ICONS[category as keyof typeof BADGE_ICONS],
                { className: 'h-5 w-5' }
              )}
              {category.charAt(0).toUpperCase() + category.slice(1)} Badges
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categoryBadges.map((badge) => (
                <div
                  key={badge.id}
                  className="p-4 rounded-lg border bg-card text-card-foreground shadow-sm"
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${BADGE_LEVELS[badge.badges.level]}`}>
                      {React.createElement(
                        BADGE_ICONS[badge.badges.category],
                        { className: 'h-4 w-4' }
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{badge.badges.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(badge.achieved_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {badge.badges.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}

      {badges.length === 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              You haven&apos;t earned any badges yet. Keep training to unlock achievements!
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}