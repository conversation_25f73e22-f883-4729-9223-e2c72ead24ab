'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { useSupabase } from '@/hooks/useSupabase'
import { toast } from "react-hot-toast"
import type { Database } from "@/types/supabase"

// Define the type for the insert operation
type GoalInsert = Database['public']['Tables']['assigned_goals']['Insert']

const GOAL_CATEGORIES = [
  { value: 'monthly_checkins', label: 'Monthly Check-ins' },
  { value: 'weight_lifted', label: 'Total Weight Lifted' },
  { value: 'personal_records', label: 'Personal Records' },
  { value: 'attendance_streak', label: 'Attendance Streak' },
  { value: 'exercise_variety', label: 'Exercise Variety' },
]

interface Props {
  userId: string
  onSuccess: () => void
}

export function GoalAssignmentForm({ userId, onSuccess }: Props) {
  const { supabase } = useSupabase()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    target_value: 0,
    duration_days: 30,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      const goalData: GoalInsert = {
        assigned_by: user.id,
        pelatis_id: userId, // Changed from user_id to pelatis_id
        title: formData.title,
        description: formData.description || null,
        category: formData.category,
        target_value: formData.target_value,
        start_date: new Date().toISOString(),
        due_date: formData.duration_days
          ? new Date(Date.now() + formData.duration_days * 24 * 60 * 60 * 1000).toISOString()
          : null,
        current_value: 0,
        status: 'pending'
      }

      const { error } = await supabase
        .from('assigned_goals')
        .insert(goalData)

      if (error) throw error

      toast.success('Goal assigned successfully')
      onSuccess()
    } catch (error) {
      toast.error('Failed to assign goal')
      console.error(error)
    }
  }


  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: e.target.value
          }))}
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: e.target.value
          }))}
        />
      </div>

      <div>
        <Label htmlFor="category">Category</Label>
        <Select
          value={formData.category}
          onValueChange={(value) => setFormData(prev => ({
            ...prev,
            category: value
          }))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {GOAL_CATEGORIES.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="target_value">Target Value</Label>
        <Input
          id="target_value"
          type="number"
          value={formData.target_value}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            target_value: Number(e.target.value)
          }))}
          min={0}
          required
        />
      </div>

      <div>
        <Label htmlFor="duration_days">Duration (days)</Label>
        <Input
          id="duration_days"
          type="number"
          value={formData.duration_days}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            duration_days: Number(e.target.value)
          }))}
          min={1}
        />
      </div>

      <Button type="submit" className="w-full">
        Assign Goal
      </Button>
    </form>
  )
}