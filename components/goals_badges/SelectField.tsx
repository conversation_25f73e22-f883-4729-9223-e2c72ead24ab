// components/SimpleSelect.tsx
"use client"

import React from 'react'
import { useFormContext } from 'react-hook-form'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

const GOAL_CATEGORIES = [
  { value: "monthly_checkins", label: "Monthly Check-ins" },
  { value: "exercise", label: "Exercise" },
  { value: "strength_training", label: "Strength Training" },
  { value: "nutrition", label: "Nutrition" },
  { value: "flexibility", label: "Flexibility" }
]

export function SimpleSelect({ name = "category", label = "Category" }) {
  const form = useFormContext()
  
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <select
              className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              {...field}
            >
              <option value="" disabled>Select an option</option>
              {GOAL_CATEGORIES.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}