'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface QuickSessionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateSession: (programId: string, time: string) => Promise<void>;
  programs: { id: string; name: string }[];
  selectedDate: string;
}

export function QuickSessionDialog({
  isOpen,
  onClose,
  onCreateSession,
  programs,
  selectedDate
}: QuickSessionDialogProps) {
  const [selectedProgram, setSelectedProgram] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const handleSubmit = async () => {
    if (!selectedProgram || !selectedTime) {
      setError('Please select both program and time');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onCreateSession(selectedProgram, selectedTime);
      setSelectedProgram('');
      setSelectedTime('');
      onClose();
    } catch (error) {
      console.error('Error creating session:', error);
      setError(error instanceof Error ? error.message : 'Failed to create session');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setSelectedProgram('');
    setSelectedTime('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Session for {formatDate(selectedDate)}</DialogTitle>
        </DialogHeader>
        
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="program">Program</Label>
            <Select
              value={selectedProgram}
              onValueChange={setSelectedProgram}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a program" />
              </SelectTrigger>
              <SelectContent>
                {programs.map(program => (
                  <SelectItem key={program.id} value={program.id}>
                    {program.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="time">Time</Label>
            <Input
              id="time"
              type="time"
              value={selectedTime}
              onChange={(e) => setSelectedTime(e.target.value)}
              step="60"
            />
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleClose} 
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!selectedProgram || !selectedTime || isSubmitting}
          >
            {isSubmitting ? 'Creating...' : 'Create Session'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}