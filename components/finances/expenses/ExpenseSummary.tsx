import { format } from 'date-fns'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card"

type SummaryData = {
  totalAmount: number
  categoryTotals: { [key: string]: number }
  periodStart: Date
  periodEnd: Date
}

export function ExpenseSummary({ data }: { data: SummaryData }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">€{data.totalAmount.toFixed(2)}</div>
          <p className="text-xs text-muted-foreground">
            {format(data.periodStart, 'dd/MM/yyyy')} - {format(data.periodEnd, 'dd/MM/yyyy')}
          </p>
        </Card<PERSON>ontent>
      </Card>

      {Object.entries(data.categoryTotals).map(([category, amount]) => (
        <Card key={category}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{category}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">€{amount.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {((amount / data.totalAmount) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
