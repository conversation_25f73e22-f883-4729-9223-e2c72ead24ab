import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { CalendarIcon, Download } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type Category = {
  id: number;
  name: string;
}

type Vendor = {
  id: number;
  name: string;
}

// Export the FilterState type with vendor property
export type FilterState = {
  dateRange: { from: Date; to: Date } | undefined;
  category: string;
  vendor: string;
  paymentMethod: string;
}

type ExpenseFiltersProps = {
  onFilterChange: (filters: FilterState) => void;
  onExport: () => void;
}

export function ExpenseFilters({ 
  onFilterChange, 
  onExport
}: ExpenseFiltersProps) {
  const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
  const [toDate, setToDate] = useState<Date | undefined>(undefined);
  const [categories, setCategories] = useState<Category[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [paymentMethods] = useState<string[]>(['CASH', 'VIVA', 'WINBANK']);
  
  const supabase = createClientComponentClient();

  const [filters, setFilters] = useState<FilterState>({
    dateRange: undefined,
    category: 'all',
    vendor: 'all',
    paymentMethod: 'all'
  });

  useEffect(() => {
    const fetchData = async () => {
      // Fetch categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('expense_categories')
        .select('id, name')
        .order('name');

      if (categoriesError) {
        console.error('Error fetching categories:', categoriesError);
      } else {
        setCategories(categoriesData || []);
      }

      // Fetch vendors
      const { data: vendorsData, error: vendorsError } = await supabase
        .from('vendors')
        .select('id, name')
        .order('name');

      if (vendorsError) {
        console.error('Error fetching vendors:', vendorsError);
      } else {
        setVendors(vendorsData || []);
      }
    };

    fetchData();
  }, [supabase]);

  const handleDateChange = (type: 'from' | 'to', date: Date | undefined) => {
    const newFromDate = type === 'from' ? date : fromDate;
    const newToDate = type === 'to' ? date : toDate;

    let dateRange: { from: Date; to: Date } | undefined = undefined;

    if (newFromDate || newToDate) {
      const effectiveFromDate = newFromDate || newToDate;
      const effectiveToDate = newToDate || newFromDate;

      if (effectiveFromDate && effectiveToDate) {
        dateRange = {
          from: effectiveFromDate,
          to: effectiveToDate
        };
      }
    }

    if (type === 'from') setFromDate(date);
    if (type === 'to') setToDate(date);
    
    handleFilterChange({ dateRange });
  };

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange({
      ...updatedFilters,
      category: updatedFilters.category === 'all' ? '' : updatedFilters.category,
      vendor: updatedFilters.vendor === 'all' ? '' : updatedFilters.vendor,
      paymentMethod: updatedFilters.paymentMethod === 'all' ? '' : updatedFilters.paymentMethod,
    });
  };

  const renderFilterSelect = (
    value: string,
    onValueChange: (value: string) => void,
    placeholder: string,
    allLabel: string,
    items: Array<{ id: number | string; name: string } | string>
  ) => (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">{allLabel}</SelectItem>
        {items.map(item => {
          const [key, label] = typeof item === 'string' 
            ? [item, item.replace(/_/g, ' ')] 
            : [item.id.toString(), item.name];
          return (
            <SelectItem key={key} value={key.toString()}>
              {label}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );

  return (
    <div className="flex flex-wrap gap-4 items-center mb-6">
      <div className="flex gap-2">
        {['from', 'to'].map((type) => (
          <Popover key={type}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={`w-[200px] justify-start text-left font-normal ${
                  !(type === 'from' ? fromDate : toDate) ? 'text-muted-foreground' : ''
                }`}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {type === 'from' 
                  ? (fromDate ? format(fromDate, "PPP") : <span>Start date</span>)
                  : (toDate ? format(toDate, "PPP") : <span>End date</span>)
                }
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={type === 'from' ? fromDate : toDate}
                onSelect={(date) => handleDateChange(type as 'from' | 'to', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {renderFilterSelect(
        filters.category,
        (value) => handleFilterChange({ category: value }),
        "All Categories",
        "All Categories",
        categories
      )}

      {renderFilterSelect(
        filters.vendor,
        (value) => handleFilterChange({ vendor: value }),
        "All Vendors",
        "All Vendors",
        vendors
      )}

      {renderFilterSelect(
        filters.paymentMethod,
        (value) => handleFilterChange({ paymentMethod: value }),
        "All Payment Methods",
        "All Methods",
        paymentMethods
      )}

      <Button variant="outline" onClick={onExport}>
        <Download className="mr-2 h-4 w-4" />
        Export CSV
      </Button>
    </div>
  );
}
