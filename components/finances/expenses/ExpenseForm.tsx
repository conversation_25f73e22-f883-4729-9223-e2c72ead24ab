import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/types/supabase'

// Remove unused type definitions since they're only used in the schema
const PAYMENT_METHODS = ['VIVA', 'WINBANK', 'CASH'] as const;
const RECURRING_PERIODS = ['monthly', 'quarterly', 'biannual', 'annual'] as const;

const formSchema = z.object({
  date: z.string().min(1, "Date is required"),
  category_id: z.string().min(1, "Category is required"),
  vendor_id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  amount: z.string().min(1, "Amount is required"),
  payment_method: z.enum(PAYMENT_METHODS, {
    required_error: "Payment method is required",
  }),
  reference_number: z.string().optional(),
  recurring: z.boolean().default(false),
  recurring_period: z.enum(RECURRING_PERIODS).optional(),
})

type FormSchema = z.infer<typeof formSchema>

type Expense = Database['public']['Tables']['admin_expenses']['Row'] & {
  category: {
    name: string
  }
  vendor: {
    name: string
  } | null
}

type ExpenseFormProps = {
  expense?: Expense
  onSuccess: () => void
  onCancel: () => void
}

type Category = {
  id: number
  name: string
  description: string | null
  created_at: string | null
}

type Vendor = {
  id: number
  name: string
  category_id: number | null  // Updated to allow null
  contact_info: string | null
  created_at: string | null
}

export function ExpenseForm({ expense, onSuccess, onCancel }: ExpenseFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const supabase = createClientComponentClient<Database>();

  // Add useEffect to check admin status
  useEffect(() => {
    const checkAdminStatus = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: roles, error } = await supabase.rpc('getUserRoles', {
          p_user_id: user.id
        });
        
        if (!error && Array.isArray(roles)) {
          setIsAdmin(roles.includes('admin'));
        }
      }
    };

    checkAdminStatus();
  }, [supabase]);

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: expense?.date || new Date().toISOString().split('T')[0],
      category_id: expense?.category_id?.toString() || '', 
      vendor_id: expense?.vendor_id?.toString() || '',
      description: expense?.description || '',
      amount: expense?.amount?.toString() || '', 
      payment_method: (expense?.payment_method as typeof PAYMENT_METHODS[number]) || 'CASH', // Fixed: Use proper type and default value
      reference_number: expense?.reference_number || '',
      recurring: expense?.recurring || false,
      recurring_period: (expense?.recurring_period as typeof RECURRING_PERIODS[number]) || undefined,
    },
  })

  useEffect(() => {
    const fetchData = async () => {
      const { data: categoriesData } = await supabase
        .from('expense_categories')
        .select('*')
        .order('name')

      const { data: vendorsData } = await supabase
        .from('vendors')
        .select('*')
        .order('name')

      if (categoriesData) setCategories(categoriesData)
      if (vendorsData) setVendors(vendorsData)
    }

    fetchData()
  }, [supabase])

  useEffect(() => {
    const categoryId = form.watch('category_id');
    const updateFilteredVendors = () => {
      if (categoryId) {
        setFilteredVendors(vendors.filter(v => v.category_id === parseInt(categoryId)));
      } else {
        setFilteredVendors(vendors);
      }
    };

    updateFilteredVendors();
  }, [vendors, form]); // Add form to dependencies

  // Update the form submit button text based on whether we're editing or creating
  const submitButtonText = isSubmitting 
    ? expense 
      ? "Updating..." 
      : "Adding..." 
    : expense 
      ? "Update Expense" 
      : "Add Expense";

  async function onSubmit(values: FormSchema) {
    try {
      if (!isAdmin) {
        throw new Error('Only administrators can manage expenses');
      }

      setIsSubmitting(true);

      // Find the category name from the selected category_id
      const selectedCategory = categories.find(
        cat => cat.id.toString() === values.category_id
      );

      if (!selectedCategory) {
        throw new Error('Selected category not found');
      }

      const expenseData = {
        date: values.date,
        category_id: parseInt(values.category_id),
        category: selectedCategory.name, // Add the required category field
        vendor_id: values.vendor_id ? parseInt(values.vendor_id) : null,
        description: values.description.trim(),
        amount: parseFloat(values.amount),
        payment_method: values.payment_method,
        reference_number: values.reference_number?.trim() || null,
        recurring: values.recurring,
        recurring_period: values.recurring ? values.recurring_period : null
      };

      let result;
      
      if (expense?.id) {
        result = await supabase
          .from('admin_expenses')
          .update(expenseData)
          .eq('id', expense.id);
      } else {
        result = await supabase
          .from('admin_expenses')
          .insert([expenseData]);
      }

      if (result.error) {
        throw result.error;
      }

      toast({
        title: "Success",
        description: expense ? "Expense updated successfully" : "Expense added successfully",
      });

      onSuccess();
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save expense",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="vendor_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Vendor (Optional)</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select vendor" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {filteredVendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id.toString()}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amount (€)</FormLabel>
              <FormControl>
                <Input type="number" step="0.01" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="payment_method"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment Method</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="VIVA">Viva</SelectItem>
                  <SelectItem value="WINBANK">Winbank</SelectItem>
                  <SelectItem value="CASH">Cash</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="recurring"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Recurring Expense</FormLabel>
              </div>
            </FormItem>
          )}
        />

        {form.watch('recurring') && (
          <FormField
            control={form.control}
            name="recurring_period"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Recurring Period</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="biannual">Biannual</SelectItem>
                    <SelectItem value="annual">Annual</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="reference_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Reference Number (Optional)</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {submitButtonText}
          </Button>
        </div>
      </form>
    </Form>
  )
}


