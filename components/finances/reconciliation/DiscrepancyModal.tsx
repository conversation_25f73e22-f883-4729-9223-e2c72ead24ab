"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";
import type { Database } from "@/types/supabase";

// Use the proper type from the Database definition
type AccountBalance = Database['public']['Tables']['account_balances']['Row'];

interface DiscrepancyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  account: AccountBalance | null | undefined;
  systemBalance: number | null;
  actualBalance: number | null;
  difference: number | null;
  notes?: string;
}

const DiscrepancyModal: React.FC<DiscrepancyModalProps> = ({ open, onOpenChange, account, systemBalance, actualBalance, difference }) => {
  const { toast } = useToast();
  const { supabase, user } = useSupabase();
  const [notes, setNotes] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAdjust = async () => {
    setError(null);
    setSubmitting(true);
    if (!user || !account || systemBalance === null || actualBalance === null || difference === null) {
      setError("Missing required data.");
      setSubmitting(false);
      return;
    }
    try {
      const { error: insertError } = await supabase.from("reconciliation_records").insert({
        account_name: account.account_name as Database['public']['Enums']['payment_method'],
        actual_balance: actualBalance,
        system_balance: systemBalance,
        difference: difference,
        notes,
        created_by: user.id,
        reconciliation_date: new Date().toISOString(),
        adjusted: true,
      });
      if (insertError) {
        setError(insertError.message);
        toast({
          title: "Adjustment failed",
          description: insertError.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Adjustment recorded",
          description: "The discrepancy adjustment was recorded.",
        });
        setNotes("");
        onOpenChange(false);
      }
    } catch (e: unknown) {
      const error = e instanceof Error ? e : new Error('Unknown error');
      setError(error.message);
      toast({
        title: "Adjustment failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Discrepancy Resolution</DialogTitle>
          <DialogDescription>
            Please review the discrepancy and provide notes for the adjustment.
          </DialogDescription>
        </DialogHeader>
        <Card>
          <CardContent className="space-y-4 pt-4">
            <div>
              <div className="text-sm font-medium">Account</div>
              <div>{account ? account.account_name : "--"}</div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium">System Balance</div>
                <div>
  {systemBalance !== null && systemBalance !== undefined
    ? systemBalance.toLocaleString(undefined, { minimumFractionDigits: 2 })
    : "--"}
</div>
              </div>
              <div>
                <div className="text-sm font-medium">Actual Balance</div>
                <div>
  {actualBalance != null
    ? actualBalance.toLocaleString(undefined, { minimumFractionDigits: 2 })
    : "--"}
</div>
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Difference</div>
              <div className={difference != null && difference !== 0 ? "text-destructive font-semibold" : ""}>
  {difference != null
    ? difference.toLocaleString(undefined, { minimumFractionDigits: 2 })
    : "--"}
</div>
            </div>
            <div>
              <div className="text-sm font-medium mb-1">Adjustment Notes</div>
              <Textarea value={notes} onChange={e => setNotes(e.target.value)} placeholder="Describe the reason for the adjustment..." disabled={submitting} />
            </div>
            {error && (
              <Alert variant="destructive">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
        <DialogFooter>
          <Button variant="secondary" onClick={() => onOpenChange(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleAdjust} disabled={submitting}>
            {submitting ? "Saving..." : "Submit Adjustment"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DiscrepancyModal;