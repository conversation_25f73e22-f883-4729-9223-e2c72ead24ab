"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Card, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";
import { useAccountBalances } from "@/hooks/useAccountBalances";
import DiscrepancyModal from "./DiscrepancyModal";
import type { Database } from "@/types/supabase";

// Use the proper type from the Database definition
type ReconciliationRecord = Database['public']['Tables']['reconciliation_records']['Insert'];
type PaymentMethod = Database['public']['Enums']['payment_method'];

type FormValues = {
  account_name: PaymentMethod;
  actual_balance: number | "";
  notes?: string;
};


const defaultValues: FormValues = {
  account_name: "CASH" as PaymentMethod, // Default to CASH
  actual_balance: "",
  notes: "",
};

const ReconciliationForm: React.FC = () => {
  const form = useForm<FormValues>({
    defaultValues,
    mode: "onChange" // Enable validation on change
  });
  const { toast } = useToast();
  const { supabase, user } = useSupabase();
  const { balances, loading: loadingBalances, error: errorBalances } = useAccountBalances();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDiscrepancy, setShowDiscrepancy] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  // Log for debugging
  useEffect(() => {
    console.log("Current user:", user);
    console.log("Balances:", balances);
    console.log("Form values:", form.getValues());
  }, [user, balances, form]);

  // Set a timeout for loading to provide fallback
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (loadingBalances) {
      // If loading takes more than 5 seconds, show timeout message
      timer = setTimeout(() => {
        setLoadingTimeout(true);
      }, 5000);
    } else {
      setLoadingTimeout(false);
    }

    return () => {
      clearTimeout(timer);
    };
  }, [loadingBalances]);

  const selectedAccount = useMemo(() => {
    const accountName = form.watch("account_name");
    if (!balances || !accountName) return null;
    return balances.find(b => b.account_name === accountName);
  }, [balances, form.watch("account_name"), form]);

  const systemBalance = selectedAccount ? selectedAccount.current_balance : null;
  const actualBalance = form.watch("actual_balance") === "" ? null : Number(form.watch("actual_balance"));
  const difference =
    systemBalance !== null && actualBalance !== null ? actualBalance - systemBalance : null;

  const onSubmit = async (values: FormValues) => {
    console.log("Form submitted with values:", values);
    setError(null);
    setSubmitting(true);

    try {
      // Check if user is logged in
      if (!user) {
        console.error("No user found");
        setError("You must be logged in to reconcile. Please refresh the page or log in again.");
        return;
      }

      // Check if account is selected
      if (!selectedAccount) {
        console.error("No account selected");
        setError("Please select an account from the dropdown.");
        return;
      }

      // Check if actual balance is entered
      if (actualBalance === null) {
        console.error("No actual balance entered");
        setError("Please enter the actual balance amount.");
        return;
      }

      // Check if system balance is available
      if (systemBalance === null) {
        console.error("System balance is null");
        setError("System balance could not be determined. Please try again.");
        return;
      }

      // Create the record object
      const insertObj: ReconciliationRecord = {
        account_name: selectedAccount.account_name as PaymentMethod,
        actual_balance: Number(actualBalance),
        system_balance: systemBalance,
        difference: difference!,
        notes: values.notes || null,
        created_by: user.id,
        reconciliation_date: new Date().toISOString(),
        adjusted: false,
      };

      console.log("Inserting reconciliation record:", insertObj);

      // Insert the record
      const { error: insertError, data } = await supabase
        .from("reconciliation_records")
        .insert(insertObj)
        .select();

      if (insertError) {
        console.error("Insert error:", insertError);
        setError(insertError.message);
        toast({
          title: "Reconciliation failed",
          description: insertError.message,
          variant: "destructive",
        });
      } else {
        console.log("Reconciliation record created:", data);
        toast({
          title: "Reconciliation recorded",
          description: "The reconciliation was successfully recorded.",
        });
        form.reset(defaultValues);

        // Optionally refresh the page to show the new record
        // window.location.reload();
      }
    } catch (e: unknown) {
      console.error("Submission error:", e);
      const error = e instanceof Error ? e : new Error('Unknown error');
      setError(error.message);
      toast({
        title: "Reconciliation failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="mb-8 max-w-xl mx-auto">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">New Reconciliation</CardTitle>
        <div className="text-sm text-muted-foreground">
          {loadingBalances ?
            <span className="text-amber-500">Loading account data...</span> :
            <span className="text-green-500">Account data loaded</span>
          }
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {errorBalances && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error loading balances</AlertTitle>
            <AlertDescription>
              {errorBalances ? errorBalances.message : "Unknown error occurred"}
            </AlertDescription>
          </Alert>
        )}

        {loadingTimeout && (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <AlertTitle>Loading is taking longer than expected</AlertTitle>
            <AlertDescription>
              <p>There might be an issue fetching account data. You can try:</p>
              <ul className="list-disc pl-5 mt-2">
                <li>Refreshing the page</li>
                <li>Checking your internet connection</li>
                <li>Verifying you&apos;re logged in with admin privileges</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="account_name"
              rules={{ required: "Required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      console.log("Account selected:", value);
                      field.onChange(value);
                    }}
                    value={field.value}
                    disabled={submitting || (loadingBalances && !loadingTimeout)}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="z-50" position="popper">
                      {balances && balances.length > 0 ? (
                        balances.map((b) => (
                          <SelectItem key={b.account_name} value={b.account_name}>{b.account_name}</SelectItem>
                        ))
                      ) : loadingTimeout ? (
                        <>
                          <SelectItem value="Cash">Cash</SelectItem>
                          <SelectItem value="Bank">Bank</SelectItem>
                          <SelectItem value="VIVA">VIVA</SelectItem>
                          <SelectItem value="Credit Card">Credit Card</SelectItem>
                          <div className="px-2 py-1.5 text-xs text-muted-foreground">
                            Manual options (loading failed)
                          </div>
                        </>
                      ) : (
                        <SelectItem value="loading" disabled>No accounts found</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">System Balance</label>
                <Input value={systemBalance !== null && systemBalance !== undefined ? systemBalance.toLocaleString(undefined, { minimumFractionDigits: 2 }) : "--"} disabled readOnly />
              </div>
              <FormField
                control={form.control}
                name="actual_balance"
                rules={{ required: "Required" }}
                render={({ field: { onChange, value, ...fieldProps } }) => (
                  <FormItem>
                    <FormLabel>Actual Balance</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={value === "" ? "" : value}
                        onChange={(e) => {
                          const val = e.target.value;
                          onChange(val === "" ? "" : parseFloat(val));
                        }}
                        {...fieldProps}
                        disabled={submitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Difference</label>
              <Input value={difference !== null ? difference.toLocaleString(undefined, { minimumFractionDigits: 2 }) : "--"} disabled readOnly className={difference !== 0 && difference !== null ? "text-destructive" : ""} />
            </div>
            <FormField
              control={form.control}
              name="notes"
              render={({ field: { onChange, value, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Notes (optional)"
                      value={value || ""}
                      onChange={(e) => onChange(e.target.value)}
                      {...fieldProps}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {difference !== null && difference !== 0 && (
              <Button type="button" variant="destructive" onClick={() => setShowDiscrepancy(true)}>
                Resolve Discrepancy
              </Button>
            )}
            <Button type="submit" disabled={submitting || loadingBalances} className="w-full">
              {submitting ? "Saving..." : "Record Reconciliation"}
            </Button>
          </form>
        </Form>
        <DiscrepancyModal
          open={showDiscrepancy}
          onOpenChange={setShowDiscrepancy}
          account={selectedAccount}
          systemBalance={systemBalance}
          actualBalance={actualBalance}
          difference={difference}
        />
      </CardContent>
    </Card>
  );
};

export default ReconciliationForm;