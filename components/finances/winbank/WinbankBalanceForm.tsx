"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, CardH<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";


type FormValues = {
  transaction_date: Date;
  balance: number | "";
  description: string;
};

const defaultValues: FormValues = {
  transaction_date: new Date(),
  balance: "",
  description: "",
};

const WinbankBalanceForm: React.FC = () => {
  const form = useForm<FormValues>({
    defaultValues,
    mode: "onChange"
  });
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onSubmit = async (values: FormValues) => {
    console.log("Form submitted with values:", values);
    setError(null);
    setSubmitting(true);

    try {
      if (!values.transaction_date) {
        setError("Please select a transaction date.");
        return;
      }

      const balance = typeof values.balance === 'number'
        ? values.balance
        : parseFloat(values.balance as unknown as string);

      if (isNaN(balance)) {
        setError("Please enter a valid balance amount.");
        return;
      }

      // Create the winbank balance record with proper typing
      const { error: insertError, data } = await supabase
        .from("winbank_balances")
        .insert({
          transaction_date: values.transaction_date.toISOString(),
          balance: balance,
          description: values.description || null
        })
        .select();

      if (insertError) {
        console.error("Insert error:", insertError);
        setError(insertError.message);
        toast({
          title: "Record creation failed",
          description: insertError.message,
          variant: "destructive",
        });
      } else {
        console.log("Winbank balance record created:", data);
        toast({
          title: "Balance record created",
          description: `The Winbank balance record for ${format(values.transaction_date, 'dd/MM/yyyy')} was successfully created.`,
        });
        form.reset(defaultValues);
      }
    } catch (e: unknown) {
      console.error("Submission error:", e);
      const error = e instanceof Error ? e : new Error('Unknown error');
      setError(error.message);
      toast({
        title: "Record creation failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="mb-8 max-w-xl mx-auto">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Add Winbank Balance Record</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="transaction_date"
              rules={{ required: "Required" }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Transaction Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value ? "text-muted-foreground" : ""
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="balance"
              rules={{ required: "Required" }}
              render={({ field: { onChange, value, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>Balance Amount (€)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      value={value === "" ? "" : value}
                      onChange={(e) => {
                        const val = e.target.value;
                        onChange(val === "" ? "" : parseFloat(val));
                      }}
                      {...fieldProps}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., After deposit, Monthly statement" {...field} disabled={submitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={submitting} className="w-full">
              {submitting ? "Saving..." : "Save Balance Record"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default WinbankBalanceForm;
