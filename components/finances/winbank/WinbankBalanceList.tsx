"use client";

import React, { useState, useEffect } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { format, parseISO } from "date-fns";
import { Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { Database } from "@/types/supabase";

// Use the proper type from the Database definition
type WinbankBalance = Database['public']['Tables']['winbank_balances']['Row'];

const WinbankBalanceList: React.FC = () => {
  const [balances, setBalances] = useState<WinbankBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const supabase = createClientComponentClient<Database>();

  const fetchBalances = async () => {
    try {
      setLoading(true);
      const { data, error: fetchError } = await supabase
        .from("winbank_balances")
        .select("*")
        .order("transaction_date", { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setBalances(data || []);
    } catch (err) {
      console.error("Error fetching Winbank balances:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalances();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this balance record? This action cannot be undone.")) {
      return;
    }

    try {
      const { error: deleteError } = await supabase
        .from("winbank_balances")
        .delete()
        .eq("id", id);

      if (deleteError) {
        throw deleteError;
      }

      toast({
        title: "Record deleted",
        description: "The Winbank balance record has been deleted.",
      });

      // Refresh the list
      fetchBalances();
    } catch (err) {
      console.error("Error deleting record:", err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to delete record",
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Winbank Balance Records</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : balances.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>No Winbank balance records found.</p>
            <p className="text-sm mt-2">Add your first record using the form above.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Balance</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {balances.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{format(parseISO(record.transaction_date), "dd/MM/yyyy")}</TableCell>
                    <TableCell>{formatCurrency(record.balance)}</TableCell>
                    <TableCell>{record.description || "-"}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(record.id)}
                        className="h-8 w-8"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default WinbankBalanceList;
