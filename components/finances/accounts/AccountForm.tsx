"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";
import type { Database } from '@/types/supabase';

type PaymentMethod = Database["public"]["Enums"]["payment_method"];

type FormValues = {
  account_name: PaymentMethod;
  initial_balance: number | "";
};

const defaultValues: FormValues = {
  account_name: "CASH" as PaymentMethod, // Default to CASH as a valid payment method
  initial_balance: 0,
};

const AccountForm: React.FC = () => {
  const form = useForm<FormValues>({
    defaultValues,
    mode: "onChange"
  });
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onSubmit = async (values: FormValues) => {
    console.log("Form submitted with values:", values);
    setError(null);
    setSubmitting(true);

    try {
      if (!values.account_name) {
        setError("Please enter an account name.");
        return;
      }

      const initialBalance = typeof values.initial_balance === 'number'
        ? values.initial_balance
        : parseFloat(values.initial_balance as unknown as string);

      if (isNaN(initialBalance)) {
        setError("Please enter a valid initial balance.");
        return;
      }

      // Create the account with proper typing
      const { error: insertError, data } = await supabase
        .from("account_balances")
        .insert({
          account_name: values.account_name,
          current_balance: initialBalance,
          initial_balance: initialBalance,
          last_updated: new Date().toISOString()
        })
        .select();

      if (insertError) {
        console.error("Insert error:", insertError);
        setError(insertError.message);
        toast({
          title: "Account creation failed",
          description: insertError.message,
          variant: "destructive",
        });
      } else {
        console.log("Account created:", data);
        toast({
          title: "Account created",
          description: `The account "${values.account_name}" was successfully created.`,
        });
        form.reset(defaultValues);
      }
    } catch (e: unknown) {
      console.error("Submission error:", e);
      const error = e instanceof Error ? e : new Error('Unknown error');
      setError(error.message);
      toast({
        title: "Account creation failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="mb-8 max-w-xl mx-auto">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Create New Account</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="account_name"
              rules={{ required: "Required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Cash, Bank, VIVA" {...field} disabled={submitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="initial_balance"
              rules={{ required: "Required" }}
              render={({ field: { onChange, value, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>Initial Balance</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={value === "" ? "" : value}
                      onChange={(e) => {
                        const val = e.target.value;
                        onChange(val === "" ? "" : parseFloat(val));
                      }}
                      {...fieldProps}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={submitting} className="w-full">
              {submitting ? "Creating..." : "Create Account"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AccountForm;
