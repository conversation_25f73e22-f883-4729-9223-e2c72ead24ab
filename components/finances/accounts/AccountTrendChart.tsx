import React from 'react';

interface Props {
  accountId: string;
}

// Using accountId as a comment to acknowledge we're aware it's not used yet
// but will be used in future implementation for real data fetching
const AccountTrendChart: React.FC<Props> = (/* { accountId } */) => {
  return (
    <div className="bg-blue-50 p-3 rounded-md mt-3 mb-2">
      <div className="h-12 w-full flex items-end px-1">
        {/* Placeholder bars for the chart */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            style={{
              height: `${20 + Math.random() * 60}%`,
            }}
            className="w-[8%] bg-blue-500 mx-[1px] rounded-t"
          />
        ))}
      </div>
      <div className="text-xs text-blue-600 mt-1 text-center">
        Balance trend (last 12 months)
      </div>
    </div>
  );
};

export default AccountTrendChart;