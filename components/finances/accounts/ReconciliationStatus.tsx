import React from 'react';

interface Props {
  lastReconciled: string | null;
}

function getStatus(lastReconciled: string | null) {
  if (!lastReconciled) {
    return {
      colorClass: 'text-red-600',
      bgClass: 'bg-red-50',
      label: 'Never reconciled',
      icon: '❌'
    };
  }

  const days = Math.floor((Date.now() - new Date(lastReconciled).getTime()) / (1000 * 60 * 60 * 24));

  if (days <= 7) {
    return {
      colorClass: 'text-green-600',
      bgClass: 'bg-green-50',
      label: `Reconciled ${days}d ago`,
      icon: '✅'
    };
  }

  return {
    colorClass: 'text-yellow-600',
    bgClass: 'bg-yellow-50',
    label: `Reconciled ${days}d ago`,
    icon: '⚠️'
  };
}

const ReconciliationStatus: React.FC<Props> = ({ lastReconciled }) => {
  const status = getStatus(lastReconciled);

  return (
    <div className={`${status.bgClass} ${status.colorClass} font-medium mt-3 p-2 rounded-md text-sm flex items-center`}>
      <span role="img" aria-label="status" className="mr-1">{status.icon}</span>
      {status.label}
    </div>
  );
};

export default ReconciliationStatus;