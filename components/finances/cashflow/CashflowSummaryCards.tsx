"use client";

import React from 'react';
import type { Database } from "@/types/supabase";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

type AccountBalance = Database['public']['Tables']['account_balances']['Row'];
type PaymentMethod = Database['public']['Enums']['payment_method'];

interface CashflowSummaryCardsProps {
  balances: AccountBalance[] | null;
  loading: boolean;
  error: string | null;
}

// Define fixed account order
const accountOrder: PaymentMethod[] = ['CASH', 'WINBANK', 'VIVA'];

// Helper function to get user-friendly account label
function getAccountLabel(name: PaymentMethod): string {
  switch (name) {
    case 'CASH':
      return 'Cash';
    case 'WINBANK':
      return 'Winbank';
    case 'VIVA':
      return 'Viva';
    default:
      return name;
  }
}

const CashflowSummaryCards: React.FC<CashflowSummaryCardsProps> = ({ balances, loading, error }) => {
  // Loading state
  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        {accountOrder.map((name) => (
          <Card key={name}>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">{getAccountLabel(name)}</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-24 mb-2" />
            </CardContent>
          </Card>
        ))}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Total</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-24 mb-2" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="mb-8">
        <Alert variant="destructive">
          <AlertTitle>Error loading balances</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // No data state
  if (!balances) {
    return <div className="py-8 text-center text-muted-foreground">No account data available</div>;
  }

  // Filter accounts to match our preferred order
  const filteredBalances = balances.filter(b =>
    accountOrder.includes(b.account_name)
  );

  // Calculate total balance
  const totalBalance = filteredBalances.reduce(
    (sum, account) => sum + Number(account.current_balance),
    0
  );

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8">
      {/* Display each account card */}
      {accountOrder.map((accountName) => {
        const account = filteredBalances.find(
          b => b.account_name === accountName
        );

        return (
          <Card key={accountName}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">
                {getAccountLabel(accountName)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold" aria-live="polite">
                € {account
                  ? Number(account.current_balance).toLocaleString(undefined, { minimumFractionDigits: 2 })
                  : '--'}
              </div>
              {account && account.last_reconciled && (
                <div className="text-xs text-muted-foreground mt-1">
                  Last reconciled: {new Date(account.last_reconciled).toLocaleDateString()}
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}

      {/* Total balance card */}
      <Card className="border-primary/20 bg-primary/5">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-semibold">Total</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-primary" aria-live="polite">
            € {totalBalance.toLocaleString(undefined, { minimumFractionDigits: 2 })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CashflowSummaryCards;