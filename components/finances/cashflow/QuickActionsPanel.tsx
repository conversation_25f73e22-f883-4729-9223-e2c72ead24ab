import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  CreditCard,
  ArrowLeftRight,
  BarChart3,
  Calculator
} from 'lucide-react';

const QuickActionsPanel: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-3">
          <Button asChild variant="outline" className="h-auto py-4 px-4 flex flex-col items-center">
            <Link href="/admin/finances/expenses/add">
              <CreditCard className="h-5 w-5 mb-1" />
              <span>Add Expense</span>
            </Link>
          </Button>

          <Button asChild variant="outline" className="h-auto py-4 px-4 flex flex-col items-center">
            <Link href="/admin/finances/transfers">
              <ArrowLeftRight className="h-5 w-5 mb-1" />
              <span>Transfer Funds</span>
            </Link>
          </Button>

          <Button asChild variant="outline" className="h-auto py-4 px-4 flex flex-col items-center">
            <Link href="/admin/finances/reconciliation">
              <Calculator className="h-5 w-5 mb-1" />
              <span>Reconcile</span>
            </Link>
          </Button>

          <Button asChild variant="outline" className="h-auto py-4 px-4 flex flex-col items-center">
            <Link href="/admin/finances/analysis">
              <BarChart3 className="h-5 w-5 mb-1" />
              <span>Cashflow Analysis</span>
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActionsPanel;