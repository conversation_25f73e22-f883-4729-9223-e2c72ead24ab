"use client";

import React, { useState } from "react";
import { useCashflowAnalysis } from "@/hooks/useCashflowAnalysis";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format, parseISO } from "date-fns";
import { ArrowUpRight, ArrowDownRight, ArrowLeftRight, AlertCircle, CheckCircle2 } from "lucide-react";
import Link from "next/link";
import type { Database } from "@/types/supabase";

type PaymentMethod = Database['public']['Enums']['payment_method'];

const CashflowAnalysis: React.FC = () => {
  const [period, setPeriod] = useState<"week" | "month" | "quarter" | "year">("month");
  const { data, loading, error } = useCashflowAnalysis(period);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (e) {
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Cashflow Analysis</h2>
        <Tabs value={period} onValueChange={(v: string) => setPeriod(v as "week" | "month" | "quarter" | "year")}>
          <TabsList>
            <TabsTrigger value="week">Last 7 Days</TabsTrigger>
            <TabsTrigger value="month">Last 30 Days</TabsTrigger>
            <TabsTrigger value="quarter">Last 3 Months</TabsTrigger>
            <TabsTrigger value="year">Last 12 Months</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-12 w-full mb-4" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      ) : data ? (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Income Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Total Income</CardTitle>
                <CardDescription>All payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-2">{formatCurrency(data.income.total)}</div>
                <div className="space-y-1">
                  {Object.entries(data.income.byMethod).map(([method, amount]) => (
                    <div key={method} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{method}</span>
                      <span>{formatCurrency(amount)}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Expenses Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Total Expenses</CardTitle>
                <CardDescription>All payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-2">{formatCurrency(data.expenses.total)}</div>
                <div className="space-y-1">
                  {Object.entries(data.expenses.byMethod).map(([method, amount]) => (
                    <div key={method} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{method}</span>
                      <span>{formatCurrency(amount)}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Net Cash Flow */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Net Cashflow</CardTitle>
                <CardDescription>Income minus expenses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold mb-2 ${data.income.total - data.expenses.total >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(data.income.total - data.expenses.total)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {data.income.total - data.expenses.total >= 0
                    ? "Positive cashflow for this period"
                    : "Negative cashflow for this period"}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Account Balances and Reconciliation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Account Balances & Reconciliation</CardTitle>
              <CardDescription>Comparing actual vs. expected balances</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account</TableHead>
                    <TableHead>Actual Balance</TableHead>
                    <TableHead>Expected Balance</TableHead>
                    <TableHead>Difference</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(data.accountBalances).map(([account, balance]) => {
                    const expected = data.expectedBalances[account as PaymentMethod] || 0;
                    const difference = data.balanceDifferences[account as PaymentMethod] || 0;
                    const isReconciled = Math.abs(difference) < 0.01; // Consider reconciled if difference is less than 1 cent

                    return (
                      <TableRow key={account}>
                        <TableCell className="font-medium">{account}</TableCell>
                        <TableCell>{formatCurrency(balance)}</TableCell>
                        <TableCell>{formatCurrency(expected)}</TableCell>
                        <TableCell className={difference !== 0 ? 'text-red-600' : 'text-green-600'}>
                          {formatCurrency(difference)}
                        </TableCell>
                        <TableCell>
                          {isReconciled ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                              Reconciled
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                              <AlertCircle className="h-3.5 w-3.5 mr-1" />
                              Needs Reconciliation
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Winbank Reconciliation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Winbank Reconciliation</CardTitle>
              <CardDescription>Compare system balance with your Winbank statement</CardDescription>
            </CardHeader>
            <CardContent>
              {data.winbankReconciliation.lastRecordedBalance === null ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No Winbank balance records found.</p>
                  <Button asChild className="mt-4" variant="outline">
                    <Link href="/admin/finances/winbank">Add Winbank Balance Records</Link>
                  </Button>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-muted-foreground">System Balance</h3>
                      <p className="text-2xl font-bold">{formatCurrency(data.winbankReconciliation.systemBalance)}</p>
                      <p className="text-sm text-muted-foreground">
                        Calculated from payments, expenses, and transfers
                      </p>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-muted-foreground">Last Recorded Balance</h3>
                      <p className="text-2xl font-bold">
                        {formatCurrency(data.winbankReconciliation.lastRecordedBalance || 0)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        From statement on {formatDate(data.winbankReconciliation.lastRecordedDate || '')}
                      </p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium">Difference</h3>
                        <p className={`text-lg font-bold ${
                          data.winbankReconciliation.difference &&
                          Math.abs(data.winbankReconciliation.difference) > 0.01
                            ? 'text-red-600'
                            : 'text-green-600'
                        }`}>
                          {formatCurrency(data.winbankReconciliation.difference || 0)}
                        </p>
                      </div>

                      <div>
                        {data.winbankReconciliation.isReconciled ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            Reconciled
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            <AlertCircle className="h-4 w-4 mr-1" />
                            Needs Reconciliation
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Button asChild variant="outline" size="sm">
                      <Link href="/admin/finances/winbank">
                        Manage Winbank Balance Records
                      </Link>
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Recent Transactions</CardTitle>
              <CardDescription>Last 50 transactions across all accounts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.recentTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>{formatDate(transaction.date)}</TableCell>
                        <TableCell>
                          {transaction.type === 'income' && (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              <ArrowUpRight className="h-3.5 w-3.5 mr-1" />
                              Income
                            </Badge>
                          )}
                          {transaction.type === 'expense' && (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              <ArrowDownRight className="h-3.5 w-3.5 mr-1" />
                              Expense
                            </Badge>
                          )}
                          {transaction.type === 'transfer' && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              <ArrowLeftRight className="h-3.5 w-3.5 mr-1" />
                              Transfer
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{transaction.description}</TableCell>
                        <TableCell className={transaction.type === 'income' ? 'text-green-600' : transaction.type === 'expense' ? 'text-red-600' : ''}>
                          {formatCurrency(transaction.amount)}
                        </TableCell>
                        <TableCell>{transaction.method}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        <Alert>
          <AlertTitle>No Data</AlertTitle>
          <AlertDescription>No cashflow data available for the selected period.</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default CashflowAnalysis;
