import React from 'react';
import type { Database } from "@/types/supabase";

interface Props {
  unreconciledAccounts: Database['public']['Tables']['account_balances']['Row'][];
  largeTransfers: Database['public']['Tables']['account_transfers']['Row'][];
  loading: boolean;
  error: string | null;
}

function daysAgo(dateStr: string | null) {
  if (!dateStr) return 'Never';
  const then = new Date(dateStr);
  const now = new Date();
  const diff = Math.floor((now.getTime() - then.getTime()) / (1000 * 60 * 60 * 24));
  return diff;
}

const AlertsPanel: React.FC<Props> = ({ unreconciledAccounts, largeTransfers, loading, error }) => {
  if (loading) return <div>Loading alerts...</div>;
  if (error) return <div style={{ color: 'red' }}>Error: {error}</div>;
  if (!unreconciledAccounts.length && !largeTransfers.length) return <div>No alerts</div>;

  return (
    <div>
      {unreconciledAccounts.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <strong>Unreconciled Accounts:</strong>
          <ul>
            {unreconciledAccounts.map(acc => (
              <li key={acc.id} style={{ color: '#b26a00', margin: '8px 0' }}>
                <span role="img" aria-label="warning">⚠️</span> <b>{acc.account_name}</b> not reconciled for <b>{typeof acc.last_reconciled === 'string' ? daysAgo(acc.last_reconciled) : 'N/A'}</b> days
              </li>
            ))}
          </ul>
        </div>
      )}
      {largeTransfers.length > 0 && (
        <div>
          <strong>Large Transfers (&gt; €1,000):</strong>
          <ul>
            {largeTransfers.map(tr => (
              <li key={tr.id} style={{ color: '#c62828', margin: '8px 0' }}>
                <span role="img" aria-label="money">💸</span> <b>€{Number(tr.amount).toLocaleString(undefined, { minimumFractionDigits: 2 })}</b> from <b>{tr.from_account}</b> to <b>{tr.to_account}</b> ({tr.status})
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AlertsPanel;