"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { <PERSON>ert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/hooks/useSupabase";
import { Enums, TablesInsert } from "@/types/supabase";

// Define the schema for the form
const transferFormSchema = z.object({
  from_account: z.string().min(1, "From account is required"),
  to_account: z.string().min(1, "To account is required"),
  amount: z.number().positive("Amount must be positive"),
  initiated_date: z.date({
    required_error: "Date is required",
  }),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
}).refine(data => data.from_account !== data.to_account, {
  message: "From and To accounts must be different",
  path: ["to_account"],
});

type TransferFormValues = z.infer<typeof transferFormSchema>;

type PaymentMethod = Enums<'payment_method'>;

// Define possible payment methods based on your enum
const paymentMethods: { value: PaymentMethod; label: string }[] = [
  { value: "CASH", label: "Cash" },
  { value: "WINBANK", label: "Winbank" },
  { value: "VIVA", label: "Viva" },
  { value: "OTHER", label: "Other" },
];

interface TransferFormProps {
  onTransferCreated?: () => void;
}

const TransferForm: React.FC<TransferFormProps> = ({ onTransferCreated }) => {
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<TransferFormValues>({
    resolver: zodResolver(transferFormSchema),
    defaultValues: {
      from_account: "",
      to_account: "",
      amount: 0,
      initiated_date: new Date(),
      reference_number: "",
      notes: "",
    },
  });

  const onSubmit = async (values: TransferFormValues) => {
    setError(null);
    setSubmitting(true);

    try {
      const transferData: TablesInsert<'account_transfers'> = {
        from_account: values.from_account as PaymentMethod,
        to_account: values.to_account as PaymentMethod,
        amount: values.amount,
        initiated_date: values.initiated_date.toISOString(),
        reference_number: values.reference_number || null,
        notes: values.notes || null,
        status: "pending",
      };

      const { error: insertError } = await supabase
        .from("account_transfers")
        .insert(transferData);

      if (insertError) {
        throw new Error(insertError.message);
      }

      toast({
        title: "Transfer created",
        description: "The transfer was successfully recorded.",
      });

      form.reset({
        from_account: "",
        to_account: "",
        amount: 0,
        initiated_date: new Date(),
        reference_number: "",
        notes: "",
      });

      if (onTransferCreated) {
        onTransferCreated();
      }
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : "Unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Transfer failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">New Transfer</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="from_account"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>From Account</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={submitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="to_account"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>To Account</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={submitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (€)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      value={field.value || ""}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="initiated_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      value={field.value ? new Date(field.value).toISOString().slice(0, 10) : ""}
                      onChange={(e) => {
                        const date = e.target.value ? new Date(e.target.value) : null;
                        field.onChange(date);
                      }}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reference_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reference (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Reference number"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={submitting}
              className="w-full"
            >
              {submitting ? "Processing..." : "Create Transfer"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default TransferForm;