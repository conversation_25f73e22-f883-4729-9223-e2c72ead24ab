"use client";

import React from "react";
import type { Tables } from "@/types/supabase";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>ert, <PERSON>ertTitle, AlertDescription } from "@/components/ui/alert";

interface Props {
  transfers: Tables<'account_transfers'>[] | null;
  loading: boolean;
  error: string | null;
}

const columns = [
  { key: "initiated_date", label: "Date" },
  { key: "from_account", label: "From" },
  { key: "to_account", label: "To" },
  { key: "amount", label: "Amount (€)" },
  { key: "status", label: "Status" },
  { key: "reference_number", label: "Reference" },
  { key: "notes", label: "Notes" },
];

const TransferList: React.FC<Props> = ({ transfers, loading, error }) => {
  if (loading) {
    return (
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((col) => (
              <TableHead key={col.key}>{col.label}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {[...Array(5)].map((_, i) => (
            <TableRow key={i}>
              {columns.map((col) => (
                <TableCell key={col.key}>
                  <Skeleton className="h-5 w-full" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertTitle>Error loading transfers</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!transfers || transfers.length === 0) {
    return <div className="py-8 text-center text-muted-foreground">No transfers found.</div>;
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {columns.map((col) => (
            <TableHead key={col.key}>{col.label}</TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {transfers.map((tr) => (
          <TableRow key={tr.id}>
            <TableCell>{tr.initiated_date ? tr.initiated_date.slice(0, 10) : ""}</TableCell>
            <TableCell>{tr.from_account}</TableCell>
            <TableCell>{tr.to_account}</TableCell>
            <TableCell className="font-semibold text-blue-700">{Number(tr.amount).toLocaleString(undefined, { minimumFractionDigits: 2 })}</TableCell>
            <TableCell>{tr.status}</TableCell>
            <TableCell>{tr.reference_number || ""}</TableCell>
            <TableCell>{tr.notes || ""}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default TransferList;