// components/AdminDebug.tsx
'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Define the type for debug state
interface DebugInfo {
  userId?: string;
  email?: string;
  roles?: string[];
  error?: string;
}

export function AdminDebug() {
  const [debug, setDebug] = useState<DebugInfo | null>(null);
  const supabase = createClientComponentClient();

  useEffect(() => {
    async function checkAdmin() {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          console.log('Debug: Checking roles for', session.user.id);

          const { data: roles, error } = await supabase.rpc('getUserRoles', {
            p_user_id: session.user.id
          });

          console.log('Debug: Role response', { roles, error });

          setDebug({
            userId: session.user.id,
            email: session.user.email,
            roles,
            error: error?.message
          });
        }
      } catch (err) {
        console.error('Debug component error:', err);
        setDebug(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'An unknown error occurred'
        }));
      }
    }
    checkAdmin();
  }, [supabase]);

  if (!debug) return null;

  return process.env.NODE_ENV === 'development' ? (
    <div className="fixed bottom-4 right-4 p-4 bg-black/80 text-white rounded-lg text-xs">
      <pre>{JSON.stringify(debug, null, 2)}</pre>
    </div>
  ) : null;
}