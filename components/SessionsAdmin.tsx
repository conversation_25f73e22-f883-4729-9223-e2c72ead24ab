'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SupabaseClient } from '@supabase/supabase-js';
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { QuickSessionDialog } from "@/components/QuickSessionDialog";
import ClientSelector from '@/components/ClientSelector';
import Link from 'next/link';
import ReactMarkdown from 'react-markdown';
import { Plus, User, Clock, CheckCircle, X } from 'lucide-react';

// Keep your existing interfaces
interface Message {
  type: 'success' | 'error';
  content: string;
}


interface Session {
  id: string;
  start_time: string;
  program_name?: string;
  check_ins?: CheckIn[];
  bookings?: Booking[];
  programs?: {
    name: string;
  };
}

interface CheckIn {
  id: string;
  pelatis_id: string;
  pelates: {
    id: string;
    client_name: string;
    name: string;
    last_name: string;
  }[];
}

interface Booking {
  id: string;
  pelatis_id: string;
  pelates: {
    id: string;
    client_name: string;
    name: string;
    last_name: string;
  } | null;
}

interface SubscriptionInfo {
  days_until_expiration: number | null;
  end_date: string | null;
  subscription_status: string | null;
  program_name_display: string | null;
  last_payment_end_date?: string | null;
  last_program_name?: string | null;
}

interface FullClientOption {
  value: string;
  label: string;
  pelatis_id: string;
  session_id: string;
  subscription?: SubscriptionInfo;
}

interface Program {
  id: string;
  name: string;
}

interface WodContent {
  id: string;
  content: string;
  date: string;
}

interface SessionsAdminProps {
  lang?: string;
  initialSessions: Session[];
  initialClients: { id: string; client_name: string }[];
}

// 2. State type definitions
type ModalMode = 'check-in' | 'booking';









  const SessionsAdmin: React.FC<SessionsAdminProps> = ({ lang, initialSessions, initialClients }) => {
    // Basic states
    const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());
    const [currentMonth, setCurrentMonth] = useState<number>(new Date().getMonth());
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [isDeleting, setIsDeleting] = useState<boolean>(false);
    const [quickAddDate, setQuickAddDate] = useState<string | null>(null);

  
    // Session related states
    const [sessions, setSessions] = useState<Session[]>(initialSessions);
    const [selectedSession, setSelectedSession] = useState<Session | null>(null);
    const [modalMode, setModalMode] = useState<ModalMode>('check-in');
    
    // Client related states
    const [clients, setClients] = useState<FullClientOption[]>(initialClients.map(client => ({
      value: client.id,
      label: client.client_name,
      pelatis_id: client.id,
      session_id: ''
    })));
    const [selectedClients, setSelectedClients] = useState<FullClientOption[]>([]);
    const [checkedInClients, setCheckedInClients] = useState<FullClientOption[]>([]);
    const [bookedClients, setBookedClients] = useState<FullClientOption[]>([]);
    const [todayBookings, setTodayBookings] = useState<FullClientOption[]>([]);
    const [showTodayBookings, setShowTodayBookings] = useState<boolean>(false);
  
    // Message states
    const [messages, setMessages] = useState<Message[]>([]);
    const [dialogMessages, setDialogMessages] = useState<Message[]>([]);
  
    // WOD related state
    const [wods, setWods] = useState<Record<string, WodContent>>({});
    const [programs, setPrograms] = useState<Program[]>([]);


    
  const supabase = createClientComponentClient();





  const fetchSessionsForMonth = useCallback(async (year: number, month: number) => {
    const startDate = new Date(Date.UTC(year, month, 1, 0, 0, 0));
    const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59));

    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          programs (name),
          check_ins (id, pelatis_id),
          bookings (id, pelatis_id)
        `)
        .gte('start_time', startDate.toISOString())
        .lte('start_time', endDate.toISOString())
        .order('start_time');

      if (error) throw error;
      
      const formattedSessions = data?.map(session => ({
        ...session,
        program_name: session.programs?.name || 'N/A'
      })) || [];
      
      setSessions(formattedSessions);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to fetch sessions.' }]);
    }
  }, [supabase]);

  const fetchClients = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('pelates')
        .select('id, client_name, auth_user_id')
        .order('client_name');
  
      if (error) throw error;
  
      if (data) {
        setClients(data.map(client => ({
          value: client.id,           // This is pelates.id
          label: client.client_name,
          pelatis_id: client.id,      // Same as value
          session_id: '',             // Empty for now
          subscription: undefined     // Optional
        })));
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
      setDialogMessages(prev => [...prev, { type: 'error', content: 'Failed to load clients.' }]);
    }
  }, [supabase]);

  const deleteSessionOnly = useCallback(async (sessionId: string) => {
    console.log('Delete function called with sessionId:', sessionId); // Debug log
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('sessions')
        .delete()
        .eq('id', sessionId);
  
      if (error) {
        console.error('Supabase delete error:', error); // Debug log
        throw error;
      }
  
      setMessages(prev => [...prev, { type: 'success', content: 'Session deleted successfully.' }]);
      setSelectedSession(null);
      await fetchSessionsForMonth(currentYear, currentMonth);
      console.log('Sessions after deletion:', sessions); // Debug log
    } catch (error) {
      console.error('Error deleting session:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to delete session.' }]);
    } finally {
      setIsDeleting(false);
    }
  }, [supabase, currentYear, currentMonth, fetchSessionsForMonth]);

  const checkBookingsAndDelete = useCallback(async (sessionId: string) => {
    setIsDeleting(true);
    try {
      const { count, error: bookingsError } = await supabase
      .from('bookings')
      .select('id', { count: 'exact', head: true })
      .eq('booked_session_id', sessionId);
    
    if (bookingsError) throw bookingsError;
    
    if (count && count > 0) {
      setShowConfirmModal(true);
    } else {
      await deleteSessionOnly(sessionId);
    }
    } catch (error) {
      console.error('Error checking bookings:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to check bookings.' }]);
    } finally {
      setIsDeleting(false);
    }
  }, [supabase, deleteSessionOnly]);



  const deleteSessionAndBookings = useCallback(async (sessionId: string) => {
    setIsDeleting(true);
    try {
      // Remove the bookings first
      await supabase
        .from('bookings')
        .delete()
        .eq('booked_session_id', sessionId);
  
      // Then delete the session
      await deleteSessionOnly(sessionId);
  
      setMessages(prev => [...prev, { type: 'success', content: 'Session and associated bookings deleted successfully.' }]);
    } catch (error) {
      console.error('Error deleting session and bookings:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to delete session and bookings.' }]);
    } finally {
      setShowConfirmModal(false);
      setIsDeleting(false);
    }
  }, [supabase, deleteSessionOnly]);
  

  const fetchWodsForMonth = (
    supabase: SupabaseClient,
    year: number,
    month: number,
    setWods: React.Dispatch<React.SetStateAction<{ [key: string]: { id: string; content: string; date: string } }>>,
    setMessages: React.Dispatch<React.SetStateAction<{ type: 'success' | 'error'; content: string }[]>>
  ) => async () => {
    const startDate = `${year}-${String(month + 1).padStart(2, '0')}-01`;
    const lastDay = new Date(year, month + 1, 0).getDate();
    const endDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
    
  try {
    const { data, error } = await supabase
      .from('wod')
      .select('id, content, date')
      .gte('date', startDate)
      .lte('date', endDate)
      .eq('is_published', true);

    if (error) throw error;

    const wodMap = (data || []).reduce((acc, wod) => {
      acc[wod.date] = { id: wod.id, content: wod.content, date: wod.date };
      return acc;
    }, {} as { [key: string]: { id: string; content: string; date: string } });

    setWods(wodMap);
  } catch (error) {
    console.error('Error fetching WODs:', error);
    setMessages(prev => [...prev, { type: 'error', content: 'Failed to fetch WODs.' }]);
  }
};

const memoizedFetchWodsForMonth = useCallback(() => {
  fetchWodsForMonth(supabase, currentYear, currentMonth, setWods, setMessages);
}, [supabase, currentYear, currentMonth]);

useEffect(() => {
  // Only fetch new data if the month or year changes
  if (sessions.length === 0 || 
      new Date(sessions[0].start_time).getMonth() !== currentMonth ||
      new Date(sessions[0].start_time).getFullYear() !== currentYear) {
    fetchSessionsForMonth(currentYear, currentMonth);
    memoizedFetchWodsForMonth();
  }
}, [currentYear, currentMonth, fetchSessionsForMonth, memoizedFetchWodsForMonth, sessions]);

// Then modify your createCheckIns function:
async function createCheckIns() {
  if (!selectedSession) {
    setMessages(prev => [...prev, { 
      type: 'error', 
      content: 'No session selected' 
    }]);
    return;
  }

  if (selectedClients.length === 0) {
    setMessages(prev => [...prev, { 
      type: 'error', 
      content: 'Please select at least one client' 
    }]);
    return;
  }

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) throw userError;
    if (!user?.id) throw new Error('No authenticated user found');

    const { data: userRoles, error: rolesError } = await supabase.rpc('getUserRoles', {
      p_user_id: user.id
    });
    if (rolesError) throw rolesError;

    const isAdmin = Array.isArray(userRoles) && userRoles.includes('admin');

    if (!isAdmin) {
      const { data: userPelates } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', user.id)
        .maybeSingle();

      if (!userPelates) {
        throw new Error('No associated client record found');
      }
      if (Array.isArray(userPelates) && userPelates.length > 1) {
        const validPelatesIds = userPelates.map(p => p.id);
        if (!selectedClients.some(client => validPelatesIds.includes(client.pelatis_id))) {
          throw new Error('You can only create check-ins for yourself');
        }
      } else {
        if (!selectedClients.some(client => client.pelatis_id === userPelates.id)) {
          throw new Error('You can only create check-ins for yourself');
        }
      }
    }

    const checkInsToCreate = selectedClients.map(client => ({
      session_id: selectedSession.id,
      pelatis_id: client.pelatis_id,
      check_in_time: new Date().toISOString()
    }));

    const { data: newCheckIns, error: insertError } = await supabase
      .from('check_ins')
      .insert(checkInsToCreate)
      .select(`
        *,
        pelates (
          id,
          client_name,
          name,
          last_name
        )
      `);

    if (insertError) throw insertError;
    if (!newCheckIns) throw new Error('Failed to create check-ins');

    setMessages(prev => [...prev, { 
      type: 'success', 
      content: `Successfully created ${checkInsToCreate.length} check-in${checkInsToCreate.length !== 1 ? 's' : ''}` 
    }]);
    
    setSelectedClients([]);
    
    if (selectedSession) {
      await showSessionDetails(selectedSession, 'check-in');
    }

  } catch (error) {
    console.error('Error creating check-ins:', error);
    const errorMessage = error instanceof Error 
      ? error.message 
      : typeof error === 'object' && error !== null && 'message' in error
        ? (error as { message: string }).message
        : 'Failed to create check-ins';
    
    setMessages(prev => [...prev, { 
      type: 'error', 
      content: errorMessage 
    }]);
  }
}

useEffect(() => {
  fetchClients();
}, [fetchClients]);

useEffect(() => {
  if (lang) {
    console.log(`Current language: ${lang}`);
    // You can add logic here to handle language-specific behavior
  }
}, [lang]);

  const showSessionDetails = useCallback(async (session: Session, mode: 'check-in' | 'booking') => {
    setSelectedSession(session);
    setModalMode(mode);
    setDialogMessages([]);
    setCheckedInClients([]);
    setBookedClients([]);
    
    try {
      if (mode === 'check-in') {
        const { data: checkIns, error: checkInError } = await supabase
        .from('check_ins')
        .select(`
          id, 
          pelatis_id,
          pelates: pelates!check_ins_pelatis_id_fkey (
            id,
            client_name,
            name,
            last_name
          )
        `)
        .eq('session_id', session.id);

  
        if (checkInError) throw checkInError;
  
        if (checkIns && checkIns.length > 0) {
          const clientIds = checkIns.map(c => c.pelatis_id).filter(Boolean);
          
          // Fetch active subscriptions
          const { data: subscriptions, error: subsError } = await supabase
            .from('active_subscriptions')
            .select('*')
            .in('client_id', clientIds);
  
          if (subsError) throw subsError;
  
          // Create subscription map
          const subsMap = (subscriptions || []).reduce((acc, sub) => {
            if (sub.client_id) {
              acc[sub.client_id] = {
                days_until_expiration: sub.days_until_expiration,
                end_date: sub.end_date,
                subscription_status: 'active',
                program_name_display: sub.program_name_display
              };
            }
            return acc;
          }, {} as Record<string, SubscriptionInfo>);
  
          // Format check-ins with proper client name handling
          const formattedCheckIns: FullClientOption[] = checkIns.map(c => {
            const pelatesRecord = Array.isArray(c.pelates) ? c.pelates[0] : c.pelates;
            const displayName = pelatesRecord?.client_name || 
                               `${pelatesRecord?.name || ''} ${pelatesRecord?.last_name || ''}`.trim() || 
                               `Unknown Client (ID: ${c.pelatis_id})`;
          
            return {
              value: c.id,
              label: displayName,
              pelatis_id: c.pelatis_id || '',
              session_id: session.id,
              subscription: c.pelatis_id ? subsMap[c.pelatis_id] : {
                days_until_expiration: null,
                end_date: null,
                subscription_status: 'inactive',
                program_name_display: null
              }
            };
          });
  
          setCheckedInClients(formattedCheckIns);
        }
      } else {
        // Booking logic with array handling
        const { data: bookings, error: bookingError } = await supabase
        .from('bookings')
        .select(`
          id,
          pelatis_id,
          pelates!bookings_pelatis_id_fkey (
            id,
            client_name,
            name,
            last_name
          )
        `)
        .eq('booked_session_id', session.id);
    
      if (bookingError) throw bookingError;
    
      if (bookings && bookings.length > 0) {
        // Fetch active subscriptions for booked clients
        const clientIds = bookings.map(b => b.pelatis_id).filter(Boolean);
        
        const { data: subscriptions, error: subsError } = await supabase
          .from('active_subscriptions')
          .select('*')
          .in('client_id', clientIds);
    
        if (subsError) throw subsError;
    
        // Create subscription map
        const subsMap = (subscriptions || []).reduce((acc, sub) => {
          if (sub.client_id) {
            acc[sub.client_id] = {
              days_until_expiration: sub.days_until_expiration,
              end_date: sub.end_date,
              subscription_status: 'active',
              program_name_display: sub.program_name_display
            };
          }
          return acc;
        }, {} as Record<string, SubscriptionInfo>);
    
        const formattedBookings: FullClientOption[] = bookings.map(b => {
          const pelatesRecord = Array.isArray(b.pelates) ? b.pelates[0] : b.pelates;
          const displayName = pelatesRecord?.client_name || 
                             `${pelatesRecord?.name || ''} ${pelatesRecord?.last_name || ''}`.trim() || 
                             `Unknown Client (ID: ${b.pelatis_id})`;
          
          return {
            value: b.id,
            label: displayName,
            pelatis_id: b.pelatis_id,
            session_id: session.id,
            subscription: b.pelatis_id ? subsMap[b.pelatis_id] : {
              days_until_expiration: null,
              end_date: null,
              subscription_status: 'inactive',
              program_name_display: null
            }
          };
        });
        
        setBookedClients(formattedBookings);
      }
    }
    } catch (error) {
      console.error(`Error in showSessionDetails (${mode}):`, error);
      setDialogMessages(prev => [...prev, { 
        type: 'error', 
        content: `Failed to fetch ${mode} details. Please try again or contact support.` 
      }]);
    }
  }, [supabase]);

  const WodButton: React.FC<{ wodContent: string; wodDate: string }> = ({ wodContent, wodDate }) => {
    const [isOpen, setIsOpen] = useState(false);

    const formatDate = (dateString: string) => {
      const [year, month, day] = dateString.split('-');
      return `${day}/${month}/${year}`;
    };

    return (
      <>
        <Button
          variant="outline"
          size="sm"
          className="mt-auto w-full text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800"
          onClick={() => setIsOpen(true)}
        >
          View WOD
        </Button>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Workout of the Day - {formatDate(wodDate)}</DialogTitle>
            </DialogHeader>
            <div className="mt-2">
              <ReactMarkdown className="prose max-w-none">
                {wodContent}
              </ReactMarkdown>
            </div>
            <DialogFooter>
              <Button onClick={() => setIsOpen(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    );
  };

  // Add this useEffect to fetch programs
useEffect(() => {
  const fetchPrograms = async () => {
    try {
      const { data, error } = await supabase
        .from('programs')
        .select('id, name');
      if (error) throw error;
      setPrograms(data || []);
    } catch (error) {
      console.error('Error fetching programs:', error);
    }
  };
  
  fetchPrograms();
}, [supabase]);

// Add this function to handle quick session creation
const handleQuickSessionCreate = async (programId: string, time: string) => {
  if (!quickAddDate) return;

  try {
    const [hours, minutes] = time.split(':');
    const sessionDate = new Date(quickAddDate);
    sessionDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    // Step 1: Get the current user's ID
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User is not authenticated');
    }

    // Step 2: Check if the user is an admin
    const { data: roles, error: rolesError } = await supabase.rpc('getUserRoles', {
      p_user_id: user.id // Use the correct parameter name (p_user_id)
    });

    if (rolesError) throw rolesError;
    if (!roles.includes('admin')) {
      throw new Error('Unauthorized: Only admins can create sessions');
    }

    // Step 3: Create the session
    const { error } = await supabase
      .from('sessions')
      .insert({
        program_id: programId,
        start_time: sessionDate.toISOString(),
        duration: 60,
        max_participants: 10,
        created_by: user.id, // Use the user's ID
        created_at: new Date().toISOString()
      })
      .select();

    if (error) {
      console.error('Supabase error:', error);
      throw error;
    }

    // Step 4: Refresh sessions and show success message
    fetchSessionsForMonth(currentYear, currentMonth);
    setMessages(prev => [...prev, {
      type: 'success',
      content: 'Session created successfully'
    }]);
  } catch (error) {
    console.error('Error creating check-ins:', error);
    const errorMessage = error instanceof Error
      ? error.message
      : typeof error === 'object' && error !== null && 'message' in error
        ? (error as { message: string }).message
        : 'Failed to create check-ins.';

    setMessages(prev => [...prev, {
      type: 'error',
      content: errorMessage
    }]);
  }
};

  function renderCalendar() {
    const monthNames = ["January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
    const calendar = [];
    const today = new Date();
  
    // Day headers
    const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
      <div key={day} className="font-bold text-center py-2 text-xs sm:text-sm">{day}</div>
    ));
  
    // Empty cells for days before the first of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendar.push(
        <div key={`empty-${i}`} className="min-h-[80px] sm:min-h-[100px]"></div>
      );
    }
  
    // Calendar days
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const daySessions = sessions.filter(session => 
        new Date(session.start_time).toDateString() === new Date(dateString).toDateString()
      );

      const isToday = new Date(dateString).toDateString() === today.toDateString();
      const hasWod = dateString in wods;

      calendar.push(
        <Card key={day} className={`min-h-[80px] sm:min-h-[100px] ${isToday ? 'bg-blue-100' : ''} flex flex-col`}>
          <CardHeader className="p-1 sm:p-2 flex justify-between items-center">
  <div className="font-bold text-xs sm:text-sm">{day} <Link 
  href={`/admin/sessions/day?date=${dateString}`}
  className="text-blue-600 hover:text-blue-800 text-xs"
>(View Day) 
</Link>
</div>
  <Button
    variant="ghost"
    size="sm"
    className="h-6 w-6 p-0"
    onClick={() => setQuickAddDate(`${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`)}
  ><span>new Session</span>
    <Plus className="h-4 w-4" />
  </Button>
 
</CardHeader>

          <CardContent className="p-1 sm:p-2 flex-grow flex flex-col">
            {daySessions.map(session => (
              <Button
                key={session.id}
                variant={new Date(session.start_time) < new Date() ? "secondary" : "default"}
                size="sm"
                className="w-full md:flex md:flex-wrap mb-1 text-left justify-start text-xs sm:text-sm"
                onClick={() => showSessionDetails(session, 'check-in')}
              >
                <span className="truncate text-xs md:w-full">
                  {new Date(session.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false })} - {session.program_name}
                </span>
                <span>
                {session.check_ins && session.check_ins.length > 0 && (
                  <span className="ml-1 text-xs text-green-600">c: ({session.check_ins.length})</span>
                )}
                {session.bookings && session.bookings.length > 0 && (
                  <span className="ml-1 text-xs text-blue-600">b:({session.bookings.length})</span>
                )}
                </span>
              </Button>
            ))}
            {hasWod && (
              <WodButton 
                wodContent={wods[dateString].content} 
                wodDate={wods[dateString].date}
              />
            )}
          </CardContent>
        </Card>
      );
    }
  
    return (
      <div>
<div className="mb-4 flex justify-between items-center">
  <Button 
    onClick={() => {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(prev => prev - 1);
      } else {
        setCurrentMonth(prev => prev - 1);
      }
    }} 
    className="text-xs sm:text-sm"
  >
    Prev
  </Button>
  <span className="text-sm sm:text-lg font-semibold">
    {monthNames[currentMonth]} {currentYear}
  </span>
  <Button 
    onClick={() => {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(prev => prev + 1);
      } else {
        setCurrentMonth(prev => prev + 1);
      }
    }} 
    className="text-xs sm:text-sm"
  >
    Next
  </Button>
</div>
        <Button onClick={() => {
  setShowTodayBookings(true);
  fetchTodayBookings();
}} className="mb-4">View Today&apos;s Bookings</Button>
        <div className="grid md:grid-cols-7 gap-1 sm:gap-2">
          {dayHeaders}
          {calendar}
        </div>
      </div>
    );
  }


  async function createBookings() {
    if (!selectedSession) return;
    if (selectedClients.length === 0) {
      setMessages(prev => [...prev, { type: 'error', content: 'Please select at least one client' }]);
      return;
    }

    try {
      const bookings = selectedClients.map(client => ({
        booked_session_id: selectedSession.id,
        pelatis_id: client.value
      }));

      const { error } = await supabase.from('bookings').insert(bookings);

      if (error) throw error;

      setMessages(prev => [...prev, { type: 'success', content: 'Bookings created successfully' }]);
      showSessionDetails(selectedSession, 'booking');
      setSelectedClients([]);
    } catch (error) {
      console.error('Error creating bookings:', error);
      setMessages(prev => [...prev, { type: 'error', content: 'Failed to create bookings.' }]);
    }
  }

  async function deleteCheckIn(checkInId: string) {
    try {
      const { error } = await supabase
        .from('check_ins')
        .delete()
        .eq('id', checkInId);

      if (error) throw error;

      setDialogMessages(prev => [...prev, { type: 'success', content: 'Check-in deleted successfully.' }]);
      setCheckedInClients(prev => prev.filter(client => client.value !== checkInId));
      
      if (selectedSession) {
        await showSessionDetails(selectedSession, 'check-in');
      }
    } catch (error) {
      console.error('Error deleting check-in:', error);
      setDialogMessages(prev => [...prev, { type: 'error', content: 'Failed to delete check-in.' }]);
    }
  }

  async function deleteBooking(bookingId: string) {
    try {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);
  
      if (error) throw error;
  
      setDialogMessages(prev => [...prev, { type: 'success', content: 'Booking deleted successfully.' }]);
      setTodayBookings(prev => prev.filter(booking => booking.value !== bookingId));
    } catch (error) {
      console.error('Error deleting booking:', error);
      setDialogMessages(prev => [...prev, { type: 'error', content: 'Failed to delete booking.' }]);
    }
  }
 // New function to handle check-in creation
 async function createCheckInFromBooking(bookingId: string, pelatis_id: string, session_id: string) {
  try {
    const { error } = await supabase
      .from('check_ins')
      .insert({
        session_id: session_id,
        pelatis_id: pelatis_id,
        check_in_time: new Date().toISOString()
      })
      .select();

    if (error) throw error;

    setDialogMessages(prev => [...prev, { type: 'success', content: 'Check-in created successfully.' }]);
    
    // Remove the booking from the list
    setTodayBookings(prev => prev.filter(booking => booking.value !== bookingId));

    // Delete the booking
    await deleteBooking(bookingId);
  } catch (error) {
    console.error('Error creating check-in:', error);
    setDialogMessages(prev => [...prev, { type: 'error', content: 'Failed to create check-in.' }]);
  }
}
const fetchTodayBookings = useCallback(async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  try {
    const { data: todaySessions, error: sessionsError } = await supabase
      .from('sessions')
      .select('id')
      .gte('start_time', today.toISOString())
      .lt('start_time', tomorrow.toISOString());

    if (sessionsError) throw sessionsError;

    if (todaySessions && todaySessions.length > 0) {
      const sessionIds = todaySessions.map(session => session.id);

      // Updated query to fetch all client information
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          pelatis_id,
          booked_session_id,
          pelates:pelates (
            id,
            client_name,
            name,
            last_name
          )
        `)
        .in('booked_session_id', sessionIds);

      if (bookingsError) throw bookingsError;

      if (bookings && bookings.length > 0) {
        // Fetch active subscriptions for booked clients
        const clientIds = bookings.map(booking => booking.pelatis_id).filter(Boolean);
        
        const { data: subscriptions, error: subsError } = await supabase
          .from('active_subscriptions')
          .select('*')
          .in('client_id', clientIds);

        if (subsError) throw subsError;

        // Create subscription map
        const subsMap = (subscriptions || []).reduce((acc, sub) => {
          if (sub.client_id) {
            acc[sub.client_id] = {
              days_until_expiration: sub.days_until_expiration,
              end_date: sub.end_date,
              subscription_status: 'active',
              program_name_display: sub.program_name_display
            };
          }
          return acc;
        }, {} as Record<string, SubscriptionInfo>);

        const formattedBookings: FullClientOption[] = bookings.map(booking => {
          const pelatesRecord = Array.isArray(booking.pelates) ? booking.pelates[0] : booking.pelates;
          const displayName = pelatesRecord?.client_name || 
                            `${pelatesRecord?.name || ''} ${pelatesRecord?.last_name || ''}`.trim() || 
                            `Unknown Client (ID: ${booking.pelatis_id})`;

          return {
            value: booking.id,
            label: displayName,
            pelatis_id: booking.pelatis_id,
            session_id: booking.booked_session_id,
            subscription: booking.pelatis_id ? subsMap[booking.pelatis_id] : {
              days_until_expiration: null,
              end_date: null,
              subscription_status: 'inactive',
              program_name_display: null
            }
          };
        });
        
        setTodayBookings(formattedBookings);
      } else {
        setTodayBookings([]);
      }
    } else {
      setTodayBookings([]);
    }
  } catch (error) {
    console.error('Error fetching today\'s bookings:', error);
    setDialogMessages(prev => [...prev, {
      type: 'error',
      content: 'Failed to fetch today\'s bookings. Please try again or contact support.'
    }]);
  }
}, [supabase]);

  return (
    <div className="bg-gray-100 min-h-screen p-2 sm:p-4 md:p-8">
    <div className="max-w-6xl mx-auto bg-white p-2 sm:p-4 md:p-6 rounded-lg shadow-lg">
      <div className="mb-4">
        {messages.map((msg, index) => (
          <div key={index} className={`p-2 sm:p-4 mb-2 sm:mb-4 rounded-md text-sm ${msg.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {msg.content}
          </div>
        ))}
      </div>
      
      {renderCalendar()}
    </div>
    {selectedSession && (
      <Dialog open={!!selectedSession} onOpenChange={() => {
        setSelectedSession(null);
        setDialogMessages([]);
        setCheckedInClients([]);
        setBookedClients([]);
      }}>
        <DialogContent className="w-full max-w-lg sm:max-w-xl md:max-w-2xl">
          <DialogHeader>
            <DialogTitle className={`text-lg sm:text-xl ${modalMode === 'check-in' ? 'text-green-600' : 'text-blue-600'}`}>
              {modalMode === 'check-in' ? 'Check-ins' : 'Bookings'} for Session
            </DialogTitle>
          </DialogHeader>
          <div className="py-2 sm:py-4 text-sm sm:text-base">
            <p>Date: {new Date(selectedSession.start_time).toLocaleDateString()}</p>
            <p>Time: {new Date(selectedSession.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false })}</p>
            <p>Program: {selectedSession.program_name || 'N/A'}</p> 
          </div>

          <div className="mb-2 sm:mb-4 max-h-48 sm:max-h-60 overflow-y-auto">
  {dialogMessages.map((msg, index) => (
    <div key={index} className={`p-2 sm:p-4 mb-2 rounded-md text-sm ${msg.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
      {msg.content}
    </div>
  ))}
  <h3 className={`font-semibold mb-2 ${modalMode === 'check-in' ? 'text-green-600' : 'text-blue-600'}`}>
    {modalMode === 'check-in' ? 'Checked-in Clients:' : 'Booked Clients:'}
  </h3>
  <ul className="space-y-1 sm:space-y-2">
    {modalMode === 'check-in' 
       ? checkedInClients.map(client => (
        <li key={client.value} className="flex justify-between items-center bg-green-50 p-2 sm:p-3 rounded">
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="font-medium">{client.label}</span>
              <Link 
                href={`/admin/users/${client.pelatis_id}`}
                className="inline-flex items-center text-gray-500 hover:text-gray-700"
                title="View user profile"
              >
                <User size={16} />
              </Link>
            </div>
            {client.subscription && (
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Clock className="h-3 w-3" />
                {client.subscription.subscription_status === 'active' ? (
                  <span className={
                    client.subscription.days_until_expiration && 
                    client.subscription.days_until_expiration <= 7 ? 
                    'text-amber-600' : ''
                  }>
                    Expires in {client.subscription.days_until_expiration} days 
                    {client.subscription.program_name_display && 
                      ` | ${client.subscription.program_name_display}`}
                  </span>
                ) : (
                  <span className="text-red-600">Inactive subscription</span>
                )}
              </div>
            )}
          </div>
  <div className="flex items-center gap-2">
    <Button 
      variant="destructive" 
      size="sm" 
      onClick={() => deleteCheckIn(client.value)}
      className="text-xs sm:text-sm"
    >
      Delete
    </Button>
  </div>
</li>
        ))
      : bookedClients.map(client => (
<li key={client.value} className="flex justify-between items-center bg-blue-50 p-2 sm:p-3 rounded">
  <div className="flex flex-col">
    <div className="flex items-center gap-2">
      <span className="font-medium">{client.label}</span>
      <a 
        href={`/admin/users/${client.pelatis_id}`}
        className="inline-flex items-center text-gray-500 hover:text-gray-700"
        title="View user profile"
      >
        <User size={16} />
      </a>
    </div>
    {client.subscription && (
      <div className="flex items-center gap-2 text-xs text-gray-600">
        <Clock className="h-3 w-3" />
        {client.subscription.days_until_expiration !== null && (
          client.subscription.days_until_expiration <= 0 ? (
            <span className="text-red-600">Subscription expired</span>
          ) : (
            <span className={client.subscription.days_until_expiration <= 7 ? 'text-amber-600' : ''}>
              {client.subscription.days_until_expiration} days remaining | {client.subscription.program_name_display}
            </span>
          )
        )}
      </div>
    )}
  </div>
  <div className="flex items-center gap-2">
    <Button 
      variant="destructive" 
      size="sm" 
      onClick={() => deleteBooking(client.value)}
      className="text-xs sm:text-sm"
    >
      Delete
    </Button>
  </div>
</li>
        ))
    }
  </ul>
</div>
          <div className="mb-2 sm:mb-4">
            <ClientSelector
              clients={clients}
              selectedClients={selectedClients}
              onChange={setSelectedClients}
            />
          </div>
          <DialogFooter className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Button onClick={() => checkBookingsAndDelete(selectedSession.id)} variant="destructive" className="text-xs sm:text-sm">Delete Session</Button>
            {modalMode === 'check-in' ? (
              <Button onClick={createCheckIns} variant="default" className="bg-green-500 hover:bg-green-600 text-white text-xs sm:text-sm">Create Check-ins</Button>
            ) : (
              <Button onClick={createBookings} variant="default" className="bg-blue-500 hover:bg-blue-600 text-white text-xs sm:text-sm">Create Bookings</Button>
            )}
            <Button 
              onClick={() => showSessionDetails(selectedSession, modalMode === 'check-in' ? 'booking' : 'check-in')} 
              variant="outline"
              className="text-xs sm:text-sm"
            >
              Switch to {modalMode === 'check-in' ? 'Bookings' : 'Check-ins'}
            </Button>
            <Button onClick={() => setSelectedSession(null)} variant="secondary" className="text-xs sm:text-sm">Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )}

    {showTodayBookings && (
  <Dialog open={showTodayBookings} onOpenChange={setShowTodayBookings}>
    <DialogContent className="w-full max-w-lg sm:max-w-xl md:max-w-2xl">
      <DialogHeader>
        <DialogTitle className="text-lg sm:text-xl text-blue-600">
          Today&apos;s Bookings
        </DialogTitle>
      </DialogHeader>
      <div className="py-2 sm:py-4 text-sm sm:text-base">
        {todayBookings.length === 0 ? (
          <p>No bookings for today.</p>
        ) : (
          <ul className="list-disc list-inside space-y-1 sm:space-y-2">
{todayBookings.map((booking: FullClientOption) => (
  <li key={booking.value} className="flex justify-between items-center bg-blue-50 p-1 sm:p-2 rounded text-sm">
    <span>{booking.label}</span>
    <div>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => createCheckInFromBooking(booking.value, booking.pelatis_id, booking.session_id)}
        className="text-green-500 mr-2"
      >
        <CheckCircle size={16} />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => deleteBooking(booking.value)}
        className="text-red-500"
      >
        <X size={16} />
      </Button>
    </div>
  </li>
))}
          </ul>
        )}
      </div>
      <DialogFooter>
        <Button onClick={() => setShowTodayBookings(false)} variant="secondary" className="text-xs sm:text-sm">Close</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
)}
     

     {showConfirmModal && selectedSession && (
 <Dialog open={showConfirmModal} onOpenChange={setShowConfirmModal}>
 <DialogContent className="sm:max-w-[425px]">
   <DialogHeader>
     <DialogTitle>Confirm Deletion</DialogTitle>
   </DialogHeader>
   <div className="py-4">
     <p>Are you sure you want to delete this session?</p>
   </div>
   <DialogFooter className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
     <Button onClick={() => deleteSessionOnly(selectedSession?.id ?? '')} variant="destructive" disabled={isDeleting}>
       {isDeleting ? 'Deleting...' : 'Delete Session Only'}
     </Button>
     <Button onClick={() => deleteSessionAndBookings(selectedSession?.id ?? '')} variant="destructive" disabled={isDeleting}>
       {isDeleting ? 'Deleting...' : 'Delete Session and Bookings'}
     </Button>
     <Button onClick={() => setShowConfirmModal(false)} variant="secondary" disabled={isDeleting}>
       Cancel
     </Button>
   </DialogFooter>
 </DialogContent>
</Dialog>

      )}


<QuickSessionDialog
  isOpen={!!quickAddDate}
  onClose={() => setQuickAddDate(null)}
  onCreateSession={handleQuickSessionCreate}
  programs={programs}
  selectedDate={quickAddDate || ''}
/>
    </div>
  );
};

        
  

export default SessionsAdmin;