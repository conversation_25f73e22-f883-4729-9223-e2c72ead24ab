import React, { useState, useEffect } from 'react';
import { Bell, CheckCheck, Info, AlertCircle, CreditCard, Trophy, Check } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNotifications } from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { el } from 'date-fns/locale';
import { cn } from "@/lib/utils";
import type { Database } from '@/types/supabase';

type NotificationRow = Database['public']['Tables']['notifications']['Row'];

const getNotificationIcon = (type: string | null): JSX.Element => {
  switch(type) {
    case 'payment':
    case 'admin_payment':
      return <CreditCard className="h-4 w-4 text-primary" />;
    case 'info':
      return <Info className="h-4 w-4 text-blue-500" />;
    case 'achievement':
      return <Trophy className="h-4 w-4 text-yellow-500" />;
    case 'membership':
      return <AlertCircle className="h-4 w-4 text-secondary" />;
    case 'injury_report':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Bell className="h-4 w-4 text-muted-foreground" />;
  }
};

const getNotificationStyle = (type: string | null, read: boolean): string => {
  const baseStyle = cn(
    "flex items-center justify-between p-3 gap-2",
    read ? "bg-muted/30" : "bg-background",
    "hover:bg-muted/50 transition-colors"
  );

  // Fix: Use conditional class application properly
  return cn(
    baseStyle,
    type === 'injury_report' && !read ? 'border-l-2 border-red-500' : '',
    type === 'payment' && !read ? 'border-l-2 border-primary' : '',
    type === 'info' && !read ? 'border-l-2 border-blue-500' : '',
    type === 'achievement' && !read ? 'border-l-2 border-yellow-500' : ''
  );
};

interface NotificationsPopoverProps {
  userId: string;
}

export function NotificationsPopover({ userId }: NotificationsPopoverProps) {
  const [open, setOpen] = useState(false);
  const { notifications, loading, isAdmin, markAsRead, markAllAsRead } = useNotifications(userId);

  // Debug logs
  useEffect(() => {
    console.log('NotificationsPopover Debug:', {
      userId,
      isAdmin,
      notificationsCount: notifications.length,
      loading
    });
  }, [userId, isAdmin, notifications, loading]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const handleNotificationClick = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative text-white"
          aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex items-center justify-center">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-destructive opacity-75" />
              <span className="relative inline-flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground">
                {unreadCount}
              </span>
            </span>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-[calc(100vw-2rem)] sm:w-96 p-0 z-50" align="end" sideOffset={8} alignOffset={0} forceMount>
        <div className="flex items-center justify-between border-b px-4 py-3">
          <h3 className="font-semibold text-sm sm:text-base">
            Notifications {isAdmin && '(Admin View)'}
          </h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 gap-1 sm:gap-2"
              onClick={handleMarkAllAsRead}
            >
              <CheckCheck className="h-4 w-4" />
              <span className="text-xs hidden sm:inline">Mark all as read</span>
              <span className="text-xs sm:hidden">Clear all</span>
            </Button>
          )}
        </div>

        <ScrollArea className="h-[calc(100vh-20rem)] max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <span className="text-sm text-muted-foreground">Loading notifications...</span>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bell className="mb-2 h-8 w-8 text-muted-foreground" />
              <p className="text-sm font-medium text-muted-foreground">
                No notifications
              </p>
              {isAdmin && (
                <p className="text-xs text-muted-foreground mt-1">
                  You will see admin notifications here
                </p>
              )}
            </div>
          ) : (
            <div className="divide-y divide-border">
              {notifications.map((notification: NotificationRow) => (
                <div
                  key={notification.id}
                  className={getNotificationStyle(notification.type, !!notification.read)}
                >
                  <div className="flex items-start gap-3 flex-1">
                    {getNotificationIcon(notification.type)}
                    <div className="space-y-1 flex-1">
                      <p className={cn(
                        "text-sm",
                        notification.read ? "text-muted-foreground" : "text-foreground"
                      )}>
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(notification.created_at), {
                          addSuffix: true,
                          locale: el
                        })}
                      </p>
                      {isAdmin && notification.admin_impersonation && (
                        <span className="text-xs text-blue-500">Admin notification</span>
                      )}
                    </div>
                  </div>
                  {!notification.read && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 shrink-0"
                      onClick={() => handleNotificationClick(notification.id)}
                    >
                      <Check className="h-4 w-4" />
                      <span className="sr-only">Mark as read</span>
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}