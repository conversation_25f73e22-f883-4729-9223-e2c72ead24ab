'use client'

import { useState, useEffect } from 'react';
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';
import Image from 'next/image';

interface ImageDebuggerProps {
  imageId: string;
  bucket?: string;
  width?: number;
  height?: number;
}

export default function ImageDebugger({
  imageId,
  bucket = 'exercises',
  width = 200,
  height = 200
}: ImageDebuggerProps) {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [bucketVariations, setBucketVariations] = useState<string[]>([bucket]);

  useEffect(() => {
    // Try different case variations of the bucket name
    const variations = [bucket, bucket.toLowerCase(), bucket.toUpperCase(),
      bucket.charAt(0).toUpperCase() + bucket.slice(1).toLowerCase()];
    setBucketVariations(variations);

    // Start with the provided bucket
    const url = getImageUrl(imageId, bucket);
    setImageUrl(url);
    setIsLoaded(false);
    setIsError(false);
  }, [imageId, bucket]);

  return (
    <div className="border p-4 rounded-md bg-gray-50">
      <h3 className="font-medium mb-2">Image Debugger</h3>

      <div className="flex flex-col gap-4">
        <div className="relative" style={{ width, height }}>
          {imageUrl && (
            <Image
              src={imageUrl}
              alt="Debug image"
              fill
              className="object-cover rounded-md"
              onLoad={() => setIsLoaded(true)}
              onError={() => {
                setIsError(true);
                const imgElement = document.getElementById('debug-img') as HTMLImageElement;
                if (imgElement) {
                  imgElement.src = getFallbackImageUrl('Load+Error');
                }
              }}
              id="debug-img"
            />
          )}

          {!isLoaded && !isError && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-200 rounded-md">
              <p className="text-sm text-gray-500">Loading...</p>
            </div>
          )}

          {isError && (
            <div className="absolute inset-0 flex items-center justify-center bg-red-100 rounded-md">
              <p className="text-sm text-red-500">Error loading image</p>
            </div>
          )}
        </div>

        <button
          className="text-sm text-blue-500 underline"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>

        {showDetails && (
          <div className="text-sm space-y-2">
            <div>
              <span className="font-medium">Image ID:</span> {imageId || 'None'}
            </div>
            <div>
              <span className="font-medium">Bucket:</span> {bucket}
            </div>
            <div>
              <span className="font-medium">Generated URL:</span>
              <div className="bg-gray-100 p-2 rounded mt-1 break-all">
                {imageUrl || 'None'}
              </div>
            </div>
            <div>
              <span className="font-medium">Status:</span>
              {isLoaded ? (
                <span className="text-green-500">Loaded successfully</span>
              ) : isError ? (
                <span className="text-red-500">Failed to load</span>
              ) : (
                <span className="text-yellow-500">Loading...</span>
              )}
            </div>

            <div className="mt-2">
              <span className="font-medium">Try other bucket variations:</span>
              <div className="mt-1 space-y-1">
                {bucketVariations.map((bucketName, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <a
                      href={getImageUrl(imageId, bucketName)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 underline"
                    >
                      Try with bucket: {bucketName}
                    </a>
                    {bucket === bucketName && <span className="text-xs text-gray-500">(current)</span>}
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-4">
              <span className="font-medium">Direct link:</span>
              <div className="mt-1">
                <a
                  href={imageUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 underline"
                >
                  Open image in new tab
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
