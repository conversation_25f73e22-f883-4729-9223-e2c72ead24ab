import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ReactMarkdown from 'react-markdown';

interface Wod {
  id: number;
  date: string;
  content: string;
  is_published: boolean;
}

const SwipeableWodPage: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [touchStart, setTouchStart] = useState<number>(0);
  const [touchEnd, setTouchEnd] = useState<number>(0);
  const [wods, setWods] = useState<Wod[]>([]);

  // Get week bounds for date calculations
  const getWeekBounds = (date: Date): { start: Date; end: Date } => {
    const start = new Date(date);
    // Go back to previous Saturday
    start.setDate(start.getDate() - start.getDay() - 1);
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setDate(end.getDate() + 13); // Get next Saturday
    end.setHours(23, 59, 59, 999);

    return { start, end };
  };

  const fetchWods = useCallback(async (): Promise<void> => {
    try {
      const today = new Date();
      const bounds = getWeekBounds(today);
      
      // Mock data for demonstration - replace with your actual data fetching
      const mockWods: Wod[] = Array.from({ length: 14 }, (_, i) => {
        const date = new Date(bounds.start);
        date.setDate(date.getDate() + i);
        return {
          id: i,
          date: date.toISOString(),
          content: `Workout for ${date.toLocaleDateString()}\n\n1. 5 rounds:\n- 10 pushups\n- 15 squats\n- 20 situps`,
          is_published: true
        };
      });
      
      setWods(mockWods);
      
      // Set current index to today's workout
      const todayIndex = mockWods.findIndex(wod => 
        new Date(wod.date).toDateString() === new Date().toDateString()
      );
      setCurrentIndex(todayIndex !== -1 ? todayIndex : 0);
    } catch (error) {
      console.error('Error fetching WODs:', error);
    }
  }, []);

  useEffect(() => {
    fetchWods();
  }, [fetchWods]);

  const handleTouchStart = (e: React.TouchEvent): void => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent): void => {
    setTouchEnd(e.touches[0].clientX);
  };

  const handleTouchEnd = (): void => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentIndex < wods.length - 1) {
      setCurrentIndex(curr => curr + 1);
    }
    if (isRightSwipe && currentIndex > 0) {
      setCurrentIndex(curr => curr - 1);
    }

    setTouchStart(0);
    setTouchEnd(0);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const weekday = weekdays[date.getDay()];
    return `${weekday}, ${date.toLocaleDateString()}`;
  };

  const isToday = (dateString: string): boolean => {
    const today = new Date();
    const date = new Date(dateString);
    return date.toDateString() === today.toDateString();
  };

  const navigate = (direction: 'prev' | 'next'): void => {
    if (direction === 'prev' && currentIndex > 0) {
      setCurrentIndex(curr => curr - 1);
    }
    if (direction === 'next' && currentIndex < wods.length - 1) {
      setCurrentIndex(curr => curr + 1);
    }
  };

  if (!wods.length) return <div className="p-4">Loading...</div>;

  return (
    <div className="flex flex-col h-screen p-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Daily WOD</h1>
        <div className="flex gap-2">
          <button 
            onClick={() => navigate('prev')}
            disabled={currentIndex === 0}
            className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-50"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          <button 
            onClick={() => navigate('next')}
            disabled={currentIndex === wods.length - 1}
            className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-50"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </div>
      </div>

      <div 
        className="flex-1 relative overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div 
          className="absolute w-full h-full transition-transform duration-300"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          <div className="flex absolute left-0 h-full" style={{ width: `${wods.length * 100}%` }}>
            {wods.map((wod) => (
              <div 
                key={wod.id} 
                className="h-full" 
                style={{ width: `${100 / wods.length}%` }}
              >
                <Card 
                  className={`mx-4 h-full flex flex-col ${
                    isToday(wod.date) ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                >
                  <CardHeader>
                    <CardTitle>{formatDate(wod.date)}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 overflow-y-auto">
                    <div className="prose max-w-none">
                      <ReactMarkdown>{wod.content}</ReactMarkdown>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-center gap-2 mt-4">
        {wods.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full ${
              currentIndex === index ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default SwipeableWodPage;