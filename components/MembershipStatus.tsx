import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { format, parseISO } from 'date-fns';
import { CalendarDays, Crown, Timer } from 'lucide-react';

type ActiveSubscriptionsRow = {
  subscription_status?: string | null;
  program_name_display?: string | null;
  end_date?: string | null;
  days_until_expiration?: number | null;
};

const MembershipStatus = ({ 
  activeSubscription 
}: { 
  activeSubscription: ActiveSubscriptionsRow | null 
}) => {
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '-';
    return format(parseISO(dateString), 'dd/MM/yyyy');
  };

  const getStatusColor = (status: string | null | undefined) => {
    switch(status) {
      case 'Active':
        return 'bg-emerald-500';
      case 'In Grace Period':
        return 'bg-yellow-500';
      default:
        return 'bg-red-500';
    }
  };

  return (
    <Card className="mb-6 border-t-4 border-t-blue-500">
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Status */}
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              <Crown className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-medium">Status</p>
              <span className={`inline-block mt-1 px-3 py-1 rounded-full text-sm font-medium text-white ${
                getStatusColor(activeSubscription?.subscription_status)
              }`}>
                {activeSubscription?.subscription_status || 'Inactive'}
              </span>
            </div>
          </div>

          {/* Program */}
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              <Timer className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-medium">Program</p>
              <p className="mt-1 font-medium">{activeSubscription?.program_name_display || '-'}</p>
            </div>
          </div>

          {/* Expiration */}
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              <CalendarDays className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-medium">Expires</p>
              <p className="mt-1 font-medium">{formatDate(activeSubscription?.end_date)}</p>
            </div>
          </div>

          {/* Days Until Expiration */}
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              <Timer className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-medium">Days Remaining</p>
              <p className="mt-1 font-medium">
                {activeSubscription?.days_until_expiration || 0} days
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MembershipStatus;