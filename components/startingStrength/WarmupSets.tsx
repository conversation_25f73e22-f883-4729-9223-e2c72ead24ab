// components/WarmupSets.tsx
import React from 'react';
import { calculateWarmupSets } from '@/utils/strengthUtils';
import PlateCalculator from './PlateCalculator';
import { Badge } from '@/components/ui/badge';

interface WarmupSetsProps {
  workWeight: number;
  barWeight?: number;
  workReps?: number;
  showPlateCalculations?: boolean;
}

const WarmupSets: React.FC<WarmupSetsProps> = ({
  workWeight,
  barWeight = 20,
  workReps = 5,
  showPlateCalculations = true
}) => {
  // Calculate warmup sets
  const warmupSets = calculateWarmupSets(workWeight, barWeight, workReps);
  
  // If there are no warmup sets, show a message
  if (warmupSets.length === 0) {
    return (
      <div className="text-sm text-gray-500">
        No warmup sets needed for this weight.
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {warmupSets.map((set, index) => (
        <div key={index} className="border-b pb-2 last:border-0">
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium">{set.weight}kg</span>
              <span className="text-sm text-gray-500 ml-2">× {set.reps} reps</span>
              
              <Badge variant="outline" className="ml-2 bg-gray-100">
                {set.description}
              </Badge>
            </div>
          </div>
          
          {showPlateCalculations && (
            <PlateCalculator 
              targetWeight={set.weight} 
              barWeight={barWeight}
              showVisualization={false}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default WarmupSets;