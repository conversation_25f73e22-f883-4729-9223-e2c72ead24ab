// components/startingStrength/WorkoutCompletionForm.tsx
import React, { useState } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import PlateCalculator from './PlateCalculator';
import { Exercise } from '@/types/strength-program';
import WarmupSets from './WarmupSets';
import { Separator } from '@/components/ui/separator';

// Define a type for completed exercise data
interface CompletedExercise {
  exercise_id: string;
  exercise_name: string;
  weight: number;
  reps: number;
  sets: number;
  is_pr: boolean;
  is_warmup: boolean;
}

// Define a type for the exercise with completion state
interface ExerciseWithState extends Exercise {
  completed: boolean;
  actualWeight: number;
}

interface WorkoutCompletionFormProps {
  workoutId: string;
  exercises: Exercise[];
  onWorkoutComplete: (workoutId: string, completedExercises: CompletedExercise[]) => Promise<void>;
  onCancel: () => void;
}

// Removed unused WorkoutSubmissionData interface

const WorkoutCompletionForm: React.FC<WorkoutCompletionFormProps> = ({
  workoutId,
  exercises,
  onWorkoutComplete,
  onCancel
}) => {
  // Reformat exercises to group by exercise_id and separate warmup sets
  const groupedExercises = exercises.reduce<Record<string, ExerciseWithState>>((acc, exercise) => {
    if (!exercise.is_warmup) {
      if (!acc[exercise.exercise_id]) {
        acc[exercise.exercise_id] = {
          ...exercise,
          completed: false,
          actualWeight: exercise.weight
        };
      }
    }
    return acc;
  }, {});

  const [formState, setFormState] = useState({
    exercises: Object.values(groupedExercises),
    isSubmitting: false,
    allCompleted: false
  });

  const handleWeightChange = (exerciseId: string, weight: number) => {
    setFormState(prev => ({
      ...prev,
      exercises: prev.exercises.map(ex => 
        ex.exercise_id === exerciseId ? { ...ex, actualWeight: weight } : ex
      )
    }));
  };

  const toggleExerciseCompleted = (exerciseId: string) => {
    setFormState(prev => {
      const newExercises = prev.exercises.map(ex => 
        ex.exercise_id === exerciseId ? { ...ex, completed: !ex.completed } : ex
      );
      
      // Check if all exercises are now completed
      const allCompleted = newExercises.every(ex => ex.completed);
      
      return {
        ...prev,
        exercises: newExercises,
        allCompleted
      };
    });
  };

  const handleSubmit = async () => {
    setFormState(prev => ({ ...prev, isSubmitting: true }));
    
    try {
      // Format completed exercises data for submission
      const completedExercises = formState.exercises.map(exercise => ({
        exercise_id: exercise.exercise_id,
        exercise_name: exercise.exercise_name,
        weight: exercise.actualWeight,
        reps: exercise.reps,
        sets: exercise.sets,
        is_pr: exercise.actualWeight > exercise.weight, // Mark as PR if lifted more than prescribed
        is_warmup: false // These are work sets
      }));
      
      await onWorkoutComplete(workoutId, completedExercises);
    } catch (error) {
      console.error('Error completing workout:', error);
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  };

  // Removed unused _unusedHandleInputChange function

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Complete Your Workout</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {formState.exercises.map((exercise, index) => (
          <div key={index} className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{exercise.exercise_name}</h3>
                <p className="text-gray-500">
                  {exercise.sets} sets × {exercise.reps} reps
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id={`complete-${exercise.exercise_id}`}
                  checked={exercise.completed}
                  onCheckedChange={() => toggleExerciseCompleted(exercise.exercise_id)}
                />
                <label 
                  htmlFor={`complete-${exercise.exercise_id}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Completed
                </label>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <label className="block text-sm font-medium mb-1">
                Warmup Sets
              </label>
              <WarmupSets 
                workWeight={exercise.weight} 
                workReps={exercise.reps}
                showPlateCalculations
              />
            </div>
            
            <Separator />
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  Work Sets Weight (kg)
                </label>
                <Badge variant="outline" className="ml-2">
                  Prescribed: {exercise.weight}kg
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <Input 
                  type="number"
                  value={exercise.actualWeight}
                  onChange={(e) => handleWeightChange(
                    exercise.exercise_id, 
                    parseFloat(e.target.value) || 0
                  )}
                  min="0"
                  step="2.5"
                  className="max-w-[100px]"
                />
                <span>kg</span>
              </div>
              
              <PlateCalculator 
                targetWeight={exercise.actualWeight} 
                showVisualization={true}
              />
            </div>
          </div>
        ))}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={!formState.allCompleted || formState.isSubmitting}
        >
          {formState.isSubmitting ? "Saving..." : "Complete Workout"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WorkoutCompletionForm;