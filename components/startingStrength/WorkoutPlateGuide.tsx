// components/WorkoutPlateGuide.tsx
import React from 'react';
import { 
  calculateEfficientPlateArrangement, 
  formatPlateLoadingGuide 
} from '@/utils/strengthUtils';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { InfoIcon } from 'lucide-react';

interface WorkoutPlateGuideProps {
  exercises: { exercise_name: string; weight: number }[];
  barWeight?: number;
}

const WorkoutPlateGuide: React.FC<WorkoutPlateGuideProps> = ({
  exercises,
  barWeight = 20
}) => {
  // Skip if no exercises or all are just using the bar
  if (exercises.length === 0 || exercises.every(e => e.weight <= barWeight)) {
    return null;
  }
  
  // Calculate efficient plate arrangement
  const { allPlatesNeeded } = calculateEfficientPlateArrangement(exercises, barWeight);
  const plateGuide = formatPlateLoadingGuide(allPlatesNeeded);
  
  return (
    <Card className="mb-4 bg-gray-50 border-blue-100">
      <CardContent className="pt-4">
        <div className="flex items-start gap-3">
          <InfoIcon className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div>
            <CardTitle className="text-sm font-medium text-blue-700 mb-1">
              Plate Setup Guide
            </CardTitle>
            <p className="text-sm text-gray-600">
              {plateGuide}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WorkoutPlateGuide;