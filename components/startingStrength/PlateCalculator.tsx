// components/PlateCalculator.tsx
import React from 'react';
import { calculatePlates, formatPlateArrangement } from '@/utils/strengthUtils';

interface PlateVisualizationProps {
  plates: number[];
  barColor?: string;
  colorMap?: Record<number, string>;
}

// Visual representation of the plates on a bar
const PlateVisualization: React.FC<PlateVisualizationProps> = ({ 
  plates, 
  barColor = "#555",
  colorMap = {
    20: "#FF4136", // Red
    15: "#0074D9", // Blue
    10: "#2ECC40", // Green
    5: "#FFDC00",  // Yellow
    2.5: "#AAAAAA", // Light Gray
    1.25: "#111111" // Dark Gray
  }
}) => {
  // Sort plates from largest to smallest for proper stacking visualization
  const sortedPlates = [...plates].sort((a, b) => b - a);

  return (
    <div className="flex items-center justify-center py-2">
      {/* Left side (mirrored) */}
      <div className="flex items-center">
        {sortedPlates.map((plate, index) => (
          <div 
            key={`left-${index}`}
            className="rounded-sm flex items-center justify-center text-xs text-white font-bold"
            style={{
              backgroundColor: colorMap[plate] || "#333",
              height: `${Math.max(20, plate * 3)}px`,
              width: `${Math.max(4, plate / 2)}px`,
              marginLeft: "1px"
            }}
          >
            {plate >= 10 ? plate : ""}
          </div>
        ))}
      </div>

      {/* Bar */}
      <div 
        className="h-4 w-20 rounded-full mx-1"
        style={{ backgroundColor: barColor }}
      />

      {/* Right side */}
      <div className="flex items-center">
        {sortedPlates.map((plate, index) => (
          <div 
            key={`right-${index}`}
            className="rounded-sm flex items-center justify-center text-xs text-white font-bold"
            style={{
              backgroundColor: colorMap[plate] || "#333",
              height: `${Math.max(20, plate * 3)}px`,
              width: `${Math.max(4, plate / 2)}px`,
              marginRight: "1px"
            }}
          >
            {plate >= 10 ? plate : ""}
          </div>
        ))}
      </div>
    </div>
  );
};

interface PlateCalculatorProps {
  targetWeight: number;
  barWeight?: number;
  showVisualization?: boolean;
}

const PlateCalculator: React.FC<PlateCalculatorProps> = ({
  targetWeight,
  barWeight = 20,
  showVisualization = true
}) => {
  // Don't show if it's just the bar or less
  if (targetWeight <= barWeight) {
    return (
      <div className="text-sm text-gray-500 mt-1">
        <span className="font-medium">Plates:</span> Just use the empty bar
      </div>
    );
  }

  const plates = calculatePlates(targetWeight, barWeight);
  const plateText = formatPlateArrangement(plates);

  return (
    <div className="mt-1">
      <div className="text-sm text-gray-500">
        <span className="font-medium">Plates:</span> {plateText}
      </div>
      
      {showVisualization && <PlateVisualization plates={plates} />}
    </div>
  );
};

export default PlateCalculator;