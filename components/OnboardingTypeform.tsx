"use client";

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import confetti from 'canvas-confetti';
import type { Database } from '@/types/supabase';
import WaiverDialog from '@/components/WaiverDialog';
import Image from 'next/image';

const OnboardingTypeform = () => {
  const supabase = createClientComponentClient<Database>();
  const router = useRouter();
  const [pelatesId, setPelatesId] = useState('');
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showWaiverDialog, setShowWaiverDialog] = useState(false);
  
  // Form state with default values
  const [formData, setFormData] = useState({
    name: '',
    last_name: '',
    email: '',
    phone: '',
    instagram: '',
    address: '',
    date_birth: '',
    sex: '',
    height: '',
    afm: '',
    find_us: '',
    email_consent: false,
    notification_consent: false,
    waiver_signed: false,
    waiver_signature: '',
    waiver_signed_date: new Date().toISOString(),
  });

  // Define steps before using it in useEffect
  const steps = [
    // Step 1: Welcome
    {
      title: "Καλώς ήρθατε! Ας ρυθμίσουμε το προφίλ σας",
      component: (
        <div className="flex flex-col items-center text-center space-y-6">
          <h1 className="text-3xl font-bold">Καλώς ήρθατε στην κοινότητά μας!</h1>
          <p className="text-xl max-w-md">
            Χαιρόμαστε που είστε μέλος μας. Ας αφιερώσουμε λίγο χρόνο για να συμπληρώσουμε το προφίλ σας ώστε να προσαρμόσουμε καλύτερα την εμπειρία σας.
          </p>
          <Button 
            size="lg" 
            onClick={() => setCurrentStep(currentStep + 1)}
            className="mt-4 px-6"
          >
            Ας Ξεκινήσουμε
          </Button>
        </div>
      )
    },
    // Step 2: Name
    {
      title: "Πώς ονομάζεστε;",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Πώς ονομάζεστε;</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Όνομα</Label>
              <Input 
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Το όνομά σας"
                className="mt-1"
                autoFocus
              />
            </div>
            <div>
              <Label htmlFor="lastName">Επώνυμο</Label>
              <Input 
                id="lastName"
                value={formData.last_name}
                onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                placeholder="Το επώνυμό σας"
                className="mt-1"
              />
            </div>
          </div>
        </div>
      ),
      validation: () => {
        if (!formData.name || !formData.last_name) {
          setError("Παρακαλώ συμπληρώστε το όνομα και το επώνυμό σας");
          return false;
        }
        setError(null);
        return true;
      }
    },
    // Step 3: Contact Information
    {
      title: "Πώς μπορούμε να επικοινωνήσουμε μαζί σας;",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Στοιχεία επικοινωνίας</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Διεύθυνση Email *</Label>
              <Input 
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                placeholder="Το email σας"
                className="mt-1"
                disabled={!!formData.email}
              />
              {formData.email && <p className="text-sm text-gray-500 mt-1">Το email έχει οριστεί από τον λογαριασμό σας</p>}
            </div>
            <div>
              <Label htmlFor="phone">Αριθμός Τηλεφώνου *</Label>
              <Input 
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                placeholder="Το τηλέφωνό σας"
                className="mt-1"
                autoFocus={!!formData.email}
              />
            </div>
          </div>
        </div>
      ),
      validation: () => {
        if (!formData.email) {
          setError("Παρακαλώ συμπληρώστε το email σας");
          return false;
        }
        if (!formData.phone) {
          setError("Παρακαλώ συμπληρώστε το τηλέφωνό σας");
          return false;
        }
        // Basic phone validation (at least 10 digits)
        if (!/^\d{10,}$/.test(formData.phone.replace(/\D/g, ''))) {
          setError("Παρακαλώ εισάγετε έναν έγκυρο αριθμό τηλεφώνου");
          return false;
        }
        setError(null);
        return true;
      }
    },
    // Step 4: Additional Information
    {
      title: "Πείτε μας περισσότερα για εσάς",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Πείτε μας περισσότερα για εσάς</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="address">Διεύθυνση *</Label>
              <Textarea 
                id="address"
                value={formData.address}
                onChange={(e) => setFormData({...formData, address: e.target.value})}
                placeholder="Η διεύθυνσή σας"
                className="mt-1"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="instagram">Instagram (προαιρετικό)</Label>
              <Input 
                id="instagram"
                value={formData.instagram}
                onChange={(e) => setFormData({...formData, instagram: e.target.value})}
                placeholder="Το username σας στο Instagram"
                className="mt-1"
              />
            </div>
          </div>
        </div>
      ),
      validation: () => {
        if (!formData.address.trim()) {
          setError("Παρακαλώ συμπληρώστε τη διεύθυνσή σας");
          return false;
        }
        setError(null);
        return true;
      }
    },
    // Step 5: Personal Details
    {
      title: "Προσωπικά Στοιχεία",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Προσωπικά Στοιχεία</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="dob">Ημερομηνία Γέννησης *</Label>
              <Input 
                id="dob"
                type="date"
                value={formData.date_birth}
                onChange={(e) => setFormData({...formData, date_birth: e.target.value})}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="sex">Φύλο *</Label>
              <select
                id="sex"
                value={formData.sex}
                onChange={(e) => setFormData({...formData, sex: e.target.value})}
                className="w-full p-2 border rounded-md mt-1"
              >
                <option value="">Επιλέξτε...</option>
                <option value="male">Άνδρας</option>
                <option value="female">Γυναίκα</option>
              </select>
            </div>
            <div>
              <Label htmlFor="height">Ύψος (εκ.) *</Label>
              <Input 
                id="height"
                type="number"
                value={formData.height}
                onChange={(e) => setFormData({...formData, height: e.target.value})}
                placeholder="Το ύψος σας σε εκατοστά"
                className="mt-1"
              />
            </div>
          </div>
        </div>
      ),
      validation: () => {
        if (!formData.date_birth) {
          setError("Παρακαλώ επιλέξτε την ημερομηνία γέννησής σας");
          return false;
        }
        if (!formData.sex) {
          setError("Παρακαλώ επιλέξτε το φύλο σας");
          return false;
        }
        if (!formData.height || isNaN(Number(formData.height)) || Number(formData.height) < 100 || Number(formData.height) > 250) {
          setError("Παρακαλώ εισάγετε ένα έγκυρο ύψος (100-250 εκ.)");
          return false;
        }
        setError(null);
        return true;
      }
    },
    // Step 6: How did you find us?
    {
      title: "Πώς μας βρήκατε;",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Πώς μας βρήκατε;</h2>
          <div>
            <Label htmlFor="findUs">Θα θέλαμε να μάθουμε πώς ανακαλύψατε το γυμναστήριό μας</Label>
            <select
              id="findUs"
              value={formData.find_us}
              onChange={(e) => setFormData({...formData, find_us: e.target.value})}
              className="w-full p-2 border rounded-md mt-1"
            >
              <option value="">Επιλέξτε...</option>
              <option value="social_media">Μέσα Κοινωνικής Δικτύωσης</option>
              <option value="friend">Σύσταση Φίλου</option>
              <option value="search_engine">Μηχανή Αναζήτησης</option>
              <option value="online_ad">Διαδικτυακή Διαφήμιση</option>
              <option value="local_ad">Τοπική Διαφήμιση</option>
              <option value="facebook">Facebook</option>
              <option value="other">Άλλο</option>
            </select>
          </div>
        </div>
      )
    },
    // Step 7: Waiver Signing
    {
      title: "Υπογραφή Συμφωνητικού",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Συμφωνητικό Συμμετοχής</h2>
          
          {formData.waiver_signed && formData.waiver_signature ? (
            <div className="space-y-4">
              <div className="p-3 border rounded-md bg-green-50 text-green-700">
                <p className="font-medium">Έχετε ήδη υπογράψει το συμφωνητικό.</p>
                <p className="text-sm">Ημερομηνία: {new Date(formData.waiver_signed_date).toLocaleDateString('el')}</p>
              </div>
              
              <div className="border rounded-md overflow-hidden h-32 bg-white relative">
                {formData.waiver_signature && (
                  <Image 
                    src={formData.waiver_signature} 
                    alt="Υπογραφή" 
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                )}
              </div>
              
              <Button onClick={() => setShowWaiverDialog(true)}>
                Επαναϋπογραφή Συμφωνητικού
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-gray-600">
                Για να συνεχίσετε, παρακαλώ διαβάστε και υπογράψτε το συμφωνητικό συμμετοχής.
              </p>
              
              <Button 
                onClick={() => setShowWaiverDialog(true)}
                className="w-full"
              >
Ανάγνωση και Υπογραφή Συμφωνητικού
              </Button>
              
              {error && error.includes("συμφωνητικό") && (
                <p className="text-red-500 text-sm">{error}</p>
              )}
            </div>
          )}
        </div>
      ),
      validation: () => {
        if (!formData.waiver_signed || !formData.waiver_signature) {
          setError("Παρακαλώ υπογράψτε το συμφωνητικό για να συνεχίσετε");
          return false;
        }
        setError(null);
        return true;
      }
    },
    // Step 8: Communication Preferences
    {
      title: "Προτιμήσεις Επικοινωνίας",
      component: (
        <div className="space-y-6 max-w-md mx-auto">
          <h2 className="text-2xl font-bold">Προτιμήσεις Επικοινωνίας</h2>
          <p className="text-gray-600">
            Θα θέλαμε να σας ενημερώνουμε για σημαντικές πληροφορίες και αποκλειστικές προσφορές. 
            Παρακαλούμε, επιλέξτε τις προτιμήσεις σας:
          </p>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="emailConsent" 
                checked={formData.email_consent}
                onCheckedChange={(checked) => 
                  setFormData({...formData, email_consent: checked === true})
                }
              />
              <Label htmlFor="emailConsent" className="cursor-pointer">
                Συναινώ να λαμβάνω ενημερώσεις μέσω email
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="notificationConsent" 
                checked={formData.notification_consent}
                onCheckedChange={(checked) => 
                  setFormData({...formData, notification_consent: checked === true})
                }
              />
              <Label htmlFor="notificationConsent" className="cursor-pointer">
                Συναινώ να λαμβάνω ειδοποιήσεις push
              </Label>
            </div>
          </div>
        </div>
      )
    },
    // Step 9: Confirmation
    {
      title: "Σχεδόν τελειώσαμε!",
      component: (
        <div className="space-y-6 max-w-md mx-auto text-center">
          <h2 className="text-2xl font-bold">Όλα είναι έτοιμα!</h2>
          <p className="text-gray-600">
            Σας ευχαριστούμε που συμπληρώσατε το προφίλ σας. Είμαστε ενθουσιασμένοι που είστε μέλος της κοινότητάς μας.
          </p>
          <Button 
            size="lg" 
            onClick={handleSubmit}
            disabled={isLoading}
            className="mt-4 px-8"
          >
            {isLoading ? 'Αποθήκευση...' : 'Ολοκλήρωση'}
          </Button>
        </div>
      )
    }
  ];

  // Load user data from database
  useEffect(() => {
    async function loadUserData() {
      setDataLoading(true);
      try {
        // Get session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id) {
          router.push('/auth');
          return;
        }
        
        // Get user profile from pelates table
        const { data: profile, error: profileError } = await supabase
          .from('pelates')
          .select('*')
          .eq('auth_user_id', session.user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          console.error('Σφάλμα κατά τη φόρτωση του προφίλ:', profileError);
          setError('Υπήρξε πρόβλημα κατά τη φόρτωση των στοιχείων σας.');
          return;
        }

        if (profile) {
          // Store the pelates ID for later use
          setPelatesId(profile.id);
          
          // Pre-populate form with existing data
          setFormData({
            name: profile.name || '',
            last_name: profile.last_name || '',
            email: profile.email || '',
            phone: profile.phone || '',
            instagram: profile.instagram || '',
            address: profile.address || '',
            date_birth: profile.date_birth ? new Date(profile.date_birth).toISOString().split('T')[0] : '',
            sex: profile.sex || '',
            height: profile.height ? String(profile.height) : '',
            afm: profile.afm || '',
            find_us: profile.find_us || '',
            email_consent: profile.email_consent || false,
            notification_consent: profile.notification_consent || false,
            waiver_signed: profile.waiver_signed || false,
            waiver_signature: profile.waiver_signature || '',
            waiver_signed_date: profile.waiver_signed_date || new Date().toISOString(),
          });
        }
      } catch (error) {
        console.error('Σφάλμα:', error);
        setError('Υπήρξε ένα πρόβλημα κατά τη φόρτωση των δεδομένων σας.');
      } finally {
        setDataLoading(false);
      }
    }
    
    loadUserData();
  }, [supabase, router]);

  // Update progress bar when step changes
  useEffect(() => {
    const totalSteps = steps.length;
    setProgress(((currentStep + 1) / totalSteps) * 100);
  }, [currentStep, steps.length]);

  // Handle waiver signature save
  const handleSaveSignature = (signatureData: string) => {
    setFormData({
      ...formData, 
      waiver_signature: signatureData, 
      waiver_signed: true,
      waiver_signed_date: new Date().toISOString()
    });
  };

  async function handleSubmit() {
    try {
      setIsLoading(true);
      setError(null);

      if (!pelatesId) {
        throw new Error('Δεν βρέθηκε το προφίλ σας');
      }

      // Format data for submission
      const formattedData = {
        ...formData,
        height: formData.height ? parseFloat(formData.height) : null,
      };

      // Update the user's profile
      const { error: updateError } = await supabase
        .from('pelates')
        .update(formattedData)
        .eq('id', pelatesId);

      if (updateError) {
        console.error('Σφάλμα κατά την ενημέρωση του προφίλ:', updateError);
        throw new Error('Δεν ήταν δυνατή η ενημέρωση του προφίλ σας');
      }

      // Show confetti animation
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      // Redirect to profile page after short delay
      setTimeout(() => {
        router.push('/user/profile');
      }, 2000);

    } catch (error) {
      console.error('Σφάλμα κατά την υποβολή:', error);
      setError(error instanceof Error ? error.message : 'Παρουσιάστηκε σφάλμα');
    } finally {
      setIsLoading(false);
    }
  }

  function handleNext() {
    // If the current step has validation, run it
    if (steps[currentStep].validation && !steps[currentStep].validation()) {
      return;
    }
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }

  function handlePrevious() {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }

  if (dataLoading) {
    return (
      <div className="h-screen flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex flex-col justify-center items-center p-4">
        <div className="w-full max-w-3xl bg-white rounded-xl shadow-xl overflow-hidden">
          {/* Progress bar */}
          <div className="w-full px-4 pt-4">
            <Progress value={progress} className="h-2" />
          </div>
          
          {/* Content */}
          <div className="p-6 md:p-12 h-[70vh] overflow-y-auto flex flex-col">
            <AnimatePresence mode="wait">
              <motion.div 
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="flex-1 flex flex-col justify-center"
              >
                {steps[currentStep].component}
              </motion.div>
            </AnimatePresence>

            {/* Error message */}
            {error && !error.includes("συμφωνητικό") && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md">
                {error}
              </div>
            )}
            
            {/* Navigation buttons */}
            <div className="flex justify-between mt-8">
              <Button 
                variant="outline" 
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                Πίσω
              </Button>
              
              {currentStep < steps.length - 1 && (
                <Button onClick={handleNext}>
                  Συνέχεια
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Waiver Dialog */}
      <WaiverDialog
        open={showWaiverDialog}
        onOpenChange={setShowWaiverDialog}
        onSave={handleSaveSignature}
        initialSignature={formData.waiver_signature}
      />
    </>
  );
};

export default OnboardingTypeform;
