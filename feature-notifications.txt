>>>> 
'use client';
import { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { format, parseISO } from 'date-fns';
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Info, AlertTriangle, AlertCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import type { Database } from '@/types/supabase';

type NotificationType = 'info' | 'warning' | 'error';
type DatabaseNotification = Database['public']['Tables']['notifications']['Row'];

interface Notification {
  id: string;
  message: string;
  created_at: string;
  type: NotificationType | null;
  read: boolean;
  metadata: Json | null;
  link: string | null;
  client_id: string | null;
  expires_at: string | null;
}

type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

// Helper function to validate notification type
function isValidNotificationType(type: string | null): type is NotificationType | null {
  return type === null || ['info', 'warning', 'error'].includes(type);
}

// Convert database notification to frontend notification
function convertDatabaseNotification(dbNotification: DatabaseNotification): Notification {
  const type = isValidNotificationType(dbNotification.type) ? dbNotification.type : 'info';
  
  return {
    ...dbNotification,
    type
  };
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const { supabase } = useSupabase();

  useEffect(() => {
    async function fetchNotifications() {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        
        // Convert database notifications to frontend notifications
        const convertedNotifications = (data || []).map(convertDatabaseNotification);
        setNotifications(convertedNotifications);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchNotifications();
  }, [supabase]);

  const markAsRead = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, read: true } : n)
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const getIcon = (type: NotificationType | null) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-4">
      <h1 className="text-2xl font-bold mb-6">Notifications</h1>
      {notifications.length === 0 ? (
        <p className="text-gray-500">No notifications</p>
      ) : (
        notifications.map((notification) => (
          <Alert
            key={notification.id}
            className={`${notification.read ? 'bg-gray-50' : 'bg-white'}`}
            variant={notification.type === 'error' ? 'destructive' : 'default'}
          >
            <div className="flex items-start">
              {getIcon(notification.type)}
              <div className="ml-4 flex-1">
                <AlertTitle>
                  {format(parseISO(notification.created_at), 'PPp')}
                </AlertTitle>
                <AlertDescription>{notification.message}</AlertDescription>
                {notification.link && (
                  <Button 
                    variant="link" 
                    className="p-0 h-auto mt-2"
                    onClick={() => window.location.href = notification.link!}
                  >
                    View Details
                  </Button>
                )}
              </div>
              {!notification.read && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => markAsRead(notification.id)}
                >
                  Mark as Read
                </Button>
              )}
            </div>
          </Alert>
        ))
      )}
    </div>
  );
}
>>>> 
import React, { useState, useEffect } from 'react';
import { Bell, CheckCheck, Info, AlertCircle, CreditCard, Trophy, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNotifications } from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { el } from 'date-fns/locale';
import { cn } from "@/lib/utils";
import type { Database } from '@/types/supabase';

type NotificationRow = Database['public']['Tables']['notifications']['Row'];

const getNotificationIcon = (type: string | null): JSX.Element => {
  switch(type) {
    case 'payment':
    case 'admin_payment':
      return <CreditCard className="h-4 w-4 text-primary" />;
    case 'info':
      return <Info className="h-4 w-4 text-blue-500" />;
    case 'achievement':
      return <Trophy className="h-4 w-4 text-yellow-500" />;
    case 'membership':
      return <AlertCircle className="h-4 w-4 text-secondary" />;
    case 'injury_report':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Bell className="h-4 w-4 text-muted-foreground" />;
  }
};

const getNotificationStyle = (type: string | null, read: boolean): string => {
  const baseStyle = cn(
    "flex items-center justify-between p-3 gap-2",
    read ? "bg-muted/30" : "bg-background",
    "hover:bg-muted/50 transition-colors"
  );

  // Fix: Use conditional class application properly
  return cn(
    baseStyle,
    type === 'injury_report' && !read ? 'border-l-2 border-red-500' : '',
    type === 'payment' && !read ? 'border-l-2 border-primary' : '',
    type === 'info' && !read ? 'border-l-2 border-blue-500' : '',
    type === 'achievement' && !read ? 'border-l-2 border-yellow-500' : ''
  );
};

interface NotificationsPopoverProps {
  userId: string;
}

export function NotificationsPopover({ userId }: NotificationsPopoverProps) {
  const [open, setOpen] = useState(false);
  const { notifications, loading, isAdmin, markAsRead, markAllAsRead } = useNotifications(userId);

  // Debug logs
  useEffect(() => {
    console.log('NotificationsPopover Debug:', {
      userId,
      isAdmin,
      notificationsCount: notifications.length,
      loading
    });
  }, [userId, isAdmin, notifications, loading]);
  
  const unreadCount = notifications.filter(n => !n.read).length;

  const handleNotificationClick = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative text-white"
          aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex items-center justify-center">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-destructive opacity-75" />
              <span className="relative inline-flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground">
                {unreadCount}
              </span>
            </span>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-96 p-0" align="end">
        <div className="flex items-center justify-between border-b px-4 py-3">
          <h3 className="font-semibold">
            Notifications {isAdmin && '(Admin View)'}
          </h3>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 gap-2"
              onClick={handleMarkAllAsRead}
            >
              <CheckCheck className="h-4 w-4" />
              <span className="text-xs">Mark all as read</span>
            </Button>
          )}
        </div>

        <ScrollArea className="h-[calc(100vh-20rem)]">
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <span className="text-sm text-muted-foreground">Loading notifications...</span>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bell className="mb-2 h-8 w-8 text-muted-foreground" />
              <p className="text-sm font-medium text-muted-foreground">
                No notifications
              </p>
              {isAdmin && (
                <p className="text-xs text-muted-foreground mt-1">
                  You will see admin notifications here
                </p>
              )}
            </div>
          ) : (
            <div className="divide-y divide-border">
              {notifications.map((notification: NotificationRow) => (
                <div
                  key={notification.id}
                  className={getNotificationStyle(notification.type, !!notification.read)}
                >
                  <div className="flex items-start gap-3 flex-1">
                    {getNotificationIcon(notification.type)}
                    <div className="space-y-1 flex-1">
                      <p className={cn(
                        "text-sm",
                        notification.read ? "text-muted-foreground" : "text-foreground"
                      )}>
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(notification.created_at), {
                          addSuffix: true,
                          locale: el
                        })}
                      </p>
                      {isAdmin && notification.admin_impersonation && (
                        <span className="text-xs text-blue-500">Admin notification</span>
                      )}
                    </div>
                  </div>
                  {!notification.read && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 shrink-0"
                      onClick={() => handleNotificationClick(notification.id)}
                    >
                      <Check className="h-4 w-4" />
                      <span className="sr-only">Mark as read</span>
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
>>>> 
import { useState, useEffect } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

type Notification = Database['public']['Tables']['notifications']['Row'];

export function useNotifications(userId: string) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [pelatiId, setPelatiId] = useState<string | null>(null);
  
  const supabase = createClientComponentClient<Database>();

  // First, check if user is admin and get pelati_id
  useEffect(() => {
    async function initializeUser() {
      try {
        // Check if user is admin
        const { data: roles } = await supabase.rpc('getUserRoles', {
          p_user_id: userId
        });
        const userIsAdmin = roles?.includes('admin') ?? false;
        setIsAdmin(userIsAdmin);
        console.log('User is admin:', userIsAdmin);

        // Get pelati_id regardless of admin status
        const { data: pelatiData, error: pelatiError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', userId)
          .single();

        if (pelatiError && !userIsAdmin) {
          throw pelatiError;
        }
        
        console.log('Found pelati_id:', pelatiData?.id);
        setPelatiId(pelatiData?.id || null);
      } catch (error) {
        console.error('Error initializing user:', error);
      }
    }

    initializeUser();
  }, [userId, supabase]);

  // Fetch notifications based on user role
  useEffect(() => {
    if (!isAdmin && !pelatiId) return; // Wait for initialization

    let channel: RealtimeChannel;
    let mounted = true;

    const fetchNotifications = async () => {
      try {
        setLoading(true);
        console.log('Fetching notifications for:', isAdmin ? 'admin' : `pelati_id: ${pelatiId}`);
    
        let query = supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false });

        if (isAdmin) {
          // Admins see notifications where either:
          // 1. They are the client_id (their personal notifications)
          // 2. admin_impersonation is true (notifications meant for all admins)
          if (pelatiId) {
            query = query.or(`client_id.eq.${pelatiId},admin_impersonation.eq.true`);
          } else {
            query = query.eq('admin_impersonation', true);
          }
        } else if (pelatiId) {
          // Regular users only see their own notifications
          query = query.eq('client_id', pelatiId);
        } else {
          // Fallback - shouldn't happen due to the guard at the top of the effect
          console.warn('No pelatiId found for regular user, showing no notifications');
          query = query.eq('client_id', '00000000-0000-0000-0000-000000000000'); // A dummy ID that won't match anything
        }

        const { data, error } = await query;
        if (error) throw error;
        
        console.log('Fetched notifications:', data);
        
        if (mounted) {
          setNotifications(data ?? []);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    const setupSubscription = () => {
      // Make sure we have a valid filter
      let filterString: string;
      
      if (isAdmin) {
        // Admin either sees their own notifications or admin notifications
        filterString = pelatiId 
          ? `client_id=eq.${pelatiId},admin_impersonation=eq.true` 
          : `admin_impersonation=eq.true`;
      } else if (pelatiId) {
        // Regular user only sees their own notifications
        filterString = `client_id=eq.${pelatiId}`;
      } else {
        // Fallback - shouldn't happen
        filterString = `client_id=eq.00000000-0000-0000-0000-000000000000`;
      }

      const channelId = `notifications-${isAdmin ? 'admin' : (pelatiId || 'unknown')}`;
      
      channel = supabase
        .channel(channelId)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'notifications',
            filter: filterString,
          },
          (payload) => {
            console.log('Realtime notification received:', payload);
            if (!mounted) return;

            if (payload.eventType === 'INSERT') {
              const newNotification = payload.new as Notification;
              setNotifications((current) => [newNotification, ...current]);
            } else if (payload.eventType === 'UPDATE') {
              setNotifications((current) =>
                current.map((n) =>
                  n.id === payload.new.id ? { ...n, ...payload.new } : n
                )
              );
            } else if (payload.eventType === 'DELETE') {
              setNotifications((current) =>
                current.filter((n) => n.id !== payload.old.id)
              );
            }
          }
        )
        .subscribe((status) => {
          console.log('Realtime subscription status:', status);
        });
    };

    void fetchNotifications();
    setupSubscription();

    return () => {
      mounted = false;
      if (channel) {
        void supabase.removeChannel(channel);
      }
    };
  }, [isAdmin, pelatiId, supabase]);

  const markAsRead = async (notificationId: string) => {
    try {
      let query = supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);
      
      // If not admin, also filter by client_id
      if (!isAdmin && pelatiId) {
        query = query.eq('client_id', pelatiId);
      }
      
      const { error } = await query;
      if (error) throw error;

      setNotifications((current) =>
        current.map((n) =>
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  };

  const markAllAsRead = async () => {
    try {
      let query = supabase
        .from('notifications')
        .update({ read: true })
        .is('read', false);

      // If not admin, filter by client_id
      if (!isAdmin && pelatiId) {
        query = query.eq('client_id', pelatiId);
      }

      const { error } = await query;
      if (error) throw error;

      setNotifications((current) =>
        current.map((n) => ({ ...n, read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  };

  return {
    notifications,
    loading,
    isAdmin,
    markAsRead,
    markAllAsRead,
  };
}
>>>> 
/**
 * @file useFetchNotifications.ts
 * @description Custom React hook for fetching and managing notifications
 * 
 * @overview
 * Provides a comprehensive solution for:
 * - Fetching initial unread notifications
 * - Setting up real-time notification streams
 * - Managing notification lifecycle
 * 
 * @dependencies
 * - React Query (useQuery)
 * - Custom Supabase hook
 * - Real-time notifications stream hook
 * 
 * @supabase_interactions
 * - Queries notifications table
 * - Filters unread, non-expired notifications
 * - Sets up real-time subscription
 * 
 * @usage_example
 * ```typescript
 * useFetchNotifications({
 *   onNotifications: (notifications) => {
 *     // Handle incoming notifications
 *   },
 *   clientId: user.id,
 *   realtime: true
 * });
 * ```
 * 
 * @performance_considerations
 * - Limits initial fetch to 10 notifications
 * - Disables unnecessary refetching
 * - Efficient real-time updates
 * 
 * @error_handling
 * - Throws errors for failed data fetching
 * - Allows custom error management
 * 
 * @query_optimization
 * - Uses React Query for caching and state management
 * - Minimal network requests
 * 
 * @security_considerations
 * - Filters notifications by client ID
 * - Checks notification expiration
 * 
 * <AUTHOR> Name]
 * @created 2024-01-15
 * @version 1.0.0
 */
import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@/hooks/useSupabase';
import { useNotificationsStream } from './useNotificationsStream';
import type { Database } from '@/types/supabase';

// Type definition for notifications
type NotificationRow = Database['public']['Tables']['notifications']['Row'];

/**
 * Props interface for useFetchNotifications hook
 * Defines the configuration for notification fetching
 */
interface UseFetchNotificationsProps {
  /** Callback function to handle incoming notifications */
  onNotifications: (notifications: NotificationRow[]) => unknown;
  /** Client/User ID to filter notifications */
  clientId: string;
  /** Flag to enable/disable real-time updates */
  realtime: boolean;
}

/**
 * Primary hook for fetching and managing notifications
 * 
 * @param {UseFetchNotificationsProps} props - Configuration for notification fetching
 * 
 * @description
 * - Fetches initial set of unread notifications
 * - Sets up real-time notification stream
 * - Triggers callback with initial and real-time notifications
 */
export function useFetchNotifications({
  onNotifications,
  clientId,
  realtime,
}: UseFetchNotificationsProps) {
  // Fetch initial notifications
  const { data: initialNotifications } = useFetchInitialNotifications({
    clientId,
  });

  // Set up real-time notification stream
  useNotificationsStream({
    onNotifications,
    clientId,
    enabled: realtime,
  });

  // Trigger callback with initial notifications
  useEffect(() => {
    if (initialNotifications) {
      onNotifications(initialNotifications);
    }
  }, [initialNotifications, onNotifications]);
}

/**
 * Props for initial notifications fetch
 */
interface UseFetchInitialNotificationsProps {
  /** Client/User ID to filter notifications */
  clientId: string;
}

/**
 * Hook to fetch initial set of notifications
 * 
 * @param {UseFetchInitialNotificationsProps} props - Configuration for initial fetch
 * @returns {Object} React Query result with initial notifications
 * 
 * @description
 * - Fetches up to 10 unread, non-expired notifications
 * - Ordered by creation date (most recent first)
 * - Uses React Query for efficient data management
 */
function useFetchInitialNotifications({ clientId }: UseFetchInitialNotificationsProps) {
  // Access Supabase client
  const { supabase } = useSupabase();
  
  // Get current timestamp
  const now = new Date().toISOString();

  // Use React Query for notification fetching
  return useQuery({
    // Unique query key for caching
    queryKey: ['notifications', clientId],
    
    // Async function to fetch notifications
    queryFn: async () => {
      // Supabase query to fetch notifications
      const { data, error } = await supabase
        .from('notifications')
        .select()
        .eq('client_id', clientId)    // Filter by client ID
        .eq('read', false)             // Only unread notifications
        .gt('expires_at', now)         // Not expired
        .order('created_at', { ascending: false }) // Most recent first
        .limit(10);                    // Limit to 10 notifications
      
      // Throw error if query fails
      if (error) throw error;
      
      // Type assertion to ensure correct return type
      return data as NotificationRow[];
    },
    
    // Optimization flags
    refetchOnMount: false,             // Prevent unnecessary refetches
    refetchOnWindowFocus: false,       // Prevent background refetches
  });
}
>>>> 
/**
 * @file /lib/notifications.ts
 * @description Comprehensive Notification Service for managing user notifications
 * 
 * @overview
 * A robust, type-safe notification management service that provides:
 * - Centralized notification creation
 * - Flexible metadata support
 * - Automatic expiration handling
 * - Multiple notification management methods
 * 
 * @dependencies
 * - Supabase JavaScript Client
 * - Custom Supabase Database Types
 * 
 * @features
 * - Singleton service pattern
 * - Type-safe notification creation
 * - Multiple notification operations
 * - Error handling and logging
 * - Automatic notification expiration
 * 
 * @supabaseInteractions
 * - Notifications table operations
 * - Complex insert and update queries
 * 
 * @performanceConsiderations
 * - Efficient database operations
 * - Minimal overhead notification management
 * - Built-in expiration mechanism
 * 
 * @securityConsiderations
 * - Validates and sanitizes notification inputs
 * - Uses environment-based client creation
 * - Provides granular notification management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

type NotificationInsert = Database['public']['Tables']['notifications']['Insert'];
type Json = Database['public']['Tables']['notifications']['Row']['metadata'];

export class NotificationService {
  private supabase;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    
    this.supabase = createClient<Database>(supabaseUrl, supabaseKey);
  }

  async createNotification({
    clientId,
    message,
    type = 'info',
    metadata = null,
  }: {
    clientId: string;
    message: string;
    type?: 'info' | 'warning' | 'error';
    metadata?: Json;
  }) {
    const notification: NotificationInsert = {
      client_id: clientId,
      message,
      type,
      metadata,
      created_at: new Date().toISOString(),
      read: false,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days expiry
    };

    const { data, error } = await this.supabase
      .from('notifications')
      .insert(notification)
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      throw error;
    }

    return data;
  }

  async markAsRead(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAllAsRead(clientId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ read: true })
      .eq('client_id', clientId)
      .eq('read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  async deleteNotification(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  async cleanupExpiredNotifications() {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .lt('expires_at', new Date().toISOString());

    if (error) {
      console.error('Error cleaning up expired notifications:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const notificationService = new NotificationService();

// Usage example:
// import { notificationService } from '@/lib/notifications';
// 
// await notificationService.createNotification({
//   clientId: 'user123',
//   message: 'Your session has been booked',
//   type: 'info',
//   metadata: {
//     sessionId: 'session123',
//     date: '2024-01-01'
//   }
// });
