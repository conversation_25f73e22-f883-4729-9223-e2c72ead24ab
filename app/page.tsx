// app/page.tsx
'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/hooks/useSupabase'  // Use your existing hook

export default function Home() {
  const { supabase } = useSupabase()
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        router.replace('/user/front-page')
      } else {
        router.replace('/guest/calendar')
      }
    }

    checkAuth()
  }, [supabase, router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      {/* Show loading state if needed */}
    </div>
  )
}