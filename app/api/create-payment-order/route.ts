import { NextResponse } from 'next/server'
import type { CreatePaymentOrderRequest } from '@/types/payment'

export const dynamic = 'force-dynamic'

export async function POST(request: Request) {
  try {
    const body: CreatePaymentOrderRequest = await request.json()

    // Validate request body
    if (!body.amount || !body.description) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 1. Load and validate environment variables
    const clientId = process.env.VIVA_CLIENT_ID
    const clientSecret = process.env.VIVA_CLIENT_SECRET
    const sourceCode = process.env.VIVA_SOURCE_CODE

    if (!clientId || !clientSecret || !sourceCode) {
      console.error('Missing Viva credentials')
      return NextResponse.json(
        { message: 'Payment service configuration error' },
        { status: 500 }
      )
    }

    // 2. Set API URLs
    const apiUrl = process.env.NODE_ENV === 'production'
      ? 'https://api.vivapayments.com'
      : 'https://demo-api.vivapayments.com'

    const authUrl = process.env.NODE_ENV === 'production'
      ? 'https://accounts.vivapayments.com'
      : 'https://demo-accounts.vivapayments.com'

    // 4. Get access token
    const tokenResponse = await fetch(`${authUrl}/connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
      },
      body: 'grant_type=client_credentials'
    })

    const tokenData = await tokenResponse.json()

    if (!tokenResponse.ok || !tokenData.access_token) {
      console.error('Token acquisition failed:', tokenData)
      return NextResponse.json(
        { message: 'Authentication failed' },
        { status: 500 }
      )
    }

    // 5. Create payment order
    const paymentOrderRequest = {
      amount: Math.round(Number(body.amount)),
      customerTrns: body.description,
      customer: {
        email: '',
        fullName: '',
        countryCode: 'GR',
        requestLang: 'el-GR'
      },
      sourceCode: sourceCode,
      merchantTrns: `Order_${Date.now()}`,
      paymentTimeout: 300,
      allowRecurring: false,
      preauth: false
    }

    const orderResponse = await fetch(`${apiUrl}/checkout/v2/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paymentOrderRequest)
    })

    const orderData = await orderResponse.json()

    if (!orderResponse.ok) {
      console.error('Payment order creation failed:', orderData)
      return NextResponse.json(
        { message: 'Failed to create payment order' },
        { status: orderResponse.status }
      )
    }

    // 6. Generate checkout URL
    const checkoutUrl = process.env.NODE_ENV === 'production'
      ? `https://www.vivapayments.com/web/checkout?ref=${orderData.orderCode}`
      : `https://demo.vivapayments.com/web/checkout?ref=${orderData.orderCode}`

    return NextResponse.json({ checkoutUrl })

  } catch (error) {
    console.error('Payment order creation error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
