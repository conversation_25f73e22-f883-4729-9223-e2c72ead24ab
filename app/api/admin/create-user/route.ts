import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { Database } from "@/types/supabase";

interface APIError {
 message: string;
}

export async function POST(request: Request) {
 try {
   const { pelatiId, email } = await request.json();

   const supabase = createRouteHandlerClient<Database>({
     cookies
   }, {
     options: {
       global: {
         headers: {
           Authorization: `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
         }
       }
     }
   });

   const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
     email,
     email_confirm: true,
     user_metadata: { pelatiId }
   });

   if (createError) throw createError;

   if (!authUser.user) {
     throw new Error('Failed to create user');
   }

   await supabase
     .from('pelates')
     .update({ auth_user_id: authUser.user.id })
     .eq('id', pelatiId);

   await supabase
     .from('user_roles')
     .insert({
       auth_user_id: authUser.user.id,
       role_id: 4
     });

   return NextResponse.json({
     success: true,
     userId: authUser.user.id
   });

 } catch (error) {
   console.error('Error creating user:', error);
   const message = (error as APIError).message || 'Failed to create user';
   return NextResponse.json(
     { error: message },
     { status: 500 }
   );
 }
}