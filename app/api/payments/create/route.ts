// app/api/payments/create/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateInvoiceNumber } from '@/lib/invoices';
import type { Database } from '@/types/supabase';

export async function POST(request: Request) {
  try {
    const {
      pelatis_id,
      amount,
      payment_method,
      payment_info,
      program_id,
      start_date,
      end_date
    } = await request.json();

    if (!pelatis_id || !amount || !payment_method) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get client details
    const { data: client, error: clientError } = await supabase
      .from('pelates')
      .select('*')
      .eq('id', pelatis_id)
      .single();

    if (clientError) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Get next invoice number
    const invoiceNumber = await generateInvoiceNumber(supabase, 'A');

    // Get program details if provided
    let programName = '';
    if (program_id) {
      const { data: program } = await supabase
        .from('programs')
        .select('name')
        .eq('id', program_id)
        .single();

      if (program) {
        programName = program.name;
      }
    }

    // Calculate VAT (assuming 24% VAT rate)
    const vatRate = 0.24;
    const netAmount = amount / (1 + vatRate);
    const vatAmount = amount - netAmount;

    // Insert invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        invoice_series: 'A',
        invoice_number: invoiceNumber,
        issue_date: new Date().toISOString().split('T')[0],
        invoice_type: '11.2', // Receipt
        client_id: pelatis_id,
        client_name: `${client.name} ${client.last_name}`,
        client_vat: client.afm || '',
        client_country: 'GR',
        total_net: netAmount,
        total_vat: vatAmount,
        total_gross: amount,
        currency: 'EUR',
        status: 'draft'
      })
      .select()
      .single();

    if (invoiceError) {
      console.error('Error creating invoice:', invoiceError);
      // Continue with payment creation even if invoice creation fails
    }

    if (invoice) {
      // Create invoice line
      const { error: lineError } = await supabase
        .from('invoice_lines')
        .insert({
          invoice_id: invoice.id,
          line_number: 1,
          description: programName
            ? `Συνδρομή ${programName}`
            : 'Συνδρομή γυμναστηρίου',
          quantity: 1,
          unit_price: netAmount,
          net_value: netAmount,
          vat_category: 1, // 24%
          vat_amount: vatAmount,
          income_classification_type: 'E3_561_007',
          income_classification_category: 'category1_3'
        });

      if (lineError) {
        console.error('Error creating invoice line:', lineError);
      }

      // Create payment method
      const { error: paymentError } = await supabase
        .from('invoice_payment_methods')
        .insert({
          invoice_id: invoice.id,
          payment_type: payment_method === 'card' ? 4 :
                         payment_method === 'bank' ? 3 :
                         payment_method === 'cash' ? 1 : 3,
          amount: amount,
          payment_info: payment_info || ''
        });

      if (paymentError) {
        console.error('Error creating payment method:', paymentError);
      }
    }

    // Insert payment record
    const { data: payment, error: paymentInsertError } = await supabase
      .from('pliromes')
      .insert({
        pelates_id: pelatis_id,
        money_gave: amount,
        date_money_gave: new Date().toISOString(),
        way_of_payment: payment_method,
        comments: payment_info,
        course: program_id,
        start_program: start_date,
        end_date: end_date,
        price_program: amount,
        nodebt: true
      })
      .select()
      .single();

    if (paymentInsertError) {
      return NextResponse.json(
        { error: 'Failed to create payment' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      payment,
      invoice: invoice || null
    });
  } catch (error) {
    console.error('Error in payment creation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}