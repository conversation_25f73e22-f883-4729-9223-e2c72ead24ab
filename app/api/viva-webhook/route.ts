import { NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import crypto from 'crypto'
import type { Database } from '@/types/supabase'
import type { VivaWebhookPayload } from '@/types/viva'

type PaymentWebhookData = {
  event_type_id: number
  transaction_id: string
  status_id: number
  order_code: string
  amount: number
  metadata: Record<string, string | number | undefined | null>
  processed: boolean
}

// GET handler for Viva's URL verification
export async function GET() {
  try {
    const merchantId = process.env.VIVA_CLIENT_ID
    const apiKey = process.env.VIVA_CLIENT_SECRET
    
    if (!merchantId || !apiKey) {
      return NextResponse.json({ error: 'Configuration error' }, { status: 500 })
    }

    const credentials = Buffer.from(`${merchantId}:${apiKey}`).toString('base64')
    const apiUrl = process.env.NODE_ENV === 'production'
      ? 'https://www.vivapayments.com'
      : 'https://demo.vivapayments.com'

    const response = await fetch(`${apiUrl}/api/messages/config/token`, {
      headers: {
        'Authorization': `Basic ${credentials}`
      }
    })

    const data = await response.json()
    return NextResponse.json({ Key: data.Key })
  } catch (error) {
    console.error('Verification error:', error)
    return NextResponse.json({ error: 'Verification failed' }, { status: 500 })
  }
}

// POST handler for webhook notifications
export async function POST(request: Request) {
  try {
    const rawBody = await request.text()
    const body = JSON.parse(rawBody) as VivaWebhookPayload
    const signature = request.headers.get('X-Viva-Signature')
    
    // Validate webhook signature
    if (process.env.NODE_ENV === 'production') {
      const webhookSecret = process.env.VIVA_WEBHOOK_SECRET
      if (!webhookSecret) {
        throw new Error('Webhook secret not configured')
      }

      const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(rawBody)
        .digest('hex')
      
      if (signature !== expectedSignature) {
        console.error('Invalid webhook signature')
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
      }
    }

    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Store webhook data and process notifications
    const webhookData: PaymentWebhookData = {
      event_type_id: body.EventTypeId,
      transaction_id: body.EventData.TransactionId,
      status_id: Number(body.EventData.StatusId),
      order_code: body.EventData.OrderCode,
      amount: body.EventData.Amount,
      metadata: {
        ...body.EventData,
        StatusId: body.EventData.StatusId,
      },
      processed: false
    }

    const { error: webhookError } = await supabase
      .from('payment_webhooks')
      .insert(webhookData)

    if (webhookError) {
      console.error('Error storing webhook:', webhookError)
      throw webhookError
    }

    // Process notifications
    await handleNotifications(body)

    return NextResponse.json({ status: 'success' })
  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function handleNotifications(body: VivaWebhookPayload) {
  const { notificationService } = await import('@/lib/notifications')
  
  switch (body.EventTypeId) {
    case 1796: // Transaction Payment Created
      await notificationService.createNotification({
        clientId: body.EventData.OrderCode,
        message: `Payment successful: €${(body.EventData.Amount / 100).toFixed(2)}`,
        type: 'info',
        metadata: {
          transactionId: body.EventData.TransactionId,
          amount: body.EventData.Amount,
          eventType: 'payment_success'
        }
      })
      break

    case 1798: // Transaction Failed
      if (body.EventData.StatusId !== 'E') {
        console.warn('Unexpected StatusId for failed transaction:', body.EventData.StatusId)
        return
      }

      await notificationService.createNotification({
        clientId: body.EventData.OrderCode,
        message: getFailureMessage(body.EventData.ResponseEventId),
        type: 'error',
        metadata: {
          transactionId: body.EventData.TransactionId,
          eventType: 'payment_failed',
          responseEventId: body.EventData.ResponseEventId,
          cardType: body.EventData.CardTypeId,
          amount: body.EventData.Amount
        }
      })
      break

    case 1797: // Transaction Reversal Created
      await notificationService.createNotification({
        clientId: body.EventData.OrderCode,
        message: `Refund processed: €${(body.EventData.Amount / 100).toFixed(2)}`,
        type: 'info',
        metadata: {
          transactionId: body.EventData.TransactionId,
          amount: body.EventData.Amount,
          eventType: 'refund_processed'
        }
      })
      break
  }
}

function getFailureMessage(responseEventId?: number): string {
  if (!responseEventId) return 'Payment failed. Please try again.'
  
  const messages: Record<number, string> = {
    310: 'Payment declined by your bank',
    400: 'Invalid card details',
    // Add more response codes as needed
  }
  return messages[responseEventId] || 'Payment failed. Please try again.'
}
