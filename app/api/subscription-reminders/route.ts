// app/api/subscription-reminders/route.ts
import { NextResponse } from 'next/server';
import { createServerClient } from '@/utils/supabase-server';
import { Resend } from 'resend';

export const dynamic = 'force-dynamic';

// Define what we get from Supabase (with possible null values)
interface SupabaseSubscription {
  subscription_id: string | null;
  client_id: string | null;
  name: string | null;
  last_name: string | null;
  program_name: string | null;
  program_name_display: string | null;
  start_date: string | null;
  end_date: string | null;
  days_until_expiration: number | null;
  price_program: number | null;
  subscription_status: string | null;
  pelates?: {
    email: string | null;
  } | null;
}

// Define our clean app model (no nulls)
interface ExpiringSubscription {
  subscription_id: string;
  client_id: string;
  name: string;
  last_name: string;
  email: string;
  program_name: string;
  program_name_display: string;
  start_date: string;
  end_date: string;
  days_until_expiration: number;
  price_program: number;
  subscription_status: string;
}

// Function to transform Supabase data to our clean app model
function transformSubscriptionData(data: SupabaseSubscription[]): ExpiringSubscription[] {
  return data
    .filter(sub => 
      // Filter out items with missing required fields
      sub.subscription_id && 
      sub.client_id && 
      sub.days_until_expiration !== null && 
      sub.end_date !== null &&
      sub.pelates?.email !== null
    )
    .map(sub => ({
      subscription_id: sub.subscription_id!,
      client_id: sub.client_id!,
      name: sub.name || 'Unknown',
      last_name: sub.last_name || 'Client',
      email: (sub.pelates?.email || '<EMAIL>').toLowerCase(),
      program_name: sub.program_name || 'Unknown Program',
      program_name_display: sub.program_name_display || sub.program_name || 'Unknown Program',
      start_date: sub.start_date || new Date().toISOString(),
      end_date: sub.end_date!,
      days_until_expiration: sub.days_until_expiration!,
      price_program: sub.price_program || 0,
      subscription_status: sub.subscription_status || 'Unknown'
    }));
}

export async function POST(request: Request) {
  try {
    // Initialize services
    const resend = new Resend(process.env.RESEND_API_KEY);
    const fromAddress = process.env.EMAIL_FROM || '<EMAIL>';
    const supabase = createServerClient();
    
    // Get the parameters from the URL
    const url = new URL(request.url);
    
    // Main days parameter (used for tracking reminder type)
    const days = parseInt(url.searchParams.get('days') || '7', 10);
    
    // Optional range parameters (used for fetching subscriptions)
    const rangeMin = url.searchParams.has('rangeMin') 
      ? parseInt(url.searchParams.get('rangeMin')!, 10)
      : days;
    const rangeMax = url.searchParams.has('rangeMax')
      ? parseInt(url.searchParams.get('rangeMax')!, 10)
      : days;
    
    console.log(`Fetching subscriptions with days_until_expiration between ${rangeMin} and ${rangeMax}`);
    
    // Fetch expiring subscriptions with email by joining with pelates table
    const { data: rawData, error } = await supabase
      .from('latest_client_subscriptions')
      .select(`
        *,
        pelates:client_id(
          email
        )
      `)
      .gte('days_until_expiration', rangeMin)
      .lte('days_until_expiration', rangeMax)
      .order('days_until_expiration', { ascending: true });
    
    if (error) {
      console.error('Error fetching expiring subscriptions:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Transform to our clean model
    const expiringSubscriptions = transformSubscriptionData(rawData || []);
    
    // Log the data for debugging
    console.log(`Found ${expiringSubscriptions.length} valid subscriptions within range`);
    
    if (expiringSubscriptions.length === 0) {
      return NextResponse.json({ 
        message: `No valid subscriptions found within the specified range`,
        summary: { total: 0, sent: 0, skipped: 0, failed: 0 }
      });
    }
    
    // Check if we already have a tracking table
    const { error: checkTableError } = await supabase
      .from('subscription_reminders')
      .select('id')
      .limit(1);
    
    // Create tracking table if it doesn't exist (indicated by error)
    if (checkTableError) {
      if (checkTableError.message.includes('does not exist')) {
        console.log('Creating subscription_reminders table');
        await supabase.rpc('create_subscription_reminders_table');
      } else {
        console.error('Error checking reminders table:', checkTableError);
      }
    }
    
    // Determine reminder type based on range
    const getReminderType = () => {
      // If exact day, use the standard format
      if (rangeMin === rangeMax) {
        return `expiry_${days}_days`;
      }
      
      // If range includes negative days (expired)
      if (rangeMin < 0) {
        if (rangeMax < 0) {
          return `expired_${Math.abs(rangeMin)}_to_${Math.abs(rangeMax)}_days_ago`;
        }
        return `expired_and_expiring_${Math.abs(rangeMin)}_to_${rangeMax}_days`;
      }
      
      // Standard range format
      return `expiry_${rangeMin}_to_${rangeMax}_days`;
    };
    
    const reminderType = getReminderType();
    console.log(`Using reminder type: ${reminderType}`);
    
    // Get appropriate email subject based on the range
    const getEmailSubject = (subscription: ExpiringSubscription) => {
      const daysLeft = subscription.days_until_expiration;
      
      if (daysLeft < 0) {
        return `Η συνδρομή σας έληξε πριν από ${Math.abs(daysLeft)} ημέρες`;
      }
      
      if (daysLeft === 0) {
        return `Η συνδρομή σας λήγει σήμερα`;
      }
      
      if (daysLeft === 1) {
        return `Η συνδρομή σας λήγει αύριο`;
      }
      
      return `Η συνδρομή σας λήγει σε ${daysLeft} ημέρες`;
    };
    
    // Process each subscription
    const results = await Promise.all(
      expiringSubscriptions.map(async (sub) => {
        try {
          // Check if we've already sent a reminder
          const { data: existingReminders, error: remindersError } = await supabase
            .from('subscription_reminders')
            .select('id')
            .eq('subscription_id', sub.subscription_id)
            .eq('reminder_type', reminderType)
            .limit(1);
            
          if (remindersError) {
            console.error('Error checking existing reminders:', remindersError);
          }
            
          if (existingReminders && existingReminders.length > 0) {
            console.log(`Reminder already sent to ${sub.email} for subscription ${sub.subscription_id}`);
            return {
              client: `${sub.name} ${sub.last_name}`,
              email: sub.email,
              status: 'skipped',
              reason: 'reminder_already_sent'
            };
          }
          
          // Format dates nicely
          const formatDate = (dateStr: string) => {
            const date = new Date(dateStr);
            return date.toLocaleDateString('el-GR', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            });
          };
          
          const formattedEndDate = formatDate(sub.end_date);
          const daysLeft = sub.days_until_expiration;
          
          // Customize message based on how many days are left
          let expiryMessage;
          if (daysLeft < 0) {
            expiryMessage = `expired on <strong>${formattedEndDate}</strong> (${Math.abs(daysLeft)} days ago)`;
          } else if (daysLeft === 0) {
            expiryMessage = `expires <strong>today</strong> (${formattedEndDate})`;
          } else {
            expiryMessage = `will expire on <strong>${formattedEndDate}</strong> (in ${daysLeft} days)`;
          }
          
          // Prepare email content
          const subject = getEmailSubject(sub);
          
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
              <h1 style="color: #333; border-bottom: 1px solid #eaeaea; padding-bottom: 10px;">
                ${daysLeft < 0 ? 'Η Συνδρομή σας έχει Λήξει' : 'Η Συνδρομή σας Λήγει Σύντομα'}
              </h1>
              
              <p>Γεια σας ${sub.name},</p>
              
              <p>Σας ενημερώνουμε ότι η συνδρομή σας <strong>${sub.program_name_display}</strong> ${expiryMessage}.</p>
              
              <p>${daysLeft < 0 
                ? 'Για να συνεχίσετε να απολαμβάνετε τις υπηρεσίες μας, παρακαλούμε ανανεώστε τη συνδρομή σας το συντομότερο δυνατό.' 
                : 'Για να διασφαλίσετε την απρόσκοπτη πρόσβασή σας στο πρόγραμμα γυμναστικής σας, σας προτείνουμε να ανανεώσετε τη συνδρομή σας πριν τη λήξη της.'}
              </p>

              <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0;">Στοιχεία Συνδρομής:</h3>
                <p><strong>Πρόγραμμα:</strong> ${sub.program_name_display}</p>
                <p><strong>Ημερομηνία Λήξης:</strong> ${formattedEndDate}</p>
                <p><strong>Τιμή Ανανέωσης:</strong> ${formatPrice(sub.price_program)}</p>
              </div>

              <p>Εάν έχετε οποιαδήποτε απορία ή θέλετε να συζητήσουμε τις επιλογές ανανέωσης, μη διστάσετε να επικοινωνήσετε μαζί μας στη reception.</p>

              <p>Σας ευχαριστούμε που επιλέγετε το LIFT GYM για τη γυμναστική σας!</p>

              <p>Με εκτίμηση,<br>Η ομάδα του LIFT GYM</p>
            </div>
          `;
          
          // Check that RESEND_API_KEY is set
          if (!process.env.RESEND_API_KEY) {
            console.error('RESEND_API_KEY environment variable not set');
            return {
              client: `${sub.name} ${sub.last_name}`,
              email: sub.email,
              status: 'failed',
              error: 'RESEND_API_KEY environment variable not set'
            };
          }
          
          // Send the email
          console.log(`Sending reminder email to ${sub.email}`);
          const { data: emailData, error: emailError } = await resend.emails.send({
            from: fromAddress,
            to: sub.email,
            subject: subject,
            html: htmlContent,
          });
          
          if (emailError) {
            console.error(`Failed to send email to ${sub.email}:`, emailError);
            return {
              client: `${sub.name} ${sub.last_name}`,
              email: sub.email,
              status: 'failed',
              error: emailError
            };
          }
          
          // Record that we've sent this reminder
          const { error: insertError } = await supabase
            .from('subscription_reminders')
            .insert({
              subscription_id: sub.subscription_id,
              client_id: sub.client_id,
              reminder_type: reminderType,
              sent_at: new Date().toISOString()
            });
            
          if (insertError) {
            console.error(`Error recording reminder for ${sub.email}:`, insertError);
          }
            
          return {
            client: `${sub.name} ${sub.last_name}`,
            email: sub.email,
            status: 'sent',
            messageId: emailData?.id
          };
          
        } catch (error) {
          console.error(`Error processing reminder for ${sub.email}:`, error);
          return {
            client: `${sub.name} ${sub.last_name}`,
            email: sub.email,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );
    
    // Generate summary
    const sent = results.filter(r => r.status === 'sent').length;
    const skipped = results.filter(r => r.status === 'skipped').length;
    const failed = results.filter(r => r.status === 'failed').length;
    
    return NextResponse.json({
      success: true,
      summary: {
        total: results.length,
        sent,
        skipped,
        failed
      },
      details: results
    });
    
  } catch (error) {
    console.error('Subscription reminder error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Update the formatPrice function
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('el-GR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price);
};
