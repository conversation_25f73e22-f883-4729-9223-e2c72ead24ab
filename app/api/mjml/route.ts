import { NextResponse } from 'next/server';
import mjml2html from 'mjml';

// Specify Node.js runtime explicitly
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET() {
  return NextResponse.json({ message: 'MJML API endpoint is working. Please use POST method.' });
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { template, variables } = body;

    if (!template) {
      return NextResponse.json(
        { error: 'Template is required' },
        { status: 400 }
      );
    }

    // Replace variables in template
    let processedTemplate = template;
    if (variables) {
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processedTemplate = processedTemplate.replace(regex, String(value));
      });
    }

    // Process MJML with minimal options
    const { html, errors } = mjml2html(processedTemplate, {
      validationLevel: 'soft',
      minify: false // Disable minification to avoid file system operations
    });
    
    if (errors?.length) {
      return NextResponse.json(
        { error: 'MJML processing error', details: errors },
        { status: 400 }
      );
    }

    return NextResponse.json({ html });
  } catch (error) {
    console.error('MJML processing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process MJML template',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
