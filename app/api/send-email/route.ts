// app/api/send-email/route.ts
import { Resend } from 'resend';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  console.log('Email sending endpoint called');
  
  // Create a new instance for each request to ensure we're using fresh credentials
  const resend = new Resend(process.env.RESEND_API_KEY);
  
  try {
    // Log environment variable status for debugging
    console.log('API Key defined:', !!process.env.RESEND_API_KEY);
    console.log('EMAIL_FROM:', process.env.EMAIL_FROM);
    
    // Parse request body
    const { to, subject, content } = await request.json();
    console.log('Received request with:', { 
      toCount: to?.length, 
      subject: subject,
      contentPreview: content?.substring(0, 30)
    });
    
    // Validate inputs
    if (!to || !Array.isArray(to) || to.length === 0) {
      return NextResponse.json({ 
        error: 'Recipients array is required' 
      }, { status: 400 });
    }
    
    if (!subject) {
      return NextResponse.json({ 
        error: 'Subject is required' 
      }, { status: 400 });
    }
    
    if (!content) {
      return NextResponse.json({ 
        error: 'Content is required' 
      }, { status: 400 });
    }
    
    // Prepare simple HTML (no markdown conversion for now)
    const html = `<div>${content.replace(/\n/g, '<br>')}</div>`;
    
    // Determine from address
    const fromAddress = process.env.EMAIL_FROM || '<EMAIL>';
    console.log('Using FROM address:', fromAddress);
    
    // Attempt to send email with full error logging
    try {
      console.log('Sending email via Resend to first recipient:', to[0]);
      
      const { data, error } = await resend.emails.send({
        from: fromAddress,
        to: to[0], // Just send to first recipient for testing
        subject: subject,
        html: html,
        text: content,
      });
      
      console.log('Resend API response:', { data, error });
      
      if (error) {
        console.error('Resend reported an error:', error);
        return NextResponse.json({ 
          error: error,
          message: 'Resend API error'
        }, { status: 500 });
      }
      
      return NextResponse.json({ 
        success: true, 
        data: data 
      });
      
    } catch (sendError) {
      console.error('Exception from Resend:', sendError);
      return NextResponse.json({ 
        error: {
          message: sendError instanceof Error ? sendError.message : 'Error sending email',
          cause: sendError
        }
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Exception in API route:', error);
    return NextResponse.json({ 
      error: {
        message: 'Internal server error in email sender',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}