// app/api/test-email/route.ts
import { Resend } from 'resend';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const searchParams = new URL(request.url).searchParams;
  const to = searchParams.get('to') || '<EMAIL>'; // Default test email
  
  try {
    console.log('Environment variables:');
    console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'Exists (length: ' + process.env.RESEND_API_KEY.length + ')' : 'Missing');
    console.log('EMAIL_FROM:', process.env.EMAIL_FROM);
    
    const resend = new Resend(process.env.RESEND_API_KEY);
    
    console.log('About to send test email to:', to);
    
    const result = await resend.emails.send({
      from: process.env.EMAIL_FROM || '<EMAIL>', // Use Resend's default onboarding email as fallback
      to: to,
      subject: 'Test Email from App',
      html: '<p>This is a test email from your application.</p>',
      text: 'This is a test email from your application.',
    });
    
    console.log('Send result:', result);
    
    return NextResponse.json({
      success: true,
      message: 'Test email sent',
      result,
    });
  } catch (error) {
    console.error('Error in test-email route:', error);
    return NextResponse.json(
      {
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}