// app/api/mydata/add-invoice-line/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

export async function POST(request: NextRequest) {
  try {
    // Parse request
    const body = await request.json();
    const { invoiceId } = body;

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get invoice data to verify it exists
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get the next line number
    const { data: existingLines } = await supabase
      .from('invoice_lines')
      .select('line_number')
      .eq('invoice_id', invoiceId)
      .order('line_number', { ascending: false })
      .limit(1);

    const nextLineNumber = existingLines && existingLines.length > 0
      ? existingLines[0].line_number + 1
      : 1;

    // Create a sample line item
    const netValue = 100.00;
    const vatRate = 0.24; // 24%
    const vatAmount = netValue * vatRate;

    // Insert the line item
    const { data: lineItem, error: lineError } = await supabase
      .from('invoice_lines')
      .insert({
        invoice_id: invoiceId,
        line_number: nextLineNumber,
        description: 'Service Fee',
        quantity: 1,
        unit_price: netValue,
        net_value: netValue,
        vat_category: 1, // 24%
        vat_amount: vatAmount,
        income_classification_type: 'E3_561_007',
        income_classification_category: 'category1_3'
      })
      .select()
      .single();

    if (lineError) {
      return NextResponse.json(
        { error: 'Failed to add invoice line', details: lineError.message },
        { status: 500 }
      );
    }

    // Update invoice totals
    const { error: updateError } = await supabase
      .from('invoices')
      .update({
        total_net: invoice.total_net + netValue,
        total_vat: invoice.total_vat + vatAmount,
        total_gross: invoice.total_gross + netValue + vatAmount,
        updated_at: new Date().toISOString()
      })
      .eq('id', invoiceId);

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update invoice totals', details: updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Invoice line added successfully',
      lineItem
    });

  } catch (error) {
    console.error('Error adding invoice line:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unexpected error' },
      { status: 500 }
    );
  }
}
