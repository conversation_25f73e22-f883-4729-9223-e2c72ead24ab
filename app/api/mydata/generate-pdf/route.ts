// app/api/mydata/generate-pdf/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateReactInvoicePdf } from '@/lib/mydata/reactPdfGenerator';
import type { Database } from '@/types/supabase';

// Import or redefine the CompanySettings type to match exactly what pdfGenerator expects
interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log('=== PDF GENERATION API START ===');
  
  try {
    // Parse request
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    const { invoiceId } = body;

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    console.log('Processing PDF generation for invoice ID:', invoiceId);

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get company settings
    console.log('Fetching company settings...');
    const { data: companySettingsData, error: settingsError } = await supabase
      .from('company_settings')
      .select('*')
      .single();

    if (settingsError || !companySettingsData) {
      console.error('Failed to load company settings:', settingsError);
      return NextResponse.json(
        { error: 'Failed to load company settings' },
        { status: 500 }
      );
    }
    console.log('Company settings loaded:', companySettingsData.companyName);

    // Get invoice data
    console.log('Fetching invoice data...');
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      console.error('Failed to load invoice:', invoiceError);
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }
    console.log('Invoice loaded:', `${invoice.invoice_series}-${invoice.invoice_number}`);

    // Get invoice lines
    console.log('Fetching invoice lines...');
    const { data: lines, error: linesError } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('line_number');

    if (linesError || !lines || lines.length === 0) {
      console.error('Failed to load invoice lines:', linesError);
      return NextResponse.json(
        { error: 'Invoice lines not found' },
        { status: 404 }
      );
    }
    console.log('Invoice lines loaded:', lines.length);

    // Get payment methods
    console.log('Fetching payment methods...');
    const { data: paymentMethods, error: paymentsError } = await supabase
      .from('invoice_payment_methods')
      .select('*')
      .eq('invoice_id', invoiceId);

    if (paymentsError) {
      console.error('Failed to load payment methods:', paymentsError);
      // Continue without payment methods if not found
    }
    console.log('Payment methods loaded:', paymentMethods?.length || 0);

    // Generate PDF
    try {
      console.log('Preparing data for PDF generation');

      // Validate invoice data
      if (!invoice) {
        throw new Error('Invoice data is missing or invalid');
      }

      // Validate company settings
      if (!companySettingsData || !companySettingsData.companyName || !companySettingsData.vatNumber) {
        throw new Error('Company settings are missing or invalid');
      }

      // Validate invoice lines
      if (!lines || lines.length === 0) {
        throw new Error('Invoice lines are missing or empty');
      }

      // Log data for debugging
      console.log('Invoice data:', {
        id: invoice.id,
        series: invoice.invoice_series,
        number: invoice.invoice_number,
        client: invoice.client_name,
        lineCount: lines.length,
        total: invoice.total_gross
      });

      // Adapt the invoice data to match the expected InvoiceData type
      const adaptedInvoice = {
        ...invoice,
        client_country: invoice.client_country || 'GR', // Default to Greece if null
        currency: invoice.currency || 'EUR', // Default to EUR if null
        created_at: invoice.created_at || new Date().toISOString(),
        updated_at: invoice.updated_at || new Date().toISOString(),
      };

      // Adapt the company settings to match the expected CompanySettings interface
      const adaptedCompanySettings: CompanySettings = {
        id: companySettingsData.id,
        companyName: companySettingsData.companyName,
        vatNumber: companySettingsData.vatNumber,
        country: companySettingsData.country || 'GR',
        branch: companySettingsData.branch || '0',
        // Optional fields - handle properly
        address: companySettingsData.address || undefined,
        city: companySettingsData.city || undefined,
        postalCode: companySettingsData.postalCode || undefined,
        // Required fields that might be null in the database - provide defaults
        defaultClassificationType: companySettingsData.defaultClassificationType || 'E3_561',
        defaultClassificationCategory: companySettingsData.defaultClassificationCategory || 'category2_1',
      };

      console.log('Calling PDF generator');
      const pdfBuffer = await generateReactInvoicePdf(
        adaptedInvoice,
        lines,
        paymentMethods || [],
        adaptedCompanySettings
      );

      // Validate the returned buffer
      if (!pdfBuffer || !Buffer.isBuffer(pdfBuffer)) {
        throw new Error('PDF generator returned invalid buffer');
      }

      if (pdfBuffer.length === 0) {
        throw new Error('PDF generator returned empty buffer');
      }

      console.log('PDF generation successful:', {
        bufferSize: pdfBuffer.length,
        bufferType: typeof pdfBuffer,
        isBuffer: Buffer.isBuffer(pdfBuffer),
        duration: Date.now() - startTime + 'ms'
      });

      // Log PDF generation in API logs
      await supabase
        .from('api_logs')
        .insert({
          invoice_id: invoiceId,
          request_type: 'GeneratePDF',
          request_body: `Invoice ID: ${invoiceId}`,
          response_body: `PDF generated successfully, size: ${pdfBuffer.length} bytes`,
          status: 'success'
        });

      const filename = `invoice_${invoice.invoice_series}-${invoice.invoice_number}.pdf`;
      
      console.log('Returning PDF response with filename:', filename);

      // Return PDF as response
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': String(pdfBuffer.length),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError);
      console.error('PDF Error stack:', pdfError instanceof Error ? pdfError.stack : 'No stack');

      // Log error in API logs
      await supabase
        .from('api_logs')
        .insert({
          invoice_id: invoiceId,
          request_type: 'GeneratePDF',
          request_body: `Invoice ID: ${invoiceId}`,
          response_body: pdfError instanceof Error ? pdfError.message : 'Unknown error',
          status: 'error',
          error_message: pdfError instanceof Error ? pdfError.message : 'Unknown error'
        });

      return NextResponse.json(
        { error: pdfError instanceof Error ? pdfError.message : 'PDF generation failed' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error in PDF generation API:', error);
    console.error('API Error stack:', error instanceof Error ? error.stack : 'No stack');
    
    const duration = Date.now() - startTime;
    console.log(`=== PDF GENERATION API END (ERROR) - Duration: ${duration}ms ===`);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unexpected error' },
      { status: 500 }
    );
  }
}