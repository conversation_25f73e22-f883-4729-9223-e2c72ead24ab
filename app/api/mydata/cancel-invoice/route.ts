// app/api/mydata/cancel-invoice/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { MyDataService } from '@/lib/mydata/service';
import { parseXmlResponse } from '@/lib/mydata/xmlParser';
import type { Database } from '@/types/supabase';

export async function POST(request: Request) {
  try {
    const { invoiceId } = await request.json();
    
    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }
    
    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Get invoice data
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();
    
    if (invoiceError || !invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' }, 
        { status: 404 }
      );
    }
    
    if (!invoice.mark) {
      return NextResponse.json(
        { error: 'Invoice has no myDATA MARK to cancel' }, 
        { status: 400 }
      );
    }
    
    // Get company settings (for VAT number if needed)
    const { data: companySettings } = await supabase
      .from('company_settings')
      .select('vatNumber')
      .single();
    
    // Initialize myDATA service
    const environment = process.env.NODE_ENV === 'production' 
      ? 'production' 
      : 'development';
    const myDataService = new MyDataService(environment);
    
    // Cancel invoice
    const responseXml = await myDataService.cancelInvoice(
      invoice.mark,
      companySettings?.vatNumber
    );
    
    // Parse response
    const parsedResponse = parseXmlResponse(responseXml);
    
    if (parsedResponse.statusCode === 'Success') {
      // Update invoice status to canceled
      await supabase
        .from('invoices')
        .update({
          status: 'canceled',
          updated_at: new Date().toISOString()
        })
        .eq('id', invoiceId);
      
      return NextResponse.json({
        success: true,
        cancellationMark: parsedResponse.cancellationMark
      });
    } else {
      return NextResponse.json({
        success: false,
        errors: parsedResponse.errors
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error canceling invoice:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' }, 
      { status: 500 }
    );
  }
}