// app/api/mydata/check-invoice/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

export async function GET(request: NextRequest) {
  try {
    // Get invoiceId from query parameters
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoiceId');

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get invoice data
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get invoice lines
    const { data: lines } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('line_number');

    // Get payment methods
    const { data: paymentMethods } = await supabase
      .from('invoice_payment_methods')
      .select('*')
      .eq('invoice_id', invoiceId);

    // Check company settings
    const { data: companySettings } = await supabase
      .from('company_settings')
      .select('*')
      .single();

    // Define the response type
    interface CheckInvoiceResponse {
      invoice: {
        id: string;
        series: string;
        number: string; // Changed from number to string to match the database type
        status: string;
        hasLines: boolean;
        lineCount: number;
        hasPaymentMethods: boolean;
        paymentMethodCount: number;
        hasCompanySettings: boolean;
      };
      issues: string[];
    }

    // Prepare response
    const response: CheckInvoiceResponse = {
      invoice: {
        id: invoice.id,
        series: invoice.invoice_series,
        number: invoice.invoice_number,
        status: invoice.status,
        hasLines: !!(lines && lines.length > 0), // Force boolean with double negation
        lineCount: lines ? lines.length : 0,
        hasPaymentMethods: !!(paymentMethods && paymentMethods.length > 0), // Force boolean with double negation
        paymentMethodCount: paymentMethods ? paymentMethods.length : 0,
        hasCompanySettings: !!companySettings,
      },
      issues: []
    };

    // Check for issues
    if (!lines || lines.length === 0) {
      response.issues.push('No invoice lines found. Add at least one line item to the invoice.');
    }

    if (!paymentMethods || paymentMethods.length === 0) {
      response.issues.push('No payment methods found. Add at least one payment method to the invoice.');
    }

    if (!companySettings) {
      response.issues.push('Company settings not found. Configure your company settings in the myDATA settings page.');
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Unexpected error in check-invoice API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unexpected error' },
      { status: 500 }
    );
  }
}
