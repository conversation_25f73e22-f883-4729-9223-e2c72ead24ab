// app/api/mydata/check-invoice-lines/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

export async function GET(request: NextRequest) {
  try {
    // Get invoiceId from query parameters
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoiceId');

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get invoice lines
    const { data: lines, error: linesError } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('line_number');

    if (linesError) {
      console.error('Failed to load invoice lines:', linesError);
      return NextResponse.json(
        { error: 'Error checking invoice lines', details: linesError.message },
        { status: 500 }
      );
    }

    if (!lines || lines.length === 0) {
      return NextResponse.json(
        { error: 'No invoice lines found for this invoice', count: 0 },
        { status: 404 }
      );
    }

    // Return success with line count
    return NextResponse.json({
      success: true,
      count: lines.length,
      message: `Found ${lines.length} invoice lines`
    });

  } catch (error) {
    console.error('Unexpected error in check-invoice-lines API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unexpected error' },
      { status: 500 }
    );
  }
}
