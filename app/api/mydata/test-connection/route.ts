// app/api/mydata/test-connection/route.ts
import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function POST() {
  try {
    // Always use the development environment for testing
    const myDataService = new MyDataService('development');

    // Try to load credentials to verify they exist
    await myDataService.loadCredentials();

    // Request a small amount of data to test the connection
    // Use '0' as mark parameter to get just the first batch of docs
    await myDataService.getTransmittedDocs('0');

    // If we got this far, the connection was successful
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Test connection failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}