'use client'

import { useSearchParams } from 'next/navigation'
import CheckoutPage from '@/components/CheckoutPage'
import ErrorDisplay from '@/components/ErrorDisplay'
import { PaymentDetails } from '@/types/payment'

export default function Checkout() {
  const searchParams = useSearchParams()
  
  // Extract and validate query parameters
  const amount = searchParams?.get('amount')
  const description = searchParams?.get('description')
  const successUrl = searchParams?.get('successUrl')
  const failureUrl = searchParams?.get('failureUrl')

  // Convert amount to number and validate
  const numericAmount = parseInt(amount ?? '', 10)
  if (isNaN(numericAmount) || numericAmount <= 0) {
    return <ErrorDisplay message="Invalid payment amount provided." />
  }

  if (!description) {
    return <ErrorDisplay message="Payment description is required." />
  }

  const paymentDetails: PaymentDetails = {
    amount: numericAmount,
    description,
    successUrl: successUrl ?? undefined,
    failureUrl: failureUrl ?? undefined
  }

  return <CheckoutPage {...paymentDetails} />
}
