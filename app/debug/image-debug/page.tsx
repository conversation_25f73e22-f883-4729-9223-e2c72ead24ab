'use client'

import { useState } from 'react';
import ImageDebugInfo from '@/components/exercises/ImageDebugInfo';
import { getImageUrl } from '@/lib/utils/imageUtils';

export default function ImageDebugPage() {
  const [imageId, setImageId] = useState<string>('');
  const [bucket, setBucket] = useState<string>('exercises');
  const [generatedUrl, setGeneratedUrl] = useState<string>('');

  const handleGenerateUrl = () => {
    if (!imageId) return;
    const url = getImageUrl(imageId, bucket);
    setGeneratedUrl(url);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Image URL Debug Page</h1>
      
      <div className="mb-8 p-4 border rounded-md">
        <h2 className="text-xl font-semibold mb-4">Test Image URL Generation</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Image ID or Path</label>
            <input
              type="text"
              value={imageId}
              onChange={(e) => setImageId(e.target.value)}
              placeholder="Enter image ID or path"
              className="w-full p-2 border rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Bucket</label>
            <select
              value={bucket}
              onChange={(e) => setBucket(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="exercises">exercises</option>
              <option value="recipes">recipes</option>
              <option value="profiles">profiles</option>
              <option value="receipts">receipts</option>
            </select>
          </div>
        </div>
        
        <button
          onClick={handleGenerateUrl}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          Generate URL
        </button>
        
        {generatedUrl && (
          <div className="mt-4">
            <h3 className="font-medium">Generated URL:</h3>
            <p className="text-sm break-all bg-gray-100 p-2 rounded-md">{generatedUrl}</p>
            
            <div className="mt-4">
              <h3 className="font-medium">Image Preview:</h3>
              <div className="mt-2 border p-2 rounded-md">
                <img
                  src={generatedUrl}
                  alt="Generated URL preview"
                  className="max-h-48 object-contain mx-auto"
                  onError={(e) => {
                    console.error('Error loading image from generated URL');
                    const imgElement = e.currentTarget;
                    imgElement.classList.add('border', 'border-red-500');
                    imgElement.title = 'Failed to load';
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
      
      <ImageDebugInfo />
    </div>
  );
}
