'use client'

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/supabase';
import ImageDebugger from '@/components/debug/ImageDebugger';

export default function ImageTestPage() {
  interface StorageFile {
    id?: string;
    name: string;
    bucket?: string;
    created_at?: string;
    updated_at?: string;
    last_accessed_at?: string;
    metadata?: Record<string, unknown>;
  }

  const [images, setImages] = useState<StorageFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testImageId, setTestImageId] = useState<string>('');

  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
async function fetchImages() {
  try {
    setLoading(true);

    // First try direct access to the known bucket name
    const { data: files, error: filesError } = await supabase
      .storage
      .from('exercises') // Try the lowercase version first
      .list();

    if (filesError) {
      console.error(`Error accessing 'exercises' bucket directly:`, filesError);

      // Fall back to listing all buckets
      const { data: buckets, error: bucketsError } = await supabase
        .storage
        .listBuckets();

      if (bucketsError) {
        throw new Error(`Error fetching buckets: ${bucketsError.message}`);
      }

      const availableBuckets = buckets?.map(b => b.name) || [];
      console.log('Available buckets:', availableBuckets);

      if (availableBuckets.length === 0) {
        throw new Error('No storage buckets found. Check your permissions.');
      }

      // Try each available bucket
      for (const bucketName of availableBuckets) {
        try {
          const { data: bucketFiles, error: bucketError } = await supabase
            .storage
            .from(bucketName)
            .list();

          if (!bucketError && bucketFiles && bucketFiles.length > 0) {
            console.log(`Found ${bucketFiles.length} files in bucket: ${bucketName}`);
            setImages(bucketFiles.map(f => ({ ...f, bucket: bucketName })));
            if (bucketFiles[0]) {
              setTestImageId(bucketFiles[0].name);
            }
            return; // Exit function if we found files
          }
        } catch (err) {
          console.error(`Error listing files from bucket ${bucketName}:`, err);
        }
      }

      // If we get here, we didn't find any files
      throw new Error(`No files found in any bucket. Available buckets: ${availableBuckets.join(', ')}`);
    } else {
      // We successfully accessed the exercises bucket
      console.log(`Found ${files?.length || 0} files in bucket: exercises`);
      setImages(files?.map(f => ({ ...f, bucket: 'exercises' })) || []);
      if (files && files.length > 0) {
        setTestImageId(files[0].name);
      }
    }
  } catch (err) {
    setError(err instanceof Error ? err.message : 'Unknown error occurred');
    console.error('Error in fetchImages:', err);
  } finally {
    setLoading(false);
  }
}

    fetchImages();
  }, [supabase]);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Image Loading Debug Page</h1>

      {loading && <p className="text-gray-500">Loading...</p>}

      {error && (
        <div className="bg-red-100 p-4 rounded-md mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Supabase Configuration</h2>
        <div className="bg-gray-100 p-4 rounded-md space-y-2">
          <p><strong>NEXT_PUBLIC_SUPABASE_URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not defined'}</p>

          <div>
            <strong>Test URL Construction:</strong>
            <div className="mt-1 p-2 bg-gray-200 rounded-md break-all">
              {process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/exercises/test-image.jpg
            </div>
          </div>

          <div>
            <strong>Direct Access Test:</strong>
            <div className="mt-1">
              <a
                href={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/exercises/test-image.jpg`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline"
              >
                Try accessing a test image directly
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Test with Custom Image ID</h2>
        <div className="flex flex-col gap-4 mb-4">
          <div className="flex gap-4">
            <input
              type="text"
              value={testImageId}
              onChange={(e) => setTestImageId(e.target.value)}
              placeholder="Enter image ID or path"
              className="border p-2 rounded-md flex-1"
            />
          </div>

          <div className="text-sm text-gray-600">
            <p>Try these formats:</p>
            <ul className="list-disc pl-5 mt-1 space-y-1">
              <li><code>image-name.jpg</code> (just the filename)</li>
              <li><code>exercises/image-name.jpg</code> (with bucket prefix)</li>
              <li><code>recipes/image-name.jpg</code> (try different buckets)</li>
              <li>Full URL: <code>https://eebyxmqdzvutxakzijbu.supabase.co/storage/v1/object/public/exercises/image-name.jpg</code></li>
            </ul>
          </div>
        </div>

        {testImageId && (
          <ImageDebugger imageId={testImageId} />
        )}
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Different Bucket Names</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {['exercises', 'Exercises', 'EXERCISES', 'recipes', 'profiles', 'receipts'].map((bucketName) => (
            <div key={bucketName} className="border p-4 rounded-md">
              <h3 className="font-medium mb-2">Bucket: {bucketName}</h3>
              {testImageId && (
                <ImageDebugger imageId={testImageId} bucket={bucketName} />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Files Found in Storage Buckets</h2>
        {images.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((file) => (
              <div key={file.id || file.name} className="border p-4 rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <p className="font-medium">{file.name}</p>
                  {file.bucket && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {file.bucket}
                    </span>
                  )}
                </div>
                <ImageDebugger
                  imageId={file.name}
                  bucket={file.bucket || 'exercises'}
                />
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No images found</p>
        )}
      </div>
    </div>
  );
}
