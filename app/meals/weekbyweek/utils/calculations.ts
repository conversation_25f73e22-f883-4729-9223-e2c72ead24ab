export const calculateIdealWeight = (height: number, sex: 'male' | 'female'): number => {
  const heightInInches = height / 2.54;
  return sex === 'male'
    ? 48 + 2.7 * (heightInInches - 60)
    : 45.5 + 2.2 * (heightInInches - 60);
};

export const calculateWeightDifference = (
  currentWeight: number,
  height: number,
  sex: 'male' | 'female'
): { 
  idealWeight: number;
  difference: number;
  status: 'underweight' | 'overweight' | 'normal';
} => {
  const idealWeight = calculateIdealWeight(height, sex);
  const difference = currentWeight - idealWeight;
  
  let status: 'underweight' | 'overweight' | 'normal';
  if (difference > 2) {
    status = 'overweight';
  } else if (difference < -2) {
    status = 'underweight';
  } else {
    status = 'normal';
  }

  return {
    idealWeight: Math.round(idealWeight * 10) / 10,
    difference: Math.round(difference * 10) / 10,
    status
  };
};