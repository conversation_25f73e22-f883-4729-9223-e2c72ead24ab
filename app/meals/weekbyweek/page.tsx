"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Calculator } from 'lucide-react';
import { RecipeCard } from './components/recipe-card';
import { FilterSection } from './components/filter-section';
import { supabase } from '@/lib/supabase';
import type { Database } from "@/types/supabase";
import { calculateWeightDifference } from './utils/calculations';

type Recipe = Database['public']['Tables']['recipes']['Row'];

// Add RecipeCardProps interface to match the actual recipe structure


interface PersonalInfo {
  age: number;
  weight: number;
  height: number;
  sex: 'male' | 'female';
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'very' | 'extra';
  goal: 'lose' | 'maintain' | 'gain';
}

interface MacroFilters {
  minProtein: number;
  maxProtein: number;
  minCarbs: number;
  maxCarbs: number;
  minFats: number;
  maxFats: number;
  minCalories: number;
  maxCalories: number;
}

// Used for sorting recipes
interface SortConfig {
  key: keyof Recipe | 'peScore';
  direction: 'asc' | 'desc';
}

interface DailyGoal {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

type DailyGoals = Record<Day, DailyGoal>;

interface AllocatedServings {
  [day: string]: {
    [recipeId: number]: number; // Explicitly type servings as number
  };
}
type MacroName = 'calories' | 'protein' | 'carbs' | 'fats';
interface MacroTargets {
  protein: number;
  carbs: number;
  fat: number;
}

// Remove these duplicate definitions
const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as const;
type Day = typeof DAYS[number];

export default function WeekByWeek() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [selectedRecipes, setSelectedRecipes] = useState<Record<number, number>>({});
  const [allocatedServings, setAllocatedServings] = useState<AllocatedServings>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    age: 30,
    weight: 70,
    height: 170,
    sex: 'male',
    activityLevel: 'moderate',
    goal: 'maintain'
  });

  const [macroFilters, setMacroFilters] = useState<MacroFilters>({
    minProtein: 0,
    maxProtein: 100,
    minCarbs: 0,
    maxCarbs: 200,
    minFats: 0,
    maxFats: 50,
    minCalories: 0,
    maxCalories: 1000
  });

  const [weightStatus, setWeightStatus] = useState<{
    idealWeight: number;
    difference: number;
    status: 'underweight' | 'overweight' | 'normal';
  }>({
    idealWeight: 0,
    difference: 0,
    status: 'normal'
  });

  useEffect(() => {
    const status = calculateWeightDifference(
      personalInfo.weight,
      personalInfo.height,
      personalInfo.sex
    );
    setWeightStatus(status);
  }, [personalInfo.weight, personalInfo.height, personalInfo.sex]);

  const getUniqueMainIngredients = (): string[] => {
    const ingredientsSet = new Set<string>();
    recipes.forEach(recipe => {
      if (recipe.main_ingredient && Array.isArray(recipe.main_ingredient)) {
        // Add each ingredient from the array to the set
        recipe.main_ingredient.forEach(ingredient => {
          if (ingredient) {
            ingredientsSet.add(ingredient);
          }
        });
      }
    });
    return Array.from(ingredientsSet);
  };


  const safeParseInt = (value: string | null | undefined): number => {
    if (!value) return 0;
    const parsed = parseInt(value);
    return isNaN(parsed) ? 0 : parsed;
  };

  // Safe parse float utility
  // const safeParseFloat = (value: number | null): number => {
  //   if (value === null) return 0;
  //   return value;
  // };

  const calculatePEScore = (calories: number | null, protein: number | null): string => {
    if (!calories || !protein || calories === 0) return '0.0';
    const score = (protein / calories) * 100;
    return score.toFixed(1);
  };



  const [selectedIngredients, setSelectedIngredients] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'peScore',
    direction: 'desc'
  });

  const [tdee, setTdee] = useState(0);
  const [targetCalories, setTargetCalories] = useState(0);
  const [macroTargets, setMacroTargets] = useState<MacroTargets>({
    protein: 0,
    carbs: 0,
    fat: 0
  });

  const initialDailyGoals: DailyGoals = DAYS.reduce((acc, day) => ({
    ...acc,
    [day]: { calories: 0, protein: 0, carbs: 0, fat: 0 }
  }), {} as DailyGoals);


  const [, setDailyGoals] = useState<DailyGoals>(initialDailyGoals);

  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as const;
  type Day = typeof days[number];




  const calculateWeeklyChange = (): number => {
    const dailyCalorieDifference = targetCalories - tdee;
    return (dailyCalorieDifference * 7) / 7700;
  };

  const calculateTDEE = useCallback((): void => {
    let bmr: number;
    if (personalInfo.sex === 'male') {
      bmr = 10 * personalInfo.weight + 6.25 * personalInfo.height - 5 * personalInfo.age + 5;
    } else {
      bmr = 10 * personalInfo.weight + 6.25 * personalInfo.height - 5 * personalInfo.age - 161;
    }

    const activityMultipliers: Record<PersonalInfo['activityLevel'], number> = {
      sedentary: 1.2,
      light: 1.375,
      moderate: 1.55,
      very: 1.725,
      extra: 1.9
    };

    const calculatedTDEE = Math.round(bmr * activityMultipliers[personalInfo.activityLevel]);
    setTdee(calculatedTDEE);

    let target: number;
    switch (personalInfo.goal) {
      case 'lose':
        target = calculatedTDEE - 500;
        break;
      case 'gain':
        target = calculatedTDEE + 500;
        break;
      default:
        target = calculatedTDEE;
    }
    setTargetCalories(target);

    setMacroTargets({
      carbs: Math.round((target * 0.4) / 4),
      protein: Math.round((target * 0.3) / 4),
      fat: Math.round((target * 0.3) / 9)
    });
  }, [personalInfo]);

  useEffect(() => {
    calculateTDEE();
  }, [calculateTDEE]);
  useEffect(() => {
    calculateTDEE();
  }, [personalInfo]);

  useEffect(() => {
    async function fetchRecipes() {
      try {
        setIsLoading(true);
        const { data, error: supabaseError } = await supabase.from('recipes').select('*');
        if (supabaseError) throw supabaseError;
        if (data) {
          setRecipes(data as Recipe[]);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch recipes');
      } finally {
        setIsLoading(false);
      }
    }
    fetchRecipes();
  }, []);

  useEffect(() => {
    const newDailyGoals: DailyGoals = {} as DailyGoals;
    DAYS.forEach(day => {
      newDailyGoals[day] = {
        calories: targetCalories,
        protein: macroTargets.protein,
        carbs: macroTargets.carbs,
        fat: macroTargets.fat,
      };
    });
    setDailyGoals(newDailyGoals);
  }, [targetCalories, macroTargets]);


   // Type guard for checking if a value is a valid Recipe key
   const isRecipeKey = (key: string): key is keyof Recipe => {
    return key in (recipes[0] || {});
  };

  // Type guard for checking if a value is a valid sort key
  const isSortKey = (key: string): key is SortConfig['key'] => {
    return key === 'peScore' || isRecipeKey(key);
  };

  const handleSetSortConfig = (newConfig: { key: string; direction: 'asc' | 'desc' }) => {
    if (isSortKey(newConfig.key)) {
      setSortConfig({ ...newConfig, key: newConfig.key });
    }
  };




  const handleRecipeSelect = (recipeId: number): void => {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;

    const totalServings = safeParseInt(recipe.servings || '1'); // Default to 1 serving if null
    setSelectedRecipes(prev => ({
      ...prev,
      [recipeId]: totalServings
    }));
  };

  const handleRecipeUnselect = (recipeId: number): void => {
    setSelectedRecipes(prev => {
      const newSelected = { ...prev };
      delete newSelected[recipeId];
      return newSelected;
    });

    setAllocatedServings(prev => {
      const newAllocations = { ...prev };
      days.forEach(day => {
        if (newAllocations[day]) {
          delete newAllocations[day][recipeId];
        }
      });
      return newAllocations;
    });
  };

  const calculateTotalAllocatedServings = (recipeId: number): number => {
    let total = 0;
    days.forEach(day => {
      total += allocatedServings[day]?.[recipeId] || 0;
    });
    return total;
  };

  const handleAllocateServings = (day: Day, recipeId: number, newServings: number): void => {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;

    const totalServings = selectedRecipes[recipeId] || 0;
    const currentDayServings = allocatedServings[day]?.[recipeId] || 0;
    const otherDaysServings = calculateTotalAllocatedServings(recipeId) - currentDayServings;

    if (newServings + otherDaysServings > totalServings) {
      alert(`Cannot allocate ${newServings} servings. Only ${totalServings - otherDaysServings} servings available.`);
      return;
    }

    setAllocatedServings(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [recipeId]: newServings
      }
    }));
  };



  const calculateDailyTotals = (day: string): {
    calories: number;
    protein: number;
    fats: number;
    carbohydrates: number;
  } => {
    const total = { calories: 0, protein: 0, fats: 0, carbohydrates: 0 };
    const allocations = allocatedServings[day] || {};

    Object.entries(allocations).forEach(([recipeId, servings]) => {
      const recipe = recipes.find(r => r.id === parseInt(recipeId));
      if (recipe) {
        // Safely handle potentially null values
        total.calories += (recipe.calories ?? 0) * servings;
        total.protein += (recipe.protein ?? 0) * servings;
        total.fats += (recipe.fats ?? 0) * servings;
        total.carbohydrates += (recipe.carbohydrates ?? 0) * servings;
      }
    });

    return total;
  };

  const getFilteredRecipes = (): Recipe[] => {
    const filtered = recipes.filter(recipe => {
      const meetsMacroCriteria =
        (recipe.protein ?? 0) >= macroFilters.minProtein &&
        (recipe.protein ?? 0) <= macroFilters.maxProtein &&
        (recipe.carbohydrates ?? 0) >= macroFilters.minCarbs &&
        (recipe.carbohydrates ?? 0) <= macroFilters.maxCarbs &&
        (recipe.fats ?? 0) >= macroFilters.minFats &&
        (recipe.fats ?? 0) <= macroFilters.maxFats &&
        (recipe.calories ?? 0) >= macroFilters.minCalories &&
        (recipe.calories ?? 0) <= macroFilters.maxCalories;

      const meetsIngredientCriteria =
        selectedIngredients.length === 0 ||
        (recipe.main_ingredient &&
         Array.isArray(recipe.main_ingredient) &&
         recipe.main_ingredient.some(ingredient => selectedIngredients.includes(ingredient)));

      return meetsMacroCriteria && meetsIngredientCriteria;
    });

    filtered.sort((a, b) => {
      if (sortConfig.key === 'peScore') {
        const aScore = ((a.protein ?? 0) / (a.calories ?? 1)) * 100;
        const bScore = ((b.protein ?? 0) / (b.calories ?? 1)) * 100;
        return sortConfig.direction === 'asc' ? aScore - bScore : bScore - aScore;
      }

      const aValue = a[sortConfig.key] ?? 0;
      const bValue = b[sortConfig.key] ?? 0;

      return sortConfig.direction === 'asc'
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  // Rest of your JSX remains the same...
  // [Previous JSX code continues unchanged]


  return (
    <div className="relative">
{/* Floating Cards */}
<div className="fixed top-20 right-4 w-64 z-50 max-h-[calc(100vh-6rem)] overflow-y-auto space-y-4 pr-2">
  {/* Weekly Progress Card */}
  <Card className="shadow-lg border bg-white">
    <CardHeader className="pb-2">
      <CardTitle className="text-sm">Weekly Progress</CardTitle>
    </CardHeader>
    <CardContent className="space-y-2 px-3 py-2">
      {(() => {
        const weeklyTargets = {
          calories: targetCalories * 7,
          protein: macroTargets.protein * 7,
          carbs: macroTargets.carbs * 7,
          fats: macroTargets.fat * 7
        };

        // Calculate totals from selected recipes
        const totals = {
          calories: 0,
          protein: 0,
          carbs: 0,
          fats: 0
        };

        Object.entries(allocatedServings).forEach(([, dayAllocations]) => {
          Object.entries(dayAllocations).forEach(([recipeId, servings]) => {
            const recipe = recipes.find(r => r.id === parseInt(recipeId));
            if (recipe) {
              // Ensure servings is a number
              const servingsNum = Number(servings) || 0;
              totals.calories += (recipe.calories ?? 0) * servingsNum;
              totals.protein += (recipe.protein ?? 0) * servingsNum;
              totals.carbs += (recipe.carbohydrates ?? 0) * servingsNum;
              totals.fats += (recipe.fats ?? 0) * servingsNum;
            }
          });
        });


        const macros: Array<{
          name: MacroName;
          emoji: string;
          unit?: string;
          format?: boolean;
        }> = [
          { name: 'calories', emoji: '🔥', unit: '', format: true },
          { name: 'protein', emoji: '🥩', unit: 'g' },
          { name: 'carbs', emoji: '🥖', unit: 'g' },
          { name: 'fats', emoji: '🥑', unit: 'g' }
        ];
        return (
          <div className="space-y-2">
  {macros.map(({ name, emoji, format }) => {
    const value = totals[name];
    const target = weeklyTargets[name];
    const percentage = Math.round((value / target) * 100);

    const formattedValue = format ? value.toLocaleString() : value;
    const formattedTarget = format ? target.toLocaleString() : target;

    return (
      <div key={name} className="space-y-1">
        <div className="flex justify-between items-center text-xs">
          <span>{emoji} {name.charAt(0).toUpperCase() + name.slice(1)}</span>
          <span>
            {formattedValue} / {formattedTarget}
            {name !== 'calories' && 'g'}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-1">
          <div
            className={`h-1 rounded-full transition-all duration-300 ${
              percentage >= 100 ? 'bg-green-500' :
              percentage >= 80 ? 'bg-blue-500' :
              percentage >= 50 ? 'bg-orange-400' : 'bg-red-400'
            }`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
      </div>
    );
  })}
          </div>
        );
      })()}
    </CardContent>
  </Card>

  {/* Daily Overview Card */}
  <Card className="shadow-lg border bg-white">
    <CardHeader className="pb-2">
      <CardTitle className="text-sm">Daily Overview</CardTitle>
    </CardHeader>
    <CardContent className="px-3 py-2">
      <div className="space-y-3">
        {DAYS.map(day => {
          const dayTotals = calculateDailyTotals(day);
          const totalServings = Object.values(allocatedServings[day] || {}).reduce((sum, servings) => sum + servings, 0);
          const goalServings = 5;
          const servingsPercentage = Math.min((totalServings / goalServings) * 100, 100);

          return (
            <div key={day} className="pb-2 border-b last:border-b-0">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs font-medium">{day.slice(0, 3)}</span>
                <span className="text-xs">
                  <span className="font-medium">{totalServings}</span>
                  <span className="text-gray-400"> / </span>
                  <span className="text-gray-600">{goalServings}</span> servings
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div
                  className={`h-1 rounded-full transition-all duration-300 ${
                    servingsPercentage >= 100 ? 'bg-green-500' :
                    servingsPercentage >= 80 ? 'bg-blue-500' :
                    servingsPercentage >= 50 ? 'bg-orange-400' : 'bg-red-400'
                  }`}
                  style={{ width: `${servingsPercentage}%` }}
                />
              </div>
              <div className="mt-1 grid grid-cols-2 gap-1 text-[10px]">
                <span>
                  🔥 <span className="font-medium">{dayTotals.calories.toLocaleString()}</span>
                  <span className="text-gray-400"> / </span>
                  <span className="text-gray-600">{targetCalories.toLocaleString()}</span>
                </span>
                <span>
                  🥩 <span className="font-medium">{dayTotals.protein}</span>
                  <span className="text-gray-400"> / </span>
                  <span className="text-gray-600">{macroTargets.protein}</span>g
                </span>
                <span>
                  🥖 <span className="font-medium">{dayTotals.carbohydrates}</span>
                  <span className="text-gray-400"> / </span>
                  <span className="text-gray-600">{macroTargets.carbs}</span>g
                </span>
                <span>
                  🥑 <span className="font-medium">{dayTotals.fats}</span>
                  <span className="text-gray-400"> / </span>
                  <span className="text-gray-600">{macroTargets.fat}</span>g
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </CardContent>
  </Card>
</div>


    <div className="pr-72">
    <div className="space-y-6">
      {/* TDEE Calculator Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-6 h-6" />
            Personal Information & TDEE Calculator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Age</label>
              <input
                type="number"
                value={personalInfo.age}
                onChange={(e) => setPersonalInfo({ ...personalInfo, age: parseInt(e.target.value) })}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Weight (kg)</label>
              <input
                type="number"
                value={personalInfo.weight}
                onChange={(e) => setPersonalInfo({ ...personalInfo, weight: parseInt(e.target.value) })}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Height (cm)</label>
              <input
                type="number"
                value={personalInfo.height}
                onChange={(e) => setPersonalInfo({ ...personalInfo, height: parseInt(e.target.value) })}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Sex</label>
              <select
                value={personalInfo.sex}
                onChange={(e) => setPersonalInfo({ ...personalInfo, sex: e.target.value as PersonalInfo['sex'] })}
                className="w-full p-2 border rounded"
              >
                <option value="male">Male</option>
                <option value="female">Female</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Activity Level</label>
              <select
                value={personalInfo.activityLevel}
                onChange={(e) => setPersonalInfo({ ...personalInfo, activityLevel: e.target.value as PersonalInfo['activityLevel'] })}
                className="w-full p-2 border rounded"
              >
                <option value="sedentary">Sedentary</option>
                <option value="light">Lightly Active</option>
                <option value="moderate">Moderately Active</option>
                <option value="very">Very Active</option>
                <option value="extra">Extra Active</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Goal</label>
              <select
                value={personalInfo.goal}
                onChange={(e) => setPersonalInfo({ ...personalInfo, goal: e.target.value as PersonalInfo['goal'] })}
                className="w-full p-2 border rounded"
              >
                <option value="lose">Lose Weight</option>
                <option value="maintain">Maintain Weight</option>
                <option value="gain">Gain Weight</option>
              </select>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded">
              <div className="text-sm text-blue-600">TDEE</div>
              <div className="text-2xl font-bold">{tdee} calories</div>
            </div>
            <div className="p-4 bg-green-50 rounded">
              <div className="text-sm text-green-600">Target Calories</div>
              <div className="text-2xl font-bold">{targetCalories} calories</div>
            </div>
            <div className="p-4 bg-purple-50 rounded">
              <div className="text-sm text-purple-600">Remaining Calories</div>
              <div className="text-2xl font-bold">{targetCalories} calories</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Weight Status Card */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Weight Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded">
              <div className="text-sm text-blue-600 dark:text-blue-400">Ideal Weight</div>
              <div className="text-2xl font-bold">{weightStatus.idealWeight.toFixed(1)} kg</div>
            </div>
            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded">
              <div className="text-sm text-purple-600 dark:text-purple-400">Difference</div>
              <div className="text-2xl font-bold">
                {weightStatus.difference > 0 ? '+' : ''}{weightStatus.difference.toFixed(1)} kg
              </div>
            </div>
            <div className={`p-4 rounded ${
              weightStatus.status === 'normal'
                ? 'bg-green-50 dark:bg-green-900/20'
                : weightStatus.status === 'overweight'
                  ? 'bg-orange-50 dark:bg-orange-900/20'
                  : 'bg-yellow-50 dark:bg-yellow-900/20'
            }`}>
              <div className={`text-sm ${
                weightStatus.status === 'normal'
                  ? 'text-green-600 dark:text-green-400'
                  : weightStatus.status === 'overweight'
                    ? 'text-orange-600 dark:text-orange-400'
                    : 'text-yellow-600 dark:text-yellow-400'
              }`}>
                Status
              </div>
              <div className="text-2xl font-bold capitalize">{weightStatus.status}</div>
            </div>
          </div>

          {weightStatus.status !== 'normal' && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900/20 rounded">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Recommendation
              </div>
              <div className="mt-1">
                {weightStatus.status === 'overweight' ? (
                  <p>
                    To reach your ideal weight, you need to lose {weightStatus.difference.toFixed(1)} kg.
                    With your current daily calorie deficit of {Math.abs(targetCalories - tdee)} calories,
                    you can expect to reach this goal in approximately{' '}
                    {Math.abs((weightStatus.difference * 7700) / ((targetCalories - tdee) * 30)).toFixed(1)} months
                    with consistent adherence to your nutrition plan.
                  </p>
                ) : (
                  <p>
                    To reach your ideal weight, you need to gain {Math.abs(weightStatus.difference).toFixed(1)} kg.
                    With your current daily calorie surplus of {Math.abs(targetCalories - tdee)} calories,
                    you can expect to reach this goal in approximately{' '}
                    {Math.abs((weightStatus.difference * 7700) / ((targetCalories - tdee) * 30)).toFixed(1)} months
                    with consistent adherence to your nutrition plan.
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Weight Change Info */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-orange-50 rounded">
              <div className="text-sm text-orange-600">Daily Calorie {targetCalories > tdee ? 'Surplus' : 'Deficit'}</div>
              <div className="text-2xl font-bold">{Math.abs(targetCalories - tdee)} calories</div>
            </div>
            <div className="p-4 bg-purple-50 rounded">
              <div className="text-sm text-purple-600">Weekly Weight {targetCalories > tdee ? 'Gain' : 'Loss'}</div>
              <div className="text-2xl font-bold">{Math.abs(calculateWeeklyChange()).toFixed(2)} kg</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Recipes Card */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Selected Recipes</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(selectedRecipes).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
{Object.entries(selectedRecipes).map(([recipeId, totalServings]) => {
  const recipe = recipes.find(r => r.id === parseInt(recipeId));
  const totalAllocated = calculateTotalAllocatedServings(parseInt(recipeId));
  const remaining = totalServings - totalAllocated;
  const peScore = recipe ? calculatePEScore(recipe.calories, recipe.protein) : '0.0';

  if (!recipe) return null;

  return (
    <div key={recipeId} className="relative group bg-white rounded-lg shadow-sm border p-3">
      {/* Remove Button */}
      <button
        onClick={() => handleRecipeUnselect(parseInt(recipeId))}
        className="absolute -right-2 -top-2 w-6 h-6 bg-red-500 text-white rounded-full
                flex items-center justify-center opacity-0 group-hover:opacity-100
                transition-opacity z-10 shadow-lg hover:bg-red-600"
      >
        ×
      </button>

      {/* Recipe Card */}
      <div className="mb-2">
        <RecipeCard
          recipe={recipe}
          onClick={() => {}}
          isSelected={true}
          compact={true}
          peScore={peScore}
          remaining={remaining}
          allocated={totalAllocated}
        />
      </div>

      {/* Compact Daily Allocation Section */}
      <div className="mt-2 border-t pt-2">
        <div className="flex justify-between items-center mb-2 text-xs">
          <span className="font-medium">Available: {remaining}</span>
        </div>

        <div className="flex flex-wrap gap-1">
          {DAYS.map((day) => {
            const dayServings = allocatedServings[day]?.[parseInt(recipeId)] || 0;
            const canAddServing = remaining > 0;

            return (
              <div key={day} className="flex items-center">
                {dayServings > 0 && (
                  <button
                    onClick={() => {
                      handleAllocateServings(
                        day,
                        parseInt(recipeId),
                        dayServings - 1
                      );
                    }}
                    className="px-1.5 py-1 text-xs font-medium bg-gray-100 hover:bg-gray-200
                            text-gray-700 rounded-l border-r border-white"
                  >
                    -
                  </button>
                )}
                <button
                  onClick={() => {
                    if (canAddServing) {
                      handleAllocateServings(
                        day,
                        parseInt(recipeId),
                        dayServings + 1
                      );
                    }
                  }}
                  className={`
                    px-2 py-1 text-xs font-medium
                    ${dayServings > 0
                      ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}
                    ${!canAddServing && dayServings === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${dayServings > 0 && 'rounded-r'}
                    ${dayServings === 0 && 'rounded'}
                    transition-colors
                  `}
                  disabled={!canAddServing && dayServings === 0}
                >
                  {day.slice(0, 3)}
                  {dayServings > 0 && ` (${dayServings})`}
                </button>
              </div>
            );
          })}
        </div>

        {remaining === 0 && (
          <div className="mt-2 text-xs text-orange-600">
            All servings allocated
          </div>
        )}
      </div>
    </div>
  );
})}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No recipes selected. Select recipes from the available recipes below.
            </div>
          )}
        </CardContent>
      </Card>





  <Card>
    <CardHeader>
      <CardTitle>Available Recipes</CardTitle>
    </CardHeader>
    <CardContent>
    <FilterSection
selectedIngredients={selectedIngredients}
setSelectedIngredients={setSelectedIngredients}
macroFilters={macroFilters}
setMacroFilters={setMacroFilters}
mainIngredients={getUniqueMainIngredients()}
sortConfig={sortConfig}
setSortConfig={handleSetSortConfig}
/>
      {isLoading ? (
        <div>Loading recipes...</div>
      ) : error ? (
        <div className="text-red-500">{error}</div>
      ) : (
<div className="grid grid-cols-4 gap-4">
{getFilteredRecipes().map(recipe => (
    <div key={recipe.id} className="relative group">
      <RecipeCard
        recipe={recipe}
        onClick={() => handleRecipeSelect(recipe.id)}
        isSelected={Object.keys(selectedRecipes).includes(recipe.id.toString())}
        compact={false}
        peScore={calculatePEScore(recipe.calories, recipe.protein)}
        onViewRecipe={() => {}} // Optional: Add additional handling for view recipe
      />
      {Object.keys(selectedRecipes).includes(recipe.id.toString()) && (
        <button
          onClick={() => handleRecipeUnselect(recipe.id)}
          className="absolute -right-2 -top-2 w-6 h-6 bg-red-500 text-white rounded-full
                   flex items-center justify-center opacity-0 group-hover:opacity-100
                   transition-opacity z-10 shadow-lg hover:bg-red-600"
        >
          ×
        </button>
      )}
    </div>
  ))}
</div>
      )}
    </CardContent>
  </Card>





      </div>
    </div>
  </div>
);
}
