// recipe-card.tsx
import type { Database } from "@/types/supabase";
import { ExternalLink } from 'lucide-react'; // Import the ExternalLink icon
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';

type Recipe = Database['public']['Tables']['recipes']['Row'];

interface RecipeCardProps {
  recipe: Recipe;
  onClick: () => void;
  isSelected: boolean;
  compact: boolean;
  peScore: string;
  remaining?: number;
  allocated?: number;
  onViewRecipe?: () => void; // Add optional handler for view recipe button
}

export function RecipeCard({
  recipe,
  onClick,
  isSelected,
  compact,
  peScore,
  remaining,
  allocated,
  onViewRecipe
}: RecipeCardProps) {
  const imageUrl = getImageUrl(recipe.image_name || '', 'recipes');

  const cardClass = compact ? 'h-32' : 'h-48';
  const titleClass = compact ? 'text-sm' : 'text-base';

  // Handle external link click without triggering the card click
  const handleLinkClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Open recipe URL in new tab if available
    if (recipe.url) {
      window.open(recipe.url, '_blank', 'noopener,noreferrer');
    }

    // If onViewRecipe provided, call it too
    if (onViewRecipe) {
      onViewRecipe();
    }
  };

  return (
    <div
      className={`relative p-4 rounded-lg border cursor-pointer ${
        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
      }`}
      onClick={onClick}
    >
      {recipe.url && (
        <button
          onClick={handleLinkClick}
          className="absolute top-2 right-2 p-1.5 bg-white rounded-full shadow-md
                    hover:bg-gray-100 transition-colors z-10"
          title="View recipe details"
        >
          <ExternalLink className="w-4 h-4 text-blue-600" />
        </button>
      )}

      {(remaining !== undefined && allocated !== undefined) && (
        <div className="mt-2 flex justify-between items-center rounded-sm mb-2">
          <div className="flex gap-4">
            <span className="text-xs border bg-white text-center p-2 text-green-500">Remaining: {remaining}</span>
            <span className="text-xs border bg-white text-center p-2 text-red-500">Allocated: {allocated}</span>
          </div>
        </div>
      )}

      <div className={`aspect-w-16 aspect-h-9 mb-4`}>
        <img
          src={imageUrl}
          alt={recipe.name || 'Recipe'}
          className={`w-full ${cardClass} object-cover rounded-md`}
          onError={(e) => {
            // Handle error by showing a placeholder
            const imgElement = e.currentTarget as HTMLImageElement;
            imgElement.src = getFallbackImageUrl('Recipe');
          }}
        />
      </div>

      <h3 className={`${titleClass} font-medium truncate`}>{recipe.name || 'Unnamed Recipe'}</h3>

      <div className="mt-2 text-sm text-gray-600">
        <p>Servings: {recipe.servings || '1'}</p>
        <p>Calories: {recipe.calories ?? 0}</p>
        <p>Protein: {recipe.protein ?? 0}g</p>
        <p>Carbs: {recipe.carbohydrates ?? 0}g</p>
        <p>Fats: {recipe.fats ?? 0}g</p>
        {!compact && (
          <>
            <p>Fiber: {recipe.fiber ?? 0}g</p>
            <p>Sugars: {recipe.sugars ?? 0}g</p>
            <p>Salt: {recipe.salt ?? 0}g</p>
            <p>Saturated Fats: {recipe.saturated_fats ?? 0}g</p>
            {recipe.cooking_time && <p>Cooking Time: {recipe.cooking_time}</p>}
            {recipe.difficulty && <p>Difficulty: {recipe.difficulty}</p>}
            <p className="font-medium">P:E Score: {peScore}</p>
          </>
        )}
      </div>
    </div>
  );
}