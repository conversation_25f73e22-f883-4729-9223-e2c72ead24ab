import React from 'react';
import { ArrowUpDown, Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { Database } from "@/types/supabase";

type Recipe = Database['public']['Tables']['recipes']['Row'];



interface MacroFilters {
  minProtein: number;
  maxProtein: number;
  minCarbs: number;
  maxCarbs: number;
  minFats: number;
  maxFats: number;
  minCalories: number;
  maxCalories: number;
}

interface FilterSectionProps {
  selectedIngredients: string[];
  setSelectedIngredients: (value: string[]) => void;
  macroFilters: MacroFilters;
  setMacroFilters: (filters: MacroFilters) => void;
  mainIngredients: string[];
  sortConfig: {
    key: keyof Recipe | 'peScore';
    direction: 'asc' | 'desc';
  };
  setSortConfig: (config: { key: keyof Recipe | 'peScore'; direction: 'asc' | 'desc' }) => void;
}

interface SortButtonProps {
  label: string;
  sortKey: keyof Recipe | 'peScore';
  showInfo?: boolean;
}

export function FilterSection({
  selectedIngredients,
  setSelectedIngredients,
  macroFilters,
  setMacroFilters,
  mainIngredients,
  sortConfig,
  setSortConfig
}: FilterSectionProps) {
  const handleIngredientToggle = (ingredient: string) => {
    if (selectedIngredients.includes(ingredient)) {
      setSelectedIngredients(selectedIngredients.filter(i => i !== ingredient));
    } else {
      setSelectedIngredients([...selectedIngredients, ingredient]);
    }
  };

  const handleSortClick = (key: keyof Recipe | 'peScore') => {
    setSortConfig({
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === 'asc'
          ? 'desc'
          : 'asc'
    });
  };

  const SortButton = ({ label, sortKey, showInfo = false }: SortButtonProps) => (
    <div className="flex items-center gap-2">
      <button
        onClick={() => handleSortClick(sortKey)}
        className={`flex items-center gap-1 text-sm font-medium ${
          sortConfig.key === sortKey ? 'text-blue-600' : 'text-gray-600'
        }`}
      >
        {label}
        <ArrowUpDown className="w-4 h-4" />
      </button>
      {showInfo && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="w-4 h-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm space-y-1">
                <p>P:E Score = (Protein/Calories) × 100</p>
                <p>Higher score = more protein dense</p>
                <p>&gt;10: Excellent protein density</p>
                <p>&gt;5: Good protein density</p>
                <p>&le;5: Lower protein density</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );

  const handleMacroFilterChange = (
    key: keyof MacroFilters,
    value: string
  ) => {
    const numValue = parseInt(value) || 0;
    setMacroFilters({
      ...macroFilters,
      [key]: numValue
    });
  };

  return (
    <div className="space-y-6">
      {/* Ingredient Selection */}
      <div>
        <label className="block text-sm font-medium mb-2">Main Ingredients</label>
        <div className="flex flex-wrap gap-2">
          {mainIngredients.map((ingredient) => (
            <button
              key={ingredient}
              onClick={() => handleIngredientToggle(ingredient)}
              className={`px-3 py-1 rounded-full text-sm transition-colors
                ${
                  selectedIngredients.includes(ingredient)
                    ? 'bg-blue-100 text-blue-700 border-blue-300'
                    : 'bg-gray-100 text-gray-700 border-gray-200'
                } border`}
            >
              {ingredient}
            </button>
          ))}
        </div>
      </div>

      {/* Sorting Options */}
      <div>
        <label className="block text-sm font-medium mb-2">Sort By</label>
        <div className="flex flex-wrap gap-4">
          <SortButton label="Calories" sortKey="calories" />
          <SortButton label="Protein" sortKey="protein" />
          <SortButton label="Carbs" sortKey="carbohydrates" />
          <SortButton label="Fats" sortKey="fats" />
          <SortButton label="P:E Score" sortKey="peScore" showInfo={true} />
        </div>
      </div>

      {/* Macro Filters */}
      <div className="space-y-4">
        <h3 className="font-medium">Macro Filters</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MacroFilterInput
            label="Protein (g)"
            minValue={macroFilters.minProtein}
            maxValue={macroFilters.maxProtein}
            onMinChange={(value) => handleMacroFilterChange('minProtein', value)}
            onMaxChange={(value) => handleMacroFilterChange('maxProtein', value)}
          />
          <MacroFilterInput
            label="Carbs (g)"
            minValue={macroFilters.minCarbs}
            maxValue={macroFilters.maxCarbs}
            onMinChange={(value) => handleMacroFilterChange('minCarbs', value)}
            onMaxChange={(value) => handleMacroFilterChange('maxCarbs', value)}
          />
          <MacroFilterInput
            label="Fats (g)"
            minValue={macroFilters.minFats}
            maxValue={macroFilters.maxFats}
            onMinChange={(value) => handleMacroFilterChange('minFats', value)}
            onMaxChange={(value) => handleMacroFilterChange('maxFats', value)}
          />
          <MacroFilterInput
            label="Calories"
            minValue={macroFilters.minCalories}
            maxValue={macroFilters.maxCalories}
            onMinChange={(value) => handleMacroFilterChange('minCalories', value)}
            onMaxChange={(value) => handleMacroFilterChange('maxCalories', value)}
          />
        </div>
      </div>
    </div>
  );
}

interface MacroFilterInputProps {
  label: string;
  minValue: number;
  maxValue: number;
  onMinChange: (value: string) => void;
  onMaxChange: (value: string) => void;
}

const MacroFilterInput: React.FC<MacroFilterInputProps> = ({
  label,
  minValue,
  maxValue,
  onMinChange,
  onMaxChange,
}) => (
  <div>
    <label className="block text-sm text-gray-600 mb-1">{label}</label>
    <div className="flex gap-2 flex-col">
      <input
        type="number"
        className="w-full p-2 border rounded"
        placeholder="Min"
        value={minValue}
        onChange={(e) => onMinChange(e.target.value)}
      />
      <input
        type="number"
        className="w-full p-2 border rounded"
        placeholder="Max"
        value={maxValue}
        onChange={(e) => onMaxChange(e.target.value)}
      />
    </div>
  </div>
);