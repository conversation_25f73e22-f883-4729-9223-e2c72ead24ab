import type { Metadata } from 'next'
import { cookies } from 'next/headers'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { Providers } from '@/components/Providers'
import { MainContent } from '@/components/layout/MainContent'
import type { Database } from '@/types/supabase'
import './globals.css'
import { OneSignalProvider } from '@/contexts/OneSignalContext'
import OneSignalScript from '@/components/OneSignalScript';

export const metadata: Metadata = {
  title: 'Lift Gym App',
  description: 'Book Sessions and more',
  icons: {
    icon: [
      {
        url: '/favicon.ico',
      },
      {
        url: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
      },
      {
        url: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
      },
    ],
    apple: {
      url: '/apple-touch-icon.png',
    },
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
      },
    ],
  },
  manifest: '/site.webmanifest',
}


// layout.tsx
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createServerComponentClient<Database>({ cookies })
  const { data: { session } } = await supabase.auth.getSession()

  // Initialize variables
  let isAdmin = false;
  let userName = null;

  if (session) {
    // Parallel fetch for both roles and user name
    const [{ data: roles }, { data: pelatesData }] = await Promise.all([
      supabase.rpc('getUserRoles', {
        p_user_id: session.user.id
      }),
      supabase
        .from('pelates')
        .select('name')
        .eq('auth_user_id', session.user.id)
        .single()
    ]);

    isAdmin = Array.isArray(roles) && roles.includes('admin');
    userName = pelatesData?.name;
  }

  return (
    <html lang="en">
      <head>
        <OneSignalScript />
      </head>
      <body>
        <Providers>
          <OneSignalProvider>
            <MainContent 
              session={session} 
              isAdmin={isAdmin}
              userName={userName}
            >
              {children}
            </MainContent>
          </OneSignalProvider>
        </Providers>
      </body>
    </html>
  )
}
