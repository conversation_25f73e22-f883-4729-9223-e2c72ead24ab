'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const router = useRouter();
  const [settings, setSettings] = useState({
    sets: 3,
    workTime: 60,
    restTime: 30,
    countdownTime: 5,
    warningTime: 3,
    soundBeforeStart: true,
    soundBeforeEnd: true,
    fullscreen: true
  });

  useEffect(() => {
    // Load saved settings from localStorage
    const savedSettings = localStorage.getItem('timerSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  const saveSettings = () => {
    localStorage.setItem('timerSettings', JSON.stringify(settings));
    router.push('/timer');
  };

  return (
    <div className="container mx-auto py-8 max-w-md">
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Timer Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Number of Sets</Label>
              <Input
                type="number"
                min="1"
                value={settings.sets}
                onChange={(e) => setSettings({...settings, sets: parseInt(e.target.value) || 1})}
              />
            </div>

            <div className="space-y-2">
              <Label>Work Time (seconds)</Label>
              <Input
                type="number"
                min="5"
                value={settings.workTime}
                onChange={(e) => setSettings({...settings, workTime: parseInt(e.target.value) || 60})}
              />
            </div>

            <div className="space-y-2">
              <Label>Rest Time (seconds)</Label>
              <Input
                type="number"
                min="5"
                value={settings.restTime}
                onChange={(e) => setSettings({...settings, restTime: parseInt(e.target.value) || 30})}
              />
            </div>

            <div className="space-y-2">
              <Label>Countdown Time (seconds)</Label>
              <Input
                type="number"
                min="0"
                value={settings.countdownTime}
                onChange={(e) => setSettings({...settings, countdownTime: parseInt(e.target.value) || 5})}
              />
            </div>

            <div className="space-y-2">
              <Label>Warning Time (seconds before end)</Label>
              <Input
                type="number"
                min="0"
                value={settings.warningTime}
                onChange={(e) => setSettings({...settings, warningTime: parseInt(e.target.value) || 3})}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>Sound Before Start</Label>
              <Switch
                checked={settings.soundBeforeStart}
                onCheckedChange={(checked) => setSettings({...settings, soundBeforeStart: checked})}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>Sound Before End</Label>
              <Switch
                checked={settings.soundBeforeEnd}
                onCheckedChange={(checked) => setSettings({...settings, soundBeforeEnd: checked})}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label>Auto Fullscreen</Label>
              <Switch
                checked={settings.fullscreen}
                onCheckedChange={(checked) => setSettings({...settings, fullscreen: checked})}
              />
            </div>
          </div>

          <Button 
            onClick={saveSettings}
            className="w-full"
          >
            Save Settings
          </Button>
        </CardContent>
      </Card>
    </div>
  );
} 