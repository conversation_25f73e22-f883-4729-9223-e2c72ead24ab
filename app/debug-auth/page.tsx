// app/debug-auth/page.tsx (fixed iOS detection)
'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

export default function DebugAuthPage() {
  const [sessionInfo, setSessionInfo] = useState<string>('Loading...')
  const [browserInfo, setBrowserInfo] = useState<string>('Loading...')
  const [storageKeys, setStorageKeys] = useState<string>('Loading...')
  const supabase = createClientComponentClient()

  useEffect(() => {
    async function checkAuth() {
      // Get session info
      const { data, error } = await supabase.auth.getSession()
      setSessionInfo(JSON.stringify({ data, error }, null, 2))
      
      // Get browser info
      const ua = window.navigator.userAgent
      const isAndroid = /Android/i.test(ua)
      // Fixed iOS detection without MSStream
      const isIOS = /iPad|iPhone|iPod/.test(ua) && !/CriOS/.test(ua) && !/FxiOS/.test(ua)
      const isMobile = isAndroid || isIOS || /Mobi|Mobile/i.test(ua)
      
      setBrowserInfo(JSON.stringify({
        userAgent: ua,
        isAndroid,
        isIOS,
        isMobile,
        cookiesEnabled: navigator.cookieEnabled,
        localStorage: typeof localStorage !== 'undefined',
        timestamp: new Date().toISOString()
      }, null, 2))
      
      // Get storage keys
      try {
        const lsKeys = Object.keys(localStorage).filter(k => 
          k.includes('supabase') || k.includes('auth') || k.includes('flow')
        )
        
        const lsEntries = lsKeys.map(k => ({
          key: k,
          value: k.includes('token') ? '[REDACTED]' : localStorage.getItem(k)?.substring(0, 50) + '...'
        }))
        
        setStorageKeys(JSON.stringify(lsEntries, null, 2))
      } catch (e) {
        setStorageKeys(`Error reading localStorage: ${e}`)
      }
    }
    
    checkAuth()
  }, [])
  
  const refreshSession = async () => {
    setSessionInfo('Refreshing...')
    const { data, error } = await supabase.auth.getSession()
    setSessionInfo(JSON.stringify({ data, error }, null, 2))
  }
  
  const clearFlowState = () => {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.includes('flow-state') || key.includes('supabase.auth.token')) {
          localStorage.removeItem(key)
        }
      })
      setStorageKeys('Cleared flow state data. Refresh to see results.')
    } catch (e) {
      setStorageKeys(`Error clearing localStorage: ${e}`)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="flex gap-4 mb-6">
        <button 
          onClick={refreshSession}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Refresh Session
        </button>
        
        <button 
          onClick={clearFlowState}
          className="px-4 py-2 bg-red-500 text-white rounded"
        >
          Clear Flow State
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="border rounded p-4">
          <h2 className="text-xl font-semibold mb-2">Session Info</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-[400px]">
            {sessionInfo}
          </pre>
        </div>
        
        <div className="border rounded p-4">
          <h2 className="text-xl font-semibold mb-2">Browser Info</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-[400px]">
            {browserInfo}
          </pre>
        </div>
        
        <div className="border rounded p-4 md:col-span-2">
          <h2 className="text-xl font-semibold mb-2">Storage Keys</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-[400px]">
            {storageKeys}
          </pre>
        </div>
      </div>
    </div>
  )
}