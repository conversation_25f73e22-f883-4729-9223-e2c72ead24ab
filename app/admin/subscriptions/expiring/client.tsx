'use client';
import React from 'react';
import ExpiringSubscriptionsCard from '@/components/ExpiringSubscriptionsCard';

export default function SubscriptionsClientWrapper() {
  return (
    <div className="space-y-6">
      {/* Recently expired (last 5 days) */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: -5, max: -1 }}
        title="Recently Expired Subscriptions (Last 5 Days)" 
        onlyActive={false}
        badgeColor="destructive"
      />
      
      {/* Expiring today */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: 0, max: 0 }}
        title="Subscriptions Expiring Today" 
        showSendReminders={true}
        badgeColor="destructive"
      />
      
      {/* Expiring very soon (1-6 days) */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: 1, max: 6 }}
        title="Subscriptions Expiring Soon (1-6 Days)" 
        showSendReminders={true}
        badgeColor="secondary"
      />
      
      {/* Expiring in exactly 7 days */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: 7, max: 7 }}
        title="Subscriptions Expiring in 7 Days" 
        showSendReminders={true}
      />
      
      {/* Expiring in 8-14 days */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: 8, max: 14 }}
        title="Subscriptions Expiring in 8-14 Days" 
        showSendReminders={true}
      />
      
      {/* Expiring in 15-30 days */}
      <ExpiringSubscriptionsCard 
        daysRange={{ min: 15, max: 30 }}
        title="Subscriptions Expiring in 15-30 Days" 
        showSendReminders={true}
      />
    </div>
  );
}
