'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowUpDown } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import type { Database } from '@/types/supabase'

type DbSubscription = Database['public']['Views']['active_subscriptions']['Row']

// Define our application type with required fields matching the database view
type Subscription = {
  subscription_id: string // Required in our app
  client_id: string // Required in our app
  name: string | null
  last_name: string | null
  program_name: string // Required in our app
  program_name_display: string | null
  start_date: string // Required in our app
  end_date: string // Required in our app
  price_program: number | null
  subscription_status: string // Required in our app
  days_until_expiration: number // Required in our app
  program_duration: number | null
  grace_period_days: number | null
}

// Define the structure for program-specific summaries
type ProgramSummary = {
  count: number
  totalAmount: number
  activeCount: number
  graceCount: number
  expiredCount: number
}

// Define the main SubscriptionSummary type
type SubscriptionSummary = {
  totalAmount: number
  activeCount: number
  graceCount: number
  expiredCount: number
  programSummaries: {
    [programName: string]: ProgramSummary
  }
}

type SortKey = keyof Subscription
type SortOrder = 'asc' | 'desc'

export default function ActiveSubscriptions() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([])
  const [selectedProgram, setSelectedProgram] = useState<string>('all')
  const [message, setMessage] = useState('')
  const [sortKey, setSortKey] = useState<SortKey>('end_date')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  const supabase = createClientComponentClient<Database>()

  const transformDbSubscription = useCallback((dbSub: DbSubscription): Subscription | null => {
    // Required fields check - if any required field is missing, skip this subscription
    if (!dbSub.subscription_id || !dbSub.program_name || !dbSub.start_date || 
        !dbSub.end_date || !dbSub.subscription_status || typeof dbSub.days_until_expiration !== 'number') {
      return null
    }

    return {
      subscription_id: dbSub.subscription_id,
      client_id: dbSub.client_id || crypto.randomUUID(), // Fallback for null client_id
      name: dbSub.name,
      last_name: dbSub.last_name,
      program_name: dbSub.program_name,
      program_name_display: dbSub.program_name_display,
      start_date: dbSub.start_date,
      end_date: dbSub.end_date,
      price_program: dbSub.price_program,
      subscription_status: dbSub.subscription_status,
      days_until_expiration: dbSub.days_until_expiration,
      program_duration: dbSub.program_duration,
      grace_period_days: dbSub.grace_period_days
    }
  }, [])

  // Filter subscriptions based on selected program
  useEffect(() => {
    const filtered = selectedProgram === 'all'
      ? subscriptions
      : subscriptions.filter(sub => sub.program_name === selectedProgram)
    setFilteredSubscriptions(filtered)
  }, [selectedProgram, subscriptions])

  const fetchActiveSubscriptions = useCallback(async () => {
    try {
      const { data, error } = await supabase
      .from('latest_client_subscriptions')
      .select('*')
      .order(sortKey, { ascending: sortOrder === 'asc' });
  
      if (error) throw error;
  
      // Transform and filter out invalid subscriptions
      const validSubscriptions = (data || [])
        .map(transformDbSubscription)
        .filter((sub): sub is Subscription => sub !== null);
  
      setSubscriptions(validSubscriptions);
      setFilteredSubscriptions(validSubscriptions);
    } catch (error) {
      console.error('Error:', error);
      setMessage('Error fetching subscriptions');
    }
  }, [supabase, sortKey, sortOrder, transformDbSubscription]);

  // Call fetchActiveSubscriptions when the component mounts
  useEffect(() => {
    fetchActiveSubscriptions();
  }, [fetchActiveSubscriptions]);

  const calculateSummary = useCallback((subs: Subscription[]): SubscriptionSummary => {
    const summary: SubscriptionSummary = {
      totalAmount: 0,
      activeCount: 0,
      graceCount: 0,
      expiredCount: 0,
      programSummaries: {}
    }

    subs.forEach(sub => {
      // Update total amounts
      summary.totalAmount += sub.price_program || 0
      
      // Update status counts
      if (sub.subscription_status === 'Active') summary.activeCount++
      else if (sub.subscription_status === 'In Grace Period') summary.graceCount++
      else if (sub.subscription_status === 'Expired') summary.expiredCount++

      // Update program-specific summaries
      if (sub.program_name) {
        if (!summary.programSummaries[sub.program_name]) {
          summary.programSummaries[sub.program_name] = {
            count: 0,
            totalAmount: 0,
            activeCount: 0,
            graceCount: 0,
            expiredCount: 0
          }
        }

        const programSummary = summary.programSummaries[sub.program_name]
        programSummary.count++
        programSummary.totalAmount += sub.price_program || 0
        
        if (sub.subscription_status === 'Active') programSummary.activeCount++
        else if (sub.subscription_status === 'In Grace Period') programSummary.graceCount++
        else if (sub.subscription_status === 'Expired') programSummary.expiredCount++
      }
    })

    return summary
  }, [])

  const handleSort = (key: SortKey) => {
    setSortOrder(current => current === 'asc' ? 'desc' : 'asc')
    setSortKey(key)
  }

  const SortableHeader: React.FC<{ column: SortKey; label: string }> = ({ column, label }) => (
    <TableHead>
      <Button variant="ghost" onClick={() => handleSort(column)} className="flex items-center">
        {label}
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </TableHead>
  )

  const summary = calculateSummary(filteredSubscriptions)
  const allProgramSummary = calculateSummary(subscriptions)

  return (
    <div className="mb-8 space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Active Subscriptions</h2>
        <Select value={selectedProgram} onValueChange={setSelectedProgram}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select Program" />
          </SelectTrigger>
          <SelectContent>
  <SelectItem value="all">All Programs</SelectItem>
  {Object.entries(allProgramSummary.programSummaries)
    .sort((a, b) => b[1].count - a[1].count)
    .map(([programId, stats]) => {
      // Find the display name for this program ID
      const program = subscriptions.find(sub => sub.program_name === programId)
      return (
        <SelectItem key={programId} value={programId}>
          {program?.program_name_display || 'Unknown'} ({stats.count})
        </SelectItem>
      )
    })}
</SelectContent>
        </Select>
      </div>

      {/* Overall Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">€{summary.totalAmount.toFixed(2)}</div>
            <p className="text-sm text-muted-foreground">Total Value</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">{summary.activeCount}</div>
            <p className="text-sm text-muted-foreground">Active</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-yellow-600">{summary.graceCount}</div>
            <p className="text-sm text-muted-foreground">Grace Period</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">{summary.expiredCount}</div>
            <p className="text-sm text-muted-foreground">Expired</p>
          </CardContent>
        </Card>
      </div>

      {/* Program-specific Summary */}
      {selectedProgram !== 'all' && allProgramSummary.programSummaries[selectedProgram] && (
  <div className="bg-muted/50 p-4 rounded-lg">
    <h3 className="font-medium mb-2">
      {subscriptions.find(sub => sub.program_name === selectedProgram)?.program_name_display} Summary
    </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Total Members:</span>
              <p className="font-medium">{allProgramSummary.programSummaries[selectedProgram].count}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Active:</span>
              <p className="font-medium text-green-600">
                {allProgramSummary.programSummaries[selectedProgram].activeCount}
              </p>
            </div>
            <div>
              <span className="text-muted-foreground">Total Value:</span>
              <p className="font-medium">
                €{allProgramSummary.programSummaries[selectedProgram].totalAmount.toFixed(2)}
              </p>
            </div>
            <div>
              <span className="text-muted-foreground">Average Duration:</span>
              <p className="font-medium">
                {Math.round(subscriptions
                  .filter(s => s.program_name === selectedProgram)
                  .reduce((acc, s) => acc + (s.program_duration || 0), 0) / 
                  allProgramSummary.programSummaries[selectedProgram].count)} days
              </p>
            </div>
          </div>
        </div>
      )}

      {message && (
        <div className={`text-sm ${message.includes('Error') ? 'text-red-500' : 'text-green-500'}`}>
          {message}
        </div>
      )}

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <SortableHeader column="name" label="Client Name" />
              <SortableHeader column="program_name" label="Program" />
              <SortableHeader column="start_date" label="Start Date" />
              <SortableHeader column="end_date" label="End Date" />
              <SortableHeader column="price_program" label="Price" />
              <SortableHeader column="subscription_status" label="Status" />
              <SortableHeader column="days_until_expiration" label="Days Until Expiration" />
              <SortableHeader column="program_duration" label="Program Duration" />
              <SortableHeader column="grace_period_days" label="Grace Period" />
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSubscriptions.map(sub => (
              <TableRow key={sub.subscription_id}>
                <TableCell>{`${sub.name} ${sub.last_name}`}</TableCell>
                <TableCell>{sub.program_name_display}</TableCell>
                <TableCell>{new Date(sub.start_date).toLocaleDateString()}</TableCell>
                <TableCell>{new Date(sub.end_date).toLocaleDateString()}</TableCell>
                <TableCell>€{sub.price_program?.toFixed(2)}</TableCell>
                <TableCell>
                  <span className={
                    sub.subscription_status === 'Active' ? 'text-green-600' :
                    sub.subscription_status === 'In Grace Period' ? 'text-yellow-600' :
                    'text-red-600'
                  }>
                    {sub.subscription_status}
                  </span>
                </TableCell>
                <TableCell>{sub.days_until_expiration}</TableCell>
                <TableCell>{sub.program_duration} days</TableCell>
                <TableCell>{sub.grace_period_days} days</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}