import Link from 'next/link'
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function PaymentsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto bg-white p-6 rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-4">Manage Pliromes and Subscriptions</h1>
        <div className="mb-4 space-x-2">
          <Button asChild variant="outline">
            <Link href="/admin/payments/view">View Payments</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/payments/add">Add New Payment</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/subscriptions/active">Active Subscriptions</Link>
          </Button>
        </div>
        {children}
      </div>
    </div>
  )
}