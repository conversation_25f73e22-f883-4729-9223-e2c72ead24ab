// app/admin/exercise-analytics/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import ExerciseAnalyticsClient from './ExerciseAnalyticsClient'
import { redirect } from 'next/navigation'
import type { Database } from '@/types/supabase'

// Define the ExerciseRecord interface to match the one in ExerciseAnalyticsClient
interface ExerciseRecord {
  id: string;
  exercise_id: string;
  pelatis_id: string;
  weight: number;
  reps: number;
  date_achieved?: string;
  is_pr: boolean;
  pelates?: {
    name?: string;
    last_name?: string;
    client_name?: string;
    email?: string;
  };
  exercise_movements?: {
    exercise_name: string;
  };
}

export default async function ExerciseAnalyticsPage() {
  const supabase = createServerComponentClient<Database>({ cookies })

  // Authentication check
  const { data: { user } } = await supabase.auth.getUser()
  if (!user || !user.email) {
    redirect('/auth')
  }

  // Get the user's profile
  const { data: userProfile, error: profileError } = await supabase
    .from('pelates')
    .select('id, email')
    .eq('email', user.email)
    .single()

  if (profileError || !userProfile) {
    redirect('/user/profile')
  }

  // First, get unique exercise_ids from exercise_records
  const { data: uniqueExerciseIds, error: exerciseIdsError } = await supabase
    .from('exercise_records')
    .select('exercise_id')
    .not('exercise_id', 'is', null)

  if (exerciseIdsError) {
    console.error('Error fetching exercise IDs:', exerciseIdsError)
  }

  // Extract unique IDs
  const exerciseIds = uniqueExerciseIds
    ? Array.from(new Set(uniqueExerciseIds.map(r => r.exercise_id)))
    : []

  // Fetch exercises that have records
  const { data: exercisesWithRecords, error: exercisesError } = await supabase
    .from('exercise_movements')
    .select(`
      id,
      exercise_name,
      body_part,
      equipment,
      expertise_level,
      movement_category,
      description
    `)
    .in('id', exerciseIds)
    .order('exercise_name')

  if (exercisesError) {
    console.error('Error fetching exercises:', exercisesError)
  }

  // First, get unique client_ids from exercise_records
  const { data: uniqueClientIds, error: clientIdsError } = await supabase
    .from('exercise_records')
    .select('pelatis_id')
    .not('pelatis_id', 'is', null)

  if (clientIdsError) {
    console.error('Error fetching client IDs:', clientIdsError)
  }

  // Extract unique client IDs
  const clientIds = uniqueClientIds
    ? Array.from(new Set(uniqueClientIds.map(r => r.pelatis_id)))
    : []

  // Fetch clients that have records
  const { data: clientsWithRecords, error: clientsError } = await supabase
    .from('pelates')
    .select(`
      id,
      client_name,
      name,
      last_name,
      email,
      sex,
      date_birth,
      height
    `)
    .in('id', clientIds)
    .order('last_name')

  if (clientsError) {
    console.error('Error fetching clients:', clientsError)
  }

  // Try to get high-level exercise stats
  let exerciseStats = []

  // Process exercise stats client-side
  try {
    const { data: exerciseStatsRaw, error: statsRawError } = await supabase
      .from('exercise_records')
      .select(`
        exercise_id,
        pelatis_id,
        date_achieved,
        exercise:exercise_movements(exercise_name)
      `)

    if (statsRawError) throw statsRawError
    if (exerciseStatsRaw) {
      // Process the data manually
      const statsMap = new Map()

      exerciseStatsRaw.forEach(record => {
        const key = record.exercise_id
        if (!statsMap.has(key)) {
          statsMap.set(key, {
            exercise_id: record.exercise_id,
            exercise_movements: { exercise_name: record.exercise?.exercise_name || 'Unknown' },
            count: 0,
            min: null,
            max: null,
            client_ids: new Set()
          })
        }

        const stats = statsMap.get(key)
        stats.count++
        if (record.pelatis_id) stats.client_ids.add(record.pelatis_id)

        const date = new Date(record.date_achieved || new Date())
        if (!stats.min || date < new Date(stats.min)) {
          stats.min = record.date_achieved
        }
        if (!stats.max || date > new Date(stats.max)) {
          stats.max = record.date_achieved
        }
      })

      // Convert to array and sort
      exerciseStats = Array.from(statsMap.values())
        .map(stat => ({
          ...stat,
          clients_count: stat.client_ids.size
        }))
        .sort((a, b) => b.count - a.count)

      // Remove the Set which can't be serialized for component props
      exerciseStats.forEach(stat => delete stat.client_ids)
    }
  } catch (error) {
    console.error('Stats processing failed:', error)
  }

  // Get recent personal records
  const { data: topRecords, error: topRecordsError } = await supabase
    .from('exercise_records')
    .select(`
      id,
      pelatis_id,
      exercise_id,
      weight,
      reps,
      date_achieved,
      is_pr,
      pelates(name, last_name),
      exercise_movements(exercise_name)
    `)
    .eq('is_pr', true)
    .order('date_achieved', { ascending: false })
    .limit(10)

  if (topRecordsError) {
    console.error('Error fetching top records:', topRecordsError)
  }

  return (
    <ExerciseAnalyticsClient
      // Use type assertions to handle type compatibility issues
      exercises={exercisesWithRecords as unknown as Database['public']['Tables']['exercise_movements']['Row'][]}
      clients={clientsWithRecords as unknown as Database['public']['Tables']['pelates']['Row'][]}
      exerciseStats={exerciseStats || []}
      topRecords={topRecords as unknown as ExerciseRecord[]}
      userId={userProfile.id}
    />
  )
}
