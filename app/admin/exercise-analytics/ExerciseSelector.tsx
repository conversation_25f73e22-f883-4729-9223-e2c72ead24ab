'use client'

import { useState } from 'react'
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']

interface ExerciseStat {
  exercise_id: string;
  count: number;
  min?: string;
  max?: string;
  exercise_movements: {
    exercise_name: string;
  };
}

interface ExerciseSelectorProps {
  exercises: Exercise[]
  selectedExercise: Exercise | null
  onSelectExercise: (exercise: Exercise | null) => void
  exerciseStats: ExerciseStat[]
}

export default function ExerciseSelector({
  exercises,
  selectedExercise,
  onSelectExercise,
  exerciseStats
}: ExerciseSelectorProps) {
  const [filter, setFilter] = useState('')

  // Filter exercises
  const filteredExercises = exercises?.filter(exercise => {
    if (!filter.trim()) return true

    const search = filter.toLowerCase()
    const name = exercise.exercise_name.toLowerCase()
    const bodyPart = (exercise.body_part || '').toLowerCase()
    const equipment = (exercise.equipment || '').toLowerCase()

    return name.includes(search) || bodyPart.includes(search) || equipment.includes(search)
  }) || []

  // Get exercise stats
  const getExerciseStats = (exerciseId: string): ExerciseStat | undefined => {
    return exerciseStats.find(stat => stat.exercise_id === exerciseId)
  }

  return (
    <div className="space-y-4">
      <h2 className="font-semibold text-lg">Exercises</h2>

      <Input
        placeholder="Filter exercises..."
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />

      <ScrollArea className="h-[calc(100vh-300px)]">
        <div className="space-y-1">
          <Button
            variant={selectedExercise === null ? "default" : "ghost"}
            className="w-full justify-start"
            onClick={() => onSelectExercise(null)}
          >
            All Exercises ({exercises?.length || 0})
          </Button>

          {filteredExercises.map(exercise => {
            const isSelected = selectedExercise?.id === exercise.id
            const stats = getExerciseStats(exercise.id)

            return (
              <Button
                key={exercise.id}
                variant={isSelected ? "default" : "ghost"}
                className="w-full justify-start h-auto py-3"
                onClick={() => onSelectExercise(exercise)}
              >
                <div className="flex flex-col items-start text-left w-full">
                  <div className="flex items-center">
                    <Dumbbell className="h-4 w-4 mr-2 text-muted-foreground" />
                    <div className="font-medium">{exercise.exercise_name}</div>
                  </div>

                  <div className="flex flex-wrap gap-3 mt-2 text-xs text-muted-foreground">
                    {exercise.body_part && (
                      <Badge variant="outline" className="text-xs font-normal">
                        {exercise.body_part}
                      </Badge>
                    )}

                    {exercise.equipment && (
                      <Badge variant="outline" className="text-xs font-normal">
                        {exercise.equipment}
                      </Badge>
                    )}

                    {stats && (
                      <div className="text-xs">
                        {stats.count} records
                      </div>
                    )}
                  </div>
                </div>
              </Button>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}