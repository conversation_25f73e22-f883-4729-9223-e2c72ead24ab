'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, ChevronDown, ChevronUp } from 'lucide-react'
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']
type Client = Database['public']['Tables']['pelates']['Row']
type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'] & {
  exercise?: Exercise
  client?: Client
}

interface RecordsListProps {
  records: ExerciseRecord[]
  loading: boolean
  calculateOneRepMax: (weight: number, reps: number) => number
  getClientName: (client: Client | null | undefined) => string
  showClients: boolean
  showExercises: boolean
}

export default function RecordsList({
  records,
  loading,
  calculateOneRepMax,
  getClientName,
  showClients,
  showExercises
}: RecordsListProps) {
  const [sortField, setSortField] = useState<string>('date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  // Sort records
  const sortedRecords = [...records].sort((a, b) => {
    if (sortField === 'date') {
      const dateA = new Date(a.date_achieved || 0).getTime()
      const dateB = new Date(b.date_achieved || 0).getTime()
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
    }

    if (sortField === 'exercise') {
      const exerciseA = a.exercise?.exercise_name || ''
      const exerciseB = b.exercise?.exercise_name || ''
      return sortDirection === 'asc'
        ? exerciseA.localeCompare(exerciseB)
        : exerciseB.localeCompare(exerciseA)
    }

    if (sortField === 'client') {
      const clientA = getClientName(a.client)
      const clientB = getClientName(b.client)
      return sortDirection === 'asc'
        ? clientA.localeCompare(clientB)
        : clientB.localeCompare(clientA)
    }

    if (sortField === 'weight') {
      const weightA = a.weight || 0
      const weightB = b.weight || 0
      return sortDirection === 'asc' ? weightA - weightB : weightB - weightA
    }

    if (sortField === 'reps') {
      const repsA = a.reps || 0
      const repsB = b.reps || 0
      return sortDirection === 'asc' ? repsA - repsB : repsB - repsA
    }

    if (sortField === '1rm') {
      const oneRmA = calculateOneRepMax(a.weight || 0, a.reps || 1)
      const oneRmB = calculateOneRepMax(b.weight || 0, b.reps || 1)
      return sortDirection === 'asc' ? oneRmA - oneRmB : oneRmB - oneRmA
    }

    return 0
  })

  // Export records to CSV
  const exportToCsv = () => {
    const headers = [
      'Date',
      'Time',
      showExercises ? 'Exercise' : '',
      showClients ? 'Client' : '',
      'Weight',
      'Reps',
      'Sets',
      '1RM',
      'Calories',
      'Notes'
    ].filter(Boolean)

    const rows = sortedRecords.map(record => {
      const date = record.date_achieved
        ? format(new Date(record.date_achieved), 'yyyy-MM-dd')
        : ''

      const time = record.date_achieved
        ? format(new Date(record.date_achieved), 'HH:mm:ss')
        : ''

      const exercise = showExercises ? record.exercise?.exercise_name || '' : ''
      const client = showClients ? getClientName(record.client) : ''

      const oneRm = record.weight && record.reps
        ? calculateOneRepMax(record.weight, record.reps)
        : ''

      return [
        date,
        time,
        showExercises ? exercise : null,
        showClients ? client : null,
        record.weight || '',
        record.reps || '',
        record.sets || '',
        oneRm,
        record.calories || '',
        record.notes || ''
      ].filter(x => x !== null)
    })

    const csv = [
      headers.join(','),
      ...rows.map(row => row.map(cell =>
        // Handle cells with commas by wrapping in quotes
        typeof cell === 'string' && cell.includes(',')
          ? `"${cell}"`
          : cell
      ).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `exercise-records-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Exercise Records</CardTitle>

        <div className="flex items-center gap-2">
          <Select
            value={sortField}
            onValueChange={setSortField}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Date</SelectItem>
              {showExercises && <SelectItem value="exercise">Exercise</SelectItem>}
              {showClients && <SelectItem value="client">Client</SelectItem>}
              <SelectItem value="weight">Weight</SelectItem>
              <SelectItem value="reps">Reps</SelectItem>
              <SelectItem value="1rm">1RM</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')}
          >
            {sortDirection === 'asc' ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>

          <Button variant="outline" size="sm" onClick={exportToCsv} className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : records.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <p>No records found</p>
            <p className="text-sm mt-1">Try selecting a different exercise or client</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort('date')}
                  >
                    <div className="flex items-center gap-1">
                      Date/Time
                      {sortField === 'date' && (
                        sortDirection === 'asc' ?
                          <ChevronUp className="h-4 w-4" /> :
                          <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>

                  {showExercises && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('exercise')}
                    >
                      <div className="flex items-center gap-1">
                        Exercise
                        {sortField === 'exercise' && (
                          sortDirection === 'asc' ?
                            <ChevronUp className="h-4 w-4" /> :
                            <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                  )}

                  {showClients && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('client')}
                    >
                      <div className="flex items-center gap-1">
                        Client
                        {sortField === 'client' && (
                          sortDirection === 'asc' ?
                            <ChevronUp className="h-4 w-4" /> :
                            <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                  )}

                  <TableHead
                    className="cursor-pointer text-right"
                    onClick={() => handleSort('weight')}
                  >
                    <div className="flex items-center gap-1 justify-end">
                      Weight
                      {sortField === 'weight' && (
                        sortDirection === 'asc' ?
                          <ChevronUp className="h-4 w-4" /> :
                          <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>

                  <TableHead
                    className="cursor-pointer text-right"
                    onClick={() => handleSort('reps')}
                  >
                    <div className="flex items-center gap-1 justify-end">
                      Reps
                      {sortField === 'reps' && (
                        sortDirection === 'asc' ?
                          <ChevronUp className="h-4 w-4" /> :
                          <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>

                  <TableHead className="text-right">Sets</TableHead>

                  <TableHead
                    className="cursor-pointer text-right"
                    onClick={() => handleSort('1rm')}
                  >
                    <div className="flex items-center gap-1 justify-end">
                      1RM
                      {sortField === '1rm' && (
                        sortDirection === 'asc' ?
                          <ChevronUp className="h-4 w-4" /> :
                          <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>

                  <TableHead className="text-right">Calories</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {sortedRecords.map(record => {
                  const date = record.date_achieved
                    ? format(new Date(record.date_achieved), 'MMM d, yyyy')
                    : 'Unknown'

                  const time = record.date_achieved
                    ? format(new Date(record.date_achieved), 'h:mm a')
                    : ''

                  const oneRm = record.weight && record.reps
                    ? calculateOneRepMax(record.weight, record.reps)
                    : null

                  return (
                    <TableRow key={record.id} className={record.is_pr ? "bg-green-50 dark:bg-green-950/10" : ""}>
                      <TableCell>
                        <div className="font-medium">{date}</div>
                        <div className="text-sm text-muted-foreground">{time}</div>
                        {record.is_pr && (
                          <Badge variant="outline" className="mt-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                            PR
                          </Badge>
                        )}
                      </TableCell>

                      {showExercises && (
                        <TableCell>
                          <div className="font-medium">
                            {record.exercise?.exercise_name || 'Unknown'}
                          </div>
                          <div className="flex gap-1 mt-1">
                            {record.exercise?.body_part && (
                              <Badge variant="outline" className="text-xs">
                                {record.exercise.body_part}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                      )}

                      {showClients && (
                        <TableCell>
                          {getClientName(record.client)}
                        </TableCell>
                      )}

                      <TableCell className="text-right">
                        {record.weight ? `${record.weight} kg` : '-'}
                      </TableCell>

                      <TableCell className="text-right">
                        {record.reps || '-'}
                      </TableCell>

                      <TableCell className="text-right">
                        {record.sets || '-'}
                      </TableCell>

                      <TableCell className="text-right font-medium">
                        {oneRm ? `${oneRm} kg` : '-'}
                      </TableCell>

                      <TableCell className="text-right">
                        {record.calories || '-'}
                      </TableCell>

                      <TableCell className="max-w-xs truncate">
                        {record.notes || '-'}
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}