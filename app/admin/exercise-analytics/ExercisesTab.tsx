'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import ExerciseSelector from './ExerciseSelector'
import ExerciseDetailView from './ExerciseDetailView'
import RecordsList from './RecordsList'
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']
type Client = Database['public']['Tables']['pelates']['Row']
type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'] & {
  exercise?: Exercise
  client?: Client
}

interface ExerciseStat {
  exercise_id: string;
  count: number;
  min?: string;
  max?: string;
  exercise_movements: {
    exercise_name: string;
  };
}

interface ExercisesTabProps {
  exercises: Exercise[]
  selectedExercise: Exercise | null
  exerciseStats: ExerciseStat[]
  dateRange: {
    from: string
    to: string
  }
  userId: string
  onSelectExercise: (exercise: Exercise | null) => void
  getClientName: (client: Client | null | undefined) => string
  calculateOneRepMax: (weight: number, reps: number) => number
}

export default function ExercisesTab({
  exercises,
  selectedExercise,
  exerciseStats,
  dateRange,
  userId,
  onSelectExercise,
  getClientName,
  calculateOneRepMax
}: ExercisesTabProps) {
  const [records, setRecords] = useState<ExerciseRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [view, setView] = useState<'list' | 'detail'>('list')

  const supabase = createClientComponentClient<Database>()

  // Fetch records when exercise selection or date range changes
  useEffect(() => {
    const fetchRecords = async () => {
      if (!selectedExercise) {
        setRecords([])
        return
      }

      setLoading(true)
      try {
        let query = supabase
          .from('exercise_records')
          .select(`
            *,
            exercise:exercise_movements(id, exercise_name, body_part, equipment, expertise_level, movement_category),
            client:pelates(id, client_name, name, last_name, email, sex, date_birth, height)
          `)
          .eq('exercise_id', selectedExercise.id)
          .order('date_achieved', { ascending: false })

        if (dateRange.from) {
          query = query.gte('date_achieved', dateRange.from)
        }

        if (dateRange.to) {
          query = query.lte('date_achieved', `${dateRange.to}T23:59:59`)
        }

        const { data, error } = await query

        if (error) throw error
        setRecords(data as ExerciseRecord[] || [])
      } catch (error) {
        console.error('Error fetching records:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRecords()
  }, [selectedExercise, dateRange, supabase])

  // Handle selecting an exercise
  const handleSelectExercise = (exercise: Exercise | null) => {
    onSelectExercise(exercise)
    if (exercise) {
      setView('detail')
    }
  }

  return view === 'list' ? (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {/* Exercise selector */}
      <div className="md:col-span-1">
        <Card className="p-4">
          <ExerciseSelector
            exercises={exercises}
            selectedExercise={selectedExercise}
            onSelectExercise={handleSelectExercise}
            exerciseStats={exerciseStats}
          />
        </Card>
      </div>

      {/* Records list */}
      <div className="md:col-span-3">
        <RecordsList
          records={records}
          loading={loading}
          calculateOneRepMax={calculateOneRepMax}
          getClientName={getClientName}
          showClients={true}
          showExercises={false}
        />
      </div>
    </div>
  ) : (
    <div className="space-y-4">
      <Button
        variant="outline"
        onClick={() => setView('list')}
        className="gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Exercises</span>
      </Button>

      {selectedExercise && (
        <ExerciseDetailView
          exercise={selectedExercise}
          records={records}
          loading={loading}
          calculateOneRepMax={calculateOneRepMax}
          getClientName={getClientName}
          userId={userId}
        />
      )}
    </div>
  )
}