
'use client'

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useState } from 'react'
import { CalendarDays, Ruler } from 'lucide-react'

interface Client {
  id: string
  client_name?: string
  name?: string
  last_name?: string
  email?: string
  sex?: string
  date_birth?: string
  height?: number
}

interface ClientSelectorProps {
  clients: Client[]
  selectedClient: Client | null
  onSelectClient: (client: Client | null) => void
  getClientName: (client: Client | null | undefined) => string
}

export default function ClientSelector({
  clients,
  selectedClient,
  onSelectClient,
  getClientName
}: ClientSelectorProps) {
  const [filter, setFilter] = useState('')

  // Filter clients
  const filteredClients = clients?.filter(client => {
    if (!filter.trim()) return true

    const search = filter.toLowerCase()
    const name = getClientName(client).toLowerCase()
    const email = (client.email || '').toLowerCase()

    return name.includes(search) || email.includes(search)
  }) || []

  // Calculate age
  const calculateAge = (birthDate: string | undefined) => {
    if (!birthDate) return null

    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  }

  return (
    <div className="space-y-4">
      <h2 className="font-semibold text-lg">Clients with Records</h2>

      <Input
        placeholder="Filter clients..."
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />

      <ScrollArea className="h-[calc(100vh-300px)]">
        <div className="space-y-1">
          <Button
            variant={selectedClient === null ? "default" : "ghost"}
            className="w-full justify-start"
            onClick={() => onSelectClient(null)}
          >
            All Clients ({clients?.length || 0})
          </Button>

          {filteredClients.map(client => {
            const isSelected = selectedClient?.id === client.id
            const age = calculateAge(client.date_birth)

            return (
              <Button
                key={client.id}
                variant={isSelected ? "default" : "ghost"}
                className="w-full justify-start h-auto py-3"
                onClick={() => onSelectClient(client)}
              >
                <div className="flex flex-col items-start text-left w-full">
                  <div className="font-medium">{getClientName(client)}</div>

                  {client.email && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {client.email}
                    </div>
                  )}

                  <div className="flex flex-wrap gap-3 mt-2 text-xs text-muted-foreground">
                    {client.sex && (
                      <Badge variant="outline" className="text-xs font-normal">
                        {client.sex}
                      </Badge>
                    )}

                    {age && (
                      <div className="flex items-center">
                        <CalendarDays className="h-3 w-3 mr-1" />
                        {age} years
                      </div>
                    )}

                    {client.height && (
                      <div className="flex items-center">
                        <Ruler className="h-3 w-3 mr-1" />
                        {client.height} cm
                      </div>
                    )}
                  </div>
                </div>
              </Button>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}