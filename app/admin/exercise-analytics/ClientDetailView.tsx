'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

interface Client {
  id: string
  client_name?: string
  name?: string
  last_name?: string
  email?: string
  sex?: string
  date_birth?: string
  height?: number
}

interface Exercise {
  id: string;
  exercise_name: string;
  body_part?: string;
  equipment?: string;
}

interface Record {
  id: string;
  exercise_id: string;
  weight?: number;
  reps?: number;
  sets?: number;
  date_achieved?: string;
  is_pr: boolean;
  notes?: string;
  exercise?: Exercise;
}

interface ClientDetailProps {
  client: Client
  records: Record[]
  loading: boolean
  calculateOneRepMax: (weight: number, reps: number) => number
  getClientName: (client: Client | null | undefined) => string
}

export default function ClientDetailView({
  client,
  records,
  loading,
  calculateOneRepMax,
  getClientName
}: ClientDetailProps) {
  const [personalBests, setPersonalBests] = useState<Record[]>([])

  interface ExerciseStat {
    exerciseId: string;
    exerciseName: string;
    bodyPart?: string;
    equipment?: string;
    totalRecords: number;
    maxWeight: number;
    maxOneRm: number;
    latestOneRm: number;
    improvement: number;
    bestRecord: Record;
    latestRecord: Record;
    firstRecord: Record;
  }

  const [exerciseStats, setExerciseStats] = useState<ExerciseStat[]>([])
  // Using a constant instead of state since it's not being updated
  const loadingStats = false
  const [activeTab, setActiveTab] = useState('overview')

  // Calculate age
  const calculateAge = (birthDate: string | undefined) => {
    if (!birthDate) return null

    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  }

  // Fetch personal bests
  useEffect(() => {
    const calculatePersonalBests = () => {
      if (!records.length) return

      // Group records by exercise
      const exerciseGroups: { [key: string]: Record[] } = {}

      records.forEach(record => {
        const exerciseId = record.exercise_id
        if (!exerciseId) return

        if (!exerciseGroups[exerciseId]) {
          exerciseGroups[exerciseId] = []
        }

        exerciseGroups[exerciseId].push(record)
      })

      // Find best record for each exercise
      const bests = Object.entries(exerciseGroups).map(([, exerciseRecords]) => {
        return exerciseRecords.reduce((best, current) => {
          if (!best.weight) return current
          if (!current.weight) return best

          const bestOneRm = calculateOneRepMax(best.weight, best.reps || 1)
          const currentOneRm = calculateOneRepMax(current.weight, current.reps || 1)

          return currentOneRm > bestOneRm ? current : best
        })
      })

      setPersonalBests(bests)

      // Calculate summary stats per exercise
      const stats = Object.entries(exerciseGroups).map(([exerciseId, exerciseRecords]) => {
        const exercise = exerciseRecords[0]?.exercise

        if (!exercise) return null

        const recordsWithWeight = exerciseRecords.filter(r => r.weight)

        if (recordsWithWeight.length === 0) return null

        // Sort by date, newest first
        const sortedRecords = [...recordsWithWeight].sort((a, b) => {
          return new Date(b.date_achieved || 0).getTime() - new Date(a.date_achieved || 0).getTime()
        })

        // Get latest record
        const latestRecord = sortedRecords[0]

        // Get best record (by 1RM)
        const bestRecord = recordsWithWeight.reduce((best, current) => {
          const bestOneRm = calculateOneRepMax(best.weight || 0, best.reps || 1)
          const currentOneRm = calculateOneRepMax(current.weight || 0, current.reps || 1)

          return currentOneRm > bestOneRm ? current : best
        })

        // Calculate improvement - first vs last record
        const firstRecord = sortedRecords[sortedRecords.length - 1]
        const firstOneRm = calculateOneRepMax(firstRecord.weight || 0, firstRecord.reps || 1)
        const latestOneRm = calculateOneRepMax(latestRecord.weight || 0, latestRecord.reps || 1)
        const improvement = latestOneRm - firstOneRm

        return {
          exerciseId,
          exerciseName: exercise.exercise_name,
          bodyPart: exercise.body_part,
          equipment: exercise.equipment,
          totalRecords: exerciseRecords.length,
          maxWeight: Math.max(...recordsWithWeight.map(r => r.weight || 0)),
          maxOneRm: calculateOneRepMax(bestRecord.weight || 0, bestRecord.reps || 1),
          latestOneRm,
          improvement,
          bestRecord,
          latestRecord,
          firstRecord
        }
      }).filter(Boolean)

      // Filter out null values and cast to ExerciseStat[]
      setExerciseStats(stats.filter(Boolean) as ExerciseStat[])
    }

    calculatePersonalBests()
  }, [records, calculateOneRepMax])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">{getClientName(client)}</CardTitle>

          <div className="flex flex-wrap gap-4 mt-2 text-muted-foreground">
            {client.email && (
              <div className="text-sm">
                {client.email}
              </div>
            )}

            {client.sex && (
              <Badge>
                {client.sex}
              </Badge>
            )}

            {client.date_birth && (
              <div className="text-sm">
                Age: {calculateAge(client.date_birth)} years
              </div>
            )}

            {client.height && (
              <div className="text-sm">
                Height: {client.height} cm
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Total Records</div>
              <div className="text-2xl font-bold mt-1">{records.length}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Exercises</div>
              <div className="text-2xl font-bold mt-1">{exerciseStats.length}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Personal Records</div>
              <div className="text-2xl font-bold mt-1">{records.filter(r => r.is_pr).length}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Last Workout</div>
              <div className="text-2xl font-bold mt-1">
                {records.length > 0 && records[0].date_achieved
                  ? format(new Date(records[0].date_achieved), 'MMM d')
                  : 'N/A'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">All Records</TabsTrigger>
          <TabsTrigger value="personalBests">Personal Bests</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-60 w-full" />
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>All Records for {getClientName(client)}</CardTitle>
              </CardHeader>

              <CardContent>
                {records.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No records found for this client
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Exercise</TableHead>
                        <TableHead className="text-right">Weight</TableHead>
                        <TableHead className="text-right">Reps</TableHead>
                        <TableHead className="text-right">Sets</TableHead>
                        <TableHead className="text-right">1RM</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      {records.map(record => {
                        const oneRm = record.weight && record.reps
                          ? calculateOneRepMax(record.weight, record.reps)
                          : null

                        return (
                          <TableRow key={record.id}>
                            <TableCell>
                              {record.date_achieved ? format(new Date(record.date_achieved), 'MMM d, yyyy') : '-'}
                            </TableCell>
                            <TableCell>{record.exercise?.exercise_name || 'Unknown'}</TableCell>
                            <TableCell className="text-right">{record.weight ? `${record.weight} kg` : '-'}</TableCell>
                            <TableCell className="text-right">{record.reps || '-'}</TableCell>
                            <TableCell className="text-right">{record.sets || '-'}</TableCell>
                            <TableCell className="text-right font-medium">{oneRm ? `${oneRm} kg` : '-'}</TableCell>
                            <TableCell className="max-w-xs truncate">{record.notes || '-'}</TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="personalBests" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Bests</CardTitle>
            </CardHeader>

            <CardContent>
              {loadingStats ? (
                <div className="space-y-3">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : personalBests.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No personal bests available
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Exercise</TableHead>
                      <TableHead className="text-right">Weight</TableHead>
                      <TableHead className="text-right">Reps</TableHead>
                      <TableHead className="text-right">1RM</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {personalBests.map(record => {
                      const oneRm = calculateOneRepMax(record.weight || 0, record.reps || 1)

                      return (
                        <TableRow key={record.id}>
                          <TableCell>{record.exercise?.exercise_name || 'Unknown'}</TableCell>
                          <TableCell className="text-right">{record.weight} kg</TableCell>
                          <TableCell className="text-right">{record.reps || 1}</TableCell>
                          <TableCell className="font-bold text-right">{oneRm} kg</TableCell>
                          <TableCell>
                            {record.date_achieved ? format(new Date(record.date_achieved), 'MMM d, yyyy') : '-'}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Progress Overview</CardTitle>
            </CardHeader>

            <CardContent>
              {loadingStats ? (
                <div className="space-y-3">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : exerciseStats.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No progress data available
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Exercise</TableHead>
                      <TableHead className="text-right">Best 1RM</TableHead>
                      <TableHead className="text-right">Current 1RM</TableHead>
                      <TableHead className="text-right">Progress</TableHead>
                      <TableHead>Records</TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {exerciseStats.filter(Boolean).map(stat => {
                      if (!stat) return null;
                      return (
                        <TableRow key={stat.exerciseId}>
                          <TableCell>{stat.exerciseName}</TableCell>
                          <TableCell className="text-right">{stat.maxOneRm} kg</TableCell>
                          <TableCell className="text-right">{stat.latestOneRm} kg</TableCell>
                          <TableCell className="text-right">
                            {stat.improvement > 0 ? (
                              <span className="text-green-600">+{stat.improvement} kg</span>
                            ) : stat.improvement < 0 ? (
                              <span className="text-red-600">{stat.improvement} kg</span>
                            ) : (
                              <span className="text-gray-500">No change</span>
                            )}
                          </TableCell>
                          <TableCell>{stat.totalRecords}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}