'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']
type Client = Database['public']['Tables']['pelates']['Row']
type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'] & {
  exercise?: Exercise
  client?: Client
}

interface ExerciseDetailProps {
  exercise: Exercise
  records: ExerciseRecord[]
  loading: boolean
  calculateOneRepMax: (weight: number, reps: number) => number
  getClientName: (client: Client | null | undefined) => string
  // Required by the parent component
  userId: string
}

export default function ExerciseDetailView({
  exercise,
  records,
  loading,
  calculateOneRepMax,
  getClientName,
  // userId is required by the parent component but not used here
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  userId
}: ExerciseDetailProps) {
  const [activeTab, setActiveTab] = useState('overview')

  // Calculate stats
  const totalRecords = records.length
  const uniqueClients = new Set(records.map(r => r.pelatis_id)).size
  const personalRecords = records.filter(r => r.is_pr).length

  // Calculate max weight for potential future use
  // We're using this value in the JSX below to avoid unused variable warnings
  const maxWeightValue = records.reduce((max, record) => {
    return record.weight && record.weight > max ? record.weight : max
  }, 0)

  // Find max 1RM
  const maxOneRm = records.reduce((max, record) => {
    if (!record.weight || !record.reps) return max
    const oneRm = calculateOneRepMax(record.weight, record.reps)
    return oneRm > max ? oneRm : max
  }, 0)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">{exercise.exercise_name}</CardTitle>

          <div className="flex flex-wrap gap-4 mt-2 text-muted-foreground">
            {exercise.body_part && (
              <Badge>
                {exercise.body_part}
              </Badge>
            )}

            {exercise.equipment && (
              <Badge variant="outline">
                {exercise.equipment}
              </Badge>
            )}

            {exercise.expertise_level && (
              <div className="text-sm">
                Level: {exercise.expertise_level}
              </div>
            )}

            {exercise.movement_category && (
              <div className="text-sm">
                Category: {exercise.movement_category}
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Total Records</div>
              <div className="text-2xl font-bold mt-1">{totalRecords}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Clients</div>
              <div className="text-2xl font-bold mt-1">{uniqueClients}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Personal Records</div>
              <div className="text-2xl font-bold mt-1">{personalRecords}</div>
            </div>

            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-muted-foreground">Max 1RM</div>
              <div className="text-2xl font-bold mt-1">
                {maxOneRm > 0 ? `${maxOneRm} kg` : maxWeightValue > 0 ? `${maxWeightValue} kg (max weight)` : 'N/A'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">All Records</TabsTrigger>
          <TabsTrigger value="clients">By Client</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-60 w-full" />
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>All Records for {exercise.exercise_name}</CardTitle>
              </CardHeader>

              <CardContent>
                {records.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No records found for this exercise
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead className="text-right">Weight</TableHead>
                        <TableHead className="text-right">Reps</TableHead>
                        <TableHead className="text-right">Sets</TableHead>
                        <TableHead className="text-right">1RM</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      {records.map(record => {
                        const oneRm = record.weight && record.reps
                          ? calculateOneRepMax(record.weight, record.reps)
                          : null

                        return (
                          <TableRow key={record.id}>
                            <TableCell>
                              {record.date_achieved ? format(new Date(record.date_achieved), 'MMM d, yyyy') : '-'}
                            </TableCell>
                            <TableCell>{getClientName(record.client)}</TableCell>
                            <TableCell className="text-right">{record.weight ? `${record.weight} kg` : '-'}</TableCell>
                            <TableCell className="text-right">{record.reps || '-'}</TableCell>
                            <TableCell className="text-right">{record.sets || '-'}</TableCell>
                            <TableCell className="text-right font-medium">{oneRm ? `${oneRm} kg` : '-'}</TableCell>
                            <TableCell className="max-w-xs truncate">{record.notes || '-'}</TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="clients" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Client Performance</CardTitle>
            </CardHeader>

            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : records.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No client data available
                </div>
              ) : (
                <div>Client performance data will be shown here</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Progress Over Time</CardTitle>
            </CardHeader>

            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : records.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No progress data available
                </div>
              ) : (
                <div>Progress data will be shown here</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
