'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { <PERSON>, FilterX, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import ExercisesTab from './ExercisesTab'
import ClientsTab from './ClientsTab'
import AnalyticsDashboard from './AnalyticsDashboard'
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']
type Client = Database['public']['Tables']['pelates']['Row']

interface ExerciseStat {
  exercise_id: string;
  count: number;
  min?: string;
  max?: string;
  exercise_movements: {
    exercise_name: string;
  };
}

interface Record {
  id: string;
  exercise_id: string;
  pelatis_id: string;
  weight: number;
  reps: number;
  date_achieved?: string;
  is_pr: boolean;
  pelates?: {
    name?: string;
    last_name?: string;
    client_name?: string;
    email?: string;
  };
  exercise_movements?: {
    exercise_name: string;
  };
}

interface Props {
  exercises: Exercise[]
  clients: Client[]
  exerciseStats: ExerciseStat[]
  topRecords: Record[]
  userId: string
}

export default function ExerciseAnalyticsClient({
  exercises,
  clients,
  exerciseStats,
  topRecords,
  userId
}: Props) {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null)
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [dateRange, setDateRange] = useState({
    from: '',
    to: ''
  })
  const [filteredExercises, setFilteredExercises] = useState(exercises)
  const [filteredClients, setFilteredClients] = useState(clients)

  // Handle search filtering
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredExercises(exercises)
      setFilteredClients(clients)
      return
    }

    const term = searchTerm.toLowerCase()
    setFilteredExercises(
      exercises.filter(ex => ex.exercise_name.toLowerCase().includes(term))
    )
    setFilteredClients(
      clients.filter(client =>
        (client.name?.toLowerCase() || '').includes(term) ||
        (client.last_name?.toLowerCase() || '').includes(term) ||
        (client.email?.toLowerCase() || '').includes(term) ||
        (client.client_name?.toLowerCase() || '').includes(term)
      )
    )
  }, [searchTerm, exercises, clients])

  // Reset all filters
  const resetFilters = () => {
    setSelectedExercise(null)
    setSelectedClient(null)
    setSearchTerm('')
    setDateRange({ from: '', to: '' })
  }

  // Helper function to get client full name
  const getClientName = (client: Client | null | undefined) => {
    if (!client) return 'Unknown'
    if (client.name && client.last_name) return `${client.name} ${client.last_name}`
    if (client.client_name) return client.client_name
    return client.email || 'Unnamed Client'
  }

  // Calculate One Rep Max using standard formula
  function calculateOneRepMax(weight: number, reps: number = 1): number {
    if (reps === 1) return weight
    return Math.round(weight * (36 / (37 - reps)) * 10) / 10
  }

  // Handle selecting an exercise (for detail view)
  const handleSelectExercise = (exercise: Exercise | null) => {
    setSelectedExercise(exercise)
    setActiveTab('exercises')
  }

  // Handle selecting a client (for detail view)
  const handleSelectClient = (client: Client | null) => {
    setSelectedClient(client)
    setActiveTab('clients')
  }

  return (
    <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">Exercise Analytics</h1>
          <p className="text-muted-foreground">
            View and analyze exercise records across all clients
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search exercises or clients..."
              className="w-full md:w-64 pl-9"
            />
          </div>

          <Button variant="outline" onClick={resetFilters} className="gap-2">
            <FilterX className="h-4 w-4" />
            <span>Reset</span>
          </Button>
        </div>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="text-sm font-medium">Date from:</div>
          <Input
            type="date"
            value={dateRange.from}
            onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
            className="w-auto"
          />
        </div>

        <div className="flex items-center gap-3">
          <div className="text-sm font-medium">to:</div>
          <Input
            type="date"
            value={dateRange.to}
            onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
            className="w-auto"
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span>Dashboard</span>
          </TabsTrigger>
          <TabsTrigger value="exercises" className="flex items-center gap-2">
            <Dumbbell className="h-4 w-4" />
            <span>Exercises</span>
          </TabsTrigger>
          <TabsTrigger value="clients" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Clients</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          {/*
            Using type assertions to resolve type mismatches between components.
            This is necessary because the Database types from Supabase don't exactly match
            the interface types defined in the components.
          */}
          <AnalyticsDashboard
            exerciseStats={exerciseStats}
            topRecords={topRecords}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            exercises={exercises as any}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            clients={clients as any}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSelectExercise={(exercise) => handleSelectExercise(exercise as any)}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSelectClient={(client) => handleSelectClient(client as any)}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            getClientName={(client) => getClientName(client as any)}
            calculateOneRepMax={calculateOneRepMax}
            onParentTabChange={setActiveTab}
          />
        </TabsContent>

        <TabsContent value="exercises">
          <ExercisesTab
            exercises={filteredExercises}
            selectedExercise={selectedExercise}
            exerciseStats={exerciseStats}
            dateRange={dateRange}
            userId={userId}
            onSelectExercise={setSelectedExercise}
            getClientName={getClientName}
            calculateOneRepMax={calculateOneRepMax}
          />
        </TabsContent>

        <TabsContent value="clients">
          <ClientsTab
            clients={filteredClients.map(client => ({
              id: client.id,
              client_name: client.client_name || undefined,
              name: client.name || undefined,
              last_name: client.last_name || undefined,
              email: client.email || undefined,
              sex: client.sex || undefined,
              date_birth: client.date_birth || undefined,
              height: client.height || undefined
            }))}
            selectedClient={selectedClient ? {
              id: selectedClient.id,
              client_name: selectedClient.client_name || undefined,
              name: selectedClient.name || undefined,
              last_name: selectedClient.last_name || undefined,
              email: selectedClient.email || undefined,
              sex: selectedClient.sex || undefined,
              date_birth: selectedClient.date_birth || undefined,
              height: selectedClient.height || undefined
            } : null}
            dateRange={dateRange}
            onSelectClient={(client) => {
              if (client === null) {
                setSelectedClient(null);
                return;
              }

              // Find the original client in the filteredClients array
              const originalClient = filteredClients.find(c => c.id === client.id);
              setSelectedClient(originalClient || null);
            }}
            getClientName={(client) => {
              if (!client) return 'Unknown';
              return getClientName({
                ...client,
                // Add null properties to match the DatabaseClient type
                address: null,
                afm: null,
                auth_user_id: null,
                created_at: null,
                waiver_signed_date: null,
                // Add any other required properties
              } as Client);
            }}
            calculateOneRepMax={calculateOneRepMax}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}