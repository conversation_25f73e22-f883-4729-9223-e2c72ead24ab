'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  BarChart as BarChartIcon,
  TrendingUp,
  Calendar,
  Users
} from 'lucide-react'

interface Exercise {
  id: string;
  exercise_name: string;
}

interface Client {
  id: string;
  name?: string;
  last_name?: string;
  client_name?: string;
  email?: string;
}

interface ExerciseStat {
  exercise_id: string;
  count: number;
  min?: string;
  max?: string;
  exercise_movements: {
    exercise_name: string;
  };
}

interface Record {
  id: string;
  exercise_id: string;
  pelatis_id: string;
  weight: number;
  reps: number;
  date_achieved?: string;
  is_pr: boolean;
  pelates?: {
    name?: string;
    last_name?: string;
    client_name?: string;
    email?: string;
  };
  exercise_movements?: {
    exercise_name: string;
  };
}

interface AnalyticsDashboardProps {
  exerciseStats: ExerciseStat[]
  topRecords: Record[]
  exercises: Exercise[]
  clients: Client[]
  onSelectExercise: (exercise: Exercise) => void
  onSelectClient: (client: Client) => void
  getClientName: (client: Client) => string
  calculateOneRepMax: (weight: number, reps: number) => number
  onParentTabChange?: (tab: string) => void
}

export default function AnalyticsDashboard({
  exerciseStats,
  topRecords,
  exercises,
  clients,
  onSelectExercise,
  onSelectClient,
  getClientName,
  calculateOneRepMax,
  onParentTabChange
}: AnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [mostPopularExercises, setMostPopularExercises] = useState<ExerciseStat[]>([])
  const [mostActiveClients, setMostActiveClients] = useState<{client: Client, count: number}[]>([])
  const [recentPRs, setRecentPRs] = useState<Record[]>([])

  // Process exercise stats to find most popular exercises
  useEffect(() => {
    if (!exerciseStats || exerciseStats.length === 0) return

    // Sort by record count
    const sortedByCount = [...exerciseStats].sort((a, b) => b.count - a.count)

    // Take top 5
    setMostPopularExercises(sortedByCount.slice(0, 5))
  }, [exerciseStats])

  // Process top records to extract recent PRs
  useEffect(() => {
    if (!topRecords || topRecords.length === 0) return

    // Filter for PRs
    const prs = topRecords.filter(record => record.is_pr)

    // Sort by date
    const sortedByDate = [...prs].sort((a, b) => {
      return new Date(b.date_achieved || 0).getTime() - new Date(a.date_achieved || 0).getTime()
    })

    // Take top 5
    setRecentPRs(sortedByDate.slice(0, 5))
  }, [topRecords])

  // Find most active clients
  useEffect(() => {
    if (!topRecords || topRecords.length === 0 || !clients || clients.length === 0) return

    // Count records per client
    const clientRecordCounts: { [key: string]: { client: Client, count: number } } = {}

    topRecords.forEach(record => {
      const clientId = record.pelatis_id
      if (!clientId) return

      if (!clientRecordCounts[clientId]) {
        const client = clients.find(c => c.id === clientId)
        if (client) {
          clientRecordCounts[clientId] = { client, count: 0 }
        } else {
          return; // Skip if client not found
        }
      }

      clientRecordCounts[clientId].count++
    })

    // Convert to array and sort
    const clientsArray = Object.values(clientRecordCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .map((item: { client: Client, count: number }) => ({
        client: item.client,
        count: item.count
      }))

    setMostActiveClients(clientsArray)
  }, [topRecords, clients])

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <BarChartIcon className="h-5 w-5 text-blue-500" />
              <div className="text-sm text-muted-foreground">Total Exercises</div>
            </div>
            <div className="text-2xl font-bold mt-2">{exercises?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-500" />
              <div className="text-sm text-muted-foreground">Total Clients</div>
            </div>
            <div className="text-2xl font-bold mt-2">{clients?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div className="text-sm text-muted-foreground">Total Records</div>
            </div>
            <div className="text-2xl font-bold mt-2">{topRecords?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-orange-500" />
              <div className="text-sm text-muted-foreground">Last Record</div>
            </div>
            <div className="text-2xl font-bold mt-2">
              {topRecords?.length > 0 && topRecords[0]?.date_achieved
                ? format(new Date(topRecords[0].date_achieved), 'MMM d')
                : 'N/A'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="topExercises">Top Exercises</TabsTrigger>
          <TabsTrigger value="recentPRs">Recent PRs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Popular Exercises */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Most Popular Exercises</CardTitle>
              </CardHeader>
              <CardContent>
                {mostPopularExercises.length === 0 ? (
                  <div className="h-60 flex items-center justify-center text-muted-foreground">
                    No exercise data available
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Exercise</TableHead>
                        <TableHead className="text-right">Records</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mostPopularExercises.map(exercise => {
                        const matchingExercise = exercises.find(e => e.id === exercise.exercise_id)

                        return (
                          <TableRow key={exercise.exercise_id}>
                            <TableCell>
                              {exercise.exercise_movements?.exercise_name || matchingExercise?.exercise_name || 'Unknown'}
                            </TableCell>
                            <TableCell className="text-right">
                              {exercise.count}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  if (matchingExercise) {
                                    onSelectExercise(matchingExercise);
                                    // Change the parent tab to 'exercises'
                                    if (onParentTabChange) {
                                      onParentTabChange('exercises');
                                    }
                                  }
                                }}
                              >
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

            {/* Active Clients */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Most Active Clients</CardTitle>
              </CardHeader>
              <CardContent>
                {mostActiveClients.length === 0 ? (
                  <div className="h-60 flex items-center justify-center text-muted-foreground">
                    No client data available
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Client</TableHead>
                        <TableHead className="text-right">Records</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mostActiveClients.map(item => (
                        <TableRow key={item.client.id}>
                          <TableCell>
                            {getClientName(item.client)}
                          </TableCell>
                          <TableCell className="text-right">
                            {item.count}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onSelectClient(item.client)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="topExercises" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Exercise Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              {exerciseStats.length === 0 ? (
                <div className="h-60 flex items-center justify-center text-muted-foreground">
                  No exercise statistics available
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Exercise</TableHead>
                      <TableHead className="text-right">Records</TableHead>
                      <TableHead>First Record</TableHead>
                      <TableHead>Last Record</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {exerciseStats.map(stat => {
                      const matchingExercise = exercises.find(e => e.id === stat.exercise_id)

                      return (
                        <TableRow key={stat.exercise_id}>
                          <TableCell>{stat.exercise_movements.exercise_name}</TableCell>
                          <TableCell className="text-right">{stat.count}</TableCell>
                          <TableCell>{stat.min ? format(new Date(stat.min), 'MMM d, yyyy') : '-'}</TableCell>
                          <TableCell>{stat.max ? format(new Date(stat.max), 'MMM d, yyyy') : '-'}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                if (matchingExercise) {
                                  onSelectExercise(matchingExercise);
                                  // Change the parent tab to 'exercises'
                                  if (onParentTabChange) {
                                    onParentTabChange('exercises');
                                  }
                                }
                              }}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recentPRs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Personal Records</CardTitle>
            </CardHeader>
            <CardContent>
              {recentPRs.length === 0 ? (
                <div className="h-60 flex items-center justify-center text-muted-foreground">
                  No personal records available
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Client</TableHead>
                      <TableHead>Exercise</TableHead>
                      <TableHead className="text-right">Weight</TableHead>
                      <TableHead className="text-right">Reps</TableHead>
                      <TableHead className="text-right">1RM</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentPRs.map(record => {
                      const oneRm = calculateOneRepMax(record.weight || 0, record.reps || 1)
                      const clientName =
                        record.pelates?.name && record.pelates?.last_name ?
                        `${record.pelates.name} ${record.pelates.last_name}` :
                        getClientName({ id: record.pelatis_id, client_name: record.pelates?.client_name, email: record.pelates?.email })

                      return (
                        <TableRow key={record.id}>
                          <TableCell>
                            {record.date_achieved ? format(new Date(record.date_achieved), 'MMM d, yyyy') : '-'}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto"
                              onClick={() => {
                                const client = clients.find(c => c.id === record.pelatis_id)
                                if (client) {
                                  onSelectClient(client);
                                  // Change the parent tab to 'clients'
                                  if (onParentTabChange) {
                                    onParentTabChange('clients');
                                  }
                                }
                              }}
                            >
                              {clientName}
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="link"
                              className="p-0 h-auto"
                              onClick={() => {
                                const exercise = exercises.find(e => e.id === record.exercise_id)
                                if (exercise) {
                                  onSelectExercise(exercise);
                                  // Change the parent tab to 'exercises'
                                  if (onParentTabChange) {
                                    onParentTabChange('exercises');
                                  }
                                }
                              }}
                            >
                              {record.exercise_movements?.exercise_name || 'Unknown'}
                            </Button>
                          </TableCell>
                          <TableCell className="text-right">{record.weight} kg</TableCell>
                          <TableCell className="text-right">{record.reps || 1}</TableCell>
                          <TableCell className="text-right font-bold">
                            <Badge variant="outline" className="bg-green-50 border-green-200 text-green-700">
                              {oneRm} kg
                            </Badge>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}