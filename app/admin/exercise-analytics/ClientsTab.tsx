'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import ClientSelector from './ClientSelector'
import ClientDetailView from './ClientDetailView'
import RecordsList from './RecordsList'
import type { Database } from '@/types/supabase'

// Define the Client interface to match ClientSelector.tsx
interface Client {
  id: string
  client_name?: string
  name?: string
  last_name?: string
  email?: string
  sex?: string
  date_birth?: string
  height?: number
}

// Define the DetailRecord interface to match ClientDetailView.tsx
interface DetailRecord {
  id: string;
  exercise_id: string;
  weight?: number;
  reps?: number;
  sets?: number;
  date_achieved?: string;
  is_pr: boolean;
  notes?: string;
  exercise?: Exercise;
}

type Exercise = Database['public']['Tables']['exercise_movements']['Row']
type DatabaseClient = Database['public']['Tables']['pelates']['Row']
type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'] & {
  exercise?: Exercise
  client?: DatabaseClient
}

// Note: We're using type assertions (as any) to handle type compatibility
// between the database types and the component interface types

interface ClientsTabProps {
  clients: Client[]
  selectedClient: Client | null
  dateRange: {
    from: string
    to: string
  }
  onSelectClient: (client: Client | null) => void
  getClientName: (client: Client | null | undefined) => string
  calculateOneRepMax: (weight: number, reps: number) => number
}

export default function ClientsTab({
  clients,
  selectedClient,
  dateRange,
  onSelectClient,
  getClientName,
  calculateOneRepMax
}: ClientsTabProps) {
  const [records, setRecords] = useState<ExerciseRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [view, setView] = useState<'list' | 'detail'>('list')

  const supabase = createClientComponentClient<Database>()

  // Fetch records when client selection or date range changes
  useEffect(() => {
    const fetchRecords = async () => {
      if (!selectedClient) {
        setRecords([])
        return
      }

      setLoading(true)
      try {
        let query = supabase
          .from('exercise_records')
          .select(`
            *,
            exercise:exercise_movements(id, exercise_name, body_part, equipment, expertise_level, movement_category),
            client:pelates(id, client_name, name, last_name, email, sex, date_birth, height)
          `)
          .eq('pelatis_id', selectedClient.id)
          .order('date_achieved', { ascending: false })

        if (dateRange.from) {
          query = query.gte('date_achieved', dateRange.from)
        }

        if (dateRange.to) {
          query = query.lte('date_achieved', `${dateRange.to}T23:59:59`)
        }

        const { data, error } = await query

        if (error) throw error
        // Type assertion to match the expected ExerciseRecord[] type
        setRecords(data as ExerciseRecord[] || [])
      } catch (error) {
        console.error('Error fetching records:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRecords()
  }, [selectedClient, dateRange, supabase])

  // Handle selecting a client
  const handleSelectClient = (client: Client | null) => {
    onSelectClient(client)
    if (client) {
      setView('detail')
    }
  }

  return view === 'list' ? (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {/* Client selector */}
      <div className="md:col-span-1">
        <Card className="p-4">
          <ClientSelector
            clients={clients}
            selectedClient={selectedClient}
            onSelectClient={handleSelectClient}
            getClientName={getClientName}
          />
        </Card>
      </div>

      {/* Records list */}
      <div className="md:col-span-3">
        <RecordsList
          records={records as unknown as ExerciseRecord[]}
          loading={loading}
          calculateOneRepMax={calculateOneRepMax}
          getClientName={(client: DatabaseClient | null | undefined) =>
            getClientName(client as unknown as Client | null | undefined)
          }
          showClients={false}
          showExercises={true}
        />
      </div>
    </div>
  ) : (
    <div className="space-y-4">
      <Button
        variant="outline"
        onClick={() => setView('list')}
        className="gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Clients</span>
      </Button>

      {selectedClient && (
        <ClientDetailView
          client={selectedClient}
          records={records.map(record => ({
            id: record.id,
            exercise_id: record.exercise_id,
            weight: record.weight || undefined,
            reps: record.reps || undefined,
            sets: record.sets || undefined,
            date_achieved: record.date_achieved || undefined,
            is_pr: record.is_pr || false,
            notes: record.notes || undefined,
            exercise: record.exercise
          } as DetailRecord))}
          loading={loading}
          calculateOneRepMax={calculateOneRepMax}
          getClientName={(client: Client | null | undefined) =>
            getClientName(client as unknown as Client | null | undefined)
          }
        />
      )}
    </div>
  )
}