'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { format, parseISO } from 'date-fns'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import type { Database } from '@/types/supabase'

type NotificationRow = Database['public']['Tables']['notifications']['Row']
type PelatesRow = Database['public']['Tables']['pelates']['Row']

interface NotificationWithClient extends NotificationRow {
  pelates?: Pick<PelatesRow, 'name' | 'last_name'> | null;
  client_name?: string;
}

type ReadFilter = 'all' | 'read' | 'unread'
type DateFilter = '7days' | '30days' | 'all'
type NotificationType = 'all' | 'payment' | 'booking' | 'injury_report'

const TYPE_LABELS: Record<NotificationType, string> = {
  all: 'All Types',
  payment: 'Payments',
  booking: 'Bookings',
  injury_report: 'Injury Reports'
}

const getBadgeVariant = (type: string | null) => {
  switch (type) {
    case 'payment':
      return 'default'
    case 'booking':
      return 'secondary'
    case 'injury_report':
      return 'destructive'
    default:
      return 'outline'
  }
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<NotificationWithClient[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<NotificationWithClient[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState('')
  const [readFilter, setReadFilter] = useState<ReadFilter>('all')
  const [dateFilter, setDateFilter] = useState<DateFilter>('7days')
  const [typeFilter, setTypeFilter] = useState<NotificationType>('all')

  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const { data, error: fetchError } = await supabase
          .from('notifications')
          .select(`
            *,
            pelates:client_id (
              name,
              last_name
            )
          `)
          .order('created_at', { ascending: false })
    
        if (fetchError) throw fetchError
    
        const transformedNotifications: NotificationWithClient[] = (data || []).map(notification => {
          const pelatesData = notification.pelates as Pick<PelatesRow, 'name' | 'last_name'> | null;
          return {
            ...notification,
            client_name: pelatesData
              ? `${pelatesData.name || ''} ${pelatesData.last_name || ''}`.trim()
              : 'Unknown'
          }
        })
    
        setNotifications(transformedNotifications)
        setFilteredNotifications(transformedNotifications)
      } catch (err) {
        console.error('Error:', err)
        setError('Failed to fetch notifications')
      }
      setLoading(false)
    }

    fetchNotifications()
  }, [supabase])

  useEffect(() => {
    let result = [...notifications]

    // Type filter
    if (typeFilter !== 'all') {
      result = result.filter(notification => notification.type === typeFilter)
    }

    // Search filter
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase()
      result = result.filter(notification => 
        notification.message.toLowerCase().includes(lowerSearchTerm) ||
        (notification.client_name?.toLowerCase() || '').includes(lowerSearchTerm)
      )
    }

    // Read status filter
    if (readFilter === 'read') {
      result = result.filter(notification => notification.read)
    } else if (readFilter === 'unread') {
      result = result.filter(notification => !notification.read)
    }

    // Date filter
    const now = new Date()
    if (dateFilter === '7days') {
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      result = result.filter(notification => 
        new Date(notification.created_at) >= sevenDaysAgo
      )
    } else if (dateFilter === '30days') {
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      result = result.filter(notification => 
        new Date(notification.created_at) >= thirtyDaysAgo
      )
    }

    setFilteredNotifications(result)
  }, [notifications, searchTerm, readFilter, dateFilter, typeFilter])

  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)

      if (error) throw error

      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, read: true } 
            : notification
        )
      )
    } catch (err) {
      console.error('Error marking notification as read:', err)
    }
  }

  const markAllAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .is('read', false)

      if (error) throw error

      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      )
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
    }
  }

  if (loading) {
    return <div className="p-4">Loading notifications...</div>
  }

  if (error) {
    return (
      <Alert variant="destructive" className="m-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  const typeCounts = notifications.reduce((acc, notification) => {
    const type = notification.type as NotificationType || 'all'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<NotificationType, number>)

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">Admin Notifications</h1>
      
      <div className="space-y-4">
        <Tabs 
          value={typeFilter} 
          onValueChange={(value) => setTypeFilter(value as NotificationType)}
          className="w-full"
        >
          <TabsList className="grid grid-cols-4 w-full">
            {Object.entries(TYPE_LABELS).map(([type, label]) => (
              <TabsTrigger key={type} value={type} className="relative">
                {label}
                {typeCounts[type as NotificationType] > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {typeCounts[type as NotificationType]}
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        <div className="flex space-x-2">
          <Input 
            placeholder="Search notifications" 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-grow"
          />
          
          <Select 
            value={readFilter} 
            onValueChange={(value: ReadFilter) => setReadFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Read Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Notifications</SelectItem>
              <SelectItem value="read">Read</SelectItem>
              <SelectItem value="unread">Unread</SelectItem>
            </SelectContent>
          </Select>
          
          <Select 
            value={dateFilter} 
            onValueChange={(value: DateFilter) => setDateFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Date Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 Days</SelectItem>
              <SelectItem value="30days">Last 30 Days</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          
          {filteredNotifications.some(n => !n.read) && (
            <Button 
              variant="outline" 
              onClick={markAllAsRead}
            >
              Mark All as Read
            </Button>
          )}
        </div>
      </div>

      {filteredNotifications.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          No notifications found
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Message</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredNotifications.map(notification => (
              <TableRow key={notification.id}>
                <TableCell>
                  {format(parseISO(notification.created_at), 'dd/MM/yyyy HH:mm')}
                </TableCell>
                <TableCell>
                  <Badge variant={getBadgeVariant(notification.type)}>
                    {TYPE_LABELS[notification.type as NotificationType] || notification.type}
                  </Badge>
                </TableCell>
                <TableCell>{notification.client_name}</TableCell>
                <TableCell>{notification.message}</TableCell>
                <TableCell>
                  {notification.metadata && Object.entries(notification.metadata).map(([key, value]) => (
                    <div key={key} className="text-sm text-muted-foreground">
                      {key}: {typeof value === 'object' ? JSON.stringify(value) : value}
                    </div>
                  ))}
                </TableCell>
                <TableCell>
                  <Badge variant={notification.read ? 'secondary' : 'default'}>
                    {notification.read ? 'Read' : 'Unread'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {!notification.read && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => markAsRead(notification.id)}
                    >
                      Mark Read
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  )
}