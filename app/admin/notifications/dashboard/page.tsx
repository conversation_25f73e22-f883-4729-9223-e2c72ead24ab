// app/admin/notifications/dashboard/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { AdminPushNotificationSettings } from '@/components/admin/AdminPushNotificationSettings';

export default function NotificationDashboard() {
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    types: [] as {type: string, count: number}[]
  });
  const [loading, setLoading] = useState(true);
  const supabase = createClientComponentClient<Database>();

  const fetchStats = async () => {
    setLoading(true);
    try {
      // Get total count
      const { count: totalCount } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true });

      // Get unread count
      const { count: unreadCount } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('read', false);

      // Get types breakdown
      const { data: typesData } = await supabase.rpc('get_notification_type_distribution');

      setStats({
        total: totalCount || 0,
        unread: unreadCount || 0,
        types: typesData?.map(item => ({
          type: item.type || 'unknown',
          count: parseInt(item.count as unknown as string)
        })) || []
      });

    } catch (error) {
      console.error('Error fetching notification stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Notifications Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Total Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">{stats.total}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Unread Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">{stats.unread}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              onClick={fetchStats}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Refreshing...' : 'Refresh Stats'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Notification Types</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.types.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Count</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stats.types.map((item) => (
                    <TableRow key={item.type}>
                      <TableCell>
                      <Badge variant={
  item.type === 'error' ? 'destructive' :
  item.type === 'warning' ? 'outline' : // Changed 'warning' to 'outline'
  'default'
}>
                          {item.type}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">{item.count}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-gray-500 text-center py-6">No notification data available</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Push Notification Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <AdminPushNotificationSettings />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}