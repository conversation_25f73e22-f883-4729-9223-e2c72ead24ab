// app/admin/notifications/subscriptions/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { format, parseISO } from 'date-fns';
import type { Database } from '@/types/supabase';

type Subscription = {
  id: string;
  pelatis_id: string;
  player_id: string;
  enabled: boolean;
  created_at: string;
  updated_at: string;
  pelatis?: {
    name: string | null;
    last_name: string | null;
    email: string | null;
  } | null;
};

export default function NotificationSubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function fetchSubscriptions() {
      try {
        setLoading(true);
        setError(null);
        
        const { data, error } = await supabase
          .from('onesignal_subscriptions')
          .select(`
            *,
            pelatis:pelatis_id (
              name,
              last_name,
              email
            )
          `)
          .order('updated_at', { ascending: false });
        
        if (error) throw error;
        setSubscriptions(data || []);
      } catch (err) {
        console.error('Error fetching subscriptions:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    }
    
    fetchSubscriptions();
  }, [supabase]);

  const handleToggleStatus = async (subscription: Subscription) => {
    try {
      const { error } = await supabase
        .from('onesignal_subscriptions')
        .update({ enabled: !subscription.enabled })
        .eq('id', subscription.id);
      
      if (error) throw error;
      
      // Update local state
      setSubscriptions(prev => prev.map(sub => 
        sub.id === subscription.id 
          ? { ...sub, enabled: !sub.enabled } 
          : sub
      ));
    } catch (err) {
      console.error('Error updating subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to update subscription');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <p className="text-red-500">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">Push Notification Subscriptions</h1>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {subscriptions.length === 0 ? (
              <p>No active subscriptions found.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4">User</th>
                      <th className="text-left py-2 px-4">Subscription Date</th>
                      <th className="text-left py-2 px-4">Last Updated</th>
                      <th className="text-left py-2 px-4">Status</th>
                      <th className="text-left py-2 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {subscriptions.map((sub) => (
                      <tr key={sub.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-4">
                          <div>
                            <p className="font-medium">
                              {sub.pelatis?.name} {sub.pelatis?.last_name}
                            </p>
                            <p className="text-sm text-gray-600">{sub.pelatis?.email}</p>
                          </div>
                        </td>
                        <td className="py-2 px-4">
                          {format(parseISO(sub.created_at), 'dd/MM/yyyy HH:mm')}
                        </td>
                        <td className="py-2 px-4">
                          {format(parseISO(sub.updated_at), 'dd/MM/yyyy HH:mm')}
                        </td>
                        <td className="py-2 px-4">
                          <span className={`px-2 py-1 rounded ${
                            sub.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {sub.enabled ? 'Active' : 'Disabled'}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          <Button
                            variant={sub.enabled ? "destructive" : "default"}
                            size="sm"
                            onClick={() => handleToggleStatus(sub)}
                          >
                            {sub.enabled ? 'Disable' : 'Enable'}
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}