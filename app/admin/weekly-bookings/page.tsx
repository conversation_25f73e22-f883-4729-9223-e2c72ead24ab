"use client";

import { useState, useEffect, useCallback } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, addDays, startOfWeek } from 'date-fns';
import { enGB } from 'date-fns/locale';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Database } from '@/types/supabase';

// Using our new view structure
type BookingForecast = {
  pelatis_id: string;
  name: string | null;
  last_name: string | null;
  subscription_id: string;
  subscription_program_id: string;  // Updated from program_name
  program_name_display: string | null;
  subscription_status: string;
  days_until_expiration: number;
  end_date: string;
  session_id: string;
  program_id: string;
  start_time: string;
  duration: number;
  booking_id: string;
  session_program_name: string;  // Updated from program_name
  // Add this for backward compatibility
  program_name?: string;
};

interface ClientBookings {
  pelatis_id: string;
  name: string | null;
  last_name: string | null;
  subscription_id: string;
  subscription_program_id: string;  // Updated from program_name
  program_name_display: string | null;
  subscription_status: string;
  days_until_expiration: number;
  end_date: string;
  // For backward compatibility
  program_name?: string;
  // Map of date strings to bookings for that date
  weeklyBookings: { [key: string]: BookingForecast[] };
}

export default function FourWeekBookingForecast() {
  const supabase = createClientComponentClient<Database>();
  const [clientBookings, setClientBookings] = useState<ClientBookings[]>([]);
  const [loading, setLoading] = useState(true);
  const [weekDates, setWeekDates] = useState<string[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<string>('all');
  const [programs, setPrograms] = useState<{id: string, name: string}[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<ClientBookings[]>([]);

  // Generate date array for the next 4 weeks from the current Sunday
  const getNextFourWeekDates = useCallback(() => {
    const dates: string[] = [];
    // Start from current Sunday (or today if already Sunday)
    const sunday = startOfWeek(new Date(), { weekStartsOn: 0 });

    // Generate dates for 28 days (4 weeks)
    for (let i = 0; i < 28; i++) {
      const nextDay = addDays(sunday, i);
      dates.push(format(nextDay, 'yyyy-MM-dd'));
    }
    return dates;
  }, []);

  // Fetch all programs for filtering
  const fetchPrograms = useCallback(async () => {
    const { data: programsData, error } = await supabase
      .from('programs')
      .select('id, name');

    if (error) {
      console.error('Error fetching programs:', error);
      return;
    }

    if (programsData) {
      setPrograms(programsData);
    }
  }, [supabase]);

  // Fetch booking forecast data using our new view
  const fetchBookingForecast = useCallback(async () => {
    setLoading(true);
    const dates = getNextFourWeekDates();
    setWeekDates(dates);

    try {
      // Get data from our new view
      // Using type assertion to bypass type checking since the view is not in the generated types
      const { data, error } = await supabase
        .from('weekly_booking_forecast' as unknown as keyof Database['public']['Tables'])
        .select('*');

      if (error) {
        console.error('Error fetching booking forecast:', error);
        setLoading(false);
        return;
      }

      if (data) {
        // Group by client
        const clientMap = new Map<string, ClientBookings>();

        // Cast data to BookingForecast[] to handle type issues
        (data as unknown as BookingForecast[]).forEach((booking) => {
          if (!booking.pelatis_id || !booking.start_time) return;

          // Get or create client entry
          if (!clientMap.has(booking.pelatis_id)) {
            clientMap.set(booking.pelatis_id, {
              pelatis_id: booking.pelatis_id,
              name: booking.name,
              last_name: booking.last_name,
              subscription_id: booking.subscription_id,
              subscription_program_id: booking.subscription_program_id,
              program_name: booking.program_name,
              program_name_display: booking.program_name_display,
              subscription_status: booking.subscription_status,
              days_until_expiration: booking.days_until_expiration,
              end_date: booking.end_date,
              weeklyBookings: {}
            });

            // Initialize all dates with empty arrays
            dates.forEach(date => {
              clientMap.get(booking.pelatis_id)!.weeklyBookings[date] = [];
            });
          }

          // Add booking to the appropriate date
          const bookingDate = format(new Date(booking.start_time), 'yyyy-MM-dd');
          if (dates.includes(bookingDate)) {
            clientMap.get(booking.pelatis_id)!.weeklyBookings[bookingDate].push(booking);
          }
        });

        const clientBookingsArray = Array.from(clientMap.values());
        setClientBookings(clientBookingsArray);
        setFilteredBookings(clientBookingsArray);
      }
    } catch (error) {
      console.error('Error processing booking data:', error);
    } finally {
      setLoading(false);
    }
  }, [supabase, getNextFourWeekDates]);

  // Apply program filter
  useEffect(() => {
    if (selectedProgram === 'all') {
      setFilteredBookings(clientBookings);
    } else {
      setFilteredBookings(
        clientBookings.filter(client => client.program_name === selectedProgram)
      );
    }
  }, [selectedProgram, clientBookings]);

  // Initial data fetch
  useEffect(() => {
    fetchPrograms();
    fetchBookingForecast();
  }, [fetchPrograms, fetchBookingForecast]);

  // Format dates for display (e.g., "Mon 19/5")
  const formatDateHeader = (dateStr: string) => {
    const date = new Date(dateStr);
    return format(date, 'EEE d/M', { locale: enGB });
  };

  // Group dates into weeks for better display
  const weeklyDates = weekDates.reduce<string[][]>((weeks, date, index) => {
    const weekIndex = Math.floor(index / 7);
    if (!weeks[weekIndex]) weeks[weekIndex] = [];
    weeks[weekIndex].push(date);
    return weeks;
  }, []);

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>4-Week Booking Forecast</CardTitle>
          <Select value={selectedProgram} onValueChange={setSelectedProgram}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by Program" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Programs</SelectItem>
              {programs.map(program => (
                <SelectItem key={program.id} value={program.id}>
                  {program.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div>Loading booking forecast...</div>
          ) : filteredBookings.length === 0 ? (
            <div>No active subscriptions with bookings found.</div>
          ) : (
            // Display week by week
            weeklyDates.map((weekDays, weekIndex) => (
              <div key={weekIndex} className="mb-8">
                <h3 className="text-lg font-medium mb-4">Week {weekIndex + 1}</h3>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Days Left</TableHead>
                        {weekDays.map(date => (
                          <TableHead key={date}>
                            {formatDateHeader(date)}
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBookings.map(client => (
                        <TableRow key={`${client.pelatis_id}-week-${weekIndex}`}>
                          <TableCell>{`${client.name || ''} ${client.last_name || ''}`}</TableCell>
                          <TableCell>
                            <span className={
                              client.subscription_status === 'Active' ? 'text-green-600' :
                              client.subscription_status === 'In Grace Period' ? 'text-yellow-600' :
                              'text-red-600'
                            }>
                              {client.subscription_status}
                            </span>
                          </TableCell>
                          <TableCell>{client.days_until_expiration}</TableCell>

                          {weekDays.map(date => (
                            <TableCell key={`${client.pelatis_id}-${date}`}>
                              {client.weeklyBookings[date]?.length > 0 ? (
                                client.weeklyBookings[date].map((booking, idx) => (
                                  <div key={idx} className="text-sm">
                                    {booking.program_name_display || booking.program_name}
                                    <span className="ml-1 text-xs text-gray-500">
                                      ({format(new Date(booking.start_time), 'HH:mm')})
                                    </span>
                                  </div>
                                ))
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
}