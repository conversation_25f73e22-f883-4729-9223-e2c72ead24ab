'use client'

import { use<PERSON><PERSON>back, useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/supabase'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from '@/components/ui/button'
import { format, parseISO, differenceInMonths, isBefore, subMonths, startOfMonth, endOfMonth, isAfter } from 'date-fns'
import { Users, Calendar, Clock, BarChart, TrendingUp, TrendingDown } from 'lucide-react'
import { MemberData, MonthlyMembersData } from '@/types/new-members-report'
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  Cartesian<PERSON>rid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'

export default function NewMembersReportPage() {
  const [timeRange, setTimeRange] = useState<'6' | '12' | '24' | 'all'>('12')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [monthlyMembers, setMonthlyMembers] = useState<MonthlyMembersData[]>([])
  const [filteredMembers, setFilteredMembers] = useState<MonthlyMembersData[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const supabase = createClientComponentClient<Database>()

  const fetchMembersData = useCallback(async () => {
    try {
      setIsLoading(true)

      // Calculate the start date based on the selected time range
      const today = new Date()
      let startDate = today

      if (timeRange !== 'all') {
        startDate = subMonths(today, parseInt(timeRange))
      } else {
        // For 'all', go back 5 years as a reasonable default
        startDate = subMonths(today, 60)
      }

      // Fetch members with their join dates (start_program from pliromes) and creation dates from pelates
      const { data, error } = await supabase
        .from('pliromes')
        .select(`
          id,
          pelates_id,
          start_program,
          end_date,
          pelates (
            id,
            name,
            last_name,
            client_name,
            created_at
          )
        `)
        .gte('start_program', startDate.toISOString())
        .order('start_program', { ascending: false })

      if (error) {
        console.error('Error fetching members data:', error)
        return
      }

      // Process the data to group by month
      const membersByMonth: Record<string, MemberData[]> = {}
      // Track unique members by pelates_id to avoid duplicates
      const processedMembersByMonth: Record<string, Set<string>> = {}

      data.forEach(record => {
        if (!record.start_program || !record.pelates || !record.pelates.created_at || !record.pelates_id) return;
        
        // Use pelates creation date as the primary date for grouping
        const creationDate = new Date(record.pelates.created_at)
        const monthKey = format(creationDate, 'yyyy-MM')
        const displayName = record.pelates.client_name ||
          `${record.pelates.name || ''} ${record.pelates.last_name || ''}`.trim()

        // Skip if we've already processed this member for this month
        if (!processedMembersByMonth[monthKey]) {
          processedMembersByMonth[monthKey] = new Set<string>()
        }

        // Use pelates_id to track unique members - TypeScript now knows it's non-null
        if (processedMembersByMonth[monthKey].has(record.pelates_id)) {
          return // Skip duplicate members in the same month
        }

        // Mark this member as processed for this month
        processedMembersByMonth[monthKey].add(record.pelates_id)

        // Calculate membership duration in months based on payment start date
        const joinDate = new Date(record.start_program)
        const endDate = record.end_date ? new Date(record.end_date) : new Date()
        const durationMonths = differenceInMonths(endDate, joinDate)
        const isActive = !record.end_date || isBefore(today, new Date(record.end_date))

        if (!membersByMonth[monthKey]) {
          membersByMonth[monthKey] = []
        }

        membersByMonth[monthKey].push({
          id: record.id,
          name: displayName,
          joinDate: record.start_program,
          creationDate: record.pelates.created_at,
          membershipDuration: durationMonths,
          isActive
        })
      })

      // Convert to array and sort by month
      const monthlyData = Object.entries(membersByMonth).map(([month, members]) => ({
        month,
        members
      })).sort((a, b) => b.month.localeCompare(a.month)) // Sort newest first

      setMonthlyMembers(monthlyData)
    } catch (error) {
      console.error('Error in fetchMembersData:', error)
    } finally {
      setIsLoading(false)
    }
  }, [supabase, timeRange])

  // Apply filters to the data
  useEffect(() => {
    if (statusFilter === 'all') {
      setFilteredMembers(monthlyMembers)
      return
    }

    const filtered = monthlyMembers.map(monthData => {
      const filteredMembers = monthData.members.filter(member =>
        statusFilter === 'active' ? member.isActive : !member.isActive
      )

      return {
        ...monthData,
        members: filteredMembers
      }
    }).filter(monthData => monthData.members.length > 0) // Remove empty months

    setFilteredMembers(filtered)
  }, [monthlyMembers, statusFilter])

  useEffect(() => {
    fetchMembersData()
  }, [fetchMembersData])

  // Format helpers
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM d, yyyy')
    } catch (e) {
      return 'N/A'
    }
  }

  const formatMonthDisplay = (monthKey: string) => {
    try {
      return format(parseISO(`${monthKey}-01`), 'MMMM yyyy')
    } catch (e) {
      return monthKey
    }
  }

  // Calculate current and previous period sign-ups
  const today = new Date()
  const currentMonthStart = startOfMonth(today)
  const currentMonthEnd = endOfMonth(today)
  const previousMonthStart = startOfMonth(subMonths(today, 1))
  const previousMonthEnd = endOfMonth(subMonths(today, 1))

  // Check if we're past the 20th of the month (as per your requirements)
  const isPast20th = today.getDate() >= 20

  // Determine which periods to use based on the day of month
  const currentPeriodStart = isPast20th ? currentMonthStart : previousMonthStart
  const currentPeriodEnd = isPast20th ? currentMonthEnd : previousMonthEnd
  const previousPeriodStart = isPast20th ? previousMonthStart : startOfMonth(subMonths(today, 2))
  const previousPeriodEnd = isPast20th ? previousMonthEnd : endOfMonth(subMonths(today, 2))

  // Count unique members in current and previous periods
  // We need to track unique member IDs to avoid counting the same member twice
  const currentPeriodMemberIds = new Set<string>()
  const previousPeriodMemberIds = new Set<string>()

  // Process all members and add them to the appropriate period set
  monthlyMembers.forEach(monthData => {
    const monthDate = parseISO(`${monthData.month}-01`)
    const isCurrentPeriod = (
      (isAfter(monthDate, currentPeriodStart) || format(monthDate, 'yyyy-MM') === format(currentPeriodStart, 'yyyy-MM')) &&
      (isBefore(monthDate, currentPeriodEnd) || format(monthDate, 'yyyy-MM') === format(currentPeriodEnd, 'yyyy-MM'))
    )

    const isPreviousPeriod = (
      (isAfter(monthDate, previousPeriodStart) || format(monthDate, 'yyyy-MM') === format(previousPeriodStart, 'yyyy-MM')) &&
      (isBefore(monthDate, previousPeriodEnd) || format(monthDate, 'yyyy-MM') === format(previousPeriodEnd, 'yyyy-MM'))
    )

    // Add unique member IDs to the appropriate period set based on creation date
    monthData.members.forEach(member => {
      const creationDate = parseISO(member.creationDate)

      // Check if the creation date falls within the current period
      const isCreationInCurrentPeriod = (
        (isAfter(creationDate, currentPeriodStart) || format(creationDate, 'yyyy-MM-dd') === format(currentPeriodStart, 'yyyy-MM-dd')) &&
        (isBefore(creationDate, currentPeriodEnd) || format(creationDate, 'yyyy-MM-dd') === format(currentPeriodEnd, 'yyyy-MM-dd'))
      )

      // Check if the creation date falls within the previous period
      const isCreationInPreviousPeriod = (
        (isAfter(creationDate, previousPeriodStart) || format(creationDate, 'yyyy-MM-dd') === format(previousPeriodStart, 'yyyy-MM-dd')) &&
        (isBefore(creationDate, previousPeriodEnd) || format(creationDate, 'yyyy-MM-dd') === format(previousPeriodEnd, 'yyyy-MM-dd'))
      )

      if (isCurrentPeriod && isCreationInCurrentPeriod) {
        currentPeriodMemberIds.add(member.id)
      }
      if (isPreviousPeriod && isCreationInPreviousPeriod) {
        previousPeriodMemberIds.add(member.id)
      }
    })
  })

  // Get the count of unique members in each period
  const currentPeriodSignups = currentPeriodMemberIds.size
  const previousPeriodSignups = previousPeriodMemberIds.size

  // Calculate percentage change
  const signupPercentChange = previousPeriodSignups > 0
    ? ((currentPeriodSignups - previousPeriodSignups) / previousPeriodSignups) * 100
    : 0

  // Prepare chart data based on pelates creation date
  const chartData = monthlyMembers
    .slice() // Create a copy to avoid mutating the original
    .sort((a, b) => a.month.localeCompare(b.month)) // Sort by month (oldest first for chart)
    .map(monthData => ({
      month: format(parseISO(`${monthData.month}-01`), 'MMM yyyy'),
      newMembers: monthData.members.length,
      activeMembers: monthData.members.filter(m => m.isActive).length
    }))

  // Calculate totals
  const totalNewMembers = monthlyMembers.reduce((sum, month) => sum + month.members.length, 0)
  const totalActiveMembers = monthlyMembers.reduce(
    (sum, month) => sum + month.members.filter(m => m.isActive).length,
    0
  )
  const retentionRate = totalNewMembers > 0
    ? (totalActiveMembers / totalNewMembers) * 100
    : 0

  // Get the data to display based on filters
  const displayData = filteredMembers || monthlyMembers

  // Format the current and previous period names
  const currentPeriodName = format(currentPeriodStart, 'MMMM yyyy')
  const previousPeriodName = format(previousPeriodStart, 'MMMM yyyy')

  return (
    <div className="p-4 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">New Members Report</h1>

        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={timeRange} onValueChange={(value: '6' | '12' | '24' | 'all') => setTimeRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6">Last 6 Months</SelectItem>
              <SelectItem value="12">Last 12 Months</SelectItem>
              <SelectItem value="24">Last 24 Months</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={(value: 'all' | 'active' | 'inactive') => setStatusFilter(value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Members</SelectItem>
              <SelectItem value="active">Active Only</SelectItem>
              <SelectItem value="inactive">Inactive Only</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={() => fetchMembersData()}>
            Refresh
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <p>Loading members data...</p>
        </div>
      ) : displayData.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">No member data available for the selected filters.</p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* KPI Card */}
          <Card className="bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-500" />
                      <h3 className="text-lg font-medium">Unique New Sign-ups (By Account Creation)</h3>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      {signupPercentChange > 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                      <span className={signupPercentChange > 0 ? "text-green-500" : "text-red-500"}>
                        {Math.abs(signupPercentChange).toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  <div className="mt-2">
                    <div className="text-3xl font-bold">{currentPeriodSignups}</div>
                    <div className="text-sm text-gray-500">
                      Total Sign-ups in {currentPeriodName}
                    </div>
                  </div>

                  <div className="mt-4 text-sm">
                    <div className="flex justify-between">
                      <span>Previous Period ({previousPeriodName}):</span>
                      <span className="font-medium">{previousPeriodSignups} members</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>Change:</span>
                      <span className="font-medium">
                        {signupPercentChange > 0 ? "+" : ""}
                        {signupPercentChange.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>Calculation:</span>
                      <span className="font-medium">
                        ({currentPeriodSignups} - {previousPeriodSignups}) ÷ {previousPeriodSignups} × 100
                      </span>
                    </div>

                    <div className="mt-4 p-3 bg-gray-50 rounded-md">
                      <p className="text-xs text-gray-500 mb-2">
                        <strong>Note:</strong> After the 20th of each month, this KPI shows the current month&apos;s data.
                        Before the 20th, it shows the previous month&apos;s data to ensure complete reporting periods.
                      </p>
                      <p className="text-xs text-gray-500 mb-2">
                        <strong>Calculation Method:</strong> New members are calculated based on the pelates (client) creation date,
                        not the payment start date. Only unique members are counted per month to avoid duplicates.
                        This provides a more accurate view of when members actually joined.
                      </p>
                      <p className="text-xs text-gray-500 font-medium mb-1">Best practices:</p>
                      <ul className="text-xs text-gray-500 list-disc pl-4 space-y-1">
                        <li>Track acquisition sources to identify effective marketing channels</li>
                        <li>Compare against acquisition costs to calculate ROI</li>
                        <li>Set monthly targets based on business growth goals</li>
                        <li>Analyze seasonal patterns to optimize marketing efforts</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Summary Chart Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="h-5 w-5" />
                Unique New Members Summary (By Account Creation Date)
              </CardTitle>
              <CardDescription>
                Total of {totalNewMembers} unique new members, {totalActiveMembers} still active ({retentionRate.toFixed(1)}% retention)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      angle={-45}
                      textAnchor="end"
                      height={60}
                    />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="newMembers" name="New Members" fill="#8884d8" />
                    <Bar dataKey="activeMembers" name="Still Active" fill="#82ca9d" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-6">
            {displayData.map(monthData => (
              <Card key={monthData.month}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {formatMonthDisplay(monthData.month)}
                  </CardTitle>
                  <CardDescription>
                    {monthData.members.length} unique new member{monthData.members.length !== 1 ? 's' : ''} (based on account creation date)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Account Created</TableHead>
                        <TableHead>Payment Start</TableHead>
                        <TableHead>Membership Duration</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {monthData.members.map(member => (
                        <TableRow key={member.id}>
                          <TableCell className="font-medium">{member.name}</TableCell>
                          <TableCell>{formatDate(member.creationDate)}</TableCell>
                          <TableCell>{formatDate(member.joinDate)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              {member.membershipDuration} month{member.membershipDuration !== 1 ? 's' : ''}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              member.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {member.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  )
}











