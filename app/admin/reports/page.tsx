import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart2, TrendingUp, Users } from 'lucide-react'

export default function ReportsIndexPage() {
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Business Intelligence Reports</CardTitle>
          <CardDescription>
            Comprehensive analytics tools to make data-driven decisions for your gym business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              Our reporting suite provides powerful insights into your gym&apos;s performance across all key areas.
              Select a report category from the options above to dive deeper into specific metrics.
            </p>
            
            <h3 className="font-semibold mt-4">Key Features</h3>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Real-time business performance metrics</li>
              <li>Member acquisition and retention analytics</li>
              <li>Program and class utilization analysis</li>
              <li>Financial health and profitability tracking</li>
              <li>Interactive visualizations for informed decision-making</li>
            </ul>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Recommended Action
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">Review Retention Metrics</div>
            <p className="text-xs text-muted-foreground mt-1">
              Focus on improving member retention which has declined 2.3% since last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Top Performing Program
            </CardTitle>
            <BarChart2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">CrossFit Classes</div>
            <p className="text-xs text-muted-foreground mt-1">
              Generated 62% of total revenue this month with 87% capacity utilization
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Growth Opportunity
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">Evening Sessions</div>
            <p className="text-xs text-muted-foreground mt-1">
              Adding 2 evening classes could generate an estimated €2,800 in monthly revenue
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
