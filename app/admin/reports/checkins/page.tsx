'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import ClientSelector from '@/components/ClientSelector'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CalendarIcon, ArrowUpDown } from 'lucide-react'
import { format } from 'date-fns'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { toast } from '@/hooks/use-toast'
import type { Database, Json } from '@/types/supabase'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'

type Tables = Database['public']['Tables']

// Essential type definitions
interface FullClientOption {
  value: string
  label: string
  pelatis_id: string
  session_id: string
  subscription?: {
    days_until_expiration: number | null
    end_date: string | null
    subscription_status: string | null
    program_name_display: string | null
  }
}

type Session = Tables['sessions']['Row'] & {
  programs: Tables['programs']['Row']
}

type CheckIn = Tables['check_ins']['Row'] & {
  pelates: Tables['pelates']['Row'] | null
  sessions: (Tables['sessions']['Row'] & {
    programs: Tables['programs']['Row']
  }) | null
}

interface MonthlyCount {
  month: string
  count: number
}

interface MemberEngagementData {
  user_id: string
  name: string | null
  last_name: string | null
  total_checkins: number
  monthly_breakdown: MonthlyCount[]
  last_active_month?: string
  is_active_current_month?: boolean
  avg: number
  active_months: number
  status: string
  days_until_expiration?: number
  program_name_display?: string
}

export default function CheckInsPage() {
  const [clients, setClients] = useState<FullClientOption[]>([])
  const [sessions, setSessions] = useState<Session[]>([])
  const [selectedClients, setSelectedClients] = useState<FullClientOption[]>([])
  const [selectedSession, setSelectedSession] = useState<string>('')
  const [todayCheckIns, setTodayCheckIns] = useState<CheckIn[]>([])
  const [memberEngagementData, setMemberEngagementData] = useState<MemberEngagementData[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [activeTab, setActiveTab] = useState<string>('daily')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<string>('total_checkins')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const supabase = createClientComponentClient<Database>()

  // Get current month and previous month for filtering
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = `${currentYear}-${String(now.getMonth() + 1).padStart(2, '0')}`;

  // These are the specific month keys we want to display (May and April 2025)
  const mayKey = `2025-05`; // Format: YYYY-MM
  const aprilKey = `2025-04`; // Format: YYYY-MM

  const fetchSessions = useCallback(async (date: Date) => {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        programs (*)
      `)
      .gte('start_time', startOfDay.toISOString())
      .lt('start_time', endOfDay.toISOString())
      .order('start_time')

    if (error) throw error
    return data || []
  }, [supabase])

  const fetchTodayCheckIns = useCallback(async () => {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const { data, error } = await supabase
      .from('check_ins')
      .select(`
        *,
        pelates (*),
        sessions (
          *,
          programs (*)
        )
      `)
      .gte('check_in_time', startOfDay.toISOString())
      .lt('check_in_time', endOfDay.toISOString())
      .order('check_in_time')

    if (error) throw error
    return data || []
  }, [supabase])

  const fetchMemberEngagementData = useCallback(async () => {
    try {
      // Use the user_checkin_stats view which has pre-calculated monthly data
      const { data: userCheckInStats, error: statsError } = await supabase
        .from('user_checkin_stats')
        .select('*')

      if (statsError) {
        console.error('Error fetching user check-in stats:', statsError)
        throw statsError
      }

      // Fetch subscription status from active_subscriptions
      const { data: activeSubscriptions, error: subsError } = await supabase
        .from('active_subscriptions')
        .select('client_id, subscription_status, days_until_expiration, program_name_display')

      if (subsError) {
        console.error('Error fetching subscription data:', subsError)
      }

      // Create a map of subscription data by client_id
      const subscriptionMap = (activeSubscriptions || []).reduce((acc, sub) => {
        if (sub.client_id) {
          acc[sub.client_id] = {
            subscription_status: sub.subscription_status || 'Inactive',
            days_until_expiration: sub.days_until_expiration || 0,
            program_name_display: sub.program_name_display || ''
          };
        }
        return acc;
      }, {} as Record<string, { subscription_status: string, days_until_expiration: number, program_name_display: string }>);

      // Process the data into our format
      const processedStats: MemberEngagementData[] = userCheckInStats.map(stat => {
        // Parse the monthly_breakdown JSON if it's a string
        let monthlyBreakdown: MonthlyCount[] = [];

        try {
          if (typeof stat.monthly_breakdown === 'string') {
            monthlyBreakdown = JSON.parse(stat.monthly_breakdown) as MonthlyCount[];
          } else if (Array.isArray(stat.monthly_breakdown)) {
            // Map each item to ensure it has the correct shape
            monthlyBreakdown = stat.monthly_breakdown.map(item => {
              if (typeof item === 'object' && item !== null) {
                const jsonItem = item as Record<string, Json>;
                return {
                  month: String(jsonItem.month || ''),
                  count: typeof jsonItem.count === 'number' ? jsonItem.count : 0
                };
              }
              return { month: '', count: 0 };
            });
          } else if (stat.monthly_breakdown && typeof stat.monthly_breakdown === 'object') {
            // If it's an object with month keys, convert to array
            monthlyBreakdown = Object.entries(stat.monthly_breakdown).map(([month, count]) => ({
              month,
              count: typeof count === 'number' ? count : 0
            }));
          }
        } catch (e) {
          console.error('Error parsing monthly breakdown:', e);
        }

        // Sort monthly breakdown by date (newest first)
        monthlyBreakdown.sort((a, b) => b.month.localeCompare(a.month));

        // Calculate active months (months with at least one check-in)
        const activeMonths = monthlyBreakdown.filter(m => m.count > 0).length;

        // Calculate average check-ins per active month
        const totalCheckins = monthlyBreakdown.reduce((sum, m) => sum + m.count, 0);
        const avg = activeMonths > 0 ? Number((totalCheckins / activeMonths).toFixed(2)) : 0;

        // Check if active in current month
        const isActiveCurrentMonth = monthlyBreakdown.some(m => m.month === currentMonth && m.count > 0);

        // Find last active month
        const lastActiveMonth = monthlyBreakdown.find(m => m.count > 0)?.month;

        // Get subscription data
        const subscription = subscriptionMap[stat.pelatis_id || ''] || {
          subscription_status: 'Inactive',
          days_until_expiration: 0,
          program_name_display: ''
        };

        return {
          user_id: stat.pelatis_id || '',
          name: stat.name || '',
          last_name: stat.last_name || '',
          total_checkins: stat.total_checkins || 0,
          monthly_breakdown: monthlyBreakdown,
          avg: avg,
          active_months: activeMonths,
          is_active_current_month: isActiveCurrentMonth,
          last_active_month: lastActiveMonth,
          status: subscription.subscription_status,
          days_until_expiration: subscription.days_until_expiration,
          program_name_display: subscription.program_name_display
        };
      });

      // Sort by total check-ins by default
      processedStats.sort((a, b) => b.total_checkins - a.total_checkins);

      console.log('Processed member engagement data:', processedStats);
      setMemberEngagementData(processedStats);

    } catch (error) {
      console.error('Error fetching member engagement data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load member engagement data',
        variant: 'destructive',
      });
    }
  }, [supabase, currentMonth]);

  const fetchClients = useCallback(async (userId: string) => {
    // Check if user is admin
    const { data: userRoles, error: rolesError } = await supabase.rpc('getUserRoles', {
      p_user_id: userId
    })
    if (rolesError) throw rolesError
    const isAdmin = Array.isArray(userRoles) && userRoles.includes('admin')

    let query = supabase
      .from('pelates')
      .select(`
        id,
        name,
        last_name,
        auth_user_id
      `)

    // If not admin, only fetch the user's own record
    if (!isAdmin) {
      query = query.eq('auth_user_id', userId)
    }

    const { data, error } = await query
    if (error) throw error

    return data.map(pelate => ({
      value: pelate.id,
      label: `${pelate.last_name}, ${pelate.name}`,
      pelatis_id: pelate.id,
      session_id: '',
    }))
  }, [supabase])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        if (userError) throw userError
        if (!user?.id) {
          console.error('No user ID found')
          return
        }

        // Fetch all data concurrently with the selected date
        const [sessionsData, checkInsData, clientsData] = await Promise.all([
          fetchSessions(selectedDate),
          fetchTodayCheckIns(),
          fetchClients(user.id)
        ])

        setSessions(sessionsData)
        setTodayCheckIns(checkInsData)
        setClients(clientsData)

        // Fetch the member engagement data
        await fetchMemberEngagementData()

      } catch (error) {
        console.error('Error fetching data:', error)
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to load data',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [supabase, selectedDate, fetchSessions, fetchTodayCheckIns, fetchClients, fetchMemberEngagementData])

  // Handler for date change
  const handleDateChange = async (date: Date | undefined) => {
    if (!date) return
    setSelectedDate(date)
    setSelectedSession('') // Reset session selection when date changes

    try {
      const sessionsData = await fetchSessions(date)
      setSessions(sessionsData)
    } catch (error) {
      console.error('Error fetching sessions:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sessions for selected date',
        variant: 'destructive',
      })
    }
  }

  const handleCreateCheckIn = async () => {
    if (!selectedClients.length || !selectedSession) {
      toast({
        title: 'Error',
        description: 'Please select both a client and a session.',
        variant: 'destructive',
      })
      return
    }

    try {
      // First verify if user is admin or regular user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError) throw userError
      if (!user?.id) throw new Error('No authenticated user found')

      const { data: userRoles, error: rolesError } = await supabase.rpc('getUserRoles', {
        p_user_id: user.id
      })
      if (rolesError) throw rolesError

      const isAdmin = Array.isArray(userRoles) && userRoles.includes('admin')

      // For regular users, we need to verify they are creating a check-in for themselves
      if (!isAdmin) {
        // Get the user's pelates record
        const { data: pelatesData, error: pelatesError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', user.id)
          .single()

        if (pelatesError) throw pelatesError
        if (!pelatesData) throw new Error('No pelates record found for user')

        // Create check-in with verified pelatis_id
        const { data: newCheckIn, error: insertError } = await supabase
          .from('check_ins')
          .insert({
            pelatis_id: pelatesData.id,
            session_id: selectedSession,
          })
          .select(`
            *,
            pelates (*),
            sessions (
              *,
              programs (*)
            )
          `)
          .single()

        if (insertError) throw insertError
        if (!newCheckIn) throw new Error('Failed to create check-in')

        setTodayCheckIns(current => [...current, newCheckIn])
      } else {
        // Admin flow - can create check-ins for any user
        const { data: newCheckIn, error: insertError } = await supabase
          .from('check_ins')
          .insert({
            pelatis_id: selectedClients[0].pelatis_id,
            session_id: selectedSession,
          })
          .select(`
            *,
            pelates (*),
            sessions (
              *,
              programs (*)
            )
          `)
          .single()

        if (insertError) throw insertError
        if (!newCheckIn) throw new Error('Failed to create check-in')

        setTodayCheckIns(current => [...current, newCheckIn])
      }

      setSelectedClients([])
      setSelectedSession('')

      // Refresh the member engagement data to include the new check-in
      await fetchMemberEngagementData()

      toast({
        title: 'Success',
        description: 'Check-in created successfully.',
      })
    } catch (error) {
      console.error('Error creating check-in:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create check-in',
        variant: 'destructive',
      })
    }
  }

  const handleDeleteCheckIn = async (id: string) => {
    try {
      const { error } = await supabase
        .from('check_ins')
        .delete()
        .eq('id', id)

      if (error) throw error

      setTodayCheckIns(current => current.filter(checkIn => checkIn.id !== id))

      // Refresh member engagement data after deletion
      await fetchMemberEngagementData()

      toast({
        title: 'Success',
        description: 'Check-in deleted successfully.',
      })
    } catch (error) {
      console.error('Error deleting check-in:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete check-in. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Format month display in a user-friendly way
  const formatMonthDisplay = (monthStr: string) => {
    try {
      const [year, month] = monthStr.split('-');
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthIndex = parseInt(month, 10) - 1;
      return `${monthNames[monthIndex]} ${year}`;
    } catch (e) {
      return monthStr;
    }
  };

  // Sorting function for the table
  const sortData = (field: string) => {
    const newDirection = sortField === field && sortDirection === 'desc' ? 'asc' : 'desc';
    setSortDirection(newDirection);
    setSortField(field);

    const sortedData = [...memberEngagementData].sort((a, b) => {
      if (field === 'name') {
        const nameA = `${a.name} ${a.last_name}`.toLowerCase();
        const nameB = `${b.name} ${b.last_name}`.toLowerCase();
        return newDirection === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
      }

      if (field === 'active_months') {
        return newDirection === 'asc' ?
          a.active_months - b.active_months :
          b.active_months - a.active_months;
      }

      if (field === 'avg') {
        return newDirection === 'asc' ?
          a.avg - b.avg :
          b.avg - a.avg;
      }

      if (field === 'status') {
        // Sort by active status (active first)
        const statusA = a.status === 'active' ? 1 : 0;
        const statusB = b.status === 'active' ? 1 : 0;

        if (statusA !== statusB) {
          return newDirection === 'asc' ? statusA - statusB : statusB - statusA;
        }

        // If statuses are the same, sort by last active month
        return newDirection === 'asc' ?
          (a.last_active_month || '').localeCompare(b.last_active_month || '') :
          (b.last_active_month || '').localeCompare(a.last_active_month || '');
      }

      // For May and April columns
      if (field === 'mayCheckins') {
        const countA = a.monthly_breakdown.find(m => m.month === mayKey)?.count || 0;
        const countB = b.monthly_breakdown.find(m => m.month === mayKey)?.count || 0;
        return newDirection === 'asc' ? countA - countB : countB - countA;
      }

      if (field === 'aprilCheckins') {
        const countA = a.monthly_breakdown.find(m => m.month === aprilKey)?.count || 0;
        const countB = b.monthly_breakdown.find(m => m.month === aprilKey)?.count || 0;
        return newDirection === 'asc' ? countA - countB : countB - countA;
      }

      // Default case for numeric fields
      return newDirection === 'asc' ?
        (a[field as keyof MemberEngagementData] as number) - (b[field as keyof MemberEngagementData] as number) :
        (b[field as keyof MemberEngagementData] as number) - (a[field as keyof MemberEngagementData] as number);
    });

    setMemberEngagementData(sortedData);
  };

  // Filter members based on search term
  const filteredMemberData = memberEngagementData.filter(member =>
    `${member.name} ${member.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <div className="p-4">Loading...</div>
  }

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <Tabs defaultValue="daily" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="daily">Daily Check-ins</TabsTrigger>
          <TabsTrigger value="monthly">Member Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="daily">
          <Card>
            <CardHeader>
              <CardTitle>Manage Daily Check-ins</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <ClientSelector
                  clients={clients}
                  selectedClients={selectedClients}
                  onChange={setSelectedClients}
                />

                <div className="grid gap-2">
                  <Label>Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {format(selectedDate, 'PPP')}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={handleDateChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="session">Session</Label>
                  <Select
                    value={selectedSession}
                    onValueChange={setSelectedSession}
                  >
                    <SelectTrigger id="session">
                      <SelectValue placeholder="Select session" />
                    </SelectTrigger>
                    <SelectContent>
                      {sessions.length === 0 ? (
                        <SelectItem value="no-sessions" disabled>
                          No sessions available for this date
                        </SelectItem>
                      ) : (
                        sessions.map((session) => (
                          <SelectItem key={session.id} value={session.id}>
                            {format(new Date(session.start_time), 'HH:mm')} - {session.programs.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  onClick={handleCreateCheckIn}
                  disabled={!selectedClients.length || !selectedSession || selectedSession === 'no-sessions'}
                >
                  Create Check-in
                </Button>
              </div>

              <div className="mt-8">
                <h3 className="font-semibold mb-4">Today&apos;s Check-ins</h3>
                <div className="grid gap-4">
                  {todayCheckIns.map((checkIn) => (
                    <div
                      key={checkIn.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">
                          {checkIn.pelates && `${checkIn.pelates.last_name}, ${checkIn.pelates.name}`}
                        </p>
                        <p className="text-sm text-gray-500">
                          {checkIn.sessions && (
                            <>
                              {new Date(checkIn.sessions.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -
                              {checkIn.sessions.programs.name}
                            </>
                          )}
                        </p>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteCheckIn(checkIn.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  ))}
                  {todayCheckIns.length === 0 && (
                    <p className="text-gray-500 text-center">No check-ins for today</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Member Engagement - Check-ins Per User</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Check-in data for the current and previous month. Subscription status is shown in the Status column.
                  </p>
                </div>
                <div className="w-72">
                  <Input
                    placeholder="Search user..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('name')}>
                          Name <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('total_checkins')}>
                          Total <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('avg')}>
                          Avg <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('active_months')}>
                          Active Months <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('status')}>
                          Status <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('mayCheckins')}>
                          May 2025 <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" onClick={() => sortData('aprilCheckins')}>
                          Apr 2025 <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMemberData.length > 0 ? (
                      filteredMemberData.map((member) => (
                        <TableRow key={member.user_id}>
                          <TableCell className="font-medium">{member.last_name}, {member.name}</TableCell>
                          <TableCell>{member.total_checkins}</TableCell>
                          <TableCell>{member.avg}</TableCell>
                          <TableCell>{member.active_months}</TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                                {member.status === 'active' ? 'Active' : 'Inactive'}
                              </span>
                              {member.status === 'active' && member.days_until_expiration !== undefined && (
                                <span className="text-xs text-gray-500 mt-1">
                                  {member.days_until_expiration > 0
                                    ? `Expires in ${member.days_until_expiration} days`
                                    : 'Expired'}
                                </span>
                              )}
                              {member.status !== 'active' && member.last_active_month && (
                                <span className="text-xs text-gray-500 mt-1">
                                  Last: {formatMonthDisplay(member.last_active_month)}
                                </span>
                              )}
                              {member.program_name_display && (
                                <span className="text-xs text-gray-500 mt-1">{member.program_name_display}</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{member.monthly_breakdown.find(m => m.month === mayKey)?.count || 0}</TableCell>
                          <TableCell>{member.monthly_breakdown.find(m => m.month === aprilKey)?.count || 0}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          No members found matching your search
                        </TableCell>
                      </TableRow>
                    )}
                    {filteredMemberData.length > 0 && (
                      <>
<TableRow className="font-bold bg-gray-50">
                          <TableCell>TOTAL</TableCell>
                          <TableCell>{filteredMemberData.reduce((sum, member) => sum + member.total_checkins, 0)}</TableCell>
                          <TableCell>-</TableCell>
                          <TableCell>{filteredMemberData.reduce((sum, member) => sum + member.active_months, 0)}</TableCell>
                          <TableCell>
                            {filteredMemberData.filter(m => m.status === 'active').length} active
                          </TableCell>
                          <TableCell>
                            {filteredMemberData.reduce((sum, member) => {
                              const mayCount = member.monthly_breakdown.find(m => m.month === mayKey)?.count || 0;
                              return sum + mayCount;
                            }, 0)}
                          </TableCell>
                          <TableCell>
                            {filteredMemberData.reduce((sum, member) => {
                              const aprilCount = member.monthly_breakdown.find(m => m.month === aprilKey)?.count || 0;
                              return sum + aprilCount;
                            }, 0)}
                          </TableCell>
                        </TableRow>

                        <TableRow className="font-bold bg-gray-100">
                          <TableCell>AVERAGE</TableCell>
                          <TableCell>-</TableCell>
                          <TableCell>
                            {filteredMemberData.length > 0
                              ? (filteredMemberData.reduce((sum, member) => sum + member.avg, 0) / filteredMemberData.length).toFixed(2)
                              : 0}
                          </TableCell>
                          <TableCell>-</TableCell>
                          <TableCell>
                            {filteredMemberData.length > 0
                              ? `${Math.round((filteredMemberData.filter(m => m.status === 'active').length / filteredMemberData.length) * 100)}% active`
                              : '0% active'}
                          </TableCell>
                          <TableCell>
                            {(() => {
                              // Calculate average May check-ins per member
                              const membersWithMayCheckins = filteredMemberData.filter(
                                m => (m.monthly_breakdown.find(mb => mb.month === mayKey)?.count || 0) > 0
                              );

                              if (membersWithMayCheckins.length === 0) return 0;

                              const totalMayCheckins = membersWithMayCheckins.reduce((sum, member) => {
                                const mayCount = member.monthly_breakdown.find(m => m.month === mayKey)?.count || 0;
                                return sum + mayCount;
                              }, 0);

                              return (totalMayCheckins / membersWithMayCheckins.length).toFixed(1);
                            })()}
                          </TableCell>
                          <TableCell>
                            {(() => {
                              // Calculate average April check-ins per member
                              const membersWithAprilCheckins = filteredMemberData.filter(
                                m => (m.monthly_breakdown.find(mb => mb.month === aprilKey)?.count || 0) > 0
                              );

                              if (membersWithAprilCheckins.length === 0) return 0;

                              const totalAprilCheckins = membersWithAprilCheckins.reduce((sum, member) => {
                                const aprilCount = member.monthly_breakdown.find(m => m.month === aprilKey)?.count || 0;
                                return sum + aprilCount;
                              }, 0);

                              return (totalAprilCheckins / membersWithAprilCheckins.length).toFixed(1);
                            })()}
                          </TableCell>
                        </TableRow>
                      </>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}