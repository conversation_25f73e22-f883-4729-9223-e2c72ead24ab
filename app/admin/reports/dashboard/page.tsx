// app/admin/dashboard/page.tsx
'use client'

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PeriodSelector } from '@/components/dashboard/PeriodSelector';
import { KpiCard } from '@/components/dashboard/KpiCard';
import { InfoPopover } from '@/components/dashboard/InfoPopover';
import { usePeriodRange } from '@/hooks/usePeriodRange';
import { useDashboardMetrics } from '@/hooks/useDashboardMetrics';
import { PeriodOption } from '@/types/dashboard';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { format, parseISO } from 'date-fns';
import { Users, DollarSign, UserPlus, Activity } from 'lucide-react';

export default function BusinessDashboard() {
  const [period, setPeriod] = useState<PeriodOption>('30');
  const periodRange = usePeriodRange(period);
  const { 
    metrics, 
    memberTrend, 
    memberSegments, 
    isLoading, 
    error 
  } = useDashboardMetrics(periodRange);

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Business Dashboard</h1>
        <PeriodSelector period={period} onChange={setPeriod} />
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md text-red-800">
          <h3 className="font-semibold">Error loading dashboard data</h3>
          <p className="text-sm">{error.message}</p>
        </div>
      )}

      {isLoading ? (
        // Skeleton loading state
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={`skeleton-${i}`}>
              <CardHeader>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse mt-2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {/* KPI Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <KpiCard
              title="Active Members"
              value={metrics.activeMembers.current}
              previousValue={metrics.activeMembers.previous}
              icon={Users}
              description={`Total active paid memberships (Last ${period} days)`}
            />
            <KpiCard
              title="Monthly Recurring Revenue"
              value={metrics.mrr.current}
              previousValue={metrics.mrr.previous}
              format="currency"
              icon={DollarSign}
              description="Expected monthly revenue from active memberships"
            />
            <KpiCard
              title="New Members"
              value={metrics.newMembers.current}
              previousValue={metrics.newMembers.previous}
              icon={UserPlus}
              description={`New sign-ups in the last ${period} days`}
            />
            <KpiCard
              title="Retention Rate"
              value={metrics.retentionRate.current}
              previousValue={metrics.retentionRate.previous}
              format="percent"
              icon={Activity}
              description="Percentage of members who renewed"
            />
          </div>

          {/* Tabs for detailed analysis */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="members">Member Analysis</TabsTrigger>
              <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
              <TabsTrigger value="classes">Class Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                {/* Member & Revenue Trend Chart */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-sm font-medium">
                      Member & Revenue Trends
                    </CardTitle>
                    <InfoPopover
                      title="Member & Revenue Trends"
                      calculation="Monthly tracking of active members and revenue"
                      description="Shows correlation between member count and revenue over time"
                      importance="Identify growth patterns and understand seasonal trends"
                    />
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      {memberTrend.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={memberTrend}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="month"
                              tickFormatter={(date) => format(parseISO(date), 'MMM yy')}
                            />
                            <YAxis yAxisId="left" />
                            <YAxis yAxisId="right" orientation="right" />
                            <Tooltip 
                              formatter={(value, name) => [
                                name === 'members' 
                                  ? value 
                                  : `€${value}`, 
                                name === 'members' 
                                  ? 'Active Members' 
                                  : 'Monthly Revenue'
                              ]}
                              labelFormatter={(label) => format(parseISO(label), 'MMMM yyyy')}
                            />
                            <Legend />
                            <Line
                              yAxisId="left"
                              type="monotone"
                              dataKey="members"
                              name="Active Members"
                              stroke="#8884d8"
                              activeDot={{ r: 8 }}
                            />
                            <Line
                              yAxisId="right"
                              type="monotone"
                              dataKey="revenue"
                              name="Revenue (€)"
                              stroke="#82ca9d"
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="h-full flex items-center justify-center">
                          <p className="text-muted-foreground">No trend data available</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Member Segmentation Chart */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-sm font-medium">
                      Member Segmentation
                    </CardTitle>
                    <InfoPopover
                      title="Member Segmentation"
                      calculation="Based on check-in frequency during the selected period"
                      description="Categorizes members by activity level"
                      importance="Identify at-risk members and understand engagement patterns"
                    />
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      {memberSegments.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={memberSegments}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                              label={({ name, percent }) => 
                                `${name} (${(percent * 100).toFixed(0)}%)`
                              }
                            >
                              {memberSegments.map((entry, index) => (
                                <Cell 
                                  key={`cell-${index}`} 
                                  fill={entry.color || COLORS[index % COLORS.length]} 
                                />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="h-full flex items-center justify-center">
                          <p className="text-muted-foreground">No segment data available</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Additional overview charts and metrics would go here */}
            </TabsContent>

            {/* Additional tab content would be implemented here */}
            <TabsContent value="members">
              <div className="text-center p-8 text-muted-foreground">
                Member analysis content would go here
              </div>
            </TabsContent>
            
            <TabsContent value="revenue">
              <div className="text-center p-8 text-muted-foreground">
                Revenue analysis content would go here
              </div>
            </TabsContent>
            
            <TabsContent value="classes">
              <div className="text-center p-8 text-muted-foreground">
                Class performance content would go here
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
