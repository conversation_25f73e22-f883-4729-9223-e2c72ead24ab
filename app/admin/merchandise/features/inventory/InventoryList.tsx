// app/admin/merchandise/features/inventory/InventoryList.tsx
'use client'

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, PlusCircle, MinusCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { InventoryItem, Color } from '../../hooks/useMerchandiseData'

interface InventoryListProps {
  inventory: InventoryItem[]
  colors: Color[]
  loading: boolean
  onUpdate: () => void
}

export function InventoryList({ inventory, colors, loading, onUpdate }: InventoryListProps) {
  const supabase = createClientComponentClient()
  
  // Handle quick inventory update (add or subtract)
  const updateInventoryQuantity = async (id: string, change: number) => {
    const item = inventory.find(i => i.id === id)
    if (!item) return
    
    // Ensure quantity doesn't go below zero
    const newQuantity = Math.max(0, item.quantity + change)
    
    try {
      const { error } = await supabase
        .from('merchandise_inventory')
        .update({ 
          quantity: newQuantity,
          last_updated: new Date().toISOString()
        })
        .eq('id', id)
      
      if (error) throw error
      
      toast.success(`Inventory ${change > 0 ? 'increased' : 'decreased'} successfully`)
      onUpdate()
      
    } catch (error) {
      console.error('Error updating quantity:', error)
      toast.error('Failed to update inventory')
    }
  }
  
  // Total items calculation
  const totalItems = inventory.reduce((sum, item) => sum + item.quantity, 0)
  
  // Low stock items calculation
  const lowStockItems = inventory.filter(item => item.quantity <= item.reorder_level).length
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Current Inventory</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : inventory.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No inventory items found.</p>
            <p className="text-sm">Add t-shirts to your inventory using the form on the left.</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {inventory.map((item) => (
                  <tr key={item.id}>
                    <td className="px-4 py-3 whitespace-nowrap">{item.item_size}</td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div 
                          className="h-4 w-4 rounded-full mr-2" 
                          style={{ 
                            backgroundColor: colors.find(c => c.color_name === item.item_color)?.color_hex || '#cccccc'
                          }}
                        />
                        {item.item_color}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        {item.quantity}
                        {item.quantity <= item.reorder_level && (
                          <Badge variant="outline" className="ml-2 text-xs bg-red-50 text-red-700 border-red-200">
                            Low stock
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateInventoryQuantity(item.id, -1)}
                          disabled={item.quantity <= 0}
                        >
                          <MinusCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateInventoryQuantity(item.id, 1)}
                        >
                          <PlusCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        <div className="mt-6">
          <h3 className="text-sm font-medium mb-2">Inventory Summary</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded-md border p-4">
              <p className="text-sm text-gray-500">Total Items</p>
              <p className="text-2xl font-semibold">{totalItems}</p>
            </div>
            <div className="rounded-md border p-4">
              <p className="text-sm text-gray-500">Low Stock Items</p>
              <p className="text-2xl font-semibold">{lowStockItems}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  ) 
  }
  