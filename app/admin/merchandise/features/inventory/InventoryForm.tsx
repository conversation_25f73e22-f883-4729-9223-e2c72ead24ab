// app/admin/merchandise/features/inventory/InventoryForm.tsx
'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'react-hot-toast'
import { useMerchandiseData } from '../../hooks/useMerchandiseData'

interface InventoryFormProps {
  onSuccess: () => void
}

export function InventoryForm({ onSuccess }: InventoryFormProps) {
  const supabase = createClientComponentClient()
  const { colors, sizes, findInventoryItem } = useMerchandiseData()
  
  const [formData, setFormData] = useState({
    item_size: '',
    item_color: '',
    quantity: 0,
    reorder_level: 5
  })
  
  const [loading, setLoading] = useState(false)
  
  // Get active colors and sizes
  const activeSizes = sizes.filter(size => size.active)
  const activeColors = colors.filter(color => color.active)
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.item_size || !formData.item_color) {
      toast.error('Please select a size and color')
      return
    }
    
    setLoading(true)
    
    try {
      const existingItem = findInventoryItem(
        formData.item_size, 
        formData.item_color
      )
      
      if (existingItem) {
        // Update existing inventory
        const { error } = await supabase
          .from('merchandise_inventory')
          .update({ 
            quantity: formData.quantity,
            reorder_level: formData.reorder_level,
            last_updated: new Date().toISOString()
          })
          .eq('id', existingItem.id)
        
        if (error) throw error
        
        toast.success('Inventory updated successfully')
      } else {
        // Create new inventory item
        const { error } = await supabase
          .from('merchandise_inventory')
          .insert({
            item_type: 'T-shirt',
            item_size: formData.item_size,
            item_color: formData.item_color,
            quantity: formData.quantity,
            reorder_level: formData.reorder_level
          })
        
        if (error) throw error
        
        toast.success('Inventory item added successfully')
      }
      
      // Reset form
      setFormData({
        item_size: '',
        item_color: '',
        quantity: 0,
        reorder_level: 5
      })
      
      // Notify parent
      onSuccess()
      
    } catch (error) {
      console.error('Error submitting inventory form:', error)
      toast.error('Failed to update inventory')
    } finally {
      setLoading(false)
    }
  }
  
  // Check for existing inventory when size/color changes
  useEffect(() => {
    if (formData.item_size && formData.item_color) {
      const existingItem = findInventoryItem(
        formData.item_size,
        formData.item_color
      )
      
      if (existingItem) {
        setFormData(prev => ({
          ...prev,
          quantity: existingItem.quantity,
          reorder_level: existingItem.reorder_level
        }))
      }
    }
  }, [formData.item_size, formData.item_color, findInventoryItem])
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Update Inventory</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="size">Size</Label>
              <Select 
                value={formData.item_size} 
                onValueChange={(value) => setFormData({...formData, item_size: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {activeSizes.map(size => (
                    <SelectItem key={size.id} value={size.size_name}>
                      {size.size_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select 
                value={formData.item_color} 
                onValueChange={(value) => setFormData({...formData, item_color: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select color" />
                </SelectTrigger>
                <SelectContent>
                  {activeColors.map(color => (
                    <SelectItem key={color.id} value={color.color_name}>
                      <div className="flex items-center">
                        <div 
                          className="h-3 w-3 rounded-full mr-2" 
                          style={{ backgroundColor: color.color_hex }}
                        />
                        {color.color_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <Input 
                type="number" 
                min="0"
                value={formData.quantity} 
                onChange={(e) => setFormData({...formData, quantity: parseInt(e.target.value) || 0})}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="reorder_level">Reorder Level</Label>
              <Input 
                type="number" 
                min="0"
                value={formData.reorder_level} 
                onChange={(e) => setFormData({...formData, reorder_level: parseInt(e.target.value) || 0})}
                required
              />
              <p className="text-xs text-gray-500">
                You&apos;ll receive a warning when stock drops below this level
              </p>
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full mt-4"
            disabled={loading || !formData.item_size || !formData.item_color}
          >
            {loading ? 'Updating...' : (findInventoryItem(
              formData.item_size, 
              formData.item_color
            ) ? 'Update Inventory' : 'Add Inventory')}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
