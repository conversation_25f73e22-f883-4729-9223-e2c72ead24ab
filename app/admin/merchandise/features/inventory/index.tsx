// app/admin/merchandise/features/inventory/index.tsx
'use client'

import { InventoryForm } from './InventoryForm'
import { InventoryList } from './InventoryList'
import { useMerchandiseData } from '../../hooks/useMerchandiseData'

export function InventoryFeature() {
  const { 
    colors, 
    inventory, 
    loadingInventory, 
    fetchInventory 
  } = useMerchandiseData()
  
  // Handle inventory update
  const handleInventoryUpdate = () => {
    fetchInventory()
  }
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <InventoryForm onSuccess={handleInventoryUpdate} />
      
      <InventoryList
        inventory={inventory}
        colors={colors}
        loading={loadingInventory}
        onUpdate={handleInventoryUpdate}
      />
    </div>
  )
}