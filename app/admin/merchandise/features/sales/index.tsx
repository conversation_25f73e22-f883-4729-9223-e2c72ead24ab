// app/admin/merchandise/features/sales/index.tsx
'use client'

import { SalesForm } from './SalesForm'
import { SalesList } from './SalesList'
import { useMerchandiseData, SalesFilter } from '../../hooks/useMerchandiseData'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export function SalesFeature() {
  const { 
    colors, 
    sizes, 
    sales, 
    loadingSales, 
    fetchSales,
    fetchInventory
  } = useMerchandiseData()
  
  // Handle filter changes
  const handleFilterChange = (filters: SalesFilter) => {
    fetchSales(filters)
  }
  
  // Handle new sale success
  const handleSaleSuccess = () => {
    fetchSales()
    fetchInventory() // Also refresh inventory after adding a sale
  }
  
  // Handle sale deletion
  const handleSaleDeleted = () => {
    fetchSales()
    fetchInventory() // Refresh inventory after deleting a sale
  }
  
  // Handle sale update
  const handleSaleUpdated = () => {
    fetchSales()
    fetchInventory() // Refresh inventory after updating a sale
  }
  
  return (
    <Tabs defaultValue="view">
      <TabsList className="mb-6">
        <TabsTrigger value="view">View Sales</TabsTrigger>
        <TabsTrigger value="add">Add New Sale</TabsTrigger>
      </TabsList>
      
      <TabsContent value="view">
        <SalesList
          sales={sales}
          colors={colors}
          sizes={sizes}
          loading={loadingSales}
          onFilterChange={handleFilterChange}
          onSaleDeleted={handleSaleDeleted}
          onSaleUpdated={handleSaleUpdated}
        />
      </TabsContent>
      
      <TabsContent value="add">
        <SalesForm
          colors={colors}
          sizes={sizes}
          onSuccess={handleSaleSuccess}
        />
      </TabsContent>
    </Tabs>
  )
}
