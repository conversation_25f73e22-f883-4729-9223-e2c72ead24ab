// app/admin/merchandise/features/sales/SalesList.tsx
'use client'

import { useState } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { CalendarIcon, Loader2, Edit, Trash2 } from 'lucide-react'
import { format } from 'date-fns'
import { Sale, SalesFilter, Color, Size } from '../../hooks/useMerchandiseData'
import { SalesEditDialog } from './SalesEditDialog'
import { toast } from 'react-hot-toast'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

// Add type for the filter value
type FilterValue = string | Date | null | undefined;

interface SalesListProps {
  sales: Sale[]
  colors: Color[]
  sizes: Size[]
  loading: boolean
  onFilterChange: (filters: SalesFilter) => void
  onSaleDeleted: () => void
  onSaleUpdated: () => void
}

export function SalesList({ 
  sales, 
  colors, 
  sizes, 
  loading, 
  onFilterChange,
  onSaleDeleted,
  onSaleUpdated
}: SalesListProps) {
  const supabase = createClientComponentClient()
  const [searchParams, setSearchParams] = useState<SalesFilter>({
    size: '',
    color: '',
    startDate: null,
    endDate: null
  })
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [saleToDelete, setSaleToDelete] = useState<Sale | null>(null)
  
  // State for edit dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [saleToEdit, setSaleToEdit] = useState<Sale | null>(null)

  // Handle filter changes and trigger parent callback
  const updateFilters = (key: keyof SalesFilter, value: FilterValue) => {
    // Convert undefined to null for dates
    const normalizedValue = value === undefined ? null : value;
    const newFilters = { ...searchParams, [key]: normalizedValue };
    setSearchParams(newFilters);
    onFilterChange(newFilters);
  }
  
  // Handle delete sale
  const handleDeleteClick = (sale: Sale) => {
    setSaleToDelete(sale)
    setDeleteDialogOpen(true)
  }
  
  const confirmDelete = async () => {
    if (!saleToDelete) return
    
    try {
      // Delete the sale
      const { error } = await supabase
        .from('merchandise_sales')
        .delete()
        .eq('id', saleToDelete.id)
      
      if (error) throw error
      
      // Update inventory (add the item back)
      const { data: inventoryItem, error: inventoryFetchError } = await supabase
        .from('merchandise_inventory')
        .select('*')
        .eq('item_size', saleToDelete.item_size)
        .eq('item_color', saleToDelete.item_color)
        .single()
      
      if (inventoryFetchError && inventoryFetchError.code !== 'PGRST116') {
        throw inventoryFetchError
      }
      
      if (inventoryItem) {
        // Update existing inventory
        const { error: updateError } = await supabase
          .from('merchandise_inventory')
          .update({ 
            quantity: inventoryItem.quantity + 1,
            last_updated: new Date().toISOString()
          })
          .eq('id', inventoryItem.id)
        
        if (updateError) throw updateError
      } else {
        // Create new inventory entry
        const { error: insertError } = await supabase
          .from('merchandise_inventory')
          .insert({
            item_type: 'T-shirt',
            item_size: saleToDelete.item_size,
            item_color: saleToDelete.item_color,
            quantity: 1,
            reorder_level: 5
          })
        
        if (insertError) throw insertError
      }
      
      toast.success('Sale deleted and inventory updated')
      onSaleDeleted()
    } catch (error) {
      console.error('Error deleting sale:', error)
      toast.error('Failed to delete sale')
    } finally {
      setDeleteDialogOpen(false)
      setSaleToDelete(null)
    }
  }
  
  // Handle edit sale
  const handleEditClick = (sale: Sale) => {
    setSaleToEdit(sale)
    setEditDialogOpen(true)
  }
  
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>T-shirt Sales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="filterSize">Filter by Size</Label>
              <Select 
                value={searchParams.size || "all"} 
                onValueChange={(value) => updateFilters('size', value === "all" ? "" : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All sizes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All sizes</SelectItem>
                  {sizes.map(size => (
                    <SelectItem key={size.id} value={size.size_name}>
                      {size.size_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="filterColor">Filter by Color</Label>
              <Select 
                value={searchParams.color || "all"} 
                onValueChange={(value) => updateFilters('color', value === "all" ? "" : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All colors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All colors</SelectItem>
                  {colors.map(color => (
                    <SelectItem key={color.id} value={color.color_name}>
                      <div className="flex items-center">
                        <div 
                          className="h-3 w-3 rounded-full mr-2" 
                          style={{ backgroundColor: color.color_hex }}
                        />
                        {color.color_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>From Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {searchParams.startDate ? format(searchParams.startDate, 'PPP') : <span>Starting date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={searchParams.startDate || undefined}
                    onSelect={(date) => updateFilters('startDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div>
              <Label>To Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {searchParams.endDate ? format(searchParams.endDate, 'PPP') : <span>Ending date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={searchParams.endDate || undefined}
                    onSelect={(date) => updateFilters('endDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center">
                      <div className="flex justify-center">
                        <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                      </div>
                    </td>
                  </tr>
                ) : sales.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center">No sales found</td>
                  </tr>
                ) : (
                  sales.map((sale) => (
                    <tr key={sale.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {sale.client_name} {sale.client_last_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">{sale.item_size}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {sale.item_color && (
                            <div 
                              className="h-4 w-4 rounded-full mr-2" 
                              style={{ 
                                backgroundColor: colors.find(c => c.color_name === sale.item_color)?.color_hex || '#cccccc'
                              }} 
                            />
                          )}
                          {sale.item_color}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">€{sale.price.toFixed(2)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{sale.payment_method}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {format(new Date(sale.sale_date), 'dd/MM/yyyy')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditClick(sale)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-800"
                            onClick={() => handleDeleteClick(sale)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this sale? This will restore the item to inventory.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Edit Sale Dialog */}
      {saleToEdit && (
        <SalesEditDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          sale={saleToEdit}
          colors={colors}
          sizes={sizes}
          onSuccess={() => {
            setEditDialogOpen(false)
            setSaleToEdit(null)
            onSaleUpdated()
          }}
        />
      )}
    </>
  )
}
