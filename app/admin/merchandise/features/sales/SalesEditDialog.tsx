// app/admin/merchandise/features/sales/SalesEditDialog.tsx
'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { CalendarIcon, Loader2, AlertTriangle } from 'lucide-react'
import { format } from 'date-fns'
import { toast } from 'react-hot-toast'
import { Sale, Color, Size, InventoryItem } from '../../hooks/useMerchandiseData'

interface SalesEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  sale: Sale
  colors: Color[]
  sizes: Size[]
  onSuccess: () => void
}

export function SalesEditDialog({ 
  open, 
  onOpenChange, 
  sale, 
  colors, 
  sizes, 
  onSuccess 
}: SalesEditDialogProps) {
  const supabase = createClientComponentClient()
  const [loading, setLoading] = useState(false)
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  // Remove loadingInventory since it's not used
  
  // Form state
  const [formData, setFormData] = useState({
    item_size: sale.item_size || '',
    item_color: sale.item_color || '',
    price: sale.price.toString(),
    payment_method: sale.payment_method,
    sale_date: new Date(sale.sale_date),
    comments: sale.comments || ''
  })
  
  // Initial values for checking if size/color changed
  const initialSize = sale.item_size
  const initialColor = sale.item_color
  
  // Fetch inventory data when component mounts
  useEffect(() => {
    const fetchInventory = async () => {
      try {
        const { data, error } = await supabase
          .from('merchandise_inventory')
          .select('*')
          .eq('item_type', 'T-shirt')
        
        if (error) throw error
        if (data) setInventory(data)
      } catch (error) {
        console.error('Error fetching inventory:', error)
      }
    }
    
    if (open) {
      fetchInventory()
    }
  }, [open, supabase]) // Add supabase to dependencies
  
  // Check if inventory is available for a specific size and color
  const checkInventory = (size: string, color: string) => {
    // Only check inventory if size or color is different from original
    if (size === initialSize && color === initialColor) {
      return true // Allow keeping the current size/color
    }
    
    const item = inventory.find(item => 
      item.item_size === size && 
      item.item_color === color
    )
    
    return item ? item.quantity > 0 : false
  }
  
  // Find inventory item by size and color
  const findInventoryItem = (size: string, color: string) => {
    return inventory.find(item => 
      item.item_size === size && 
      item.item_color === color
    )
  }
  
  // Get active colors and sizes
  const activeSizes = sizes.filter(size => size.active)
  const activeColors = colors.filter(color => color.active)
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!formData.item_size || !formData.item_color || !formData.price || !formData.payment_method) {
      toast.error('Please fill in all required fields')
      return
    }
    
    // Check if size or color changed and if inventory is available
    const isSizeColorChanged = formData.item_size !== initialSize || formData.item_color !== initialColor
    
    if (isSizeColorChanged && !checkInventory(formData.item_size, formData.item_color)) {
      toast.error('Not enough inventory for the selected size and color')
      return
    }
    
    setLoading(true)
    
    try {
      // Process inventory updates if size or color changed
      if (isSizeColorChanged) {
        // First, add back the original item to inventory
        const originalItem = findInventoryItem(initialSize || '', initialColor || '')
        
        if (originalItem) {
          // Update existing inventory item
          await supabase
            .from('merchandise_inventory')
            .update({ 
              quantity: originalItem.quantity + 1,
              last_updated: new Date().toISOString()
            })
            .eq('id', originalItem.id)
        } else {
          // Create new inventory item for original product
          await supabase
            .from('merchandise_inventory')
            .insert({
              item_type: 'T-shirt',
              item_size: initialSize,
              item_color: initialColor,
              quantity: 1,
              reorder_level: 5
            })
        }
        
        // Then, subtract the new item from inventory
        const newItem = findInventoryItem(formData.item_size, formData.item_color)
        
        if (newItem) {
          await supabase
            .from('merchandise_inventory')
            .update({ 
              quantity: newItem.quantity - 1,
              last_updated: new Date().toISOString()
            })
            .eq('id', newItem.id)
        }
      }
      
      // Update the sale record
      const { error } = await supabase
        .from('merchandise_sales')
        .update({
          item_size: formData.item_size,
          item_color: formData.item_color,
          price: parseFloat(formData.price),
          payment_method: formData.payment_method,
          sale_date: formData.sale_date.toISOString(),
          comments: formData.comments || null
        })
        .eq('id', sale.id)
      
      if (error) throw error
      
      toast.success('Sale updated successfully')
      onSuccess()
    } catch (error) {
      console.error('Error updating sale:', error)
      toast.error('Failed to update sale')
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Edit Sale</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="size">Size</Label>
              <Select 
                value={formData.item_size} 
                onValueChange={(value) => setFormData({...formData, item_size: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {activeSizes.map(size => (
                    <SelectItem key={size.id} value={size.size_name}>
                      {size.size_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select 
                value={formData.item_color} 
                onValueChange={(value) => setFormData({...formData, item_color: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select color" />
                </SelectTrigger>
                <SelectContent>
                  {activeColors.map(color => (
                    <SelectItem key={color.id} value={color.color_name}>
                      <div className="flex items-center">
                        <div 
                          className="h-3 w-3 rounded-full mr-2" 
                          style={{ backgroundColor: color.color_hex }}
                        />
                        {color.color_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {formData.item_size && formData.item_color && 
               formData.item_size !== initialSize || formData.item_color !== initialColor ? (
                <div className="mt-2">
                  {checkInventory(formData.item_size, formData.item_color) ? (
                    <p className="text-xs text-green-600">
                      In stock: {findInventoryItem(formData.item_size, formData.item_color)?.quantity || 0} available
                    </p>
                  ) : (
                    <p className="text-xs text-red-600 flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Out of stock
                    </p>
                  )}
                </div>
              ) : null}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input 
                type="number" 
                step="0.01" 
                value={formData.price} 
                onChange={(e) => setFormData({...formData, price: e.target.value})}
                placeholder="0.00" 
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select 
                value={formData.payment_method} 
                onValueChange={(value) => setFormData({...formData, payment_method: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Cash">Cash</SelectItem>
                  <SelectItem value="POS">POS</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="sale_date">Sale Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.sale_date ? format(formData.sale_date, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.sale_date}
                    onSelect={(date) => setFormData({...formData, sale_date: date || new Date()})}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="comments">Comments</Label>
              <Input 
                value={formData.comments} 
                onChange={(e) => setFormData({...formData, comments: e.target.value})}
                placeholder="Any additional notes" 
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={
                loading || 
                (formData.item_size !== initialSize || formData.item_color !== initialColor) && 
                !checkInventory(formData.item_size, formData.item_color)
              }
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Update Sale
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
