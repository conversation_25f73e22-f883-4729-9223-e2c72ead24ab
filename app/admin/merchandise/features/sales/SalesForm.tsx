// app/admin/merchandise/features/sales/SalesForm.tsx
'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { AlertTriangle, CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import ClientSelector from '@/components/ClientSelector'
import { toast } from 'react-hot-toast'

// Import types from our hook
import { useMerchandiseData, Color, Size } from '../../hooks/useMerchandiseData'

interface FullClientOption {
  value: string
  label: string
  pelatis_id: string
  session_id: string
}

interface SalesFormProps {
  colors: Color[]
  sizes: Size[]
  onSuccess: () => void
}

export function SalesForm({ colors, sizes, onSuccess }: SalesFormProps) {
  const supabase = createClientComponentClient()
  const { checkInventory, findInventoryItem } = useMerchandiseData()
  
  const [clients, setClients] = useState<FullClientOption[]>([])
  const [selectedClients, setSelectedClients] = useState<FullClientOption[]>([])
  const [loading, setLoading] = useState(false)
  
  const [formData, setFormData] = useState({
    item_type: 'T-shirt',
    item_size: '',
    item_color: '',
    price: '',
    payment_method: '',
    sale_date: new Date(),
    comments: ''
  })
  
  // Fetch clients when component mounts
  useEffect(() => {
    const fetchClients = async () => {
      const { data, error } = await supabase
        .from('pelates')
        .select('id, name, last_name')
        .order('last_name', { ascending: true })
      
      if (error) {
        console.error('Error fetching clients:', error)
      } else if (data) {
        const mappedClients = data.map(client => ({
          value: client.id,
          label: `${client.name} ${client.last_name}`,
          pelatis_id: client.id,
          session_id: '',
        }))
        setClients(mappedClients)
      }
    }
    
    fetchClients()
  }, []) // Empty dependency array for running only on mount
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedClients.length) {
      toast.error('Please select a client')
      return
    }
    
    if (!formData.item_size || !formData.item_color) {
      toast.error('Please select a size and color')
      return
    }
    
    if (!checkInventory(formData.item_size, formData.item_color)) {
      toast.error('Not enough inventory available')
      return
    }
    
    setLoading(true)
    
    try {
      // Get the first selected client
      const client = selectedClients[0]
      
      // Add the sale
      const { error } = await supabase.from('merchandise_sales').insert({
        pelatis_id: client.pelatis_id,
        item_type: 'T-shirt',
        item_size: formData.item_size,
        item_color: formData.item_color,
        price: parseFloat(formData.price),
        payment_method: formData.payment_method,
        sale_date: formData.sale_date.toISOString(),
        comments: formData.comments || null
      })
      
      if (error) throw error
      
      // Update inventory
      const inventoryItem = findInventoryItem(
        formData.item_size, 
        formData.item_color
      )
      
      if (inventoryItem) {
        const { error: inventoryError } = await supabase
          .from('merchandise_inventory')
          .update({ 
            quantity: inventoryItem.quantity - 1,
            last_updated: new Date().toISOString()
          })
          .eq('id', inventoryItem.id)
        
        if (inventoryError) {
          console.error('Error updating inventory:', inventoryError)
        }
      }
      
      // Reset form
      setFormData({
        item_type: 'T-shirt',
        item_size: '',
        item_color: '',
        price: '',
        payment_method: '',
        sale_date: new Date(),
        comments: ''
      })
      setSelectedClients([])
      
      toast.success('Sale added successfully')
      onSuccess()
      
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error('Failed to add sale')
    } finally {
      setLoading(false)
    }
  }
  
  // Get active colors and sizes
  const activeSizes = sizes.filter(size => size.active)
  const activeColors = colors.filter(color => color.active)
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New T-shirt Sale</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <ClientSelector 
                clients={clients}
                selectedClients={selectedClients}
                onChange={setSelectedClients}
              />
              <p className="text-xs text-gray-500 mt-1">
                Please select only one client for each t-shirt sale.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="size">Size</Label>
              <Select 
                value={formData.item_size} 
                onValueChange={(value) => setFormData({...formData, item_size: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {activeSizes.map(size => (
                    <SelectItem key={size.id} value={size.size_name}>
                      {size.size_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select 
                value={formData.item_color} 
                onValueChange={(value) => setFormData({...formData, item_color: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select color" />
                </SelectTrigger>
                <SelectContent>
                  {activeColors.map(color => (
                    <SelectItem key={color.id} value={color.color_name}>
                      <div className="flex items-center">
                        <div 
                          className="h-3 w-3 rounded-full mr-2" 
                          style={{ backgroundColor: color.color_hex }}
                        />
                        {color.color_name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {formData.item_size && formData.item_color && (
                <div className="mt-2">
                  {checkInventory(formData.item_size, formData.item_color) ? (
                    <p className="text-xs text-green-600">
                      In stock: {findInventoryItem(formData.item_size, formData.item_color)?.quantity || 0} available
                    </p>
                  ) : (
                    <p className="text-xs text-red-600 flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Out of stock
                    </p>
                  )}
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input 
                type="number" 
                step="0.01" 
                value={formData.price} 
                onChange={(e) => setFormData({...formData, price: e.target.value})}
                placeholder="0.00" 
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select 
                value={formData.payment_method} 
                onValueChange={(value) => setFormData({...formData, payment_method: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Cash">Cash</SelectItem>
                  <SelectItem value="POS">POS</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="sale_date">Sale Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.sale_date ? format(formData.sale_date, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.sale_date}
                    onSelect={(date) => setFormData({...formData, sale_date: date || new Date()})}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="comments">Comments</Label>
              <Input 
                value={formData.comments} 
                onChange={(e) => setFormData({...formData, comments: e.target.value})}
                placeholder="Any additional notes" 
              />
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={
              loading ||
              !selectedClients.length || 
              !formData.item_size || 
              !formData.item_color || 
              !formData.price || 
              !formData.payment_method ||
              !checkInventory(formData.item_size, formData.item_color)
            }
          >
            {loading ? 'Adding...' : 'Add Sale'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
