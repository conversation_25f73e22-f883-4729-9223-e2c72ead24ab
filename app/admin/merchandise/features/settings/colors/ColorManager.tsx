// app/admin/merchandise/features/settings/colors/ColorManager.tsx
'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Loader2, Edit, Trash2, Plus } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { toast } from 'react-hot-toast'
import { Color } from '../../../hooks/useMerchandiseData'

interface ColorManagerProps {
  colors: Color[]
  loading: boolean
  onEdit: () => void
  onDelete: (id: string) => void
}

export function ColorManager({ colors, loading, onEdit, onDelete }: ColorManagerProps) {
  const supabase = createClientComponentClient()
  const [formOpen, setFormOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState({
    id: '',
    color_name: '',
    color_hex: '#000000',
    display_order: 0,
    active: true
  })
  
  // Reset form when dialog is opened
  const handleOpenChange = (open: boolean) => {
    setFormOpen(open)
    if (!open) {
      setFormData({
        id: '',
        color_name: '',
        color_hex: '#000000',
        display_order: colors.length,
        active: true
      })
    }
  }
  
  // Set form data for editing
  const handleEdit = (color: Color) => {
    setFormData({
      id: color.id,
      color_name: color.color_name,
      color_hex: color.color_hex,
      display_order: color.display_order,
      active: color.active
    })
    setFormOpen(true)
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      if (formData.id) {
        // Update existing color
        const { error } = await supabase
          .from('merchandise_colors')
          .update({
            color_name: formData.color_name,
            color_hex: formData.color_hex,
            display_order: formData.display_order,
            active: formData.active
          })
          .eq('id', formData.id)
        
        if (error) throw error
        toast.success('Color updated successfully')
      } else {
        // Create new color
        const { error } = await supabase
          .from('merchandise_colors')
          .insert({
            color_name: formData.color_name,
            color_hex: formData.color_hex,
            display_order: formData.display_order,
            active: formData.active
          })
        
        if (error) throw error
        toast.success('Color added successfully')
      }
      
      // Close form and refresh data
      setFormOpen(false)
      onEdit()
      
    } catch (error) {
      console.error('Error saving color:', error)
      toast.error('Failed to save color')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Handle color deletion with confirmation
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this color? This may affect existing inventory and sales records.')) {
      return
    }
    
    try {
      const { error } = await supabase
        .from('merchandise_colors')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      toast.success('Color deleted successfully')
      onDelete(id)
      
    } catch (error) {
      console.error('Error deleting color:', error)
      toast.error('Failed to delete color. It may be in use in inventory or sales records.')
    }
  }
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Manage Colors</CardTitle>
        <Dialog open={formOpen} onOpenChange={handleOpenChange}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New Color
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{formData.id ? 'Edit Color' : 'Add New Color'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="color_name">Color Name</Label>
                <Input 
                  id="color_name"
                  value={formData.color_name} 
                  onChange={(e) => setFormData({...formData, color_name: e.target.value})}
                  placeholder="e.g. Red, Blue, Black, etc."
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="color_hex">Color Hex Code</Label>
                <div className="flex space-x-2 items-center">
                  <Input 
                    id="color_hex"
                    type="color"
                    value={formData.color_hex} 
                    onChange={(e) => setFormData({...formData, color_hex: e.target.value})}
                    className="w-16 h-10 p-1"
                    required
                  />
                  <Input 
                    value={formData.color_hex} 
                    onChange={(e) => setFormData({...formData, color_hex: e.target.value})}
                    placeholder="#000000"
                    pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="display_order">Display Order</Label>
                <Input 
                  type="number"
                  id="display_order"
                  value={formData.display_order} 
                  onChange={(e) => setFormData({...formData, display_order: parseInt(e.target.value) || 0})}
                  placeholder="e.g. 1, 2, 3, etc."
                  required
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="color_active" 
                  checked={formData.active} 
                  onCheckedChange={(checked) => setFormData({...formData, active: checked})}
                />
                <Label htmlFor="color_active" className="cursor-pointer">Active</Label>
              </div>
              
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">Cancel</Button>
                </DialogClose>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : formData.id ? (
                    'Update Color'
                  ) : (
                    'Add Color'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : colors.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No colors defined yet.</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hex</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {colors.map((color) => (
                  <tr key={color.id}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div 
                          className="h-4 w-4 rounded-full mr-2" 
                          style={{ backgroundColor: color.color_hex }}
                        />
                        <span className="font-medium">{color.color_name}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap font-mono text-sm">
                      {color.color_hex}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {color.display_order}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <Badge 
                        variant="default" 
                        className={color.active ? "bg-green-500 hover:bg-green-600" : ""}
                      >
                        {color.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleEdit(color)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="text-red-600 hover:text-red-800"
                          onClick={() => handleDelete(color.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
