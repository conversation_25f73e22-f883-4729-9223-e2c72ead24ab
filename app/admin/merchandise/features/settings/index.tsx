// app/admin/merchandise/features/settings/index.tsx
'use client'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ColorManager } from './colors/ColorManager'
import { SizeManager } from './sizes/SizeManager'
import { useMerchandiseData } from '../../hooks/useMerchandiseData'

export function SettingsFeature() {
  const { 
    colors, 
    sizes, 
    loadingColors, 
    loadingSizes, 
    fetchColors, 
    fetchSizes 
  } = useMerchandiseData()
  
  return (
    <Tabs defaultValue="colors">
      <TabsList className="mb-6">
        <TabsTrigger value="colors">Manage Colors</TabsTrigger>
        <TabsTrigger value="sizes">Manage Sizes</TabsTrigger>
      </TabsList>
      
      <TabsContent value="colors">
        <ColorManager 
          colors={colors}
          loading={loadingColors}
          onEdit={() => fetchColors()}
          onDelete={() => fetchColors()}
        />
      </TabsContent>
      
      <TabsContent value="sizes">
        <SizeManager
          sizes={sizes}
          loading={loadingSizes}
          onEdit={() => fetchSizes()}
          onDelete={() => fetchSizes()}
        />
      </TabsContent>
    </Tabs>
  )
}
