// app/admin/merchandise/features/settings/sizes/SizeManager.tsx
'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Loader2, Edit, Trash2, Plus } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { toast } from 'react-hot-toast'
import { Size } from '../../../hooks/useMerchandiseData'

interface SizeManagerProps {
  sizes: Size[]
  loading: boolean
  onEdit: () => void
  onDelete: (id: string) => void
}

export function SizeManager({ sizes, loading, onEdit, onDelete }: SizeManagerProps) {
  const supabase = createClientComponentClient()
  const [formOpen, setFormOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState({
    id: '',
    size_name: '',
    display_order: 0,
    active: true
  })
  
  // Reset form when dialog is opened
  const handleOpenChange = (open: boolean) => {
    setFormOpen(open)
    if (!open) {
      setFormData({
        id: '',
        size_name: '',
        display_order: sizes.length,
        active: true
      })
    }
  }
  
  // Set form data for editing
  const handleEdit = (size: Size) => {
    setFormData({
      id: size.id,
      size_name: size.size_name,
      display_order: size.display_order,
      active: size.active
    })
    setFormOpen(true)
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      if (formData.id) {
        // Update existing size
        const { error } = await supabase
          .from('merchandise_sizes')
          .update({
            size_name: formData.size_name,
            display_order: formData.display_order,
            active: formData.active
          })
          .eq('id', formData.id)
        
        if (error) throw error
        toast.success('Size updated successfully')
      } else {
        // Create new size
        const { error } = await supabase
          .from('merchandise_sizes')
          .insert({
            size_name: formData.size_name,
            display_order: formData.display_order,
            active: formData.active
          })
        
        if (error) throw error
        toast.success('Size added successfully')
      }
      
      // Close form and refresh data
      setFormOpen(false)
      onEdit()
      
    } catch (error) {
      console.error('Error saving size:', error)
      toast.error('Failed to save size')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Handle size deletion with confirmation
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this size? This may affect existing inventory and sales records.')) {
      return
    }
    
    try {
      const { error } = await supabase
        .from('merchandise_sizes')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      toast.success('Size deleted successfully')
      onDelete(id)
      
    } catch (error) {
      console.error('Error deleting size:', error)
      toast.error('Failed to delete size. It may be in use in inventory or sales records.')
    }
  }
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Manage Sizes</CardTitle>
        <Dialog open={formOpen} onOpenChange={handleOpenChange}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New Size
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{formData.id ? 'Edit Size' : 'Add New Size'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="size_name">Size Name</Label>
                <Input 
                  id="size_name"
                  value={formData.size_name} 
                  onChange={(e) => setFormData({...formData, size_name: e.target.value})}
                  placeholder="e.g. S, M, L, XL, etc."
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="display_order">Display Order</Label>
                <Input 
                  type="number"
                  id="display_order"
                  value={formData.display_order} 
                  onChange={(e) => setFormData({...formData, display_order: parseInt(e.target.value) || 0})}
                  placeholder="e.g. 1, 2, 3, etc."
                  required
                />
                <p className="text-xs text-gray-500">
                  Determines the order in dropdown menus (lowest first)
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="size_active" 
                  checked={formData.active} 
                  onCheckedChange={(checked) => setFormData({...formData, active: checked})}
                />
                <Label htmlFor="size_active" className="cursor-pointer">Active</Label>
              </div>
              
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">Cancel</Button>
                </DialogClose>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : formData.id ? (
                    'Update Size'
                  ) : (
                    'Add Size'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : sizes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No sizes defined yet.</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sizes.map((size) => (
                  <tr key={size.id}>
                    <td className="px-4 py-3 whitespace-nowrap font-medium">{size.size_name}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{size.display_order}</td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <Badge 
                        variant="default" 
                        className={size.active ? "bg-green-500 hover:bg-green-600" : ""}
                      >
                        {size.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleEdit(size)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="text-red-600 hover:text-red-800"
                          onClick={() => handleDelete(size.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
