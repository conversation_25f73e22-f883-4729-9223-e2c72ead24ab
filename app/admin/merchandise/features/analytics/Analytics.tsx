// app/admin/merchandise/features/analytics/Analytics.tsx
'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSupabase } from '@/hooks/useSupabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Loader2 } from 'lucide-react'

type SalesBySizeData = {
  item_size: string
  count: number
  revenue: number
}

type SalesByColorData = {
  item_color: string
  count: number
  revenue: number
}

type SalesByDateData = {
  date: string
  count: number
  revenue: number
}

export function MerchandiseAnalytics() {
  const { supabaseClient } = useSupabase()
  const [loading, setLoading] = useState(true)
  const [sizeData, setSizeData] = useState<SalesBySizeData[]>([])
  const [colorData, setColorData] = useState<SalesByColorData[]>([])
  const [dateData, setDateData] = useState<SalesByDateData[]>([])
  const [totalSales, setTotalSales] = useState(0)
  const [totalRevenue, setTotalRevenue] = useState(0)

  const fetchData = useCallback(async () => {
    setLoading(true)

    // Fetch total sales and revenue
    const { data: totals, error: totalsError } = await supabaseClient
      .from('merchandise_sales')
      .select('id, price')
      .eq('item_type', 'T-shirt')

    if (totalsError) {
      console.error('Error fetching totals:', totalsError)
    } else if (totals) {
      setTotalSales(totals.length)
      setTotalRevenue(totals.reduce((sum, item) => sum + (item.price || 0), 0))
    }

    // Fetch sales by size
    const { data: sizeData, error: sizeError } = await supabaseClient.rpc('get_sales_by_size')

    if (sizeError) {
      console.error('Error fetching size data:', sizeError)

      // Fallback method if RPC doesn't exist
      const { data: fallbackData, error: fallbackError } = await supabaseClient
        .from('merchandise_sales')
        .select('item_size, price')
        .eq('item_type', 'T-shirt')
        .not('item_size', 'is', null)

      if (!fallbackError && fallbackData) {
        const groupedData: Record<string, SalesBySizeData> = {}

        fallbackData.forEach(sale => {
          const size = sale.item_size as string
          if (!groupedData[size]) {
            groupedData[size] = { item_size: size, count: 0, revenue: 0 }
          }
          groupedData[size].count += 1
          groupedData[size].revenue += sale.price || 0
        })

        setSizeData(Object.values(groupedData))
      }
    } else if (sizeData) {
      setSizeData(sizeData)
    }

    // Fetch sales by color
    const { data: colorData, error: colorError } = await supabaseClient.rpc('get_sales_by_color')

    if (colorError) {
      console.error('Error fetching color data:', colorError)

      // Fallback method
      const { data: fallbackData, error: fallbackError } = await supabaseClient
        .from('merchandise_sales')
        .select('item_color, price')
        .eq('item_type', 'T-shirt')
        .not('item_color', 'is', null)

      if (!fallbackError && fallbackData) {
        const groupedData: Record<string, SalesByColorData> = {}

        fallbackData.forEach(sale => {
          const color = sale.item_color as string
          if (!groupedData[color]) {
            groupedData[color] = { item_color: color, count: 0, revenue: 0 }
          }
          groupedData[color].count += 1
          groupedData[color].revenue += sale.price || 0
        })

        setColorData(Object.values(groupedData))
      }
    } else if (colorData) {
      setColorData(colorData)
    }

    // Fetch sales by date (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: dateData, error: dateError } = await supabaseClient
      .from('merchandise_sales')
      .select('sale_date, price')
      .eq('item_type', 'T-shirt')
      .gte('sale_date', thirtyDaysAgo.toISOString())
      .order('sale_date')

    if (dateError) {
      console.error('Error fetching date data:', dateError)
    } else if (dateData) {
      const groupedData: Record<string, SalesByDateData> = {}

      dateData.forEach(sale => {
        // Add null check and provide fallback to current date if sale_date is null
        const saleDate = sale.sale_date ? new Date(sale.sale_date) : new Date()
        const date = saleDate.toISOString().split('T')[0]
        if (!groupedData[date]) {
          groupedData[date] = { date, count: 0, revenue: 0 }
        }
        groupedData[date].count += 1
        groupedData[date].revenue += sale.price || 0
      })

      setDateData(Object.values(groupedData))
    }

    setLoading(false)
  }, [supabaseClient])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  return (
    <Card>
      <CardHeader>
        <CardTitle>T-shirt Sales Analytics</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-10">
            <Loader2 className="h-10 w-10 animate-spin text-gray-400" />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Sales</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{totalSales}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">€{totalRevenue.toFixed(2)}</p>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="size">
              <TabsList className="mb-4">
                <TabsTrigger value="size">By Size</TabsTrigger>
                <TabsTrigger value="color">By Color</TabsTrigger>
                <TabsTrigger value="date">By Date</TabsTrigger>
              </TabsList>

              <TabsContent value="size">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={sizeData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="item_size" />
                      <YAxis />
                      <Tooltip formatter={(value, name) => {
                        if (name === 'revenue') return [`€${(value as number).toFixed(2)}`, 'Revenue']
                        return [value, 'Sales']
                      }} />
                      <Legend />
                      <Bar dataKey="count" name="Sales" fill="#8884d8" />
                      <Bar dataKey="revenue" name="Revenue" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>

              <TabsContent value="color">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={colorData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="count"
                          nameKey="item_color"
                        >
                          {colorData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value, name, props) => {
                          return [`${value} sales`, props.payload.item_color]
                        }} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={colorData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="revenue"
                          nameKey="item_color"
                        >
                          {colorData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value, name, props) => {
                          return [`€${(value as number).toFixed(2)}`, props.payload.item_color]
                        }} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="date">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={dateData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value, name) => {
                        if (name === 'revenue') return [`€${(value as number).toFixed(2)}`, 'Revenue']
                        return [value, 'Sales']
                      }} />
                      <Legend />
                      <Bar dataKey="count" name="Sales" fill="#8884d8" />
                      <Bar dataKey="revenue" name="Revenue" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  )
}
