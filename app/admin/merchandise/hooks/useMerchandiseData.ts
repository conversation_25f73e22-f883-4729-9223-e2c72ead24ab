// app/admin/merchandise/hooks/useMerchandiseData.ts
'use client'

import { useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

export interface Color {
  id: string
  color_name: string
  color_hex: string
  display_order: number
  active: boolean
  created_at: string
}

export interface Size {
  id: string
  size_name: string
  display_order: number
  active: boolean
  created_at: string
}

export interface InventoryItem {
  id: string
  item_type: string
  item_size: string
  item_color: string
  quantity: number
  reorder_level: number
  last_updated: string
  created_at: string
}

export interface Sale {
  id: string
  pelatis_id: string
  item_type: string
  item_size: string | null
  item_color: string | null
  price: number
  payment_method: string
  sale_date: string
  comments: string | null
  created_at: string
  client_name?: string
  client_last_name?: string
}

export interface SalesFilter {
  size?: string
  color?: string
  startDate?: Date | null
  endDate?: Date | null
}

export function useMerchandiseData() {
  const supabase = createClientComponentClient()
  
  // State for entities
  const [colors, setColors] = useState<Color[]>([])
  const [sizes, setSizes] = useState<Size[]>([])
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [sales, setSales] = useState<Sale[]>([])
  
  // Loading states
  const [loadingColors, setLoadingColors] = useState(false)
  const [loadingSizes, setLoadingSizes] = useState(false) 
  const [loadingInventory, setLoadingInventory] = useState(false)
  const [loadingSales, setLoadingSales] = useState(false)

  // Fetch colors
  const fetchColors = async () => {
    setLoadingColors(true)
    try {
      const { data, error } = await supabase
        .from('merchandise_colors')
        .select('*')
        .order('display_order', { ascending: true })
      
      if (error) throw error
      if (data) setColors(data)
    } catch (error) {
      console.error('Error fetching colors:', error)
    } finally {
      setLoadingColors(false)
    }
  }

  // Fetch sizes
  const fetchSizes = async () => {
    setLoadingSizes(true)
    try {
      const { data, error } = await supabase
        .from('merchandise_sizes')
        .select('*')
        .order('display_order', { ascending: true })
      
      if (error) throw error
      if (data) setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
    } finally {
      setLoadingSizes(false)
    }
  }

  // Fetch inventory
  const fetchInventory = async () => {
    setLoadingInventory(true)
    try {
      const { data, error } = await supabase
        .from('merchandise_inventory')
        .select('*')
        .eq('item_type', 'T-shirt')
        .order('item_size', { ascending: true })
        .order('item_color', { ascending: true })
      
      if (error) throw error
      if (data) setInventory(data)
    } catch (error) {
      console.error('Error fetching inventory:', error)
    } finally {
      setLoadingInventory(false)
    }
  }

  // Fetch sales with optional filtering
  const fetchSales = async (filters?: SalesFilter) => {
    setLoadingSales(true)
    try {
      let query = supabase
        .from('merchandise_sales')
        .select(`
          *,
          pelates:pelatis_id (
            name,
            last_name
          )
        `)
        .eq('item_type', 'T-shirt')
      
      // Apply filters if provided
      if (filters) {
        if (filters.size) {
          query = query.eq('item_size', filters.size)
        }
        
        if (filters.color) {
          query = query.eq('item_color', filters.color)
        }
        
        if (filters.startDate) {
          query = query.gte('sale_date', filters.startDate.toISOString())
        }
        
        if (filters.endDate) {
          const endDate = new Date(filters.endDate)
          endDate.setHours(23, 59, 59, 999)
          query = query.lte('sale_date', endDate.toISOString())
        }
      }
      
      const { data, error } = await query.order('sale_date', { ascending: false })
      
      if (error) throw error
      if (data) {
        const formattedData = data.map(sale => ({
          ...sale,
          client_name: sale.pelates?.name || 'Unknown',
          client_last_name: sale.pelates?.last_name || '',
        }))
        setSales(formattedData)
      }
    } catch (error) {
      console.error('Error fetching sales:', error)
    } finally {
      setLoadingSales(false)
    }
  }

  // Fetch all data initially
  useEffect(() => {
    fetchColors()
    fetchSizes()
    fetchInventory()
    fetchSales()
  }, [])

  return {
    // Data
    colors,
    sizes,
    inventory,
    sales,
    
    // Loading states
    loadingColors,
    loadingSizes,
    loadingInventory,
    loadingSales,
    
    // Fetch functions
    fetchColors,
    fetchSizes,
    fetchInventory,
    fetchSales,
    
    // Utility functions
    getActiveSizes: () => sizes.filter(size => size.active),
    getActiveColors: () => colors.filter(color => color.active),
    findInventoryItem: (size: string, color: string) => 
      inventory.find(item => item.item_size === size && item.item_color === color),
    checkInventory: (size: string, color: string) => {
      const item = inventory.find(
        item => item.item_size === size && item.item_color === color
      )
      return item ? item.quantity > 0 : false
    }
  }
}