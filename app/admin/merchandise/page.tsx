// app/admin/merchandise/page.tsx
import { MerchandiseClient } from './MerchandiseClient'
import { Suspense } from 'react'
import { Loader2 } from 'lucide-react'

export const metadata = {
  title: 'Merchandise Management',
  description: 'Manage merchandise inventory and sales'
}

export default function MerchandisePage() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Merchandise Management</h1>
      
      <Suspense fallback={
        <div className="flex justify-center py-10">
          <Loader2 className="h-10 w-10 animate-spin text-gray-400" />
        </div>
      }>
        <MerchandiseClient />
      </Suspense>
    </div>
  )
}