// app/admin/merchandise/components/PageTabs.tsx
'use client'

import { ReactNode } from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'

interface Tab {
  id: string
  label: string
  content: ReactNode
}

interface PageTabsProps {
  tabs: Tab[]
  defaultTab?: string
}

export function PageTabs({ tabs, defaultTab }: PageTabsProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // Get current tab from URL or use default
  const currentTab = searchParams?.get('tab') || defaultTab || tabs[0]?.id
  
  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams?.toString() || '')
    params.set('tab', value)
    router.push(`${pathname}?${params.toString()}`, { scroll: false })
  }

  return (
    <Tabs value={currentTab} onValueChange={handleTabChange}>
      <TabsList className="mb-6">
        {tabs.map(tab => (
          <TabsTrigger key={tab.id} value={tab.id}>
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabs.map(tab => (
        <TabsContent key={tab.id} value={tab.id}>
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  )
}
