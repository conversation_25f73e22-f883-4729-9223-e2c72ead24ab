// app/admin/merchandise/MerchandiseClient.tsx
'use client'


import { PageTabs } from './components/PageTabs'
import { SalesFeature } from './features/sales'
import { InventoryFeature } from './features/inventory'
import { SettingsFeature } from './features/settings'
// Check this import path - it may be incorrect
import { MerchandiseAnalytics } from './features/analytics/Analytics'

export function MerchandiseClient() {
  // Define tabs for the main page
  const tabs = [
    {
      id: 'sales',
      label: 'Sales',
      content: <SalesFeature />
    },
    {
      id: 'inventory',
      label: 'Inventory',
      content: <InventoryFeature />
    },
    {
      id: 'analytics',
      label: 'Analytics',
      content: <MerchandiseAnalytics />
    },
    {
      id: 'settings',
      label: 'Settings',
      content: <SettingsFeature />
    }
  ]
  
  return (
    <div className="container mx-auto">
      <PageTabs tabs={tabs} defaultTab="sales" />
    </div>
  )
}
