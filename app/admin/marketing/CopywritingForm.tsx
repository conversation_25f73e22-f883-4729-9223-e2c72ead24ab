// components/admin/marketing/CopywritingForm.tsx
"use client";

import React, { useState, FormEvent, ChangeEvent, useCallback } from 'react';

interface FormDataTypes {
  targetAudience: {
    primaryGroup: string;
    ageRange: string;
    gender: string;
    painPoints: string;
    desires: string;
  };
  program: {
    name: string;
    type: string;
    mainBenefit: string;
    secondaryBenefits: string;
    uniqueSellingPoints: string;
    duration: string;
    frequency: string;
    price: string;
    startDate: string;
    limitedSpots: boolean;
    numberOfSpots: string;
  };
  marketingGoals: {
    primaryAction: string;
    urgencyFactor: string;
    promotionalOffer: string;
    timeLimit: string;
  };
  tone: {
    style: string;
    emotionalTrigger: string;
    hero: string;
    challenge: string;
    solution: string;
  };
  mediumSettings: {
    platform: string;
    format: string;
    characterLimit: string;
  };
  medicalExpertise: {
    includeReference: boolean;
    medicalBenefit: string;
    expertiseHighlight: string;
  };
}



const CopywritingForm = () => {
  // Initial state setup remains the same
  const [formData, setFormData] = useState<FormDataTypes>({
    // ...your existing initial state
    targetAudience: {
      primaryGroup: '',
      ageRange: '',
      gender: '',
      painPoints: '',
      desires: ''
    },
    program: {
      name: '',
      type: 'CrossFit',
      mainBenefit: '',
      secondaryBenefits: '',
      uniqueSellingPoints: '',
      duration: '',
      frequency: '',
      price: '',
      startDate: '',
      limitedSpots: false,
      numberOfSpots: ''
    },
    marketingGoals: {
      primaryAction: 'Sign up',
      urgencyFactor: '',
      promotionalOffer: '',
      timeLimit: ''
    },
    tone: {
      style: 'Motivational',
      emotionalTrigger: '',
      hero: '',
      challenge: '',
      solution: ''
    },
    mediumSettings: {
      platform: 'Facebook',
      format: 'Post',
      characterLimit: '500'
    },
    medicalExpertise: {
      includeReference: false,
      medicalBenefit: '',
      expertiseHighlight: ''
    }
  });
  
  const [jsonOutput, setJsonOutput] = useState('');
  const [submitted, setSubmitted] = useState(false);
  
// This function is used in form elements
  // Updated handleChange function
  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    const path = name.split('.');
    
    setFormData(prevData => {
      // Create a fresh copy
      const newData = { ...prevData };
      
      if (path.length === 2) {
        const section = path[0] as keyof FormDataTypes;
        const field = path[1];
        
        // Using a type-safe approach by recreating section objects
        switch(section) {
          case 'targetAudience': {
            const targetSection = { ...newData.targetAudience };
            // Type guard to ensure field is a valid key for this section
            if (field in targetSection) {
              // Using type assertion with key safety
              (targetSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.targetAudience = targetSection;
            }
            break;
          }
          case 'program': {
            const programSection = { ...newData.program };
            if (field in programSection) {
              (programSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.program = programSection;
            }
            break;
          }
          case 'marketingGoals': {
            const goalsSection = { ...newData.marketingGoals };
            if (field in goalsSection) {
              (goalsSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.marketingGoals = goalsSection;
            }
            break;
          }
          case 'tone': {
            const toneSection = { ...newData.tone };
            if (field in toneSection) {
              (toneSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.tone = toneSection;
            }
            break;
          }
          case 'mediumSettings': {
            const settingsSection = { ...newData.mediumSettings };
            if (field in settingsSection) {
              (settingsSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.mediumSettings = settingsSection;
            }
            break;
          }
          case 'medicalExpertise': {
            const expertiseSection = { ...newData.medicalExpertise };
            if (field in expertiseSection) {
              (expertiseSection as Record<string, string | boolean>)[field] = 
                type === 'checkbox' ? checked : value;
              newData.medicalExpertise = expertiseSection;
            }
            break;
          }
        }
      }
  // Note: We're removing the path.length === 3 case since it's not used in this form
      // If needed in the future, it can be reimplemented with proper typing
      
      return newData;
    });
  }, []);
  
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    
    const copywritingPrompt = `Create 10 iterations of compelling ad copy for LIFT Gym based on the following information:
      
Target Audience: ${formData.targetAudience.primaryGroup} (${formData.targetAudience.ageRange}, ${formData.targetAudience.gender})
Their Pain Points: ${formData.targetAudience.painPoints}
Their Desires: ${formData.targetAudience.desires}

Program: ${formData.program.name} (${formData.program.type})
Main Benefit: ${formData.program.mainBenefit}
Unique Selling Points: ${formData.program.uniqueSellingPoints}
Program Details: ${formData.program.duration}, ${formData.program.frequency}, ${formData.program.price}${formData.program.limitedSpots ? ', Limited to ' + formData.program.numberOfSpots + ' spots' : ''}

Marketing Goal: Get people to ${formData.marketingGoals.primaryAction}
Urgency: ${formData.marketingGoals.urgencyFactor}
Offer: ${formData.marketingGoals.promotionalOffer}${formData.marketingGoals.timeLimit ? ' (Valid until: ' + formData.marketingGoals.timeLimit + ')' : ''}

Tone: ${formData.tone.style}
Emotional Trigger: ${formData.tone.emotionalTrigger}
Story Elements: ${formData.tone.hero} facing ${formData.tone.challenge}, finding solution through ${formData.tone.solution}

Platform: ${formData.mediumSettings.platform} ${formData.mediumSettings.format} (${formData.mediumSettings.characterLimit} character limit)

${formData.medicalExpertise.includeReference ? 'Highlight Medical Expertise: ' + formData.medicalExpertise.medicalBenefit + ' - ' + formData.medicalExpertise.expertiseHighlight : ''}

Apply the following copywriting principles:
1. Focus on benefits over features
2. Use clear, simple language that speaks directly to the audience
3. Create an emotional connection
4. Include a strong hook to grab attention
5. Tell a compelling mini-story where appropriate
6. Address objections before they arise
7. Create a sense of urgency or exclusivity
8. End with a clear call to action
9. Use the Co-owner's medical expertise as a trust factor
10. Emphasize the transformation potential

For each ad copy version, vary the approach using different hooks, emotional triggers, or storytelling angles, while maintaining the core message and benefits.`;
    
    const finalJson = {
      ...formData,
      copywritingPrompt
    };
    
    setJsonOutput(JSON.stringify(finalJson, null, 2));
    setSubmitted(true);
  };
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonOutput);
    alert('Copied to clipboard!');
  };
  

  return (
<div className="bg-white rounded-lg shadow-md p-6">
      {!submitted ? (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Target Audience Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Target Audience</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Primary Group</label>
                <select 
                  name="targetAudience.primaryGroup" 
                  value={formData.targetAudience.primaryGroup}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Select a group</option>
                  <option value="Women with back pain">Women with back pain</option>
                  <option value="Mothers with young children">Mothers with young children</option>
                  <option value="Women interested in strength training">Women interested in strength training</option>
                  <option value="Teenagers (14+)">Teenagers (14+)</option>
                  <option value="Office workers">Office workers</option>
                  <option value="Adults over 30">Adults over 30</option>
                  <option value="Beginners to fitness">Beginners to fitness</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Age Range</label>
                <select 
                  name="targetAudience.ageRange" 
                  value={formData.targetAudience.ageRange}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Select age range</option>
                  <option value="14-18">14-18</option>
                  <option value="18-29">18-29</option>
                  <option value="30-40">30-40</option>
                  <option value="40-50">40-50</option>
                  <option value="50+">50+</option>
                  <option value="All ages">All ages</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Gender Focus</label>
                <select 
                  name="targetAudience.gender" 
                  value={formData.targetAudience.gender}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Select gender focus</option>
                  <option value="Women">Women</option>
                  <option value="Men">Men</option>
                  <option value="All genders">All genders</option>
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Pain Points</label>
                <input 
                  type="text" 
                  name="targetAudience.painPoints"
                  placeholder="e.g. back pain, lack of time, low energy" 
                  value={formData.targetAudience.painPoints}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Desires</label>
                <input 
                  type="text" 
                  name="targetAudience.desires"
                  placeholder="e.g. lose weight, gain strength, socialize" 
                  value={formData.targetAudience.desires}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
            </div>
          </div>
          
          {/* Program Details Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Program Details</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Program Name</label>
                <select 
                  name="program.name" 
                  value={formData.program.name}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Select a program</option>
                  <option value="Πλάτη Χωρίς Πόνο">Πλάτη Χωρίς Πόνο (Back Without Pain)</option>
                  <option value="LIFT για Μαμάδες">LIFT για Μαμάδες (LIFT for Moms)</option>
                  <option value="LIFT Foundations">LIFT Foundations</option>
                  <option value="CrossFit">CrossFit</option>
                  <option value="Brazilian Jiu-Jitsu">Brazilian Jiu-Jitsu</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Program Type</label>
                <select 
                  name="program.type" 
                  value={formData.program.type}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="CrossFit">CrossFit</option>
                  <option value="Strength Training">Strength Training</option>
                  <option value="Rehabilitation">Rehabilitation</option>
                  <option value="Group Fitness">Group Fitness</option>
                  <option value="Martial Arts">Martial Arts</option>
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Main Benefit</label>
                <input 
                  type="text" 
                  name="program.mainBenefit"
                  placeholder="e.g. Eliminate back pain in 4 weeks" 
                  value={formData.program.mainBenefit}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Unique Selling Points</label>
                <input 
                  type="text" 
                  name="program.uniqueSellingPoints"
                  placeholder="e.g. medical supervision, small groups, custom programming" 
                  value={formData.program.uniqueSellingPoints}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Duration</label>
                <input 
                  type="text" 
                  name="program.duration"
                  placeholder="e.g. 4 weeks, 2 months" 
                  value={formData.program.duration}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Frequency</label>
                <input 
                  type="text" 
                  name="program.frequency"
                  placeholder="e.g. 2x per week" 
                  value={formData.program.frequency}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Price</label>
                <input 
                  type="text" 
                  name="program.price"
                  placeholder="e.g. €120, €55/month" 
                  value={formData.program.price}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="limitedSpots"
                  name="program.limitedSpots"
                  checked={formData.program.limitedSpots}
                  onChange={handleChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="limitedSpots" className="text-sm font-medium">
                  Limited Spots Available
                </label>
              </div>
              
              {formData.program.limitedSpots && (
                <div>
                  <label className="block text-sm font-medium mb-1">Number of Spots</label>
                  <input 
                    type="text" 
                    name="program.numberOfSpots"
                    placeholder="e.g. 8, 10, 12" 
                    value={formData.program.numberOfSpots}
                    onChange={handleChange}
                    className="w-full p-2 border rounded"
                  />
                </div>
              )}
            </div>
          </div>
          
          {/* Marketing Goals Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Marketing Goals</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Primary Action</label>
                <select 
                  name="marketingGoals.primaryAction" 
                  value={formData.marketingGoals.primaryAction}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="Sign up">Sign up</option>
                  <option value="Book a free session">Book a free session</option>
                  <option value="Call for information">Call for information</option>
                  <option value="Visit the gym">Visit the gym</option>
                  <option value="Join the free info session">Join the free info session</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Urgency Factor</label>
                <input 
                  type="text" 
                  name="marketingGoals.urgencyFactor"
                  placeholder="e.g. Limited spots, Early bird pricing ends soon" 
                  value={formData.marketingGoals.urgencyFactor}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Promotional Offer</label>
                <input 
                  type="text" 
                  name="marketingGoals.promotionalOffer"
                  placeholder="e.g. 10% discount, Free consultation" 
                  value={formData.marketingGoals.promotionalOffer}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Time Limit</label>
                <input 
                  type="text" 
                  name="marketingGoals.timeLimit"
                  placeholder="e.g. March 15, This weekend only" 
                  value={formData.marketingGoals.timeLimit}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
            </div>
          </div>
          
          {/* Tone & Style Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Tone & Style</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Writing Style</label>
                <select 
                  name="tone.style" 
                  value={formData.tone.style}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="Motivational">Motivational</option>
                  <option value="Informative/Educational">Informative/Educational</option>
                  <option value="Friendly/Conversational">Friendly/Conversational</option>
                  <option value="Professional/Medical">Professional/Medical</option>
                  <option value="Empathetic">Empathetic</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Emotional Trigger</label>
                <select 
                  name="tone.emotionalTrigger" 
                  value={formData.tone.emotionalTrigger}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Select an emotion</option>
                  <option value="Fear of missing out">Fear of missing out</option>
                  <option value="Desire for transformation">Desire for transformation</option>
                  <option value="Frustration with current situation">Frustration with current situation</option>
                  <option value="Hope for a better future">Hope for a better future</option>
                  <option value="Pride in achievement">Pride in achievement</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Story Hero</label>
                <input 
                  type="text" 
                  name="tone.hero"
                  placeholder="e.g. Busy mom, Office worker with back pain" 
                  value={formData.tone.hero}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Challenge</label>
                <input 
                  type="text" 
                  name="tone.challenge"
                  placeholder="e.g. chronic back pain, lack of energy" 
                  value={formData.tone.challenge}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Solution</label>
                <input 
                  type="text" 
                  name="tone.solution"
                  placeholder="e.g. medically supervised exercise, supportive community" 
                  value={formData.tone.solution}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
            </div>
          </div>
          
          {/* Medium Settings Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Ad Platform & Format</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Platform</label>
                <select 
                  name="mediumSettings.platform" 
                  value={formData.mediumSettings.platform}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="Facebook">Facebook</option>
                  <option value="Instagram">Instagram</option>
                  <option value="Google">Google</option>
                  <option value="Local Newspaper">Local Newspaper</option>
                  <option value="Email">Email</option>
                  <option value="Website">Website</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Format</label>
                <select 
                  name="mediumSettings.format" 
                  value={formData.mediumSettings.format}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="Post">Post</option>
                  <option value="Ad">Ad</option>
                  <option value="Story">Story</option>
                  <option value="Email Subject Line">Email Subject Line</option>
                  <option value="Email Body">Email Body</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Character Limit</label>
                <input 
                  type="text" 
                  name="mediumSettings.characterLimit"
                  placeholder="e.g. 500, 1000, 2000" 
                  value={formData.mediumSettings.characterLimit}
                  onChange={handleChange}
                  className="w-full p-2 border rounded"
                />
              </div>
            </div>
          </div>
          
          {/* Medical Expertise Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Medical Expertise Leverage</h3>
            
            <div className="flex items-center mb-4">
              <input 
                type="checkbox" 
                id="includeReference"
                name="medicalExpertise.includeReference"
                checked={formData.medicalExpertise.includeReference}
                onChange={handleChange}
                className="h-4 w-4 mr-2"
              />
              <label htmlFor="includeReference" className="text-sm font-medium">
                Include reference to co-owner&apos;s medical expertise
              </label>
            </div>
            
            {formData.medicalExpertise.includeReference && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Medical Benefit</label>
                  <input 
                    type="text" 
                    name="medicalExpertise.medicalBenefit"
                    placeholder="e.g. Scientifically proven techniques" 
                    value={formData.medicalExpertise.medicalBenefit}
                    onChange={handleChange}
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Expertise Highlight</label>
                  <input 
                    type="text" 
                    name="medicalExpertise.expertiseHighlight"
                    placeholder="e.g. All programs medically supervised" 
                    value={formData.medicalExpertise.expertiseHighlight}
                    onChange={handleChange}
                    className="w-full p-2 border rounded"
                  />
                </div>
              </div>
            )}
          </div>
          
          <div className="pt-4">
            <button 
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded"
            >
              Generate Copywriting Prompt
            </button>
          </div>
        </form>
      ) : (
        <div>
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Your Copywriting Prompt</h2>
            <p className="text-gray-600">
              Copy this JSON output to use in your copywriting tool or with Claude
            </p>
          </div>
          
          <div className="bg-gray-100 p-4 rounded overflow-auto max-h-96 mb-6">
            <pre className="text-sm">{jsonOutput}</pre>
          </div>
          
          <div className="flex justify-between">
            <button 
              onClick={() => setSubmitted(false)}
              className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
            >
              Back to Form
            </button>
            <button 
              onClick={copyToClipboard}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            >
              Copy to Clipboard
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CopywritingForm;