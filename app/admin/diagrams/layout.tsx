// app/admin/diagrams/layout.tsx
'use client'

import Script from 'next/script'
import { useEffect } from 'react'

export default function DiagramsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  useEffect(() => {
    // Initialize mermaid when component mounts
    if (typeof window !== 'undefined') {
      import('mermaid').then((mermaid) => {
        mermaid.default.initialize({
          theme: 'default',
          securityLevel: 'loose',
        })
        mermaid.default.contentLoaded()
      })
    }
  }, [])

  return (
    <>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"
        strategy="lazyOnload"
      />
      <div className="min-h-screen bg-background">{children}</div>
    </>
  )
}