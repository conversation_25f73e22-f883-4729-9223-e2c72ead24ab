"use client";

import { useState, useEffect, useCallback } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Database } from "@/types/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";

type Goal = Database['public']['Tables']['user_goals']['Row'] | Database['public']['Tables']['assigned_goals']['Row'];

const getGoalStatus = (goal: Goal) => {
  if (goal.current_value >= goal.target_value) {
    return 'completed';
  }
  if (goal.current_value > 0) {
    return 'in-progress';
  }
  return 'pending';
};

export default function GoalsPage() {
  const supabase = createClientComponentClient<Database>();
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchGoals = useCallback(async () => {
    try {
      setLoading(true);
      const { data: userGoals, error: userError } = await supabase
        .from('user_goals')
        .select('*')
        .order('created_at', { ascending: false });

      const { data: assignedGoals, error: assignedError } = await supabase
        .from('assigned_goals')
        .select('*')
        .order('created_at', { ascending: false });

      if (userError || assignedError) {
        throw userError || assignedError;
      }

      setGoals([...(userGoals || []), ...(assignedGoals || [])]);
    } catch (error) {
      console.error('Error fetching goals:', error);
      toast.error('Failed to fetch goals');
    } finally {
      setLoading(false);
    }
  }, [supabase])

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const filteredGoals = goals.filter(goal =>
    goal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    goal.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    goal.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Goals Management</h1>
        <div className="flex gap-4">
          <Input
            placeholder="Search goals..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          <Button onClick={fetchGoals} disabled={loading}>
            Refresh
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Progress</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>Target Date</TableHead>
              <TableHead>Type</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  Loading...
                </TableCell>
              </TableRow>
            ) : filteredGoals.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No goals found
                </TableCell>
              </TableRow>
            ) : (
              filteredGoals.map((goal) => (
                <TableRow key={goal.id}>
                  <TableCell className="font-medium">{goal.title}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{goal.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(getGoalStatus(goal))}>
                      {getGoalStatus(goal)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress
                        value={(goal.current_value / goal.target_value) * 100}
                        className="w-[100px]"
                      />
                      <span className="text-sm">
                        {goal.current_value} / {goal.target_value}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(goal.start_date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {('due_date' in goal ? goal.due_date : goal.target_date)
                      ? new Date('due_date' in goal ? goal.due_date! : goal.target_date!).toLocaleDateString()
                      : '-'}
                  </TableCell>
                  <TableCell>
                    {'assigned_by' in goal ? 'Assigned' : 'Personal'}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
