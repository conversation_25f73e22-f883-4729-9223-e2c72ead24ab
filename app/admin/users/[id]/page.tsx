'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format, parseISO } from 'date-fns';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';

// UI Components
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";


// Custom Components
import { UserWODs } from "@/components/wods/UserWODs";
import AttendanceMetrics from "@/components/AttendanceMetrics";
import UserTagManager from '@/components/UserTagManager';
import UserAccountStatus from '@/components/UserAccountStatus';
import { AdminBadgeManagement } from '@/components/goals_badges/AdminBadgeManagement';
import { AdminGoalManagement } from '@/components/goals_badges/AdminGoalManagement';

// Types
type Pelati = Database['public']['Tables']['pelates']['Row'];
type Payment = Database['public']['Tables']['pliromes']['Row'];
type Tag = Database['public']['Tables']['tags']['Row'];
// Adjust the Checkin type to match the CheckIn type from Supabase
type Checkin = Database['public']['Tables']['check_ins']['Row'] & {
  sessions?: { start_time: string | null };
  session_start_time?: string;
  check_in_time: string; // Remove the | null to match CheckIn type
};
type ActiveSubscription = Database['public']['Views']['active_subscriptions']['Row'];

type GroupedCheckins = {
  [key: string]: {
    total: number;
    days: {
      [key: string]: { date: Date; times: string[] };
    };
  };
};

type GroupedPaymentsWithTotals = {
  [key: string]: { 
    total: number; 
    dates: string[]; 
    payments: Payment[] 
  };
};

interface PageProps {
  params: {
    id: string;
  };
}

const MONTH_NAMES = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

const DAY_HEADERS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function UserPage({ params }: PageProps) {
  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  // State Management
  const [pelati, setPelati] = useState<Pelati | null>(null);
  const [checkins, setCheckins] = useState<Checkin[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [activeSubscription, setActiveSubscription] = useState<ActiveSubscription | null>(null);
  const [tags, setTags] = useState<Tag[]>([]);
  const [expandedMonth, setExpandedMonth] = useState<string | null>(null);
  const [isEditingSidebar, setIsEditingSidebar] = useState(false);
  const [formData, setFormData] = useState<Partial<Pelati>>({});
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCheckin, setSelectedCheckin] = useState<Checkin | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());


  // Data Fetching
  const fetchPelatiData = useCallback(async () => {
    try {
      const { data: pelatiData, error: pelatiError } = await supabase
        .from('pelates')
        .select('*')
        .eq('id', params.id)
        .single();

      if (pelatiError) throw pelatiError;
      setPelati(pelatiData);
      setFormData(pelatiData);

      const { data: checkinsData, error: checkinsError } = await supabase
      .from('check_ins')
      .select('*, sessions(start_time)')
      .eq('pelatis_id', params.id);

    if (checkinsError) throw checkinsError;

    // Modify the mapping to ensure check_in_time is always a string
    const formattedCheckins: Checkin[] = checkinsData.map(checkin => ({
      ...checkin,
      check_in_time: checkin.check_in_time || '', // Provide empty string if null
      sessions: checkin.sessions || undefined,
      session_start_time: checkin.sessions?.start_time || undefined
    }));
    
    setCheckins(formattedCheckins);

      
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('pliromes')
        .select('*')
        .eq('pelates_id', params.id)
        .order('date_money_gave', { ascending: false });

      if (paymentsError) throw paymentsError;
      setPayments(paymentsData);

      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('active_subscriptions')
        .select('*')
        .eq('client_id', params.id)
        .maybeSingle();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        throw subscriptionError;
      }
      setActiveSubscription(subscriptionData);

      const { data: tagsData, error: tagsError } = await supabase
        .from('pelates_tags')
        .select('tags!inner(id, name, color, created_at)')
        .eq('pelatis_id', params.id);

      if (tagsError) throw tagsError;
      setTags(tagsData?.map(item => item.tags) || []);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch user data');
    }
  }, [supabase, params.id]);

  useEffect(() => {
    fetchPelatiData();
  }, [fetchPelatiData]);

  // Utility Functions
  const formatDate = useCallback((dateString: string | null): string => {
    if (!dateString) return '-';
    return format(parseISO(dateString), 'dd/MM/yyyy');
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handlePrevMonth = () => {
    setCurrentMonth(prev => {
      if (prev === 0) {
        setCurrentYear(year => year - 1); // This could go back indefinitely
        return 11;
      }
      return prev - 1;
    });
  };
  const handleNextMonth = () => {
    const maxYear = new Date().getFullYear() + 1; // Allow up to next year
    
    setCurrentMonth(prev => {
      if (prev === 11) {
        if (currentYear < maxYear) {
          setCurrentYear(year => year + 1);
          return 0;
        }
        return prev;
      }
      return prev + 1;
    });
  };

// Memoized Calculations
const groupedCheckins = useMemo(() => {
  const grouped: GroupedCheckins = {};
  
  checkins.forEach(checkin => {
    if (checkin.check_in_time) {
      const date = parseISO(checkin.check_in_time);
      const monthKey = format(date, 'MMMM yyyy');
      const dayKey = format(date, 'dd/MM/yyyy');
      const timeString = format(parseISO(checkin.session_start_time || checkin.check_in_time), 'HH:mm');

      if (!grouped[monthKey]) {
        grouped[monthKey] = { total: 0, days: {} };
      }
      grouped[monthKey].total += 1;
      if (!grouped[monthKey].days[dayKey]) {
        grouped[monthKey].days[dayKey] = { date, times: [] };
      }
      grouped[monthKey].days[dayKey].times.push(timeString);
    }
  });

  return Object.fromEntries(
    Object.entries(grouped)
      .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
  );
}, [checkins]);

const totalMoneyPaid = useMemo(() => 
  payments.reduce((sum, payment) => sum + (payment.money_gave || 0), 0),
[payments]);

const groupedPaymentsWithTotals = useMemo(() => {
  const grouped: GroupedPaymentsWithTotals = {};
  
  payments.forEach(payment => {
    if (payment.date_money_gave) {
      const date = parseISO(payment.date_money_gave);
      const monthKey = format(date, 'MMMM yyyy');
      
      if (!grouped[monthKey]) {
        grouped[monthKey] = { 
          total: 0, 
          dates: [], 
          payments: [] 
        };
      }

      if (typeof payment.money_gave === 'number') {
        grouped[monthKey].total += payment.money_gave;
      }
      
      grouped[monthKey].dates.push(formatDate(payment.date_money_gave));
      grouped[monthKey].payments.push(payment);
    }
  });

  Object.values(grouped).forEach(monthData => {
    monthData.dates.sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
    monthData.payments.sort((a, b) => {
      const dateA = a.date_money_gave ? new Date(a.date_money_gave) : new Date(0);
      const dateB = b.date_money_gave ? new Date(b.date_money_gave) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });
  });

  return Object.fromEntries(
    Object.entries(grouped)
      .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
  );
}, [payments, formatDate]);

const monthsData = useMemo(() => {
  const months: {[key: string]: { 
    checkinsData?: typeof groupedCheckins[string],
    paymentsData?: typeof groupedPaymentsWithTotals[string]
  }} = {};

  Object.entries(groupedCheckins).forEach(([month, data]) => {
    if (!months[month]) months[month] = {};
    months[month].checkinsData = data;
  });

  Object.entries(groupedPaymentsWithTotals).forEach(([month, data]) => {
    if (!months[month]) months[month] = {};
    months[month].paymentsData = data;
  });

  return Object.fromEntries(
    Object.entries(months)
      .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
  );
}, [groupedCheckins, groupedPaymentsWithTotals]);

const totalMonths = useMemo(() => 
  Object.keys(groupedCheckins).length,
[groupedCheckins]);

// Calendar Functions
function renderCalendar() {
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
  const today = new Date();
  const calendar = [];

  // Empty cells for days before the first of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendar.push(
      <div key={`empty-${i}`} className="min-h-[60px]" />
    );
  }

  // Calendar days
  for (let day = 1; day <= daysInMonth; day++) {
    const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const currentDate = new Date(dateString);
    const dayCheckins = checkins.filter(checkin => 
      checkin.check_in_time && 
      new Date(checkin.check_in_time).toDateString() === currentDate.toDateString()
    );
    const dayPayments = payments.filter(payment => 
      payment.date_money_gave && 
      new Date(payment.date_money_gave).toDateString() === currentDate.toDateString()
    );

    const isToday = currentDate.toDateString() === today.toDateString();

    calendar.push(
      <Card key={day} className={`min-h-[60px] ${isToday ? 'bg-blue-50' : ''} flex flex-col`}>
        <CardHeader className="p-1">
          <div className="font-bold text-xs">{day}</div>
        </CardHeader>
        <CardContent className="p-1 flex-grow flex flex-col">
          {dayCheckins.map(checkin => (
            <Button
              key={checkin.id}
              variant="secondary"
              size="sm"
              className="w-full mb-1 text-left justify-start text-xs"
              onClick={() => setSelectedCheckin(checkin)}
            >
              <span className="truncate">
                {format(parseISO(checkin.session_start_time || checkin.check_in_time!), 'HH:mm')}
              </span>
            </Button>
          ))}
          {dayPayments.map(payment => (
            <Button
              key={payment.id}
              variant="outline"
              size="sm"
              className="w-full mb-1 text-left justify-start text-xs bg-green-50"
              onClick={() => setSelectedPayment(payment)}
            >
              <span className="truncate">
                €{payment.money_gave?.toFixed(2)}
              </span>
            </Button>
          ))}
        </CardContent>
      </Card>
    );
  }

  return calendar;
}

// Event Handlers
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  try {
    const { error } = await supabase
      .from('pelates')
      .update(formData)
      .eq('id', params.id);
    if (error) throw error;
    await fetchPelatiData();
    setIsEditingSidebar(false);
    toast.success('User updated successfully');
  } catch (error) {
    console.error('Error updating user:', error);
    toast.error('Failed to update user');
  }
};

const handleDeleteUser = async () => {
  try {
    const { error } = await supabase
      .from('pelates')
      .delete()
      .eq('id', params.id);
    if (error) throw error;
    router.push('/admin/users/view');
    toast.success('User deleted successfully');
  } catch (error) {
    console.error('Error deleting user:', error);
    toast.error('Failed to delete user');
  }
};

if (!pelati) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
    </div>
  );
}

return (
  <div className="container mx-auto p-4">
    {/* Header Section */}
    <div className="flex justify-between items-center mb-4">
      <Link href="/admin/users/view">
        <Button>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Users
        </Button>
      </Link>
      <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
        <Trash2 className="mr-2 h-4 w-4" /> Delete User
      </Button>
    </div>

    {/* User Account Status */}
    <div className="mb-8">
      <UserAccountStatus 
        pelatiId={params.id}
        email={pelati?.email || null}
        onAccountCreated={fetchPelatiData}
      />
    </div>

    {/* User Header */}
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
      <h1 className="text-2xl font-bold mb-2 sm:mb-0">
        {pelati.name} {pelati.last_name}
      </h1>
      <div className="flex items-center">
        <span className={`mr-4 px-2 py-1 rounded ${
          activeSubscription?.subscription_status === 'active' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
        }`}>
          {activeSubscription?.subscription_status === 'active' ? 'Active' : 'Inactive'}
        </span>
        <Button onClick={() => setIsEditingSidebar(true)}>
          <Edit className="mr-2 h-4 w-4" /> Edit
        </Button>
      </div>
    </div>

    {/* User Summary */}
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
      <div>
        <h2 className="text-xl font-semibold mb-2">Personal Information</h2>
        <div className="space-y-2">
          <p><strong>Email:</strong> {pelati.email || '-'}</p>
          <p><strong>Phone:</strong> {pelati.phone || '-'}</p>
          <p><strong>Created At:</strong> {formatDate(pelati.created_at)}</p>
        </div>
      </div>
      <div>
        <h2 className="text-xl font-semibold mb-2">Summary</h2>
        <div className="space-y-2">
          <p><strong>Total Months Active:</strong> {totalMonths}</p>
          <p><strong>Total Money Paid:</strong> €{totalMoneyPaid.toFixed(2)}</p>
          {activeSubscription && (
            <p><strong>Current Plan:</strong> {activeSubscription.program_name_display}</p>
          )}
        </div>
      </div>
    </div>

    {/* Tags Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Tags</h2>
      <UserTagManager 
        userId={params.id} 
        initialTags={tags} 
        onTagsChange={setTags} 
      />
    </section>

    {/* Achievements & Goals Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Achievements & Goals</h2>
      <Tabs defaultValue="badges">
        <TabsList>
          <TabsTrigger value="badges">Badges & Achievements</TabsTrigger>
          <TabsTrigger value="goals">Goals & Progress</TabsTrigger>
        </TabsList>
        <TabsContent value="badges">
          <AdminBadgeManagement userId={params.id} />
        </TabsContent>
        <TabsContent value="goals">
          <AdminGoalManagement userId={params.id} />
        </TabsContent>
      </Tabs>
    </section>

    {/* Attendance Metrics Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Attendance Metrics</h2>
      <AttendanceMetrics 
        checkIns={checkins}
        payments={payments}
        groupedCheckins={groupedCheckins}
      />
    </section>

    {/* Monthly Data Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Monthly Activity</h2>
      {Object.entries(monthsData).map(([month, data]) => (
        <div key={month} className="mb-4">
          <Button 
            onClick={() => setExpandedMonth(expandedMonth === month ? null : month)}
            className="w-full flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 p-4 text-left"
          >
            <div>
              <span className="font-bold">{month}</span>
              <span className="ml-2">
                Check-ins: {data.checkinsData?.total || 0}
              </span>
            </div>
            <div className="mt-2 sm:mt-0">
              <span className="font-bold">
                Total Payments: €{(data.paymentsData?.total || 0).toFixed(2)}
              </span>
              {data.paymentsData?.dates && data.paymentsData.dates.length > 0 && (
                <span className="ml-2 text-sm text-gray-600">
                  ({data.paymentsData.dates.join(', ')})
                </span>
              )}
            </div>
          </Button>
          
          {expandedMonth === month && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {data.checkinsData && (
                <div>
                  <h3 className="font-semibold mb-2">Check-ins</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Times</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(data.checkinsData.days).map(([dayKey, { times }]) => (
                        <TableRow key={dayKey}>
                          <TableCell>{dayKey}</TableCell>
                          <TableCell>{times.join(', ')}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
              
              {data.paymentsData && data.paymentsData.payments.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-2">Payments</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Program</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.paymentsData.payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>{formatDate(payment.date_money_gave)}</TableCell>
                          <TableCell>€{payment.money_gave?.toFixed(2) || '-'}</TableCell>
                          <TableCell>{payment.course || '-'}</TableCell>
                          <TableCell>
                            {payment.nodebt ? 'Paid' : 'Unpaid'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </section>

    {/* Calendar Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Calendar View</h2>
      <div className="overflow-x-auto">
        <div className="mb-4 flex justify-between items-center">
          <Button onClick={handlePrevMonth}>Previous</Button>
          <span className="text-lg font-semibold">
            {MONTH_NAMES[currentMonth]} {currentYear}
          </span>
          <Button onClick={handleNextMonth}>Next</Button>
        </div>
        <div className="grid grid-cols-7 gap-1">
          {DAY_HEADERS.map(day => (
            <div key={day} className="font-bold text-center py-2 text-sm">
              {day}
            </div>
          ))}
          {renderCalendar()}
        </div>
      </div>
    </section>

    {/* Workout History Section */}
    <section className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Workout History</h2>
      <UserWODs userId={params.id} />
    </section>

    {/* Edit Dialog */}
    <Dialog open={isEditingSidebar} onOpenChange={setIsEditingSidebar}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">First Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name || ''}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                name="last_name"
                value={formData.last_name || ''}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email || ''}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleInputChange}
              />
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditingSidebar(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Update
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this user?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user account
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Check-in Details Dialog */}
      {selectedCheckin && (
        <Dialog open={!!selectedCheckin} onOpenChange={() => setSelectedCheckin(null)}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Check-in Details</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p><strong>Date:</strong> {formatDate(selectedCheckin.check_in_time)}</p>
              {selectedCheckin.check_in_time && (
                <p><strong>Time:</strong> {format(parseISO(selectedCheckin.check_in_time), 'HH:mm')}</p>
              )}
              {selectedCheckin.sessions?.start_time && (
                <p><strong>Session Start:</strong> {format(parseISO(selectedCheckin.sessions.start_time), 'HH:mm')}</p>
              )}
            </div>
            <DialogFooter>
              <Button onClick={() => setSelectedCheckin(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Payment Details Dialog */}
      {selectedPayment && (
        <Dialog open={!!selectedPayment} onOpenChange={() => setSelectedPayment(null)}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Payment Details</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p><strong>Date:</strong> {formatDate(selectedPayment.date_money_gave)}</p>
              <p><strong>Amount:</strong> €{selectedPayment.money_gave?.toFixed(2) || '-'}</p>
              <p><strong>Program:</strong> {selectedPayment.course || '-'}</p>
              <p><strong>Status:</strong> {selectedPayment.nodebt ? 'Paid' : 'Unpaid'}</p>
              {selectedPayment.comments && (
                <p><strong>Comments:</strong> {selectedPayment.comments}</p>
              )}
            </div>
            <DialogFooter>
              <Button onClick={() => setSelectedPayment(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}