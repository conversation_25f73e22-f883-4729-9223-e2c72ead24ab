"use client"
import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { ChevronLeft, ChevronRight, Edit2, Flame } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import ReactMarkdown from 'react-markdown';
import type { Database } from '@/types/supabase';
import { PostgrestError } from '@supabase/supabase-js';

interface WorkoutDetails {
  date: string;
  day: string | null;
  main_lift: string | null;
  workout_c: string | null;
  workout_b: string | null;
  wod?: {
    content: string;
    is_published: boolean | null;
    warmup: string | null;
  }
}
interface EditDialogProps {
  workout: WorkoutDetails;
  onSave: (updatedWorkout: WorkoutDetails) => Promise<void>;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const EditDialog = ({ workout, onSave, isOpen, onOpenChange }: EditDialogProps) => {
  const [editedWorkout, setEditedWorkout] = useState<WorkoutDetails>(workout);

  const handleSave = async () => {
    await onSave(editedWorkout);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Workout - {new Date(workout.date).toLocaleDateString()}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="main_lift">Main Lift</label>
            <Input
              id="main_lift"
              value={editedWorkout.main_lift || ''}
              onChange={(e) => setEditedWorkout(prev => ({ 
                ...prev, 
                main_lift: e.target.value || null 
              }))}
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="workout_b">Workout B</label>
            <Textarea
              id="workout_b"
              value={editedWorkout.workout_b || ''}
              onChange={(e) => setEditedWorkout(prev => ({ 
                ...prev, 
                workout_b: e.target.value || null 
              }))}
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="workout_c">Conditioning</label>
            <Textarea
              id="workout_c"
              value={editedWorkout.workout_c || ''}
              onChange={(e) => setEditedWorkout(prev => ({ 
                ...prev, 
                workout_c: e.target.value || null 
              }))}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};


const MainLiftsCalendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2024, 9, 1));
  const [workouts, setWorkouts] = useState<WorkoutDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingWorkout, setEditingWorkout] = useState<WorkoutDetails | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const supabase = createClientComponentClient<Database>();

  const fetchWorkouts = async (year: number, month: number) => {
    setLoading(true);
    try {
      const startDate = `${year}-${String(month + 1).padStart(2, '0')}-01`;
      const lastDay = new Date(year, month + 1, 0).getDate();
      const endDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
  
      const { data: detailsData, error: detailsError } = await supabase
        .from('wods_details')
        .select('date, day, main_lift, workout_c, workout_b')
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date') as {
          data: Database['public']['Tables']['wods_details']['Row'][] | null;
          error: PostgrestError | null;
        };
  
      const { data: wodsData, error: wodsError } = await supabase
        .from('wod')
        .select('date, content, is_published, warmup')
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date') as {
          data: Database['public']['Tables']['wod']['Row'][] | null;
          error: PostgrestError | null;
        };
  
      if (detailsError) throw detailsError;
      if (wodsError) throw wodsError;
  
      const combinedWorkouts = (detailsData || []).map(detail => {
        const matchingWod = (wodsData || []).find(wod => wod.date === detail.date);
        return {
          date: detail.date,
          day: detail.day,
          main_lift: detail.main_lift,
          workout_c: detail.workout_c,
          workout_b: detail.workout_b,
          wod: matchingWod ? {
            content: matchingWod.content,
            is_published: matchingWod.is_published,
            warmup: matchingWod.warmup
          } : undefined
        } satisfies WorkoutDetails;
      });
  
      setWorkouts(combinedWorkouts);
    } catch (err) {
      console.error('Error fetching workouts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch workouts');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveWorkout = async (updatedWorkout: WorkoutDetails) => {
    try {
      const { error } = await supabase
        .from('wods_details')
        .update({
          main_lift: updatedWorkout.main_lift,
          workout_b: updatedWorkout.workout_b,
          workout_c: updatedWorkout.workout_c
        })
        .eq('date', updatedWorkout.date);

      if (error) throw error;

      setWorkouts(prev =>
        prev.map(workout =>
          workout.date === updatedWorkout.date
            ? { ...workout, ...updatedWorkout }
            : workout
        )
      );
    } catch (err) {
      console.error('Error updating workout:', err);
      setError(err instanceof Error ? err.message : 'Failed to update workout');
    }
  };

  const previousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const nextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const getDayOfWeek = (date: Date): number => {
    const day = date.getDay();
    if (day === 0 || day === 6) return -1; // Weekend
    return day - 1;
  };

  const getCalendarWeeks = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    const weeks: Array<Array<{ date: Date | null; workout: WorkoutDetails | null }>> = [];
    let currentWeek: Array<{ date: Date | null; workout: WorkoutDetails | null }> = [];
    
    // Initialize first week with empty days until first day of month
    const firstDayOfWeek = getDayOfWeek(firstDay);
    if (firstDayOfWeek !== -1) {
      for (let i = 0; i < firstDayOfWeek; i++) {
        currentWeek.push({ date: null, workout: null });
      }
    }
    
    // Fill in all days of the month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const currentDate = new Date(year, month, day);
      const dayOfWeek = getDayOfWeek(currentDate);
      
      if (dayOfWeek !== -1) { // Skip weekends
        const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const workoutData = workouts.find(w => w.date === dateString);
        
        currentWeek.push({
          date: currentDate,
          workout: workoutData || null
        });
        
        if (currentWeek.length === 5) {
          weeks.push(currentWeek);
          currentWeek = [];
        }
      }
    }
    
    // Add remaining days if any
    if (currentWeek.length > 0) {
      while (currentWeek.length < 5) {
        currentWeek.push({ date: null, workout: null });
      }
      weeks.push(currentWeek);
    }
    
    return weeks;
  };

  useEffect(() => {
    fetchWorkouts(currentDate.getFullYear(), currentDate.getMonth());
  }, [currentDate]);

  const weeks = getCalendarWeeks(currentDate);
  const monthName = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });


  const WorkoutCell = ({ day }: { day: { date: Date | null; workout: WorkoutDetails | null } }) => {
    if (!day.date || !day.workout) return (
      <div className="min-h-40 p-2 border bg-gray-50">
        {day.date && <div className="mt-1 text-xs text-gray-400">No workout</div>}
      </div>
    );


    return (
      <div className="min-h-40 p-2 border hover:bg-gray-50">
        <div className="h-full flex flex-col">
          <div className="flex justify-between items-start mb-2">
            <span className="text-sm font-semibold">{day.date.getDate()}</span>
            <div className="flex gap-1">
              {day.workout.wod?.warmup && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="icon" className="h-6 w-6">
                      <Flame className="h-4 w-4 text-orange-500" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80">
                    <div className="space-y-2">
                      <h4 className="font-medium">Warmup</h4>
                      <ReactMarkdown className="prose prose-sm max-w-none">
                        {day.workout.wod.warmup}
                      </ReactMarkdown>
                    </div>
                  </PopoverContent>
                </Popover>
              )}
              <Button
                variant="outline"
                size="icon"
                className="h-6 w-6"
                onClick={() => {
                  setEditingWorkout(day.workout);
                  setIsEditDialogOpen(true);
                }}
              >
                <Edit2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex flex-col space-y-3 text-xs">
            {/* WOD Content */}
            {day.workout.wod?.is_published && day.workout.wod?.content && (
              <div>
                <span className="font-medium text-gray-600">WOD:</span>
                <ReactMarkdown className="prose prose-sm max-w-none">
                  {day.workout.wod.content}
                </ReactMarkdown>
              </div>
            )}
            
            {/* Main Lift */}
            {day.workout.main_lift && (
              <div>
                <span className="font-medium text-blue-600">Main Lift:</span>
                <div>{day.workout.main_lift}</div>
              </div>
            )}
            
            {/* Workout B */}
            {day.workout.workout_b && (
              <div>
                <span className="font-medium text-purple-600">Workout B:</span>
                <ReactMarkdown className="prose prose-sm max-w-none">
                  {day.workout.workout_b}
                </ReactMarkdown>
              </div>
            )}

            {/* Conditioning */}
            {day.workout.workout_c && (
              <div>
                <span className="font-medium text-green-600">Conditioning:</span>
                <ReactMarkdown className="prose prose-sm max-w-none">
                  {day.workout.workout_c}
                </ReactMarkdown>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Workout Calendar</CardTitle>
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" onClick={previousMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-lg font-semibold">{monthName}</span>
            <Button variant="outline" size="icon" onClick={nextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Loading calendar...</div>
        ) : error ? (
          <div className="text-red-500 text-center py-4">{error}</div>
        ) : (
          <div className="space-y-1">
            <div className="grid grid-cols-5 gap-1">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri'].map(day => (
                <div 
                  key={day} 
                  className="p-2 text-center font-semibold bg-gray-100"
                >
                  {day}
                </div>
              ))}
            </div>
            
            {weeks.map((week, weekIndex) => (
              <div key={weekIndex} className="grid grid-cols-5 gap-1">
                {week.map((day, dayIndex) => (
                  <WorkoutCell key={dayIndex} day={day} />
                ))}
              </div>
            ))}
          </div>
        )}
      </CardContent>
      
      {editingWorkout && (
        <EditDialog
          workout={editingWorkout}
          onSave={handleSaveWorkout}
          isOpen={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
        />
      )}
    </Card>
  );
};

export default MainLiftsCalendar;