'use client';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Button } from "@/components/ui/button";
import { Printer, Check } from "lucide-react";

interface Wod {
  id: string;
  date: string;
  content: string;
  is_published: boolean | null;
  created_at: string | null;
  published_at: string | null;
}

interface WeekData {
  startDate: string;
  weekNumber: number;
  monthYear: string;
  wods: Wod[];
}

interface MonthGroup {
  [key: string]: WeekData[];
}

// Custom component for rendering workout sections
const WorkoutSection: React.FC<{ content: string }> = ({ content }) => {
  // Split content into sections based on A), B), C) pattern
  const sections = content.split(/(?=[A-Z]\))/g).filter(Boolean);
  
  const formatExercise = (exercise: string) => {
    const lines = exercise.split('\n').map(line => line.trim()).filter(Boolean);
    return lines.map((line, i) => (
      <div key={i} className="text-[8pt] print:text-[8pt] leading-tight">
        {line}
      </div>
    ));
  };

  return (
    <div className="flex flex-col gap-1">
      {sections.map((section, _idx) => {
        // Extract section letter and content
        const [letter] = section.match(/[A-Z]\)/) || [''];
        const sectionContent = section.replace(/[A-Z]\)/, '').trim();
        
        // Split into timing/structure and exercises
        const lines = sectionContent.split('\n');
        const timingInfo = lines[0];
        const exercises = lines.slice(1).join('\n');
        
        return (
          <div key={_idx} className="mb-2 last:mb-0">
            <div className="font-bold text-[9pt] print:text-[9pt] mb-1">
              {letter} {timingInfo}
            </div>
            <div className="pl-2">
              {formatExercise(exercises)}
            </div>
          </div>
        );
      })}
    </div>
  );
};

const PrintableWodPage: React.FC = () => {
  const [weeksList, setWeeksList] = useState<WeekData[]>([]);
  const [selectedWeeks, setSelectedWeeks] = useState<string[]>([]);
  const [message, setMessage] = useState<{ text: string; isError: boolean }>({ text: '', isError: false });

  const supabase = createClientComponentClient<Database>();

  const formatMonthYear = (date: Date): string => {
    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return `${weekdays[date.getDay()]}, ${date.toLocaleDateString()}`;
  };

  const formatWeekRange = (startDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setDate(end.getDate() + 6);
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
  };

  const fetchWods = useCallback(async () => {
    try {
      const { data: wodData, error: wodError } = await supabase
        .from('wod')
        .select('*')
        .order('date', { ascending: true });

      if (wodError) throw wodError;

      // Group WODs by month and week
      const monthWeeks: { [key: string]: { [key: string]: Wod[] } } = {};
      
      wodData.forEach(wod => {
        const date = new Date(wod.date);
        const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekStartStr = weekStart.toISOString().split('T')[0];
        
        if (!monthWeeks[monthKey]) {
          monthWeeks[monthKey] = {};
        }
        if (!monthWeeks[monthKey][weekStartStr]) {
          monthWeeks[monthKey][weekStartStr] = [];
        }
        monthWeeks[monthKey][weekStartStr].push(wod);
      });

      // Convert to array with week numbers
      const weeksArray: WeekData[] = [];
      Object.entries(monthWeeks).forEach(([, weeks]) => {
        const sortedWeeks = Object.keys(weeks).sort();
        let weekNumber = 1;
        
        sortedWeeks.forEach((weekStart) => {
          weeksArray.push({
            startDate: weekStart,
            weekNumber: weekNumber,
            monthYear: formatMonthYear(new Date(weekStart)),
            wods: weeks[weekStart]
          });
          weekNumber++;
        });
      });

      setWeeksList(weeksArray);
    } catch (error) {
      console.error('Error fetching WODs:', error);
      setMessage({ 
        text: 'Failed to fetch WODs: ' + (error instanceof Error ? error.message : 'Unknown error'), 
        isError: true 
      });
    }
  }, [supabase]);

  useEffect(() => {
    fetchWods();
  }, [fetchWods]);

  const toggleWeekSelection = (weekStart: string): void => {
    setSelectedWeeks(prev => 
      prev.includes(weekStart)
        ? prev.filter(w => w !== weekStart)
        : [...prev, weekStart]
    );
  };

  const handlePrint = (): void => {
    const selectedWeekData = selectedWeeks.map(weekStart => 
      weeksList.find(w => w.startDate === weekStart)
    ).filter(Boolean);

    if (selectedWeekData.length > 0) {
      const firstWeek = selectedWeekData[0];
      const lastWeek = selectedWeekData[selectedWeekData.length - 1];
      const title = selectedWeekData.length === 1
        ? `${firstWeek?.monthYear} - Week ${firstWeek?.weekNumber}`
        : `${firstWeek?.monthYear} Week ${firstWeek?.weekNumber} to ${lastWeek?.monthYear} Week ${lastWeek?.weekNumber}`;
      
      document.title = title;
    }

    window.print();
    document.title = 'WOD Schedule';
  };

  // Group weeks by month
  const groupedByMonth = useMemo(() => {
    const grouped = weeksList.reduce<MonthGroup>((acc, week) => {
      if (!acc[week.monthYear]) {
        acc[week.monthYear] = [];
      }
      acc[week.monthYear].push(week);
      return acc;
    }, {});

    return Object.fromEntries(
      Object.entries(grouped).sort((a, b) => {
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateB.getTime() - dateA.getTime();
      })
    );
  }, [weeksList]);

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <div className="w-64 print:hidden border-r p-4 flex flex-col">
        <h2 className="text-xl font-bold mb-4">Select Weeks</h2>
        <div className="flex-1 overflow-y-auto">
          {Object.entries(groupedByMonth).map(([monthYear, monthWeeks]) => (
            <div key={monthYear} className="mb-4">
              <h3 className="font-semibold mb-2">{monthYear}</h3>
              {monthWeeks.map((week) => (
                <div
                  key={week.startDate}
                  className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer"
                  onClick={() => toggleWeekSelection(week.startDate)}
                >
                  <div className={`w-5 h-5 rounded-full border flex items-center justify-center
                    ${selectedWeeks.includes(week.startDate) ? 'bg-blue-500 border-blue-500' : 'border-gray-300'}`}
                  >
                    {selectedWeeks.includes(week.startDate) && 
                      <Check className="w-3 h-3 text-white" />
                    }
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">Week {week.weekNumber}</span>
                    <span className="text-xs text-gray-500">
                      {formatWeekRange(week.startDate)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
        <Button 
          onClick={handlePrint}
          disabled={selectedWeeks.length === 0}
          className="mt-4"
        >
          <Printer className="w-4 h-4 mr-2" />
          Print Selected Weeks
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="print:m-0 print:p-0">
          {selectedWeeks.map((weekStart) => {
            const weekData = weeksList.find(w => w.startDate === weekStart);
            if (!weekData) return null;

            return (
              <div key={weekStart} className="mb-8 last:mb-0">
                <div className="print:w-[297mm] print:h-[210mm] print:relative">
                  <div className="p-4 print:absolute print:inset-[5mm]">
                    <h2 className="text-xl font-bold mb-4 print:text-sm print:mb-2 print:text-center">
                      {weekData.monthYear} - Week {weekData.weekNumber}
                    </h2>
                    <div className="grid grid-cols-5 gap-2 print:gap-1">
                      {weekData.wods.map((wod) => (
                        <div 
                          key={wod.id} 
                          className="print:border print:border-gray-300 print:p-1 print:break-inside-avoid rounded"
                        >
                          <div className="font-bold text-[9pt] print:text-[9pt] mb-1 border-b pb-1 print:text-center">
                            {formatDate(wod.date)}
                          </div>
                          <WorkoutSection content={wod.content} />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="hidden print:block print:page-break-after-always" />
              </div>
            );
          })}
        </div>
      </div>

      {message.text && (
        <div className={`fixed bottom-4 right-4 p-2 print:hidden rounded-md ${
          message.isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
        }`}>
          {message.text}
        </div>
      )}
    </div>
  );
};

export default PrintableWodPage;