import { createServerClient } from "@/utils/supabase-server";
import WodForm from '@/components/wods/WodForm';
import { notFound } from 'next/navigation';

export default async function EditWodPage({ params }: { params: { id: string } }) {
  const supabase = createServerClient();

  // First, get the WOD data
  const { data: wodData, error: wodError } = await supabase
    .from('wod')
    .select(`
      *,
      exercises
    `)
    .eq('id', params.id)
    .single();

  if (wodError || !wodData) {
    notFound();
  }

  // Fetch all exercises using pagination
  const allExercises = [];
  let hasMore = true;
  let page = 0;
  const pageSize = 1000;

  while (hasMore) {
    const { data, error } = await supabase
      .from('exercise_movements')
      .select('*')
      .order('exercise_name')
      .range(page * pageSize, (page + 1) * pageSize - 1);

    if (error) {
      console.error('Error fetching exercises:', error);
      break;
    }

    if (data && data.length > 0) {
      allExercises.push(...data);
      page++;
      
      // If we got fewer records than the page size, we've reached the end
      if (data.length < pageSize) {
        hasMore = false;
      }
    } else {
      hasMore = false;
    }
  }

  console.log(`Fetched ${allExercises.length} total exercises`);

  return (
    <WodForm 
      wod={wodData} 
      exercises={allExercises} 
    />
  );
} 
