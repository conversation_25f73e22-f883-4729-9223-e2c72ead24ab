'use client'
import React, { useState, useEffect } from 'react';
import { Table } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

  const INITIAL_DATA = {
    "0:45-1:00": {
      timeInSeconds: 52.5, // Average of 45-60 seconds
      run: { meters: 200 },
      rower: { meters: { m: 250, f: 225 }, cals: { m: 15, f: 12 } },
      bike: { meters: { m: 500, f: 450 }, cals: { m: 15, f: 12 } },
      echo: { cals: { m: 12, f: 10 } },
      ski: { meters: { m: 200, f: 180 }, cals: { m: 12, f: 10 } }
    },
    "1:30-2:00": {
      timeInSeconds: 105, // Average of 90-120 seconds
      run: { meters: 400 },
      rower: { meters: { m: 500, f: 450 }, cals: { m: 30, f: 24 } },
      bike: { meters: { m: 1000, f: 900 }, cals: { m: 30, f: 24 } },
      echo: { cals: { m: 25, f: 20 } },
      ski: { meters: { m: 400, f: 360 }, cals: { m: 25, f: 20 } }
    },
    "3:30-4:00": {
      timeInSeconds: 225, // Average of 210-240 seconds
      run: { meters: 800 },
      rower: { meters: { m: 1000, f: 900 }, cals: { m: 60, f: 48 } },
      bike: { meters: { m: 2000, f: 1800 }, cals: { m: 60, f: 48 } },
      echo: { cals: { m: 50, f: 40 } },
      ski: { meters: { m: 800, f: 720 }, cals: { m: 50, f: 40 } }
    },
    "7:00-8:00": {
      timeInSeconds: 450, // Average of 420-480 seconds
      run: { meters: 1600 },
      rower: { meters: { m: 2000, f: 1800 }, cals: { m: 120, f: 96 } },
      bike: { meters: { m: 4000, f: 3600 }, cals: { m: 120, f: 96 } },
      echo: { cals: { m: 100, f: 80 } },
      ski: { meters: { m: 1600, f: 1440 }, cals: { m: 100, f: 80 } }
    }
  } as const;

  type TimeKey = keyof typeof INITIAL_DATA;

  // Define our base value types
  interface BaseValues {
    time: number;
    run: number;
    rowerMeters: number;
    rowerCals: number;
    bikeMeters: number;
    bikeCals: number;
    echoCals: number;
    skiMeters: number;
    skiCals: number;
  }
  
  // Define types for our form values (all strings since they're input values)
  interface FormValues {
    time: string;
    run: string;
    rowerMeters: string;
    rowerCals: string;
    bikeMeters: string;
    bikeCals: string;
    echoCals: string;
    skiMeters: string;
    skiCals: string;
  }
  
  const REFERENCE_TIME = "3:30-4:00";
  
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.round(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  const ConversionTable = () => {
    const [isMale, setIsMale] = useState(true);
    const [customValues, setCustomValues] = useState<FormValues>({
      time: '',
      run: '',
      rowerMeters: '',
      rowerCals: '',
      bikeMeters: '',
      bikeCals: '',
      echoCals: '',
      skiMeters: '',
      skiCals: ''
    });
  
    const getBaseValues = (timeKey: TimeKey = REFERENCE_TIME): BaseValues => {
      const data = INITIAL_DATA[timeKey];
      const gender = isMale ? 'm' : 'f';
      return {
        time: data.timeInSeconds,
        run: data.run.meters,
        rowerMeters: data.rower.meters[gender],
        rowerCals: data.rower.cals[gender],
        bikeMeters: data.bike.meters[gender],
        bikeCals: data.bike.cals[gender],
        echoCals: data.echo.cals[gender],
        skiMeters: data.ski.meters[gender],
        skiCals: data.ski.cals[gender]
      };
    };
  
    const calculateTime = (value: number, field: keyof BaseValues): number => {
      const baseValues = getBaseValues();
      return baseValues.time * (value / baseValues[field]);
    };
  
    const calculateAllValues = (value: string, sourceField: keyof BaseValues): FormValues => {
      if (!value || isNaN(Number(value))) {
        return Object.keys(customValues).reduce((acc, key) => ({ 
          ...acc, 
          [key]: '' 
        }), {}) as FormValues;
      }
  
      const numValue = Number(value);
      const baseValues = getBaseValues();
      const ratio = numValue / baseValues[sourceField];
      const estimatedTime = calculateTime(numValue, sourceField);
  
      return {
        time: estimatedTime ? formatTime(estimatedTime) : '',
        run: (baseValues.run * ratio).toFixed(1),
        rowerMeters: (baseValues.rowerMeters * ratio).toFixed(1),
        rowerCals: (baseValues.rowerCals * ratio).toFixed(1),
        bikeMeters: (baseValues.bikeMeters * ratio).toFixed(1),
        bikeCals: (baseValues.bikeCals * ratio).toFixed(1),
        echoCals: (baseValues.echoCals * ratio).toFixed(1),
        skiMeters: (baseValues.skiMeters * ratio).toFixed(1),
        skiCals: (baseValues.skiCals * ratio).toFixed(1)
      };
    };
  
    const handleCustomValueChange = (value: string, field: keyof BaseValues) => {
      const newValues = calculateAllValues(value, field);
      setCustomValues(newValues);
    };
  
    useEffect(() => {
      if (customValues.run) {
        handleCustomValueChange(customValues.run, 'run');
      }
    }, [isMale]);
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={isMale}
            onCheckedChange={setIsMale}
            id="gender-switch"
          />
          <Label htmlFor="gender-switch">
            {isMale ? 'Male' : 'Female'} Standards
          </Label>
        </div>

        <div className="rounded-md border">
          <Table>
            <thead>
              <tr className="bg-muted/50">
                <th className="p-2 text-center">Time Domain</th>
                <th className="p-2 text-center">Run (m)</th>
                <th className="p-2 text-center">Rower (m/cal)</th>
                <th className="p-2 text-center">Bike (m/cal)</th>
                <th className="p-2 text-center">Echo (cal)</th>
                <th className="p-2 text-center">Ski (m/cal)</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(INITIAL_DATA).map(([time, data]) => (
                <tr key={time} className="border-t">
                  <td className="p-2 font-medium text-center">{time}</td>
                  <td className="p-2 text-center">{data.run.meters}</td>
                  <td className="p-2 text-center">
                    {data.rower.meters[isMale ? 'm' : 'f']}/
                    {data.rower.cals[isMale ? 'm' : 'f']}
                  </td>
                  <td className="p-2 text-center">
                    {data.bike.meters[isMale ? 'm' : 'f']}/
                    {data.bike.cals[isMale ? 'm' : 'f']}
                  </td>
                  <td className="p-2 text-center">
                    {data.echo.cals[isMale ? 'm' : 'f']}
                  </td>
                  <td className="p-2 text-center">
                    {data.ski.meters[isMale ? 'm' : 'f']}/
                    {data.ski.cals[isMale ? 'm' : 'f']}
                  </td>
                </tr>
              ))}
              <tr className="border-t bg-muted/30">
                <td className="p-2 font-medium text-center">
                  Custom
                  <div className="text-sm font-normal mt-1">
                    {customValues.time ? `Estimated Time: ${customValues.time}` : 'Estimated Time: --:--'}
                  </div>
                </td>
                <td className="p-2 text-center">
                  <Input
                    type="number"
                    value={customValues.run}
                    onChange={(e) => handleCustomValueChange(e.target.value, 'run')}
                    className="w-24 mx-auto text-center"
                    placeholder="meters"
                  />
                </td>
                <td className="p-2">
                  <div className="flex gap-2 justify-center">
                    <Input
                      type="number"
                      value={customValues.rowerMeters}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'rowerMeters')}
                      className="w-24 text-center"
                      placeholder="meters"
                    />
                    <Input
                      type="number"
                      value={customValues.rowerCals}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'rowerCals')}
                      className="w-24 text-center"
                      placeholder="cals"
                    />
                  </div>
                </td>
                <td className="p-2">
                  <div className="flex gap-2 justify-center">
                    <Input
                      type="number"
                      value={customValues.bikeMeters}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'bikeMeters')}
                      className="w-24 text-center"
                      placeholder="meters"
                    />
                    <Input
                      type="number"
                      value={customValues.bikeCals}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'bikeCals')}
                      className="w-24 text-center"
                      placeholder="cals"
                    />
                  </div>
                </td>
                <td className="p-2 text-center">
                  <Input
                    type="number"
                    value={customValues.echoCals}
                    onChange={(e) => handleCustomValueChange(e.target.value, 'echoCals')}
                    className="w-24 mx-auto text-center"
                    placeholder="cals"
                  />
                </td>
                <td className="p-2">
                  <div className="flex gap-2 justify-center">
                    <Input
                      type="number"
                      value={customValues.skiMeters}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'skiMeters')}
                      className="w-24 text-center"
                      placeholder="meters"
                    />
                    <Input
                      type="number"
                      value={customValues.skiCals}
                      onChange={(e) => handleCustomValueChange(e.target.value, 'skiCals')}
                      className="w-24 text-center"
                      placeholder="cals"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </Table>
        </div>

        <p className="text-sm text-muted-foreground">
          * Shuttle runs completed in 25&apos; down and 25&apos; back<br />
          * Women&apos;s distances are calculated at 90% of men&apos;s distances<br />
          * Custom calculations are based on the 3:30-4:00 time domain for consistent ratios<br />
          * Estimated time is calculated based on relative intensity to reference values
        </p>
      </div>
    );
  };

  export default ConversionTable;