// app/back/page.tsx
'use client'

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Card, CardContent } from "@/components/ui/card";
import { backExerciseMarkdown } from './data';

const BackPage = () => {
  return (
    <Card className="max-w-6xl mx-auto my-6">
      <CardContent className="p-6">
        <article className="prose prose-stone max-w-none">
          <ReactMarkdown 
            remarkPlugins={[remarkGfm]}
            components={{
              table: ({ children }) => (
                <div className="overflow-x-auto">
                  <table className="border-collapse table-auto w-full">
                    {children}
                  </table>
                </div>
              ),
              th: ({ children }) => (
                <th className="border border-slate-300 dark:border-slate-700 p-2 text-left">
                  {children}
                </th>
              ),
              td: ({ children }) => (
                <td className="border border-slate-300 dark:border-slate-700 p-2">
                  {children}
                </td>
              ),
            }}
          >
            {backExerciseMarkdown}
          </ReactMarkdown>
        </article>
      </CardContent>
    </Card>
  );
};

export default BackPage;