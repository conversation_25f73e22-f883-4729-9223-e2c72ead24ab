import { createServerClient } from "@/utils/supabase-server";
import WodPageClient from '@/components/wods/WodPageClient';


export const dynamic = "force-dynamic";
export const runtime = "edge";


// Define the props interface

export default async function WodPage() {
  const supabase = createServerClient();

  // Fetch initial WODs data with exercises
  const [wodsResponse, exercisesResponse] = await Promise.all([
    supabase
      .from('wod')
      .select(`
        *,
        exercises
      `)
      .order('date', { ascending: false }),
    supabase
      .from('exercise_movements')
      .select('*')
      .order('exercise_name')
  ]);

  return (
    <WodPageClient 
      initialWods={wodsResponse.data || []} 
      exercises={exercisesResponse.data || []}
    />
  );
} 