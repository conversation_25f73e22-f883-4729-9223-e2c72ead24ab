import { createServerClient } from "@/utils/supabase-server";
import WodForm from '@/components/wods/WodForm';

export default async function AddWodPage() {
  const supabase = createServerClient();

  // Fetch all exercises with a higher limit
  const { data: exercises } = await supabase
    .from('exercise_movements')
    .select('*')
    .order('exercise_name')
    .limit(3000); // Increased limit to fetch all exercises

  return <WodForm exercises={exercises || []} />;
}
