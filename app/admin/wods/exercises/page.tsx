import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import ExercisesClient from '@/components/exercises/ExercisesClient';
import { Suspense } from 'react';

// Types for URL search params
export type ExerciseSearchParams = {
  page?: string;
  limit?: string;
  search?: string;
  body_part?: string;
  equipment?: string;
  expertise_level?: string;
  movement_category?: string;
  has_video?: string;
  has_image?: string;
  has_records?: string;
  video_source?: string;
  sort?: string;
  order?: 'asc' | 'desc';
};

// Constants
const DEFAULT_PAGE_SIZE = 50;
const MAX_PAGE_SIZE = 100;

interface ExercisesPageProps {
  searchParams: ExerciseSearchParams;
}

// Simplified server-side data fetching - similar to your original approach
async function fetchExercisesData(searchParams: ExerciseSearchParams) {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Parse search params
  const page = Math.max(1, parseInt(searchParams.page || '1'));
  const limit = Math.min(MAX_PAGE_SIZE, parseInt(searchParams.limit || DEFAULT_PAGE_SIZE.toString()));
  const offset = (page - 1) * limit;

  const {
    search,
    sort = 'exercise_name',
    order = 'asc'
  } = searchParams;

  try {
    // Step 1: Get exercises with simple query (like your original)
    let exerciseQuery = supabase
      .from('exercise_movements')
      .select('*', { count: 'exact' });

    // Apply search if provided
    if (search && search.trim()) {
      exerciseQuery = exerciseQuery.textSearch('fts', search.trim(), {
        type: 'websearch',
        config: 'english'
      });
    }

    // Apply sorting
    exerciseQuery = exerciseQuery.order(sort as keyof Database['public']['Tables']['exercises']['Row'], { ascending: order === 'asc' });

    // Apply pagination
    exerciseQuery = exerciseQuery.range(offset, offset + limit - 1);

    const { data: exercises, error: exerciseError, count } = await exerciseQuery;

    if (exerciseError) {
      console.error('Error fetching exercises:', exerciseError);
      throw exerciseError;
    }

    console.log(`Found ${exercises?.length || 0} exercises out of ${count} total`);

    if (!exercises || exercises.length === 0) {
      return {
        exercises: [],
        totalCount: count || 0,
        currentPage: page,
        pageSize: limit,
        totalPages: Math.ceil((count || 0) / limit)
      };
    }

    // Step 2: Get related data separately (like your original approach)
    const exerciseIds = exercises.map(ex => ex.id);

    const [categoriesResponse, imagesResponse, videosResponse, recordsResponse] = await Promise.all([
      // Get categories
      supabase
        .from('exercise_categories')
        .select('*')
        .in('exercise_id', exerciseIds),

      // Get images
      supabase
        .from('exercise_images')
        .select('*')
        .in('exercise_id', exerciseIds),

      // Get videos
      supabase
        .from('exercise_videos')
        .select('*')
        .in('exercise_id', exerciseIds),

      // Get exercises with records
      supabase
        .from('exercise_records')
        .select('exercise_id')
        .in('exercise_id', exerciseIds)
        .not('exercise_id', 'is', null)
    ]);

    const categories = categoriesResponse.data || [];
    const images = imagesResponse.data || [];
    const videos = videosResponse.data || [];
    const exerciseRecords = new Set((recordsResponse.data || []).map(r => r.exercise_id));

    console.log(`Related data: ${categories.length} categories, ${images.length} images, ${videos.length} videos, ${exerciseRecords.size} with records`);

    // Step 3: Combine data (like your original approach)
    let combinedExercises = exercises.map(exercise => ({
      ...exercise,
      categories: categories.filter(cat => cat.exercise_id === exercise.id),
      images: images.filter(img => img.exercise_id === exercise.id),
      videos: videos.filter(vid => vid.exercise_id === exercise.id),
      hasRecords: exerciseRecords.has(exercise.id)
    }));

    // Step 4: Apply filters on the combined data (server-side filtering)
    const {
      body_part,
      equipment,
      expertise_level,
      movement_category,
      has_video,
      has_image,
      has_records,
      video_source
    } = searchParams;

    // Apply category-based filters
    if (body_part && body_part !== 'all') {
      combinedExercises = combinedExercises.filter(exercise =>
        exercise.categories.some(cat =>
          cat.category_type === 'body_part' && cat.category_value === body_part
        )
      );
    }

    if (equipment && equipment !== 'all') {
      combinedExercises = combinedExercises.filter(exercise =>
        exercise.categories.some(cat =>
          cat.category_type === 'equipment' && cat.category_value === equipment
        )
      );
    }

    if (expertise_level && expertise_level !== 'all') {
      combinedExercises = combinedExercises.filter(exercise =>
        exercise.categories.some(cat =>
          cat.category_type === 'expertise_level' && cat.category_value === expertise_level
        )
      );
    }

    if (movement_category && movement_category !== 'all') {
      combinedExercises = combinedExercises.filter(exercise =>
        exercise.categories.some(cat =>
          cat.category_type === 'movement_category' && cat.category_value === movement_category
        )
      );
    }

    // Apply media filters
    if (has_video === 'yes') {
      combinedExercises = combinedExercises.filter(exercise => exercise.videos.length > 0);
    } else if (has_video === 'no') {
      combinedExercises = combinedExercises.filter(exercise => exercise.videos.length === 0);
    }

    if (has_image === 'yes') {
      combinedExercises = combinedExercises.filter(exercise => exercise.images.length > 0);
    } else if (has_image === 'no') {
      combinedExercises = combinedExercises.filter(exercise => exercise.images.length === 0);
    }

    if (has_records === 'yes') {
      combinedExercises = combinedExercises.filter(exercise => exercise.hasRecords);
    } else if (has_records === 'no') {
      combinedExercises = combinedExercises.filter(exercise => !exercise.hasRecords);
    }

    if (video_source && video_source !== 'all') {
      combinedExercises = combinedExercises.filter(exercise =>
        exercise.videos.some(video => video.video_source === video_source)
      );
    }

    console.log(`After filtering: ${combinedExercises.length} exercises`);

    // Note: When filters are applied, we need to recalculate pagination
    // For now, we'll return all filtered results on one page
    // In production, you might want to implement proper filtered pagination

    return {
      exercises: combinedExercises,
      totalCount: count || 0, // This is the total before filtering
      currentPage: page,
      pageSize: limit,
      totalPages: Math.ceil((count || 0) / limit)
    };

  } catch (error) {
    console.error('Error in fetchExercisesData:', error);
    return {
      exercises: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: limit,
      totalPages: 0
    };
  }
}

// Loading component
function ExercisesLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-10">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="h-12 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default async function ExercisesPage({ searchParams }: ExercisesPageProps) {
  const data = await fetchExercisesData(searchParams);

  return (
    <div className="min-h-screen bg-gray-50">
      <Suspense fallback={<ExercisesLoading />}>
        <ExercisesClient
          exercises={data.exercises}
          totalCount={data.totalCount}
          currentPage={data.currentPage}
          pageSize={data.pageSize}
          totalPages={data.totalPages}
          searchParams={searchParams}
        />
      </Suspense>
    </div>
  );
}