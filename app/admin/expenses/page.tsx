'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { format } from 'date-fns'
import { ExpenseFilters, FilterState } from '@/components/finances/expenses/ExpenseFilters'
import { ExpenseSummary } from '@/components/finances/expenses/ExpenseSummary'
import { ExpenseForm } from '@/components/finances/expenses/ExpenseForm'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { toast } from '@/hooks/use-toast'
import { Pencil, Trash2, Plus } from 'lucide-react'
import type { Database } from '@/types/supabase'

// Use the correct type from Supabase schema
type Expense = Database['public']['Tables']['admin_expenses']['Row'] & {
  category: {
    name: string
  } | null,
  vendor: {
    name: string
  } | null
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)
  const [filters, setFilters] = useState<FilterState>({
    dateRange: undefined,
    category: '',
    vendor: '',  // Add the missing vendor property
    paymentMethod: ''
  })
  const [summaryData, setSummaryData] = useState({
    totalAmount: 0,
    categoryTotals: {},
    periodStart: new Date(),
    periodEnd: new Date()
  })
  const [showAddExpense, setShowAddExpense] = useState(false)

  const supabase = createClientComponentClient<Database>()

  const calculateSummary = useCallback((data: Expense[]) => {
    const totalAmount = data.reduce((sum, expense) => sum + expense.amount, 0);

    // Safely handle null categories
    const categoryTotals = data.reduce<Record<string, number>>((acc, expense) => {
      // Use optional chaining and nullish coalescing
      const categoryName = expense.category?.name ?? 'Uncategorized';
      return {
        ...acc,
        [categoryName]: (acc[categoryName] || 0) + expense.amount
      };
    }, {});

    setSummaryData({
      totalAmount,
      categoryTotals,
      periodStart: filters.dateRange?.from || new Date(),
      periodEnd: filters.dateRange?.to || new Date()
    });
  }, [filters.dateRange])

  const fetchExpenses = useCallback(async () => {
    let query = supabase
      .from('admin_expenses')
      .select(`
        *,
        category:expense_categories(name),
        vendor:vendors(name)
      `)
      .order('date', { ascending: false })

    if (filters.dateRange?.from) {
      query = query.gte('date', filters.dateRange.from.toISOString())
    }
    if (filters.dateRange?.to) {
      query = query.lte('date', filters.dateRange.to.toISOString())
    }
    if (filters.category) {
      query = query.eq('category_id', filters.category)
    }
    if (filters.vendor) {  // Add vendor filter
      query = query.eq('vendor_id', filters.vendor)
    }
    if (filters.paymentMethod) {
      query = query.eq('payment_method', filters.paymentMethod)
    }

    const { data, error } = await query

    if (error) {
      toast({
        title: "Error fetching expenses",
        description: error.message,
        variant: "destructive"
      })
      return
    }

    setExpenses(data || [])
    calculateSummary(data || [])
  }, [supabase, filters, calculateSummary])

  useEffect(() => {
    fetchExpenses()
  }, [filters, fetchExpenses])

  const handleExport = () => {
    const csv = [
      ['Date', 'Category', 'Description', 'Amount', 'Payment Method', 'Reference'],
      ...expenses.map(expense => [
        format(new Date(expense.date), 'dd/MM/yyyy'),
        expense.category.name,
        expense.description,
        expense.amount.toString(),
        expense.payment_method,
        expense.reference_number
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.setAttribute('hidden', '')
    a.setAttribute('href', url)
    a.setAttribute('download', `expenses-${format(new Date(), 'yyyy-MM-dd')}.csv`)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this expense?')) return

    const { error } = await supabase
      .from('admin_expenses')
      .delete()
      .eq('id', id)

    if (error) {
      toast({
        title: "Error deleting expense",
        description: error.message,
        variant: "destructive"
      })
      return
    }

    toast({
      title: "Expense deleted",
      description: "The expense has been successfully deleted"
    })
    fetchExpenses()
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Administrative Expenses</h1>
        <Button onClick={() => setShowAddExpense(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Expense
        </Button>
      </div>

      {/* Add Expense Dialog */}
      <Dialog open={showAddExpense} onOpenChange={setShowAddExpense}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Expense</DialogTitle>
          </DialogHeader>
          <ExpenseForm
            onSuccess={() => {
              setShowAddExpense(false)
              fetchExpenses()
            }}
            onCancel={() => setShowAddExpense(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Expense Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Expense</DialogTitle>
          </DialogHeader>
          {editingExpense && (
            <ExpenseForm
              expense={editingExpense}
              onSuccess={() => {
                setIsDialogOpen(false)
                setEditingExpense(null)
                fetchExpenses()
              }}
              onCancel={() => {
                setIsDialogOpen(false)
                setEditingExpense(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <ExpenseFilters
        onFilterChange={setFilters}
        onExport={handleExport}
      />

      <ExpenseSummary data={summaryData} />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Vendor</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Payment Method</TableHead>
            <TableHead>Recurring</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {expenses.map((expense) => (
            <TableRow key={expense.id}>
              <TableCell>{format(new Date(expense.date), 'dd/MM/yyyy')}</TableCell>
              <TableCell>{expense.category?.name ?? '-'}</TableCell>
              <TableCell>{expense.vendor?.name || '-'}</TableCell>
              <TableCell>{expense.description}</TableCell>
              <TableCell>€{expense.amount.toFixed(2)}</TableCell>
              <TableCell>{expense.payment_method}</TableCell>
              <TableCell>
                {expense.recurring ? expense.recurring_period : 'No'}
              </TableCell>
              <TableCell>{expense.reference_number || '-'}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setEditingExpense(expense)
                      setIsDialogOpen(true)
                    }}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(expense.id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
