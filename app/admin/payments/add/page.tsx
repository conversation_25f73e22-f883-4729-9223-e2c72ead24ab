
'use client'

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import ClientSelector from '@/components/ClientSelector'
import { format, parseISO } from 'date-fns'
import type { Database } from '@/types/supabase'

type Client = { id: number; client_name: string }
type Program = { id: number; name: string }
type Duration = { id: number; duration_name: string }

type SubscriptionInfo = {
  days_until_expiration: number | null;
  end_date: string | null;
  subscription_status: string | null;
  program_name_display: string | null;
};

interface FullClientOption {
  value: string;
  label: string;
  pelatis_id: string;
  session_id: string;
  subscription?: SubscriptionInfo;
}

// Update the Payment type to include course_name
type Payment = Database['public']['Tables']['pliromes']['Row'] & {
  program: Pick<Database['public']['Tables']['programs']['Row'], 'id' | 'name'> | null;
  course_name: string;
}

type FormData = {
  pelates_id: string;
  course: string;
  course_duration_id: string;
  price_program: string;
  start_program: string;
  money_gave: string;
  date_money_gave: string;
  way_of_payment: 'CASH' | 'VIVA' | 'WINBANK';
  nodebt: boolean;
  debt: string;
  comments: string;
  receipt: string | File | null;
  checkins_allowed: '8' | '12' | 'unlimited';
}



type GroupedPayments = {
  [key: string]: {
    total: number;
    payments: Payment[];
  };
}

// Add these types at the top of your file
type UploadStatus = 'idle' | 'uploading' | 'success' | 'error';

interface UploadState {
  status: UploadStatus;
  progress: number;
  error?: string;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ['application/pdf'];

// Utility function to format file size


export default function AddPayment() {
  const [formData, setFormData] = useState<FormData>({
    pelates_id: '', // Should be a UUID string
    course: '8e52e958-e645-4561-8e9a-aa01737885c4',    // Should match a program ID
    course_duration_id: '1',
    checkins_allowed: '12' as const,
    price_program: '60',
    start_program: '',
    money_gave: '60',
    date_money_gave: new Date().toISOString().split('T')[0],
    way_of_payment: 'CASH',
    nodebt: false,
    debt: '',
    comments: '',
    receipt: ''
  });
  const [clients, setClients] = useState<Client[]>([])
  const [programs, setPrograms] = useState<Program[]>([])
  const [durations, setDurations] = useState<Duration[]>([])
  const [submissionStatus, setSubmissionStatus] = useState({ status: '', message: '' })
  const [clientPayments, setClientPayments] = useState<Payment[]>([])
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [expandedMonth, setExpandedMonth] = useState<string | null>(null)
  const [uploadState, setUploadState] = useState<UploadState>({
    status: 'idle',
    progress: 0
  });
  const [selectedFileName, setSelectedFileName] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState(false);

  const supabase = createClientComponentClient();

  useEffect(() => {
    const checkAdminStatus = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;
    
      const { data: roles } = await supabase.rpc('getUserRoles', {
        p_user_id: session.user.id,
      });
    
      setIsAdmin(roles?.includes('admin') || false);
    };
  
    checkAdminStatus();
  }, [supabase]);
  const fetchFormData = useCallback(async () => {
    try {
      const [clientsData, programsData, durationsData] = await Promise.all([
        supabase
          .from('pelates')
          .select('id, client_name')
          .order('client_name'),
        supabase.from('programs').select('id, name'),
        supabase.from('course_durations').select('id, duration_name')
      ]);
  
      console.log('Fetched clients:', clientsData); // Debug log
  
      if (clientsData.error) {
        throw clientsData.error;
      }
  
      setClients(clientsData.data || []);
      setPrograms(programsData.data || []);
      setDurations(durationsData.data || []);
    } catch (error) {
      console.error('Error fetching form data:', error);
      setSubmissionStatus({ status: 'error', message: 'Failed to fetch form data. Please try again.' });
    }
  }, [supabase]);

  const fetchClientPayments = useCallback(async (clientId: string) => {
    try {
      const { data, error } = await supabase
        .from('pliromes')
        .select(`
          *,
          program:programs!pliromes_course_fkey (
            id, 
            name
          )
        `)
        .eq('pelates_id', clientId)
        .order('date_money_gave', { ascending: false });
  
      if (error) {
        console.error('Query error:', error);
        throw error;
      }
  
      // Transform the data with correct typing
      const transformedData: Payment[] = (data || []).map(payment => ({
        ...payment,
        program: payment.program ? {
          id: payment.program.id,
          name: payment.program.name
        } : null,
        course_name: payment.program?.name || 'N/A'
      }));
  
      console.log('Fetched payments:', transformedData);
      setClientPayments(transformedData);
    } catch (error) {
      console.error('Error fetching client payments:', error);
    }
  }, [supabase]);

  const clientOptions = useMemo(() => 
    clients.map(client => ({
      value: client.id.toString(),
      label: client.client_name,
      pelatis_id: client.id.toString(),
      session_id: '',
      subscription: undefined
    })), [clients]);

  useEffect(() => {
    fetchFormData()
  }, [fetchFormData])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      nodebt: checked
    }))
  }

// Add this debug log in handleClientChange
const handleClientChange = (selected: FullClientOption[]) => {
  if (selected.length > 0) {
    const clientId = selected[0].pelatis_id;
    console.log('Selected client ID:', clientId);
    console.log('Selected client full data:', selected[0]);
    setFormData(prev => ({
      ...prev,
      pelates_id: clientId
    }));
    fetchClientPayments(clientId);
  } else {
    setFormData(prev => ({
      ...prev,
      pelates_id: ''
    }));
    setClientPayments([]);
  }
};

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '-'
    return format(parseISO(dateString), 'dd/MM/yyyy')
  }

  const groupedPayments: GroupedPayments = clientPayments.reduce((groups, payment) => {
    if (payment.date_money_gave) {
      const monthKey = format(parseISO(payment.date_money_gave), 'MMMM yyyy')
      if (!groups[monthKey]) {
        groups[monthKey] = { total: 0, payments: [] }
      }
      groups[monthKey].total += payment.money_gave || 0
      groups[monthKey].payments.push(payment)
    }
    return groups
  }, {} as GroupedPayments)


    const generateReceiptFilename = (originalFilename: string, clientId: string) => {
      const date = new Date();
      const timestamp = date.toISOString().split('T')[0]; // YYYY-MM-DD format
      const randomString = Math.random().toString(36).substring(2, 8); // 6 character random string
      const extension = originalFilename.split('.').pop()?.toLowerCase() || 'pdf';
      
      return `receipt_${clientId}_${timestamp}_${randomString}.${extension}`;
    };
    
    const handleFileUpload = async (file: File): Promise<string> => {
      try {
        setUploadState({
          status: 'uploading',
          progress: 0
        });
    
        // Validate file type and size
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          throw new Error('Invalid file type. Only PDF files are allowed.');
        }
    
        if (file.size > MAX_FILE_SIZE) {
          throw new Error(`File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
        }
    
        const filename = generateReceiptFilename(file.name, formData.pelates_id);
    
        setUploadState(prev => ({
          ...prev,
          progress: 10
        }));
    
        const { error: uploadError } = await supabase
          .storage
          .from('receipts')
          .upload(filename, file, {
            cacheControl: '3600',
            upsert: false
          });
    
        if (uploadError) throw uploadError;
    
        setUploadState({
          status: 'success',
          progress: 100
        });
    
        return filename;
    
      } catch (error) {
        setUploadState({
          status: 'error',
          progress: 0,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });
        
        // Just log to console for debugging
        console.error('File upload error:', error);
        throw error;
      }
    };


const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  const file = e.target.files?.[0];
  if (!file) {
    setSelectedFileName('');
    setFormData(prev => ({ ...prev, receipt: null }));
    return;
  }

  // Validate file type and size
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    setSubmissionStatus({ 
      status: 'error', 
      message: 'Please upload a PDF file only' 
    });
    e.target.value = ''; // Reset input
    setSelectedFileName('');
    return;
  }

  if (file.size > MAX_FILE_SIZE) {
    setSubmissionStatus({ 
      status: 'error', 
      message: `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB` 
    });
    e.target.value = ''; // Reset input
    setSelectedFileName('');
    return;
  }

  setSelectedFileName(file.name);
  setFormData(prev => ({
    ...prev,
    receipt: file
  }));
};

// Download function for existing receipts
const downloadReceipt = async (receiptPath: string) => {
  try {
    const { data, error } = await supabase
      .storage
      .from('receipts')
      .download(receiptPath);
      
    if (error) throw error;

    // Create and click a temporary download link
    const url = window.URL.createObjectURL(data);
    const a = document.createElement('a');
    a.href = url;
    a.download = receiptPath.split('/').pop() || 'receipt.pdf';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Error downloading receipt:', error);
    setSubmissionStatus({
      status: 'error',
      message: 'Failed to download receipt'
    });
  }
};




 // Updated handleSubmit
 const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  
  if (!isAdmin) {
    setSubmissionStatus({ 
      status: 'error', 
      message: 'Only administrators can add payments' 
    });
    return;
  }

  setSubmissionStatus({ status: 'submitting', message: 'Submitting payment...' });

  try {
    // First verify the pelatis exists
    const { data: verifyPelatis, error: verifyError } = await supabase
      .from('pelates')
      .select('id')
      .eq('id', formData.pelates_id)
      .single();

    console.log('Verification before payment:', {
      pelatesId: formData.pelates_id,
      verifyResult: verifyPelatis,
      verifyError
    });

    
    if (verifyError || !verifyPelatis) {
      throw new Error('Selected client does not exist in database');
    }

    let receiptPath = '';
    if (formData.receipt instanceof File) {
      receiptPath = await handleFileUpload(formData.receipt);
    } else {
      receiptPath = formData.receipt || '';
    }

    // Log payment data before insertion
    const paymentData = {
      pelates_id: formData.pelates_id,
      course: formData.course,
      course_duration_id: formData.course_duration_id ? parseInt(formData.course_duration_id) : null,
      checkins_allowed: formData.checkins_allowed,
      price_program: formData.price_program ? parseFloat(formData.price_program) : null,
      start_program: formData.start_program || null,
      money_gave: formData.money_gave ? parseFloat(formData.money_gave) : null,
      date_money_gave: formData.date_money_gave || null,
      way_of_payment: formData.way_of_payment || null,
      nodebt: formData.nodebt,
      debt: formData.debt ? parseFloat(formData.debt) : null,
      comments: formData.comments || null,
      receipt: receiptPath || null
    };

    console.log('Payment data to be inserted:', paymentData);

    const { data: insertedPayment, error: paymentError } = await supabase
      .from('pliromes')
      .insert([paymentData])
      .select()
      .single();

    if (paymentError) {
      console.error('Payment insertion error details:', paymentError);
      throw paymentError;
    }

    // Log successful payment
    console.log('Successfully inserted payment:', insertedPayment);

    setSubmissionStatus({ status: 'success', message: 'Payment added successfully!' });
    
    if (formData.pelates_id) {
      await fetchClientPayments(formData.pelates_id);
    }

    // Reset form...
    setFormData(prev => ({
      ...prev,
      course: '',
      course_duration_id: '',
      price_program: '',
      start_program: '',
      money_gave: '',
      date_money_gave: '',
      way_of_payment: 'CASH',
      nodebt: false,
      debt: '',
      comments: '',
      receipt: null
    }));
    setSelectedFileName('');
    setUploadState({ status: 'idle', progress: 0 });

  } catch (error) {
    console.error('Payment submission error:', error);
    
    // Log error details
    try {
      await supabase.from('error_logs').insert({
        error_message: error instanceof Error ? error.message : 'Unknown error',
        context: 'payment_submission',
        metadata: {
          formData: formData,
          errorDetails: error
        },
        severity: 'ERROR'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    setSubmissionStatus({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Failed to add payment'
    });
  }
};



  return (
    <div className="max-w-7xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Payment Form */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Add New Payment</h2>
          {submissionStatus.message && (
            <Alert className={`mb-4 ${submissionStatus.status === 'error' ? 'bg-red-100' : 'bg-green-100'}`}>
              <AlertDescription>{submissionStatus.message}</AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Existing form fields */}
            <div>
              <Label htmlFor="pelates_id">Client</Label>
              <ClientSelector
  clients={clientOptions}
  selectedClients={formData.pelates_id ? [{
    value: formData.pelates_id,
    label: clients.find(c => c.id.toString() === formData.pelates_id)?.client_name || '',
    pelatis_id: formData.pelates_id,
    session_id: '',
    subscription: undefined
  }] : []}
  onChange={handleClientChange}
/>
            </div>

        <div>
          <Label htmlFor="course">Program</Label>
          <Select name="course" value={formData.course} onValueChange={(value) => handleSelectChange('course', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a program" />
            </SelectTrigger>
            <SelectContent>
              {programs.map(program => (
                <SelectItem key={program.id} value={program.id.toString()}>{program.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="course_duration_id">Course Duration</Label>
          <Select name="course_duration_id" value={formData.course_duration_id} onValueChange={(value) => handleSelectChange('course_duration_id', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a duration" />
            </SelectTrigger>
            <SelectContent>
              {durations.map(duration => (
                <SelectItem key={duration.id} value={duration.id.toString()}>{duration.duration_name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
  <Label htmlFor="checkins_allowed">Checkins Allowed</Label>
  <Select 
    name="checkins_allowed" 
    value={formData.checkins_allowed} 
    onValueChange={(value) => handleSelectChange('checkins_allowed', value)}
  >
    <SelectTrigger>
      <SelectValue placeholder="Select checkins limit" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="8">8 Checkins</SelectItem>
      <SelectItem value="12">12 Checkins</SelectItem>
      <SelectItem value="unlimited">Unlimited</SelectItem>
    </SelectContent>
  </Select>
</div>

        <div>
          <Label htmlFor="price_program">Price Program</Label>
          <Input type="number" id="price_program" name="price_program" value={formData.price_program} onChange={handleInputChange} />
        </div>

        <div>
          <Label htmlFor="start_program">Start Program</Label>
          <Input type="date" id="start_program" name="start_program" value={formData.start_program} onChange={handleInputChange} />
        </div>

        <div>
          <Label htmlFor="money_gave">Money Gave</Label>
          <Input type="number" id="money_gave" name="money_gave" value={formData.money_gave} onChange={handleInputChange} />
        </div>

        <div>
          <Label htmlFor="date_money_gave">Date Money Gave</Label>
          <Input type="date" id="date_money_gave" name="date_money_gave" value={formData.date_money_gave} onChange={handleInputChange} />
        </div>

        <div>
          <Label htmlFor="way_of_payment">Way of Payment</Label>
          <Select 
            name="way_of_payment" 
            value={formData.way_of_payment} 
            onValueChange={(value) => handleSelectChange('way_of_payment', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select payment method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="CASH">Cash</SelectItem>
              <SelectItem value="VIVA">Card (Viva)</SelectItem>
              <SelectItem value="WINBANK">Bank Transfer</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox 
            id="nodebt" 
            checked={formData.nodebt} 
            onCheckedChange={handleCheckboxChange}
          />
          <Label htmlFor="nodebt">No Debt</Label>
        </div>

        <div>
          <Label htmlFor="debt">Debt</Label>
          <Input type="number" id="debt" name="debt" value={formData.debt} onChange={handleInputChange} />
        </div>

        <div>
          <Label htmlFor="comments">Comments</Label>
          <Textarea id="comments" name="comments" value={formData.comments} onChange={handleInputChange} />
        </div>

        <div>
      <Label htmlFor="receipt">Receipt (PDF only, max {(MAX_FILE_SIZE / (1024 * 1024))}MB)</Label>
      <div className="space-y-2">
        <div className="flex gap-2 items-center">
          <Input 
            type="file" 
            id="receipt" 
            accept="application/pdf"
            onChange={handleFileChange}
            className="hidden"
          />
          <Button 
            type="button"
            onClick={() => document.getElementById('receipt')?.click()}
            variant="outline"
          >
            Choose PDF File
          </Button>
          {selectedFileName && (
            <span className="text-sm text-gray-600">
              Selected: {selectedFileName}
            </span>
          )}
        </div>

        {uploadState.status === 'uploading' && (
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${uploadState.progress}%` }}
            ></div>
            <p className="text-sm text-gray-500 mt-1">
              Uploading... {uploadState.progress}%
            </p>
          </div>
        )}
        
        {typeof formData.receipt === 'string' && formData.receipt && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Current receipt: {formData.receipt.split('/').pop()}
            </span>
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={() => {
                if (typeof formData.receipt === 'string') {
                  window.open(
                    `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/receipts/${formData.receipt}`,
                    '_blank'
                  );
                }
              }}
            >
              View Receipt
            </Button>
          </div>
        )}

        {uploadState.error && (
          <Alert className="bg-red-100">
            <AlertDescription>{uploadState.error}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>

        <Button type="submit">Add Payment</Button>
          </form>
        </div>

        {/* Payment History */}
        {formData.pelates_id && (
          <div>
            <h2 className="text-2xl font-bold mb-4">Payment History</h2>
            {Object.entries(groupedPayments).map(([month, { total, payments }]) => (
              <div key={month} className="mb-4">
                <Button 
                  onClick={() => setExpandedMonth(expandedMonth === month ? null : month)}
                  className="w-full flex justify-between items-center mb-2 p-4"
                  variant="outline"
                >
                  <span className="font-bold">{month}</span>
                  <span>Total: €{total.toFixed(2)}</span>
                </Button>
                
                {expandedMonth === month && (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Program</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {payments.map((payment) => (
                        <TableRow 
                          key={payment.id}
                          className="cursor-pointer hover:bg-gray-100"
                          onClick={() => setSelectedPayment(payment)}
                        >
                          <TableCell>{formatDate(payment.date_money_gave)}</TableCell>
                          <TableCell>€{payment.money_gave?.toFixed(2) || '-'}</TableCell>
                          <TableCell>{payment.course_name}</TableCell>
                          <TableCell>{payment.nodebt ? 'Paid' : 'Unpaid'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            ))}

{typeof formData.receipt === 'string' && formData.receipt && (
  <div className="flex items-center gap-2">
    <span className="text-sm text-gray-600">
      Current receipt: {formData.receipt.split('/').pop()}
    </span>
    <Button 
      type="button" 
      variant="outline" 
      size="sm"
      onClick={() => {
        if (typeof formData.receipt === 'string') {
          const url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/receipts/${formData.receipt}`;
          window.open(url, '_blank');
        }
      }}
    >
      View Receipt
    </Button>
  </div>
)}



{/* Payment Details Dialog */}
{selectedPayment && (
  <Dialog open={!!selectedPayment} onOpenChange={() => setSelectedPayment(null)}>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Payment Details</DialogTitle>
      </DialogHeader>
      <div className="py-4">
        <p><strong>Date:</strong> {formatDate(selectedPayment.date_money_gave)}</p>
        <p><strong>Amount:</strong> €{selectedPayment.money_gave?.toFixed(2) || '-'}</p>
        <p><strong>Program:</strong> {selectedPayment.course_name}</p>
        <p><strong>Checkins Allowed:</strong> {selectedPayment.checkins_allowed}</p>
        <p><strong>Payment Method:</strong> {selectedPayment.way_of_payment || '-'}</p>
        <p><strong>Status:</strong> {selectedPayment.nodebt ? 'Paid' : 'Unpaid'}</p>
        {selectedPayment.comments && (
          <p><strong>Comments:</strong> {selectedPayment.comments}</p>
        )}
      </div>
      <DialogFooter className="flex gap-2">
        {selectedPayment.receipt && (
          <Button 
            onClick={() => downloadReceipt(selectedPayment.receipt as string)}
            variant="outline"
          >
            Download Receipt
          </Button>
        )}
        <Button onClick={() => setSelectedPayment(null)}>Close</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
)}
          </div>
        )}
      </div>
    </div>
  )
}
