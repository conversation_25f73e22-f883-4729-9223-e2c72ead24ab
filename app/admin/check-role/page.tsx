// app/admin/check-role/page.tsx
'use client';

import React from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useEffect, useState } from 'react';
import type { Database } from '@/types/supabase';

export default function RoleCheckPage() {
  const [role, setRole] = useState<string[] | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function checkRole() {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.user) {
        setUserId(session.user.id);
        
        // Get user roles
        const { data: roles } = await supabase.rpc('getUserRoles', {
          p_user_id: session.user.id,
        });
        setRole(roles);
      }
    }
    
    checkRole();
  }, [supabase]);

  if (!userId) {
    return <div className="p-4">Not logged in</div>;
  }

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Role Check</h1>
      <div className="bg-gray-100 p-4 rounded">
        <p>User ID: {userId}</p>
        <p>Roles: {role ? JSON.stringify(role, null, 2) : 'Loading...'}</p>
      </div>
    </div>
  );
}