// app/admin/invoices/view/[id]/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';
import type { Database } from '@/types/supabase';
import { notFound } from 'next/navigation';

export const dynamic = 'force-dynamic';

export default async function InvoiceViewPage({
  params
}: {
  params: { id: string }
}) {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Ensure user is authenticated and is admin
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>Please log in to access this page.</p>
      </div>
    );
  }

// app/admin/invoices/view/[id]/page.tsx (continued)
  // Get invoice
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', params.id)
    .single();

  // Get client details
  let clientData = null;
  if (invoice && invoice.client_id) {
    const { data: client } = await supabase
      .from('pelates')
      .select('id, name, last_name, email, afm')
      .eq('id', invoice.client_id)
      .single();
    clientData = client;
  }

  if (invoiceError || !invoice) {
    console.error('Error fetching invoice:', invoiceError);
    return notFound();
  }

  // Get invoice lines
  const { data: lines, error: linesError } = await supabase
    .from('invoice_lines')
    .select('*')
    .eq('invoice_id', invoice.id)
    .order('line_number');

  if (linesError) {
    console.error('Error fetching invoice lines:', linesError);
  }

  // Get payment methods
  const { data: paymentMethods, error: paymentsError } = await supabase
    .from('invoice_payment_methods')
    .select('*')
    .eq('invoice_id', invoice.id);

  if (paymentsError) {
    console.error('Error fetching payment methods:', paymentsError);
  }

  // Get company settings
  const { data: companySettings, error: settingsError } = await supabase
    .from('company_settings')
    .select('*')
    .single();

  if (settingsError) {
    console.error('Error fetching company settings:', settingsError);
  }

  // Utility for payment method type display
  const getPaymentMethodName = (type: number) => {
    switch (type) {
      case 1: return 'Μετρητά';
      case 2: return 'Επιταγή';
      case 3: return 'Τραπεζικό έμβασμα';
      case 4: return 'Πιστωτική κάρτα';
      default: return `Τύπος ${type}`;
    }
  };

  // Utility for VAT category display
  const getVatCategoryName = (category: number) => {
    switch (category) {
      case 1: return '24%';
      case 2: return '13%';
      case 3: return '6%';
      case 4: return '0%';
      case 5: return '17%';
      case 6: return '9%';
      case 7: return '4%';
      default: return `Κατηγορία ${category}`;
    }
  };

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">
          Invoice {invoice.invoice_series}-{invoice.invoice_number}
        </h1>

        <div className="flex space-x-2">
          <Link
            href="/admin/invoices"
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            Back to List
          </Link>

          {invoice.status === 'draft' && (
            <Link
              href={`/admin/invoices/edit/${invoice.id}`}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Edit
            </Link>
          )}

          {invoice.qr_url && (
            <a
              href={invoice.qr_url}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              View myDATA QR
            </a>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Invoice details */}
        <div className="space-y-4 p-4 border rounded-md">
          <h2 className="text-lg font-semibold">Invoice Details</h2>

          <dl className="grid grid-cols-2 gap-x-4 gap-y-2">
            <dt className="text-sm font-medium text-gray-500">Series</dt>
            <dd className="text-sm text-gray-900">{invoice.invoice_series}</dd>

            <dt className="text-sm font-medium text-gray-500">Number</dt>
            <dd className="text-sm text-gray-900">{invoice.invoice_number}</dd>

            <dt className="text-sm font-medium text-gray-500">Issue Date</dt>
            <dd className="text-sm text-gray-900">
              {new Date(invoice.issue_date).toLocaleDateString()}
            </dd>

            <dt className="text-sm font-medium text-gray-500">Invoice Type</dt>
            <dd className="text-sm text-gray-900">{invoice.invoice_type}</dd>

            <dt className="text-sm font-medium text-gray-500">Status</dt>
            <dd className="text-sm text-gray-900">
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                  ${invoice.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : ''}
                  ${invoice.status === 'submitted' ? 'bg-green-100 text-green-800' : ''}
                  ${invoice.status === 'error' ? 'bg-red-100 text-red-800' : ''}
                  ${invoice.status === 'canceled' ? 'bg-gray-100 text-gray-800' : ''}
                `}
              >
                {invoice.status}
              </span>
            </dd>

            {invoice.mark && (
              <>
                <dt className="text-sm font-medium text-gray-500">MARK</dt>
                <dd className="text-sm text-gray-900">{invoice.mark}</dd>
              </>
            )}

            <dt className="text-sm font-medium text-gray-500">Created</dt>
            <dd className="text-sm text-gray-900">
              {invoice.created_at ? new Date(invoice.created_at).toLocaleString() : '-'}
            </dd>

            <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd className="text-sm text-gray-900">
              {invoice.updated_at ? new Date(invoice.updated_at).toLocaleString() : '-'}
            </dd>
          </dl>
        </div>

        {/* Client details */}
        <div className="space-y-4 p-4 border rounded-md">
          <h2 className="text-lg font-semibold">Client Details</h2>

          <dl className="grid grid-cols-2 gap-x-4 gap-y-2">
            <dt className="text-sm font-medium text-gray-500">Name</dt>
            <dd className="text-sm text-gray-900">
              {clientData ? `${clientData.name} ${clientData.last_name || ''}` : invoice.client_name}
            </dd>

            <dt className="text-sm font-medium text-gray-500">VAT</dt>
            <dd className="text-sm text-gray-900">
              {clientData ? clientData.afm : (invoice.client_vat || '-')}
            </dd>

            <dt className="text-sm font-medium text-gray-500">Email</dt>
            <dd className="text-sm text-gray-900">
              {clientData ? clientData.email : '-'}
            </dd>

            <dt className="text-sm font-medium text-gray-500">Country</dt>
            <dd className="text-sm text-gray-900">{invoice.client_country || 'GR'}</dd>
          </dl>
        </div>
      </div>

      {/* Invoice lines */}
      <div className="space-y-4 p-4 border rounded-md">
        <h2 className="text-lg font-semibold">Invoice Lines</h2>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Line #
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Qty
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit Price
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Net Value
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  VAT
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  VAT Amount
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {!lines || lines.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-3 py-4 text-center text-sm text-gray-500">
                    No invoice lines
                  </td>
                </tr>
              ) : (
                lines.map((line) => (
                  <tr key={line.id}>
                    <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {line.line_number}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {line.description}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {line.quantity.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(line.unit_price)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(line.net_value)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getVatCategoryName(line.vat_category)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(line.vat_amount)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(line.net_value + line.vat_amount)}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
            <tfoot className="bg-gray-50">
              <tr>
                <td className="px-3 py-3" colSpan={4}>
                  <span className="text-sm font-semibold">Totals</span>
                </td>
                <td className="px-3 py-3 text-sm font-semibold">
                  {formatCurrency(invoice.total_net)}
                </td>
                <td className="px-3 py-3"></td>
                <td className="px-3 py-3 text-sm font-semibold">
                  {formatCurrency(invoice.total_vat)}
                </td>
                <td className="px-3 py-3 text-sm font-semibold">
                  {formatCurrency(invoice.total_gross)}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      {/* Payment methods */}
      <div className="space-y-4 p-4 border rounded-md">
        <h2 className="text-lg font-semibold">Payment Methods</h2>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Info
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {!paymentMethods || paymentMethods.length === 0 ? (
                <tr>
                  <td colSpan={3} className="px-3 py-4 text-center text-sm text-gray-500">
                    No payment methods
                  </td>
                </tr>
              ) : (
                paymentMethods.map((method) => (
                  <tr key={method.id}>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPaymentMethodName(method.payment_type)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(method.amount)}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {method.payment_info || '-'}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Company details */}
      {companySettings && (
        <div className="space-y-4 p-4 border rounded-md">
          <h2 className="text-lg font-semibold">Company Details</h2>

          <dl className="grid grid-cols-2 gap-x-4 gap-y-2">
            <dt className="text-sm font-medium text-gray-500">Company Name</dt>
            <dd className="text-sm text-gray-900">{companySettings.companyName}</dd>

            <dt className="text-sm font-medium text-gray-500">VAT Number</dt>
            <dd className="text-sm text-gray-900">{companySettings.vatNumber}</dd>

            <dt className="text-sm font-medium text-gray-500">Branch</dt>
            <dd className="text-sm text-gray-900">{companySettings.branch || '0'}</dd>

            <dt className="text-sm font-medium text-gray-500">Address</dt>
            <dd className="text-sm text-gray-900">
              {companySettings.address}, {companySettings.postalCode} {companySettings.city}
            </dd>
          </dl>
        </div>
      )}

      {/* myDATA Actions */}
      <div className="flex justify-end space-x-3">
        {invoice.status === 'draft' && (
          <Link
            href={`/admin/invoices/send-to-mydata/${invoice.id}`}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Submit to myDATA
          </Link>
        )}

        {invoice.status === 'submitted' && (
          <Link
            href={`/admin/invoices/cancel-mydata/${invoice.id}`}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Cancel in myDATA
          </Link>
        )}
      </div>
    </div>
  );
}