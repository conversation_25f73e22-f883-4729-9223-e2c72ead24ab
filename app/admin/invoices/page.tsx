// app/admin/invoices/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import InvoiceManagementDashboard from '@/components/invoices/InvoiceManagementDashboard';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function InvoicesPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Get invoices with error handling
  const { data: invoices, error } = await supabase
    .from('invoices')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching invoices:', error);
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Invoices</h1>
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
          <p className="text-red-600">Error loading invoices: {error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Invoice Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your invoices and submit them to myDATA
          </p>
        </div>
      </div>

      <InvoiceManagementDashboard initialInvoices={invoices || []} />
    </div>
  );
}