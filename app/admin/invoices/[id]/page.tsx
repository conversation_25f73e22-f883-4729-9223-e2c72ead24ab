// app/admin/invoices/[id]/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import InvoiceDetails from '@/components/mydata/InvoiceDetails';
import InvoiceLines from '@/components/mydata/InvoiceLines';
import InvoiceActions from '@/components/mydata/InvoiceActions';
import MyDataNavigation from '@/components/mydata/MyDataNavigation';
import QrCodeViewer from '@/components/mydata/QrCodeViewer';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function InvoiceDetailPage({
  params
}: {
  params: { id: string }
}) {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Get invoice data
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', params.id)
    .single();

  if (invoiceError || !invoice) {
    notFound();
  }

  // Get invoice lines
  const { data: lines, error: linesError } = await supabase
    .from('invoice_lines')
    .select('*')
    .eq('invoice_id', params.id)
    .order('line_number');

  if (linesError) {
    console.error('Error fetching invoice lines:', linesError);
  }

  // We don't need to fetch payment methods for this page
  // If needed in the future, uncomment the code below
  /*
  const { data: paymentMethods, error: paymentsError } = await supabase
    .from('invoice_payment_methods')
    .select('*')
    .eq('invoice_id', params.id);

  if (paymentsError) {
    console.error('Error fetching payment methods:', paymentsError);
  }
  */

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link href="/admin/invoices">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invoices
            </Button>
          </Link>

          <h1 className="text-2xl font-bold">
            Invoice {invoice.invoice_series}-{invoice.invoice_number}
          </h1>
        </div>

        <InvoiceActions
          invoiceId={invoice.id}
          invoiceStatus={invoice.status}
          hasMyDataMark={!!invoice.mark}
        />
      </div>

      <MyDataNavigation />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <InvoiceDetails invoice={invoice} className="mb-6" />

          {lines && lines.length > 0 && (
            <InvoiceLines
              lines={lines}
              total={{
                netValue: invoice.total_net,
                vatAmount: invoice.total_vat,
                grossValue: invoice.total_gross
              }}
            />
          )}
        </div>

        <div className="space-y-6">
          {invoice.mark && invoice.qr_url && (
            <QrCodeViewer
              qrUrl={invoice.qr_url}
              mark={invoice.mark}
            />
          )}
        </div>
      </div>
    </div>
  );
}