// app/admin/invoices/[id]/check/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Invoice<PERSON>hecker from '@/components/mydata/InvoiceChecker';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function InvoiceCheckPage({
  params
}: {
  params: { id: string }
}) {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  // Get invoice data
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', params.id)
    .single();
  
  if (invoiceError || !invoice) {
    notFound();
  }
  
  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link href={`/admin/invoices/${params.id}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invoice
            </Button>
          </Link>
          
          <h1 className="text-2xl font-bold">
            Check Invoice {invoice.invoice_series}-{invoice.invoice_number}
          </h1>
        </div>
      </div>
      
      <p className="text-gray-600">
        Before submitting an invoice to myDATA, make sure it has all the required information.
        This page will help you check if your invoice is ready to be submitted.
      </p>
      
      <InvoiceChecker 
        invoiceId={invoice.id} 
        invoiceTotal={invoice.total_gross} 
      />
    </div>
  );
}
