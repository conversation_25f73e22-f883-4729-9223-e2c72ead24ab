
// app/admin/exercise-records/client.tsx
'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { format, parseISO } from 'date-fns'
import { useRouter } from 'next/navigation'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import ReactMarkdown from 'react-markdown'
import { toast } from 'sonner'
import {
  Calendar,
  Clock,
  Edit,
  Dumbbell,
  User,
  Save,
  Check,
  InfoIcon,
  Loader2
} from 'lucide-react'
import { UserExerciseHistory } from './UserExerciseHistory'
// Removed unused imports
import type { Database } from '@/types/supabase'

// Type definitions
type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'];
type Exercise = Database['public']['Tables']['exercise_movements']['Row'];
type Wod = Database['public']['Tables']['wod']['Row'];
type Session = Database['public']['Tables']['sessions']['Row'] & {
  programs: Database['public']['Tables']['programs']['Row'] | null;
  check_ins: Array<{
    id: string;
    pelatis_id: string;
    check_in_time: string;
    session_id: string;
    pelates: Database['public']['Tables']['pelates']['Row'];
  }>;
};

interface SelectedMetrics {
  weight: boolean;
  reps: boolean;
  sets: boolean;
  time: boolean;
  calories: boolean;
  notes: boolean;
}

type MetricType = 'weight' | 'reps' | 'sets' | 'time' | 'calories' | 'notes';

interface FormDataEntry {
  weight: string;
  reps: string;
  sets: string;
  time: string;
  calories: string;
  notes: string;
  wodId?: string; // Added to fix type error
}

interface FormDataState {
  [userId: string]: {
    [exerciseId: string]: FormDataEntry;
  };
}

interface RecordsState {
  [userId: string]: {
    [exerciseId: string]: ExerciseRecord | null;
  };
}

interface SavingState {
  [userId: string]: {
    [exerciseId: string]: boolean;
  };
}

interface Props {
  initialWod: Wod | null;
  exercises: Exercise[];
  initialSessions: Session[];
}

// Commented out to fix unused interface warning
/*
interface PRUserData {
  weight: string;
  reps: string;
  sets: string;
  time: string;
  calories: string;
  notes: string;
}
*/

// Custom error type
type ApiError = {
  message: string;
} & Error;

export function CoachExerciseRecordsClient({
  initialWod,
  exercises,
  initialSessions
}: Props) {
  const router = useRouter();
  const supabase = createClientComponentClient<Database>();

  // State management
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [wod, setWod] = useState<Wod | null>(initialWod);
  const [sessions, setSessions] = useState<Session[]>(initialSessions);
  const [selectedSession, setSelectedSession] = useState<Session | null>(
    initialSessions.length > 0 ? initialSessions[0] : null
  );
  const [wodExercises, setWodExercises] = useState<Exercise[]>([]);
  const [selectedExercises, setSelectedExercises] = useState<Exercise[]>([]);
  const [isFetching, setIsFetching] = useState(false);
  const [formData, setFormData] = useState<FormDataState>({});
  const [records, setRecords] = useState<RecordsState>({});
  const [saving, setSaving] = useState<SavingState>({});
  const [historicalPRs, setHistoricalPRs] = useState<Record<string, Record<string, ExerciseRecord[]>>>({});
  const [prDialogOpen, setPrDialogOpen] = useState(false);
  const [selectedUserExercise, setSelectedUserExercise] = useState<{
    userId: string;
    exerciseId: string;
    userName: string;
    exerciseName: string;
  } | null>(null);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [historyUserExercise, setHistoryUserExercise] = useState<{
    userId: string;
    exerciseId: string;
    userName: string;
    exerciseName: string;
  } | null>(null);
  // Default selected metrics
  const [selectedMetrics, setSelectedMetrics] = useState<SelectedMetrics>({
    weight: true,
    reps: true,
    sets: false,
    time: false,
    calories: false,
    notes: false
  });

  // Fetch session and WOD data when date changes
  const fetchData = useCallback(async () => {
    try {
      setIsFetching(true)

      // Fetch WOD for selected date
      const { data: wodData, error: wodError } = await supabase
        .from('wod')
        .select('*')
        .eq('date', selectedDate)
        .single()

      if (wodError && wodError.code !== 'PGRST116') {
        console.error('Error fetching WOD:', wodError)
      }

      setWod(wodData || null)

      // Fetch sessions for selected date with all required fields
      const startOfDay = `${selectedDate}T00:00:00`
      const endOfDay = `${selectedDate}T23:59:59`

      const { data: sessionsData, error: sessionsError } = await supabase
        .from('sessions')
        .select(`
          id,
          created_at,
          created_by,
          duration,
          max_participants,
          program_id,
          start_time,
          programs:program_id (
            id,
            name,
            created_at,
            description
          ),
          check_ins (
            id,
            pelatis_id,
            check_in_time,
            session_id,
            pelates:pelatis_id (*)
          )
        `)
        .gte('start_time', startOfDay)
        .lte('start_time', endOfDay)
        .order('start_time')

      if (sessionsError) {
        console.error('Error fetching sessions:', sessionsError)
        toast.error('Failed to load sessions')
        return
      }

      // Type assertion to ensure the data matches our Session type
      const typedSessionsData = (sessionsData || []) as Session[]
      setSessions(typedSessionsData)
      setSelectedSession(typedSessionsData.length > 0 ? typedSessionsData[0] : null)
    } catch (error) {
      const apiError = error as ApiError
      console.error('Error fetching data:', apiError)
      toast.error('Failed to load data')
    } finally {
      setIsFetching(false)
    }
  }, [selectedDate, supabase])

  // Effect to fetch data when date changes
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Fetch exercises associated with the WOD
  useEffect(() => {
    if (wod && wod.exercises && wod.exercises.length > 0 && exercises.length > 0) {
      const wodExercisesList = exercises.filter(ex =>
        wod.exercises?.includes(ex.id)
      )
      setWodExercises(wodExercisesList)
      setSelectedExercises(wodExercisesList)
    } else {
      setWodExercises([])
      setSelectedExercises([])
    }
  }, [wod, exercises])

  // Initialize form data and fetch existing records when session or exercises change
  const memoizedFetchHistoricalPRs = useCallback(async (userIds: string[], exerciseIds: string[]) => {
    try {
      const historicalData: Record<string, Record<string, ExerciseRecord[]>> = {}

      userIds.forEach(userId => {
        historicalData[userId] = {}
        exerciseIds.forEach(exerciseId => {
          historicalData[userId][exerciseId] = []
        })
      })

      const { data, error } = await supabase
        .from('exercise_records')
        .select('*')
        .in('pelatis_id', userIds)
        .in('exercise_id', exerciseIds)
        .lt('date_achieved', `${selectedDate}T00:00:00`)
        .order('date_achieved', { ascending: false })

      if (error) {
        throw error
      }

      data?.forEach(record => {
        if (historicalData[record.pelatis_id] &&
            historicalData[record.pelatis_id][record.exercise_id]) {
          historicalData[record.pelatis_id][record.exercise_id].push(record)
        }
      })

      setHistoricalPRs(historicalData)
    } catch (error) {
      console.error('Error fetching historical PRs:', error)
    }
  }, [supabase, selectedDate])

  useEffect(() => {
    const fetchExistingRecords = async () => {
      if (!selectedSession || selectedExercises.length === 0) return;

      try {
        setIsFetching(true);

        const newFormData: FormDataState = {};
        const newRecords: RecordsState = {};
        const newSaving: SavingState = {};

        // Filter out check-ins with null pelatis_id and create array of valid user IDs
        const userIds = selectedSession.check_ins
          .map(ci => ci.pelatis_id)
          .filter((id): id is string => id !== null);

        if (userIds.length === 0) return;

        // Fetch existing records for these users for the selected date
        const startOfDay = `${selectedDate}T00:00:00`;
        const endOfDay = `${selectedDate}T23:59:59`;

        const { data: existingRecords, error } = await supabase
          .from('exercise_records')
          .select('*')
          .in('pelatis_id', userIds)
          .in('exercise_id', selectedExercises.map(ex => ex.id))
          .gte('date_achieved', startOfDay)
          .lte('date_achieved', endOfDay);

        if (error) throw error;

        // Initialize form data structure for all users and exercises
        selectedSession.check_ins.forEach(checkIn => {
          const userId = checkIn.pelatis_id;
          if (!userId) return; // Skip if pelatis_id is null

          newFormData[userId] = {};
          newRecords[userId] = {};
          newSaving[userId] = {};

          selectedExercises.forEach(exercise => {
            const existingRecord = existingRecords?.find(
              record => record.pelatis_id === userId && record.exercise_id === exercise.id
            ) || null;

            newFormData[userId][exercise.id] = {
              weight: existingRecord?.weight?.toString() || '',
              reps: existingRecord?.reps?.toString() || '',
              sets: existingRecord?.sets?.toString() || '',
              time: existingRecord?.time || '',
              calories: existingRecord?.calories?.toString() || '',
              notes: existingRecord?.notes || ''
            };

            newRecords[userId][exercise.id] = existingRecord;
            newSaving[userId][exercise.id] = false;
          });
        });

        setFormData(newFormData);
        setRecords(newRecords);
        setSaving(newSaving);

        await memoizedFetchHistoricalPRs(userIds, selectedExercises.map(ex => ex.id));

      } catch (error) {
        const apiError = error as ApiError;
        console.error('Error fetching existing records:', apiError);
        toast.error(`Failed to load existing records: ${apiError.message}`);
      } finally {
        setIsFetching(false);
      }
    };

    fetchExistingRecords();
  }, [selectedSession, selectedExercises, selectedDate, memoizedFetchHistoricalPRs, supabase]);

  // Handle input changes
  const handleInputChange = (
    userId: string,
    exerciseId: string,
    field: MetricType,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      [userId]: {
        ...prev[userId],
        [exerciseId]: {
          ...prev[userId]?.[exerciseId],
          [field]: value
        }
      }
    }))
  }

  // Check if a value is a PR
  const isPR = (userId: string, exerciseId: string, field: 'weight' | 'reps', value: string): boolean => {
    if (!value || !historicalPRs[userId]?.[exerciseId]) return false

    const numValue = parseFloat(value)
    if (isNaN(numValue)) return false

    return !historicalPRs[userId][exerciseId].some(record => {
      const recordValue = field === 'weight' ? record.weight : record.reps
      return recordValue != null && recordValue >= numValue
    })
  }

  // Handle saving a record
  const saveRecord = async (userId: string, exerciseId: string) => {
    try {
      setSaving(prev => ({
        ...prev,
        [userId]: {
          ...prev[userId],
          [exerciseId]: true
        }
      }));

      const userData = formData[userId]?.[exerciseId];
      if (!userData) return;

      // First get the pelatis record for the user - Fixed query syntax
      const { data: pelatisData, error: pelatisError } = await supabase
        .from('pelates')
        .select('id, auth_user_id') // Include auth_user_id in the select
        .eq('auth_user_id', userId)
        .single();

      if (pelatisError || !pelatisData) {
        // If no record found with auth_user_id, try direct pelatis_id
        const { data: directPelatisData, error: directError } = await supabase
          .from('pelates')
          .select('id')
          .eq('id', userId) // Using userId as pelatis_id directly
          .single();

        if (directError || !directPelatisData) {
          console.error('Pelatis lookup error:', pelatisError || directError);
          throw new Error('Could not find pelatis record for user');
        }

        // Use the direct pelatis_id
        const recordData = {
          pelatis_id: userId, // Using the direct pelatis_id
          exercise_id: exerciseId,
          date_achieved: `${selectedDate}T${userData.time || new Date().toTimeString().split(' ')[0]}`,
          weight: userData.weight ? parseFloat(userData.weight) : null,
          reps: userData.reps ? parseInt(userData.reps) : null,
          sets: userData.sets ? parseInt(userData.sets) : null,
          time: userData.time || null,
          calories: userData.calories ? parseInt(userData.calories) : null,
          notes: userData.notes || null,
          wod_id: userData.wodId || null
        };

        const existingRecord = records[userId]?.[exerciseId];

        if (existingRecord) {
          // Update existing record
          const { data, error } = await supabase
            .from('exercise_records')
            .update(recordData)
            .eq('id', existingRecord.id)
            .select('*, exercise:exercise_movements(*)')
            .single();

          if (error) throw error;

          setRecords(prev => ({
            ...prev,
            [userId]: {
              ...prev[userId],
              [exerciseId]: data
            }
          }));

          toast.success('Record updated successfully');
        } else {
          // Create new record
          const { data, error } = await supabase
            .from('exercise_records')
            .insert(recordData)
            .select('*, exercise:exercise_movements(*)')
            .single();

          if (error) throw error;

          setRecords(prev => ({
            ...prev,
            [userId]: {
              ...prev[userId],
              [exerciseId]: data
            }
          }));

          toast.success('Record created successfully');
        }
      } else {
        // Use the pelatis_id from auth_user_id lookup
        const recordData = {
          pelatis_id: pelatisData.id,
          exercise_id: exerciseId,
          date_achieved: `${selectedDate}T${userData.time || new Date().toTimeString().split(' ')[0]}`,
          weight: userData.weight ? parseFloat(userData.weight) : null,
          reps: userData.reps ? parseInt(userData.reps) : null,
          sets: userData.sets ? parseInt(userData.sets) : null,
          time: userData.time || null,
          calories: userData.calories ? parseInt(userData.calories) : null,
          notes: userData.notes || null,
          wod_id: userData.wodId || null
        };

        const existingRecord = records[userId]?.[exerciseId];

        if (existingRecord) {
          const { data, error } = await supabase
            .from('exercise_records')
            .update(recordData)
            .eq('id', existingRecord.id)
            .select('*, exercise:exercise_movements(*)')
            .single();

          if (error) throw error;

          setRecords(prev => ({
            ...prev,
            [userId]: {
              ...prev[userId],
              [exerciseId]: data
            }
          }));

          toast.success('Record updated successfully');
        } else {
          const { data, error } = await supabase
            .from('exercise_records')
            .insert(recordData)
            .select('*, exercise:exercise_movements(*)')
            .single();

          if (error) throw error;

          setRecords(prev => ({
            ...prev,
            [userId]: {
              ...prev[userId],
              [exerciseId]: data
            }
          }));

          toast.success('Record created successfully');
        }
      }
    } catch (error) {
      console.error('Error saving record:', error);
      toast.error('Failed to save record');
    } finally {
      setSaving(prev => ({
        ...prev,
        [userId]: {
          ...prev[userId],
          [exerciseId]: false
        }
      }));
    }
  }

  // Create PR notification using the structure from useBadges.ts
  // Commented out to fix unused function warning
  /*
  const createPRNotification = async (
    userId: string,
    exerciseId: string,
    userData: PRUserData,
    isWeightPR: boolean,
    isRepsPR: boolean
  ) => {
    try {
      const exercise = exercises.find(ex => ex.id === exerciseId)
      if (!exercise) return

      // Get the PR details
      const prDetails = []
      if (isWeightPR) prDetails.push(`${userData.weight}kg`)
      if (isRepsPR) prDetails.push(`${userData.reps} reps`)

      // Create metadata following the structure in useBadges.ts
      const metadata = {
        badge_id: exerciseId, // Reusing badge_id for exercise_id
        badge_name: exercise.exercise_name,
        badge_level: 1, // Using 1 as default level (bronze)
        eventType: 'badge_earned', // Same eventType as in useBadges.ts
        prType: isWeightPR ? 'weight' : 'reps',
        prValue: isWeightPR ? userData.weight : userData.reps
      }

      // Create notification
      const notification = {
        client_id: userId,
        message: `New PR for ${exercise.exercise_name}: ${prDetails.join(', ')}! 🏆`,
        type: 'info',
        metadata: metadata,
        read: false,
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      }

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notification)

      if (notificationError) throw notificationError

    } catch (error) {
      const apiError = error as ApiError
      console.error('Error creating PR notification:', apiError)
      toast.error(`Failed to create PR notification: ${apiError.message}`)
    }
  }
  */

  // Handle saving all records
  const saveAllRecords = async () => {
    try {
      toast.info('Saving all records...')

      for (const userId in formData) {
        for (const exerciseId in formData[userId]) {
          await saveRecord(userId, exerciseId)
        }
      }

      toast.success('All records saved')
    } catch (error) {
      console.error('Error saving all records:', error)
      toast.error('Failed to save some records')
    }
  }

  // Open PR dialog
  const openPRDialog = (userId: string, exerciseId: string) => {
    const user = selectedSession?.check_ins.find(ci => ci.pelatis_id === userId)?.pelates
    const exercise = selectedExercises.find(ex => ex.id === exerciseId)

    if (!user || !exercise) return

    setSelectedUserExercise({
      userId,
      exerciseId,
      userName: `${user.name} ${user.last_name}`,
      exerciseName: exercise.exercise_name
    })

    setPrDialogOpen(true)
  }

  // Navigate to WOD edit page
  const navigateToWodEdit = () => {
    if (wod) {
      router.push(`/admin/wods/edit/${wod.id}`)
    }
  }

  const openHistoryDialog = (userId: string, exerciseId: string) => {
    const user = selectedSession?.check_ins.find(ci => ci.pelatis_id === userId)?.pelates;
    const exercise = selectedExercises.find(ex => ex.id === exerciseId);

    if (!user || !exercise) return;

    setHistoryUserExercise({
      userId,
      exerciseId,
      userName: `${user.name} ${user.last_name}`,
      exerciseName: exercise.exercise_name
    });

    setHistoryDialogOpen(true);
  };


  // ExerciseRecords component removed - was unused

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Coach Exercise Records</h1>
          <p className="text-muted-foreground">
            Record exercise data for multiple members at once
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <Button
            variant="outline"
            onClick={() => {
              const today = new Date()
              setSelectedDate(today.toISOString().split('T')[0])
            }}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Today
          </Button>
          <div className="relative">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="rounded-md border p-2"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sessions and WOD */}
        <div className="space-y-6 lg:col-span-1">
          {/* Session Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Sessions</CardTitle>
              <CardDescription>
                Select a session to record exercises
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isFetching ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading sessions...</span>
                </div>
              ) : sessions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No sessions found for this date</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {sessions.map((session) => (
                    <div
                      key={session.id}
                      className={`p-3 rounded-md cursor-pointer transition-colors ${
                        selectedSession?.id === session.id
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-muted border border-transparent'
                      }`}
                      onClick={() => setSelectedSession(session)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            {formatTime(session.start_time)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {session.programs?.name}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            <User className="h-3 w-3 mr-1" />
                            {session.check_ins.length}
                          </Badge>
                          {selectedSession?.id === session.id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* WOD Display */}
          <Card>
            <CardHeader className="pb-3 flex flex-row items-start justify-between">
              <div>
                <CardTitle>Workout of the Day</CardTitle>
                <CardDescription>
                  {formatDate(selectedDate)}
                </CardDescription>
              </div>
              {wod && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={navigateToWodEdit}
                  className="gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Edit WOD
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {!wod ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No workout scheduled for this day</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Exercise Tags */}
                  <div className="flex flex-wrap gap-2 pb-2">
                    {wodExercises.length > 0 ? (
                      wodExercises.map((exercise) => (
                        <Badge
                          key={exercise.id}
                          variant="outline"
                          className="bg-primary/10 border-primary/30"
                        >
                          <Dumbbell className="h-3 w-3 mr-1" />
                          {exercise.exercise_name}
                        </Badge>
                      ))
                    ) : (
                      <div className="text-sm text-muted-foreground">
                        No exercises assigned to this WOD
                      </div>
                    )}
                  </div>

                  {/* WOD Content */}
                  <div className="bg-muted/30 rounded-lg p-4">
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown>{wod.content}</ReactMarkdown>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metrics Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Metrics to Record</CardTitle>
              <CardDescription>
                Select which data points to collect
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="weight"
                    checked={selectedMetrics.weight}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, weight: checked === true }))
                    }
                  />
                  <Label htmlFor="weight">Weight (kg)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="reps"
                    checked={selectedMetrics.reps}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, reps: checked === true }))
                    }
                  />
                  <Label htmlFor="reps">Reps</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sets"
                    checked={selectedMetrics.sets}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, sets: checked === true }))
                    }
                  />
                  <Label htmlFor="sets">Sets</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="time"
                    checked={selectedMetrics.time}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, time: checked === true }))
                    }
                  />
                  <Label htmlFor="time">Time</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="calories"
                    checked={selectedMetrics.calories}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, calories: checked === true }))
                    }
                  />
                  <Label htmlFor="calories">Calories</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notes"
                    checked={selectedMetrics.notes}
                    onCheckedChange={(checked) =>
                      setSelectedMetrics(prev => ({ ...prev, notes: checked === true }))
                    }
                  />
                  <Label htmlFor="notes">Notes</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Exercise Recording Table */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                <div>
                  <CardTitle>Exercise Records</CardTitle>
                  <CardDescription>
                    {selectedSession
                      ? `${selectedSession.check_ins.length} members checked in`
                      : 'Select a session to view checked-in members'}
                  </CardDescription>
                </div>
                <Button
                  onClick={saveAllRecords}
                  disabled={!selectedSession || selectedSession.check_ins.length === 0}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save All Records
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {isFetching ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading data...</span>
                </div>
              ) : !selectedSession ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>Select a session to view members</p>
                </div>
              ) : selectedSession.check_ins.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <User className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No members checked in to this session</p>
                </div>
              ) : wodExercises.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Dumbbell className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No exercises assigned to this WOD</p>
                  <Button
                    variant="link"
                    onClick={navigateToWodEdit}
                  >
                    Edit WOD to add exercises
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[180px] bg-muted/50 sticky left-0 z-10">Member</TableHead>
                        {wodExercises.map((exercise) => (
                          <TableHead
                            key={exercise.id}
                            className="min-w-[180px] text-center"
                          >
                            <div className="flex flex-col items-center">
                              <div className="font-medium">{exercise.exercise_name}</div>
                              <div className="text-xs text-muted-foreground">{exercise.body_part}</div>

                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 mt-1 text-xs"
                                  >
                                    <span>Batch Entry</span>
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-md">
                                  <DialogHeader>
                                    <DialogTitle>Batch Entry for {exercise.exercise_name}</DialogTitle>
                                  </DialogHeader>
                                  <div className="space-y-4 py-4">
                                    <div className="text-sm text-muted-foreground">
                                      Apply the same value to all members for this exercise
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                      {selectedMetrics.weight && (
                                        <div className="space-y-2">
                                          <Label htmlFor={`batch-weight-${exercise.id}`}>Weight (kg)</Label>
                                          <Input
                                            id={`batch-weight-${exercise.id}`}
                                            type="number"
                                            placeholder="Weight"
                                          />
                                        </div>
                                      )}

                                      {selectedMetrics.reps && (
                                        <div className="space-y-2">
                                          <Label htmlFor={`batch-reps-${exercise.id}`}>Reps</Label>
                                          <Input
                                            id={`batch-reps-${exercise.id}`}
                                            type="number"
                                            placeholder="Reps"
                                          />
                                        </div>
                                      )}

                                      {selectedMetrics.sets && (
                                        <div className="space-y-2">
                                          <Label htmlFor={`batch-sets-${exercise.id}`}>Sets</Label>
                                          <Input
                                            id={`batch-sets-${exercise.id}`}
                                            type="number"
                                            placeholder="Sets"
                                          />
                                        </div>
                                      )}

                                      {selectedMetrics.time && (
                                        <div className="space-y-2">
                                          <Label htmlFor={`batch-time-${exercise.id}`}>Time (mm:ss)</Label>
                                          <Input
                                            id={`batch-time-${exercise.id}`}
                                            type="text"
                                            placeholder="00:00"
                                          />
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <DialogFooter>
                                    <Button
                                      onClick={() => {
                                        // Get values from inputs
                                        const weightInput = document.getElementById(`batch-weight-${exercise.id}`) as HTMLInputElement;
                                        const repsInput = document.getElementById(`batch-reps-${exercise.id}`) as HTMLInputElement;
                                        const setsInput = document.getElementById(`batch-sets-${exercise.id}`) as HTMLInputElement;
                                        const timeInput = document.getElementById(`batch-time-${exercise.id}`) as HTMLInputElement;

                                        // Apply to all members
                                        const newFormData = { ...formData };
                                        selectedSession?.check_ins.forEach(checkIn => {
                                          const userId = checkIn.pelatis_id;

                                          if (!newFormData[userId]) {
                                            newFormData[userId] = {};
                                          }

                                          if (!newFormData[userId][exercise.id]) {
                                            newFormData[userId][exercise.id] = {
                                              weight: '',
                                              reps: '',
                                              sets: '',
                                              time: '',
                                              calories: '',
                                              notes: ''
                                            };
                                          }

                                          if (weightInput?.value) {
                                            newFormData[userId][exercise.id].weight = weightInput.value;
                                          }

                                          if (repsInput?.value) {
                                            newFormData[userId][exercise.id].reps = repsInput.value;
                                          }

                                          if (setsInput?.value) {
                                            newFormData[userId][exercise.id].sets = setsInput.value;
                                          }

                                          if (timeInput?.value) {
                                            newFormData[userId][exercise.id].time = timeInput.value;
                                          }
                                        });

                                        setFormData(newFormData);
                                        toast.success('Batch values applied');
                                      }}
                                    >
                                      Apply to All
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedSession.check_ins.map((checkIn) => (
                        <TableRow key={checkIn.id}>
                          <TableCell className="font-medium bg-muted/50 sticky left-0 z-10">
                            {checkIn.pelates.name} {checkIn.pelates.last_name}
                          </TableCell>

                          {wodExercises.map((exercise) => {
                            const userId = checkIn.pelatis_id;
                            const exerciseId = exercise.id;
                            const isSaving = saving[userId]?.[exerciseId] || false;

                            return (
// Here's a revised version with more explicit styling to ensure icon visibility

<TableCell key={exercise.id} className="p-2">
  <div className="bg-muted/20 rounded-md p-3 space-y-3">
    <div className="flex justify-between items-center">
      <div className="flex space-x-3 items-center">
        {/* Trophy Button - with explicit icon styling */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                onClick={() => openPRDialog(userId, exerciseId)}
                className="inline-flex h-8 w-8 items-center justify-center rounded-md border border-amber-300 bg-amber-100 hover:bg-amber-200"
                style={{ padding: '0', position: 'relative' }}
              >
                {/* Inline SVG with !important styles to ensure visibility */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#d97706"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="important-icon"
                  style={{
                    display: 'block',
                    visibility: 'visible',
                    color: '#d97706'
                  }}
                >
                  <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path>
                  <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path>
                  <path d="M4 22h16"></path>
                  <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path>
                  <path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path>
                  <path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path>
                </svg>

                {/* Fallback text in case SVG doesn't render */}
                <span className="sr-only">View PRs</span>
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View personal records</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* History Button - with explicit icon styling */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                onClick={() => openHistoryDialog(userId, exerciseId)}
                className="inline-flex h-8 w-8 items-center justify-center rounded-md border border-blue-300 bg-blue-100 hover:bg-blue-200"
                style={{ padding: '0', position: 'relative' }}
              >
                {/* Using a fallback icon approach with Unicode character */}
                <span
                  className="text-blue-600 font-bold text-lg important-icon"
                  style={{
                    display: 'block',
                    visibility: 'visible'
                  }}
                >
                  ⌛
                </span>

                {/* Fallback text */}
                <span className="sr-only">View History</span>
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View exercise history</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Save Button with text-only approach */}
      <button
        type="button"
        onClick={() => saveRecord(userId, exerciseId)}
        disabled={isSaving}
        className="inline-flex h-8 px-4 items-center justify-center rounded-md border bg-white hover:bg-gray-50"
      >
        {isSaving ? (
          <span className="mr-1 text-primary">⟳</span>
        ) : (
          <span className="mr-1 text-primary">✓</span>
        )}
        <span className="text-xs">Save</span>
      </button>
    </div>
    <div className="grid gap-2">
      {selectedMetrics.weight && (
        <div className="space-y-1">
          <Label
            htmlFor={`weight-${userId}-${exerciseId}`}
            className="text-xs flex items-center"
          >
            Weight (kg)
            {isPR(userId, exerciseId, 'weight', formData[userId]?.[exerciseId]?.weight || '') && (
              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-[10px] h-4">PR</Badge>
            )}
          </Label>
          <Input
            id={`weight-${userId}-${exerciseId}`}
            type="number"
            placeholder="Weight"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.weight || ''}
            onChange={(e) => handleInputChange(userId, exerciseId, 'weight', e.target.value)}
          />
        </div>
      )}

      {selectedMetrics.reps && (
        <div className="space-y-1">
          <Label
            htmlFor={`reps-${userId}-${exerciseId}`}
            className="text-xs flex items-center"
          >
            Reps
            {isPR(userId, exerciseId, 'reps', formData[userId]?.[exerciseId]?.reps || '') && (
              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-[10px] h-4">PR</Badge>
            )}
          </Label>
          <Input
            id={`reps-${userId}-${exerciseId}`}
            type="number"
            placeholder="Reps"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.reps || ''}
            onChange={(e) => handleInputChange(userId, exerciseId, 'reps', e.target.value)}
          />
        </div>
      )}

      {selectedMetrics.sets && (
        <div className="space-y-1">
          <Label
            htmlFor={`sets-${userId}-${exerciseId}`}
            className="text-xs"
          >
            Sets
          </Label>
          <Input
            id={`sets-${userId}-${exerciseId}`}
            type="number"
            placeholder="Sets"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.sets || ''}
            onChange={(e) => handleInputChange(userId, exerciseId, 'sets', e.target.value)}
          />
        </div>
      )}

      {selectedMetrics.time && (
        <div className="space-y-1">
          <Label
            htmlFor={`time-${userId}-${exerciseId}`}
            className="text-xs"
          >
            Time (mm:ss)
          </Label>
          <Input
            id={`time-${userId}-${exerciseId}`}
            type="text"
            placeholder="00:00"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.time || ''}
            onChange={(e) => {
              const value = e.target.value;
              if (value.length <= 5) {
                const cleaned = value.replace(/[^\d:]/g, '');
                if (cleaned.length === 2 && !value.includes(':')) {
                  handleInputChange(userId, exerciseId, 'time', cleaned + ':');
                } else {
                  handleInputChange(userId, exerciseId, 'time', cleaned);
                }
              }
            }}
          />
        </div>
      )}

      {selectedMetrics.calories && (
        <div className="space-y-1">
          <Label
            htmlFor={`calories-${userId}-${exerciseId}`}
            className="text-xs"
          >
            Calories
          </Label>
          <Input
            id={`calories-${userId}-${exerciseId}`}
            type="number"
            placeholder="Calories"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.calories || ''}
            onChange={(e) => handleInputChange(userId, exerciseId, 'calories', e.target.value)}
          />
        </div>
      )}

      {selectedMetrics.notes && (
        <div className="space-y-1">
          <Label
            htmlFor={`notes-${userId}-${exerciseId}`}
            className="text-xs"
          >
            Notes
          </Label>
          <Input
            id={`notes-${userId}-${exerciseId}`}
            type="text"
            placeholder="Notes"
            className="h-8 text-sm"
            value={formData[userId]?.[exerciseId]?.notes || ''}
            onChange={(e) => handleInputChange(userId, exerciseId, 'notes', e.target.value)}
          />
        </div>
      )}
    </div>
  </div>
</TableCell>
                            );
                          })}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
            {selectedSession && selectedSession.check_ins.length > 0 && wodExercises.length > 0 && (
              <CardFooter className="flex justify-end pt-6">
                <Button
                  onClick={saveAllRecords}
                  disabled={isFetching}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save All Records
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      {/* PR Dialog */}
      <Dialog open={prDialogOpen} onOpenChange={setPrDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Previous Records</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 pt-4">
            {selectedUserExercise && (
              <>
                <div className="bg-muted p-3 rounded-md">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{selectedUserExercise.userName}</span>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <Dumbbell className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedUserExercise.exerciseName}</span>
                  </div>
                </div>

                {historicalPRs[selectedUserExercise.userId]?.[selectedUserExercise.exerciseId]?.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Weight</TableHead>
                        <TableHead>Reps</TableHead>
                        <TableHead>Sets</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {historicalPRs[selectedUserExercise.userId][selectedUserExercise.exerciseId]
                        .slice(0, 5) // Show last 5 records only
                        .map((record, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              {record.date_achieved ? format(new Date(record.date_achieved), 'dd/MM/yyyy') : '-'}
                            </TableCell>
                            <TableCell>{record.weight || '-'}</TableCell>
                            <TableCell>{record.reps || '-'}</TableCell>
                            <TableCell>{record.sets || '-'}</TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    <InfoIcon className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No previous records found</p>
                  </div>
                )}
              </>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setPrDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Exercise History Dialog */}
{historyUserExercise && (
  <UserExerciseHistory
    open={historyDialogOpen}
    onOpenChange={setHistoryDialogOpen}
    userId={historyUserExercise.userId}
    exerciseId={historyUserExercise.exerciseId}
    userName={historyUserExercise.userName}
    exerciseName={historyUserExercise.exerciseName}
  />
)}
    </div>
  );
}


// Utility functions
const formatTime = (dateTimeString: string): string => {
  if (!dateTimeString) return '';
  try {
    const date = parseISO(dateTimeString);
    return format(date, 'HH:mm');
  } catch (error) {
    console.error('Error formatting time:', error);
    return '';
  }
}

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  try {
    const date = parseISO(`${dateString}T00:00:00`);
    return format(date, 'EEEE, MMMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}
