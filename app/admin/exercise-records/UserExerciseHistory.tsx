'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { format } from 'date-fns'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import {
  User,
  Dumbbell,
  CalendarRange,
  Trophy,
  Loader2,
  ArrowUpFromLine,
  ArrowDown,
  FileText
} from 'lucide-react'
import type { Database } from '@/types/supabase'

type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'];

interface UserExerciseHistoryProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  exerciseId: string
  userName: string
  exerciseName: string
}

export function UserExerciseHistory({
  open,
  onOpenChange,
  userId,
  exerciseId,
  userName,
  exerciseName
}: UserExerciseHistoryProps) {
  const supabase = createClientComponentClient<Database>()
  const [records, setRecords] = useState<ExerciseRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [sortField, setSortField] = useState<'date_achieved' | 'weight' | 'reps'>('date_achieved')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [personalBests, setPersonalBests] = useState<{
    weight: number | null
    reps: number | null
    sets: number | null
    time: string | null
    calories: number | null
  }>({
    weight: null,
    reps: null,
    sets: null,
    time: null,
    calories: null
  })

  useEffect(() => {
    const fetchExerciseHistory = async () => {
      if (!open) return

      setIsLoading(true)
      try {
        // Fetch all records for this user and exercise
        const { data, error } = await supabase
          .from('exercise_records')
          .select('*')
          .eq('pelatis_id', userId)
          .eq('exercise_id', exerciseId)
          .order('date_achieved', { ascending: false })

        if (error) throw error

        setRecords(data || [])

        // Calculate personal bests
        if (data && data.length > 0) {
          // Calculate numeric personal bests
          const pbs = {
            weight: Math.max(...data.map(r => r.weight || 0)),
            reps: Math.max(...data.map(r => r.reps || 0)),
            sets: Math.max(...data.map(r => r.sets || 0)),
            calories: Math.max(...data.map(r => r.calories || 0))
          }

          // Handle time separately - find the smallest non-null time
          let bestTime: string | null = null;
          const validTimes = data
            .filter(r => r.time)
            .map(r => r.time)
            .filter((time): time is string => time !== null)

          if (validTimes.length > 0) {
            // Simple sort by "MM:SS" format (not handling hours for now)
            validTimes.sort((a, b) => {
              const aTime = a.split(':').map(Number)
              const bTime = b.split(':').map(Number)

              // Convert to seconds for comparison
              const aSeconds = (aTime[0] || 0) * 60 + (aTime[1] || 0)
              const bSeconds = (bTime[0] || 0) * 60 + (bTime[1] || 0)

              return aSeconds - bSeconds
            })

            bestTime = validTimes[0]
          }

          setPersonalBests({
            weight: pbs.weight || null,
            reps: pbs.reps || null,
            sets: pbs.sets || null,
            time: bestTime,
            calories: pbs.calories || null
          })
        }
      } catch (error) {
        console.error('Error fetching exercise history:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchExerciseHistory()
  }, [open, userId, exerciseId, supabase])

  // Sort records based on current sort field and direction
  const sortedRecords = [...records].sort((a, b) => {
    if (sortField === 'date_achieved') {
      const dateA = a.date_achieved ? new Date(a.date_achieved).getTime() : 0
      const dateB = b.date_achieved ? new Date(b.date_achieved).getTime() : 0
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
    }

    if (sortField === 'weight') {
      const weightA = a.weight || 0
      const weightB = b.weight || 0
      return sortDirection === 'asc' ? weightA - weightB : weightB - weightA
    }

    if (sortField === 'reps') {
      const repsA = a.reps || 0
      const repsB = b.reps || 0
      return sortDirection === 'asc' ? repsA - repsB : repsB - repsA
    }

    return 0
  })

  // Toggle sort when clicking on column headers
  const toggleSort = (field: 'date_achieved' | 'weight' | 'reps') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc') // Default to descending for new sort field
    }
  }

  const getSortIcon = (field: 'date_achieved' | 'weight' | 'reps') => {
    if (sortField !== field) return null

    return sortDirection === 'asc' ?
      <ArrowUpFromLine className="h-3 w-3 ml-1" /> :
      <ArrowDown className="h-3 w-3 ml-1" />
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Exercise History</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* User & Exercise Info */}
          <div className="bg-muted p-4 rounded-lg flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{userName}</span>
              </div>
              <div className="flex items-center gap-2">
                <Dumbbell className="h-4 w-4 text-muted-foreground" />
                <span>{exerciseName}</span>
              </div>
            </div>

            {/* Personal Bests */}
            {!isLoading && records.length > 0 && (
              <Card className="border shadow-sm">
                <CardContent className="p-3">
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <Trophy className="h-3 w-3 text-amber-500 mr-1" />
                    Personal Bests
                  </h3>
                  <div className="grid grid-cols-2 gap-x-6 gap-y-1 text-sm">
                    {personalBests.weight && personalBests.weight > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Weight:</span>
                        <span className="font-medium">{personalBests.weight} kg</span>
                      </div>
                    )}
                    {personalBests.reps && personalBests.reps > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Reps:</span>
                        <span className="font-medium">{personalBests.reps}</span>
                      </div>
                    )}
                    {personalBests.sets && personalBests.sets > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Sets:</span>
                        <span className="font-medium">{personalBests.sets}</span>
                      </div>
                    )}
                    {personalBests.time && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Time:</span>
                        <span className="font-medium">{personalBests.time}</span>
                      </div>
                    )}
                    {personalBests.calories && personalBests.calories > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Calories:</span>
                        <span className="font-medium">{personalBests.calories}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Records Table */}
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading exercise history...</span>
            </div>
          ) : sortedRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-2 opacity-20" />
              <p>No exercise records found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:text-primary"
                      onClick={() => toggleSort('date_achieved')}
                    >
                      <div className="flex items-center">
                        <CalendarRange className="h-4 w-4 mr-1" />
                        Date
                        {getSortIcon('date_achieved')}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:text-primary"
                      onClick={() => toggleSort('weight')}
                    >
                      <div className="flex items-center">
                        Weight (kg)
                        {getSortIcon('weight')}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:text-primary"
                      onClick={() => toggleSort('reps')}
                    >
                      <div className="flex items-center">
                        Reps
                        {getSortIcon('reps')}
                      </div>
                    </TableHead>
                    <TableHead>Sets</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Calories</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedRecords.map((record) => {
                    // Check if this record is a PB for weight or reps
                    const isWeightPB = record.weight === personalBests.weight && record.weight !== null && record.weight > 0
                    const isRepsPB = record.reps === personalBests.reps && record.reps !== null && record.reps > 0
                    const isSetsPB = record.sets === personalBests.sets && record.sets !== null && record.sets > 0
                    const isTimePB = record.time === personalBests.time && record.time !== null
                    const isCaloriesPB = record.calories === personalBests.calories && record.calories !== null && record.calories > 0

                    return (
                      <TableRow key={record.id}>
                        <TableCell>
                          {record.date_achieved
                            ? format(new Date(record.date_achieved), 'dd/MM/yyyy')
                            : '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {record.weight || '-'}
                            {isWeightPB && (
                              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-xs">PB</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {record.reps || '-'}
                            {isRepsPB && (
                              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-xs">PB</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {record.sets || '-'}
                            {isSetsPB && (
                              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-xs">PB</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {record.time || '-'}
                            {isTimePB && (
                              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-xs">PB</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {record.calories || '-'}
                            {isCaloriesPB && (
                              <Badge className="ml-1 bg-amber-500 hover:bg-amber-500 text-xs">PB</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {record.notes || '-'}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}