// app/admin/exercise-records/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { CoachExerciseRecordsClient } from './client'
// import { getUserRoles } from '@/hooks/useServerUser'
import type { Database } from '@/types/supabase'

export default async function CoachExerciseRecordsPage() {
  const supabase = createServerComponentClient<Database>({
    cookies
  })

  // Check if user is logged in
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    redirect('/auth')
  }

  // Check if user is an admin/coach
  const { data: userRoles } = await supabase.rpc('getUserRoles', {
    p_user_id: user.id
  });

  if (!userRoles || !userRoles.includes('admin')) {
    redirect('/user/front-page')
  }

  const today = new Date().toISOString().split('T')[0]

  // Fetch all data needed for the page
  const [
    wodResponse,
    exercisesResponse,
    sessionsResponse
  ] = await Promise.all([
    // Get today's WOD
    supabase
      .from('wod')
      .select('*')
      .eq('date', today)
      .single(),

    // Get all exercises
    supabase
      .from('exercise_movements')
      .select('*')
      .order('exercise_name'),

    // Get today's sessions
    supabase
      .from('sessions')
      .select(`
        *,
        programs:program_id (
          id,
          name,
          created_at,
          description
        ),
        check_ins (
          id,
          pelatis_id,
          check_in_time,
          session_id,
          pelates:pelatis_id (
            id,
            name,
            last_name
          )
        )
      `)
      .gte('start_time', `${today}T00:00:00`)
      .lte('start_time', `${today}T23:59:59`)
      .order('start_time')
  ])

  // Type assertion to match the Session type in client.tsx
  const typedSessions = (sessionsResponse.data || []).map(session => {
    return {
      ...session,
      check_ins: (session.check_ins || []).filter(checkIn =>
        checkIn.pelatis_id !== null && checkIn.pelates !== null
      ) as {
        id: string;
        pelatis_id: string;
        check_in_time: string;
        session_id: string;
        pelates: Database['public']['Tables']['pelates']['Row'];
      }[]
    }
  });

  return (
    <CoachExerciseRecordsClient
      initialWod={wodResponse.data}
      exercises={exercisesResponse.data || []}
      initialSessions={typedSessions}
    />
  )
}
