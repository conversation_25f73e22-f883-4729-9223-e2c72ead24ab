'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import {
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  DialogHeader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  Di<PERSON>Footer,
} from '@/components/ui/dialog'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { Check, Plus, Dumbbell, Search, X, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Database } from '@/types/supabase'

type Exercise = Database['public']['Tables']['exercise_movements']['Row']

interface ExerciseSelectorProps {
  exercises: Exercise[]
  selectedExercises: Exercise[]
  onSelectExercises: (exercises: Exercise[]) => void
}

export function ExerciseSelector({ 
  exercises, 
  selectedExercises, 
  onSelectExercises
}: ExerciseSelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selected, setSelected] = useState<string[]>(
    selectedExercises.map(ex => ex.id)
  )
  const router = useRouter()
  
  // Filter exercises based on search query
  const filteredExercises = searchQuery === '' 
    ? exercises 
    : exercises.filter(exercise => {
        const query = searchQuery.toLowerCase()
        return (
          exercise.exercise_name.toLowerCase().includes(query) ||
          (exercise.body_part && exercise.body_part.toLowerCase().includes(query)) ||
          (exercise.equipment && exercise.equipment.toLowerCase().includes(query))
        )
      })

  // Toggle selection of an exercise
  const toggleExercise = (exerciseId: string) => {
    setSelected(prev => {
      if (prev.includes(exerciseId)) {
        return prev.filter(id => id !== exerciseId)
      } else {
        return [...prev, exerciseId]
      }
    })
  }
  
  // Apply selection
  const applySelection = () => {
    const selectedExs = exercises.filter(ex => selected.includes(ex.id))
    onSelectExercises(selectedExs)
    setOpen(false)
  }

  // Navigate to exercise management page
  const navigateToExercisesPage = () => {
    router.push('/admin/wods/exercises')
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Add Exercises
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select Exercises</DialogTitle>
        </DialogHeader>
        <div className="flex items-center justify-between mb-4">
          <Button 
            variant="outline" 
            onClick={navigateToExercisesPage}
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Manage Exercises
          </Button>
        </div>
        <div className="flex items-center border rounded-md px-3 py-2">
          <Search className="h-4 w-4 mr-2 text-muted-foreground" />
          <input
            className="flex w-full bg-transparent p-0 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            placeholder="Search exercises..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="border rounded-md h-80 overflow-y-auto">
          <Command>
            <CommandList>
              <CommandEmpty>No exercises found.</CommandEmpty>
              <CommandGroup>
                {filteredExercises.map((exercise) => {
                  const isSelected = selected.includes(exercise.id)
                  return (
                    <CommandItem
                      key={exercise.id}
                      onSelect={() => toggleExercise(exercise.id)}
                      className="flex items-center justify-between cursor-pointer"
                    >
                      <div className="flex items-center">
                        <Dumbbell className={cn(
                          "h-4 w-4 mr-2",
                          isSelected ? "text-primary" : "text-muted-foreground"
                        )} />
                        <div>
                          <p className="font-medium">{exercise.exercise_name}</p>
                          <p className="text-xs text-muted-foreground">
                            {exercise.body_part} • {exercise.equipment}
                          </p>
                        </div>
                      </div>
                      <div className={cn(
                        "flex h-6 w-6 items-center justify-center rounded-full border",
                        isSelected ? "bg-primary border-primary text-primary-foreground" : "border-muted"
                      )}>
                        {isSelected && <Check className="h-4 w-4" />}
                      </div>
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
        
        <div className="flex flex-wrap gap-2 mt-2">
          <div className="text-sm font-medium">Selected:</div>
          {selected.length === 0 ? (
            <div className="text-sm text-muted-foreground">No exercises selected</div>
          ) : (
            selected.map(id => {
              const exercise = exercises.find(ex => ex.id === id)
              return exercise ? (
                <Badge key={id} variant="outline" className="flex items-center gap-1">
                  {exercise.exercise_name}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleExercise(id)
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ) : null
            })
          )}
        </div>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={applySelection}>
            Apply ({selected.length})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
