// app/admin/support/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { AdminSupportTable } from './AdminSupportTable';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function AdminSupportPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  const { data: threads } = await supabase
    .from('support_threads')
    .select(`
      *,
      client:pelates!support_threads_client_id_fkey (
        name,
        last_name
      ),
      support_messages (
        *,
        sender:pelates!support_messages_sender_id_fkey (
          name,
          last_name
        )
      )
    `)
    .order('created_at', { ascending: false });

  const { count: unreadCount } = await supabase
    .from('support_threads')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'open');

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Support Tickets</h1>
          <p className="text-gray-500 mt-1">
            {unreadCount || 0} unread tickets
          </p>
        </div>
      </div>

      <AdminSupportTable threads={threads} />
    </div>
  );
}