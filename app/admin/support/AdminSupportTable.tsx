'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { useSupabase } from '@/hooks/useSupabase';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/types/supabase';

type MessageStatus = Database['public']['Enums']['message_status'];

type Thread = Database['public']['Tables']['support_threads']['Row'] & {
  client: {
    name: string | null;
    last_name: string | null;
  } | null;
  support_messages: Array<{
    sender: {
      name: string | null;
      last_name: string | null;
    } | null;
  }>;
};

interface AdminSupportTableProps {
  threads: Thread[] | null;
}

export function AdminSupportTable({ threads: initialThreads }: AdminSupportTableProps) {
  const [threads, setThreads] = useState(initialThreads);
  const router = useRouter();
  const { supabase } = useSupabase();
  const { toast } = useToast();

  const updateThreadStatus = async (threadId: string, status: MessageStatus) => {
    try {
      const { error } = await supabase
        .from('support_threads')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', threadId);

      if (error) throw error;

      setThreads(prev => 
        prev?.map(thread => 
          thread.id === threadId 
            ? { ...thread, status } 
            : thread
        ) ?? null
      );

      toast({
        title: "Status updated",
        description: `Ticket status updated to ${status}`,
      });

    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "Failed to update ticket status",
        variant: "destructive",
      });
    }
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Client</TableHead>
          <TableHead>Title</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Last Updated</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {threads?.map((thread) => (
          <TableRow 
            key={thread.id}
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => router.push(`/admin/support/${thread.id}`)}
          >
            <TableCell>
              {thread.client?.name} {thread.client?.last_name}
            </TableCell>
            <TableCell>{thread.title}</TableCell>
            <TableCell>
              <Badge variant="outline">{thread.category}</Badge>
            </TableCell>
            <TableCell 
              onClick={(e) => e.stopPropagation()}
            >
              <Select
                value={thread.status ?? 'open'}
                onValueChange={(value: MessageStatus) => {
                  updateThreadStatus(thread.id, value);
                }}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </TableCell>
            <TableCell>
              {thread.created_at ? format(new Date(thread.created_at), 'PP') : 'N/A'}
            </TableCell>
            <TableCell>
              {thread.updated_at ? 
                format(new Date(thread.updated_at), 'PP') : 
                'Not updated'}
            </TableCell>
          </TableRow>
        ))}
        {!threads?.length && (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8 text-gray-500">
              No support tickets found.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}