// app/admin/support/[threadId]/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { notFound, redirect } from 'next/navigation';
import { getServerUser } from '@/utils/auth.server';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AdminReplyForm } from './AdminReplyForm';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

async function getThread(threadId: string) {
  try {
    const supabase = createServerComponentClient<Database>({ cookies });

    const { data: thread, error } = await supabase
      .from('support_threads')
      .select(`
        *,
        client:pelates!support_threads_client_id_fkey (
          id,
          name,
          last_name
        ),
        support_messages (
          *,
          sender:pelates!support_messages_sender_id_fkey (
            name,
            last_name
          )
        )
      `)
      .eq('id', threadId)
      .single();

    if (error) {
      console.error('Error fetching thread:', error);
      return null;
    }

    return thread;
  } catch (error) {
    console.error('Error in getThread:', error);
    return null;
  }
}

export default async function AdminThreadPage({ 
  params 
}: { 
  params: { threadId: string } 
}) {
  // Verify admin status
  const { clientId: adminId, isAdmin } = await getServerUser();
  if (!isAdmin) {
    redirect('/auth');
  }

  const thread = await getThread(params.threadId);
  if (!thread) {
    notFound();
  }

  // Ensure support_messages is always an array
  const messages = thread.support_messages || [];

  // Provide a default status if null
  const currentStatus = thread.status ?? 'open';

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold mb-2">{thread.title}</h1>
          <div className="flex gap-2 items-center">
            <Badge>{thread.category}</Badge>
            <Badge variant="outline">{currentStatus}</Badge>
            {thread.client && (
              <span className="text-sm text-gray-500">
                From: {thread.client.name} {thread.client.last_name}
              </span>
            )}
          </div>
        </div>
        
        <Button 
          variant="outline" 
          className="mt-2"
          onClick={() => window.history.back()}
        >
          Back to List
        </Button>
      </div>

      <div className="space-y-4">
        {messages.map((message) => (
          <Card key={message.id} className={`p-4 ${
            message.is_coach_reply ? 'bg-blue-50' : ''
          }`}>
            <div className="flex justify-between items-start mb-2">
              <div>
                <div className="font-medium">
                  {message.sender?.name} {message.sender?.last_name}
                  {message.is_coach_reply && (
                    <Badge variant="secondary" className="ml-2">Coach</Badge>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  {message.created_at ? new Date(message.created_at).toLocaleString() : ''}
                </div>
              </div>
            </div>
            <p className="text-gray-700 whitespace-pre-wrap">{message.content}</p>
          </Card>
        ))}
      </div>

      <AdminReplyForm 
        threadId={thread.id} 
        clientId={adminId}
        currentStatus={currentStatus}
      />
    </div>
  );
}