'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSupabase } from '@/hooks/useSupabase';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import type { Database } from '@/types/supabase';

type MessageStatus = Database['public']['Enums']['message_status'];

interface AdminReplyFormProps {
  threadId: string;
  clientId: string;
  currentStatus: MessageStatus;
}

export function AdminReplyForm({ 
  threadId, 
  clientId,
  currentStatus
}: AdminReplyFormProps) {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState<MessageStatus>(currentStatus);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    setLoading(true);
    try {
      // Start a transaction
      const { error: messageError } = await supabase
        .from('support_messages')
        .insert({
          thread_id: threadId,
          content: message.trim(),
          sender_id: clientId,
          is_coach_reply: true
        });

      if (messageError) throw messageError;

      // Update thread status and timestamps
      const { error: threadError } = await supabase
        .from('support_threads')
        .update({ 
          status,
          last_reply_at: new Date().toISOString(),
          last_updated_at: new Date().toISOString()
        })
        .eq('id', threadId);

      if (threadError) throw threadError;

      setMessage('');
      router.refresh();
      toast({ 
        title: 'Reply sent',
        description: 'Your reply has been sent and the ticket status has been updated.'
      });
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({ 
        title: 'Error sending reply',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex gap-4 items-center">
        <div className="flex-1">
          <label className="text-sm font-medium mb-1 block">
            Update Status
          </label>
          <Select
            value={status}
            onValueChange={(value: MessageStatus) => setStatus(value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">
          Reply Message
        </label>
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your reply..."
          className="min-h-[150px]"
          disabled={loading}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button
          type="submit"
          disabled={loading || !message.trim()}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            'Send Reply'
          )}
        </Button>
      </div>
    </form>
  );
}