"use client";

import { useState, useEffect, useCallback } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Check, Clock, User, X, Calendar, ChevronDown, ChevronRight,
  CheckCircle2, AlertCircle, Loader2, UserPlus
} from "lucide-react";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";

// Use a different name to avoid conflict with the main Database type


interface Session {
  id: string;
  start_time: string;
  duration: number;
  max_participants: number;
  program_id: string;
  created_at: string | null;
  created_by: string | null;
  programs: {
    id: string;
    name: string;
  };
  check_ins: {
    id: string;
    pelatis_id: string | null;
    pelates: {
      id: string;
      name: string | null;
      last_name: string | null;
    } | null;
  }[];
  bookings: {
    id: string;
    pelatis_id: string | null;
    pelates: {
      id: string;
      name: string | null;
      last_name: string | null;
    } | null;
  }[];
}

interface Wod {
  id: string;
  content: string;
  warmup: string | null;
  date: string;
}

interface Pelatis {
  id: string;
  name: string | null;
  last_name: string | null;
  client_name: string | null;
}

type SessionStatus = "past" | "current" | "upcoming";
type FilterType = "all" | "current" | "available" | "pending";

export default function DailyPage() {
  // Import the Database type from @/types/supabase
  const supabase = createClientComponentClient<import('@/types/supabase').Database>();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [wod, setWod] = useState<Wod | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [pelates, setPelates] = useState<Pelatis[]>([]);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState<FilterType>("all");
  const [expandedSessions, setExpandedSessions] = useState<Set<string>>(new Set());

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Fetch WOD for the selected date
      const { data: wodData, error: wodError } = await supabase
        .from('wod')
        .select('*')
        .eq('date', selectedDate)
        .single();

      if (wodError && wodError.code !== 'PGRST116') {
        throw wodError;
      }

      setWod(wodData);

      // Fetch sessions for the selected date
      const { data: sessionsData, error: sessionsError } = await supabase
        .from('sessions')
        .select(`
          *,
          programs:program_id (
            id,
            name
          ),
          check_ins (
            id,
            pelatis_id,
            pelates:pelatis_id (
              id,
              name,
              last_name
            )
          ),
          bookings (
            id,
            pelatis_id,
            pelates:pelatis_id (
              id,
              name,
              last_name
            )
          )
        `)
        .gte('start_time', startOfDay.toISOString())
        .lte('start_time', endOfDay.toISOString())
        .order('start_time');

      if (sessionsError) throw sessionsError;
      setSessions(sessionsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  }, [selectedDate, supabase]);

  const fetchPelates = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('pelates')
        .select('id, name, last_name, client_name')
        .order('name');

      if (error) throw error;
      setPelates(data || []);
    } catch (error) {
      console.error('Error fetching pelates:', error);
      toast.error('Failed to fetch pelates');
    }
  }, [supabase]);

  useEffect(() => {
    fetchData();
    fetchPelates();

    // Set up auto-refresh every minute to update current session highlighting
    const intervalId = setInterval(() => {
      fetchData();
    }, 60000);

    return () => clearInterval(intervalId);
  }, [fetchData, fetchPelates]);

  async function handleCheckIn(sessionId: string, pelatisId: string | null) {
    if (!pelatisId) {
      toast.error('Invalid pelatis ID');
      return;
    }

    try {
      const { error } = await supabase
        .from('check_ins')
        .insert({
          session_id: sessionId,
          pelatis_id: pelatisId,
          check_in_time: new Date().toISOString()
        });

      if (error) throw error;
      toast.success('Check-in successful');
      fetchData();
    } catch (error) {
      console.error('Error checking in:', error);
      toast.error('Failed to check in');
    }
  }

  async function handleUncheck(sessionId: string, pelatisId: string | null) {
    if (!pelatisId) {
      toast.error('Invalid pelatis ID');
      return;
    }

    try {
      const { error } = await supabase
        .from('check_ins')
        .delete()
        .eq('session_id', sessionId)
        .eq('pelatis_id', pelatisId);

      if (error) throw error;
      toast.success('Attendance removed');
      fetchData();
    } catch (error) {
      console.error('Error unchecking:', error);
      toast.error('Failed to remove attendance');
    }
  }

  function formatTime(timeString: string) {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function formatDuration(minutes: number) {
    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes === 0 ?
        `${hours} hr` :
        `${hours} hr ${remainingMinutes} min`;
    }
  }

  function getAvailablePelates(session: Session) {
    if (!pelates || !session) return [];
    return pelates.filter(pelatis =>
      !session.check_ins.some(ci => ci.pelatis_id === pelatis.id) &&
      !session.bookings.some(b => b.pelatis_id === pelatis.id)
    );
  }

  function getFilteredPelates(session: Session) {
    const available = getAvailablePelates(session);
    if (!searchQuery) return available;

    const query = searchQuery.toLowerCase();
    return available.filter(pelatis =>
      (pelatis.name?.toLowerCase().includes(query) ||
       pelatis.last_name?.toLowerCase().includes(query) ||
       pelatis.client_name?.toLowerCase().includes(query))
    );
  }

  function getSessionStatus(session: Session): SessionStatus {
    const now = new Date();
    const sessionStart = new Date(session.start_time);
    const sessionEnd = new Date(sessionStart);
    sessionEnd.setMinutes(sessionEnd.getMinutes() + session.duration);

    if (now >= sessionEnd) {
      return "past";
    } else if (now >= sessionStart && now < sessionEnd) {
      return "current";
    } else {
      return "upcoming";
    }
  }

  function getSessionStatusLabel(status: SessionStatus): string {
    switch (status) {
      case "past": return "Completed";
      case "current": return "In Progress";
      case "upcoming": return "Upcoming";
    }
  }

  function getAttendancePercentage(session: Session): number {
    if (!session.max_participants) return 0;
    return Math.min(100, Math.round((session.check_ins.length / session.max_participants) * 100));
  }

  function toggleSessionExpanded(sessionId: string) {
    setExpandedSessions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId);
      } else {
        newSet.add(sessionId);
      }
      return newSet;
    });
  }

  function getPendingCheckIns(session: Session) {
    return session.bookings.filter(booking =>
      !session.check_ins.some(ci => ci.pelatis_id === booking.pelatis_id)
    );
  }

  function filterSessions() {
    switch (activeFilter) {
      case "available":
        return sessions.filter(session =>
          session.check_ins.length < session.max_participants &&
          getSessionStatus(session) !== "past"
        );
      case "pending":
        return sessions.filter(session =>
          getPendingCheckIns(session).length > 0
        );
      case "current":
        return sessions.filter(session =>
          getSessionStatus(session) === "current"
        );
      default:
        return sessions;
    }
  }

  const filteredSessions = filterSessions();
  const todayDateString = new Date().toISOString().split('T')[0];
  const isToday = selectedDate === todayDateString;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">
            Daily Sessions
          </h1>
          <p className="text-muted-foreground">
            {new Date(selectedDate).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
            {isToday && <Badge className="ml-2 bg-primary">Today</Badge>}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const today = new Date();
              setSelectedDate(today.toISOString().split('T')[0]);
            }}
          >
            <Calendar className="h-4 w-4 mr-1" />
            Today
          </Button>
          <div className="relative">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="rounded-md border p-2"
            />
          </div>
        </div>
      </div>

      <Tabs defaultValue="sessions" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="sessions">
            <Clock className="h-4 w-4 mr-2" />
            Sessions
          </TabsTrigger>
          <TabsTrigger value="wod">
            <Calendar className="h-4 w-4 mr-2" />
            Workout of the Day
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                <CardTitle>Sessions Schedule</CardTitle>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={activeFilter === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter("all")}
                  >
                    All Sessions
                  </Button>
                  <Button
                    variant={activeFilter === "current" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter("current")}
                  >
                    Current
                  </Button>
                  <Button
                    variant={activeFilter === "available" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter("available")}
                  >
                    Available Spots
                  </Button>
                  <Button
                    variant={activeFilter === "pending" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter("pending")}
                  >
                    Pending Check-ins
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading sessions...</span>
                </div>
              ) : filteredSessions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No sessions found for the selected filters</p>
                  {activeFilter !== "all" && (
                    <Button
                      variant="link"
                      onClick={() => setActiveFilter("all")}
                    >
                      Show all sessions
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredSessions.map((session) => {
                    const status = getSessionStatus(session);
                    const isExpanded = expandedSessions.has(session.id);
                    const pendingCheckIns = getPendingCheckIns(session);
                    const attendancePercentage = getAttendancePercentage(session);

                    return (
                      <Card
                        key={session.id}
                        className={`overflow-hidden border-l-4 transition-all ${
                          status === "current"
                            ? "border-l-primary"
                            : status === "past"
                              ? "border-l-gray-300 bg-gray-50"
                              : "border-l-blue-300"
                        }`}
                      >
                        <div
                          className={`p-4 cursor-pointer ${
                            status === "current" ? "bg-primary/5" : ""
                          }`}
                          onClick={() => toggleSessionExpanded(session.id)}
                        >
                          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                            <div className="flex items-center gap-2">
                              {status === "current" ? (
                                <Badge variant="default" className="bg-primary animate-pulse">
                                  <div className="flex items-center gap-1">
                                    <span className="inline-block w-2 h-2 rounded-full bg-white mr-1"></span>
                                    Now
                                  </div>
                                </Badge>
                              ) : (
                                <Badge variant={status === "past" ? "outline" : "secondary"}>
                                  {getSessionStatusLabel(status)}
                                </Badge>
                              )}
                              <span className="font-semibold text-lg">
                                {formatTime(session.start_time)}
                              </span>
                              <span className="text-muted-foreground hidden sm:inline">
                                • {formatDuration(session.duration)}
                              </span>
                              <span className="font-medium text-primary">
                                {session.programs?.name || 'Unknown Program'}
                              </span>
                            </div>
                            <div className="flex items-center gap-4 w-full sm:w-auto">
                              <div className="flex flex-col w-full sm:w-auto">
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <User className="h-3 w-3" />
                                  <span>{session.check_ins.length}/{session.max_participants}</span>
                                </div>
                                <Progress value={attendancePercentage} className="h-1 w-full sm:w-24" />
                              </div>
                              {pendingCheckIns.length > 0 && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                        {pendingCheckIns.length} pending
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent side="bottom">
                                      {pendingCheckIns.length} booked {pendingCheckIns.length === 1 ? 'person' : 'people'} not checked in
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                              <div className="text-muted-foreground">
                                {isExpanded ? (
                                  <ChevronDown className="h-5 w-5" />
                                ) : (
                                  <ChevronRight className="h-5 w-5" />
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {isExpanded && (
                          <div className="px-4 pb-4 pt-0">
                            <div className="bg-gray-50 p-4 rounded-md mb-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                  <h3 className="font-semibold mb-2 flex items-center text-sm text-muted-foreground">
                                    <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                                    CHECKED IN ({session.check_ins.length})
                                  </h3>
                                  {session.check_ins.length === 0 ? (
                                    <p className="text-sm text-muted-foreground italic">No check-ins yet</p>
                                  ) : (
                                    <div className="space-y-2">
                                      {session.check_ins.map((checkIn) => (
                                        <div key={checkIn.id} className="flex items-center justify-between gap-2 bg-white p-2 rounded-md shadow-sm">
                                          <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-green-500" />
                                            <span>
                                              {checkIn.pelates?.name || ''} {checkIn.pelates?.last_name || ''}
                                            </span>
                                          </div>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleUncheck(session.id, checkIn.pelatis_id)}
                                            className="text-red-500 hover:text-red-700"
                                          >
                                            <X className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>

                                <div>
                                  <h3 className="font-semibold mb-2 flex items-center text-sm text-muted-foreground">
                                    <AlertCircle className="h-4 w-4 mr-1 text-amber-500" />
                                    BOOKED BUT NOT CHECKED IN ({pendingCheckIns.length})
                                  </h3>
                                  {pendingCheckIns.length === 0 ? (
                                    <p className="text-sm text-muted-foreground italic">All bookings checked in</p>
                                  ) : (
                                    <div className="space-y-2">
                                      {pendingCheckIns.map((booking) => (
                                        <div key={booking.id} className="flex items-center justify-between gap-2 bg-white p-2 rounded-md shadow-sm border-l-2 border-amber-300">
                                          <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-amber-500" />
                                            <span>
                                              {booking.pelates?.name || ''} {booking.pelates?.last_name || ''}
                                            </span>
                                          </div>
                                          <Button
                                            variant="default"
                                            size="sm"
                                            onClick={() => handleCheckIn(session.id, booking.pelatis_id)}
                                            className="text-xs"
                                          >
                                            <Check className="h-3 w-3 mr-1" />
                                            Mark Present
                                          </Button>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <div className="text-sm text-muted-foreground">
                                {session.max_participants - session.check_ins.length} spots available
                              </div>
                              <Dialog open={isDialogOpen && selectedSession?.id === session.id} onOpenChange={setIsDialogOpen}>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedSession(session);
                                      setIsDialogOpen(true);
                                      setSearchQuery("");
                                    }}
                                    disabled={status === "past"}
                                  >
                                    <UserPlus className="h-4 w-4 mr-2" />
                                    Add Walk-in Attendee
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-md">
                                  <DialogHeader>
                                    <DialogTitle>Add Walk-in Attendee</DialogTitle>
                                  </DialogHeader>
                                  <div className="space-y-4 pt-4">
                                    <div>
                                      <Label htmlFor="search" className="text-muted-foreground text-sm">Search for person</Label>
                                      <Input
                                        id="search"
                                        placeholder="Search by name..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="mt-1"
                                      />
                                    </div>
                                    <div className="max-h-[300px] overflow-y-auto space-y-2 bg-gray-50 p-2 rounded-md">
                                      {selectedSession && getFilteredPelates(selectedSession).length > 0 ? (
                                        selectedSession && getFilteredPelates(selectedSession).map((pelatis) => (
                                          <Button
                                            key={pelatis.id}
                                            variant="outline"
                                            className="w-full justify-start bg-white"
                                            onClick={() => {
                                              if (selectedSession) {
                                                handleCheckIn(selectedSession.id, pelatis.id);
                                                setIsDialogOpen(false);
                                              }
                                            }}
                                          >
                                            <User className="h-4 w-4 mr-2" />
                                            <span>
                                              {pelatis.client_name || `${pelatis.name} ${pelatis.last_name}`}
                                            </span>
                                          </Button>
                                        ))
                                      ) : (
                                        <div className="text-center py-4 text-muted-foreground">
                                          {searchQuery ? "No matching people found" : "No available people to check in"}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <DialogFooter>
                                    <Button
                                      variant="outline"
                                      onClick={() => setIsDialogOpen(false)}
                                    >
                                      Cancel
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </div>
                        )}
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="wod">
          <Card>
            <CardHeader>
              <CardTitle>Workout of the Day</CardTitle>
              <CardDescription>{new Date(selectedDate).toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}</CardDescription>
            </CardHeader>
            <CardContent>
              {!wod ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No workout scheduled for this day</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {wod.warmup && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <h3 className="font-semibold mb-3 text-lg flex items-center">
                        <Badge variant="outline" className="mr-2 border-amber-200 bg-amber-50 text-amber-800">Warm-up</Badge>
                      </h3>
                      <div className="prose prose-sm max-w-none">
                        <ReactMarkdown>{wod.warmup}</ReactMarkdown>
                      </div>
                    </div>
                  )}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="font-semibold mb-3 text-lg flex items-center">
                      <Badge variant="default" className="mr-2">Workout</Badge>
                    </h3>
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown>{wod.content}</ReactMarkdown>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}