Okay, based on the provided documentation excerpts for the Digital Client REST API and the myDATA REST API, here are the necessary details for a developer to create an ERP connection to these AADE services:

**1. Introduction and Purpose of the APIs**

*   The **myDATA** platform (my Digital Accounting and Tax Application) is the new electronic platform from AADE that introduces electronic books into the daily routine of businesses. It is a significant step in the digital transformation of the public sector and businesses. Its primary goal is to serve businesses by offering an innovative digital platform for fulfilling their tax obligations. This will lead to the automation of tax form completion and will eliminate current obligations like submitting Customer-Supplier Statements (MYF). The platform provides easy solutions for businesses with computerised accounting systems, allowing them to transmit the necessary data massively and automatically, as well as for other businesses using a simple data entry form on the AADE website.
*   The **Digital Client API for the Digital Client Register** is designed to provide a comprehensive customer management solution for businesses and professionals using computerised accounting systems. Its goal is to facilitate seamless and uninterrupted data exchange between ERP or other accounting/commercial systems and the Independent Authority for Public Revenue (AADE).

**2. Overall Purpose of the APIs for ERP Systems**

*   For businesses and professionals with computerised accounting systems, AADE provides a **REST API interface on a public cloud infrastructure (Microsoft Azure)**.
*   This allows ERP systems or other accounting/commercial systems to connect with AADE seamlessly and uninterruptedly for the exchange of relevant data.
*   Using these APIs, businesses can **automate Customer Management** by sending, receiving, updating, and cancelling customer records. They can also **ensure Compliance** by connecting with AADE to comply with regulatory requirements for submitting customer data.
*   The myDATA API specifically allows businesses using a connected information system to perform automated connection functions such as:
    *   Sending data for issued documents (invoices).
    *   Sending income classifications related to issued documents.
    *   Sending expense classifications related to submitted documents.
    *   Sending payment methods related to submitted documents.
*   Overall, the Digital Client API is a crucial tool for businesses wanting to modernise their customer management processes, reduce operating costs, and improve business performance.

**3. Technological Requirements**

*   For the implementation of communication between a software system and the interfaces, the following technologies are used:
    *   **HTTPS – Secure HTTP**
    *   **Webservice**
    *   **REST API – REST interface required for the data reporting process**
    *   **XML – eXtensible Markup Language**
*   The interfaces can be used by any software that can implement HTTPS calls and create XML documents compatible with the schema described in the documents.
*   In addition to the relevant data, the software must also be able to send the necessary user identification information automatically within the same HTTPS call.

**4. User Authentication and Registration**

*   Using the API functions requires a **user authentication process**.
*   Authentication is performed by sending a **username (aade-user-id)** and a **subscription key (ocp-apim-subscription-key)** in the headers of every call.
*   The subscription key is a unique string per user and is common for all API functions.
*   The Digital Client API specifically uses the **same credentials (username and subscription key)** as the myDATA REST API services.
*   Through user authentication via headers, the interface will also gain access to the **VAT number declared by the user during registration**, so it is not necessary to enter this information again in every service call.
*   To obtain these credentials for the myDATA REST API, a user must **create an account in the interface registry** through a special registration process offered by the myDATA electronic platform.
*   The registration process for the myDATA REST API services is done via the application available at the URL: **https://www1.aade.gr/saadeapps2/bookkeeper-web**.
    *   The user is initially asked to log in with their taxisnet credentials.
    *   Then, they select "Registration form for the myDATA REST API".
    *   On the next form, they select "New user registration".
    *   After filling in the username on the form, they select "Add registration".
    *   Finally, on the next screen, they click "Add" to complete the new user registration.
*   Upon successful registration, the user is created in the relevant REST API registry, and a special subscription key is provided which the user will use for authentication during service calls. The subscription key is the value in the "API Code" column on the screen showing all created subscription keys.
*   After registration, the user can log in to the interface portal with their account details, where they can view and change the subscription key.
*   **Note:** For the development and testing phase, registration for the myDATA REST API services is done via the application available at the URL: **https://mydata-dev-register.azurewebsites.net**.

**5. General API Call Structure**

*   Each API function is used by sending an **HTTPS call** (either **GET or POST**, depending on the function) to the corresponding **URL link**.
*   The call must include the appropriate **header** containing information necessary for user authentication (username and subscription key). Required headers are `aade-user-id` and `ocp-apim-subscription-key`.
*   The call must also include a **body in XML format**, whose structure depends on the service being called. (Note: The CancelClient method in the DCL API does not require an XML body, and the CancelInvoice method in the myDATA API does not require an XML body).
*   For each call, the user will receive a **response** with information on the outcome of their call, also in **XML format**.
*   In **submission services (POST calls)**, the user can send one or multiple objects by embedding them in the XML body of the call. The response can contain one or more error messages or a successful submission message for each Digital Client record. For myDATA, the response can contain messages for each document/accounting entry or classification.
*   In **retrieval or simple cancellation services**, the user will send the unique numbers of the Digital Client records they are interested in as **parameters** during the call. For myDATA retrieval services like RequestDocs and RequestTransmittedDocs, search criteria are sent as parameters. For RequestMyIncome, RequestMyExpenses, RequestVatInfo, RequestE3Info, search criteria and date ranges are sent as parameters.

**6. Digital Client API (DCL) Operations**

The Digital Client API provides the following functions/methods:

*   **/SendClient:** Process for sending a new Digital Client Register record.
    *   URL: `https://mydatapi.aade.gr/DCL/SendClient`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: In XML format containing the `NewDigitalClientDoc` element, which contains a new Client. The structure is described by the `newDigitalClientType` type. Details of this structure are in section 5.
    *   Development URL: `https://mydataapidev.aade.gr/DCL/SendClient`
*   **/UpdateClient:** Process for sending additional data for an existing Digital Client Register record.
    *   URL: `https://mydatapi.aade.gr/DCL/UpdateClient`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: An XML element of type `updateClientType`. The structure is described in section 4.2.2.
        *   Key fields include `initialDclId` (required, the initial record to update).
        *   Optional fields for update include `updateUniqueId` (filled by service), `clientServiceType`, `entryCompletion`, `nonIssueInvoice`, `amount`, `completionDateTime` (filled by service), `isDiffVehReturnLocation`, `vehicleReturnLocation`, `providedServiceCategory`, `providedServiceCategoryOther`, `invoiceKind`, `offSiteProvidedService`, `exitDateTime` (filled by service), `entityVatNumber` (used for third parties), `cooperatingVatNumber`, `otherBranch`, `reasonNonIssueType`, `comments`, `invoiceCounterparty`, `invoiceCounterpartyCountry`.
    *   Development URL: `https://mydataapidev.aade.gr/DCL/UpdateClient`
*   **/CancelClient:** Process for cancelling a Digital Client Register record without simultaneously submitting a new one.
    *   Method: **POST**
    *   Call: Submit the unique DCL ID of the record to be cancelled as a parameter.
    *   URL: `https://mydatapi.aade.gr/DCL/CancelClient?DCLID={DCLID}[&entityVatNumber={entityVatNumber}]`
    *   Parameters:
        *   `DCLID`: **Required**. Unique number of the Digital Client Register record to be cancelled.
        *   `entityVatNumber`: **Optional**. VAT number of the entity that issued the record to be cancelled, only required if the method is called by a third party (e.g., legal entity representative or accountant).
    *   No XML body is required.
    *   Success: The cancellation action receives its own DCLID, which is returned to the user, and the record is considered cancelled.
    *   Failure: The corresponding error message is returned.
    *   Development URL: `https://mydataapidev.aade.gr/DCL/CancelClient?DCLID={DCLID}[&entityVatNumber={entityVatNumber}]`
*   **/RequestClients:** Process for receiving one or more Digital Client Register records, updates, correlations, or cancellations submitted by the user.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters acting as search criteria.
    *   URL: `https://mydatapi.aade.gr/DCL/RequestClients?DCLID={dclid}[&maxdclid={maxdclid}][&entityVatNumber={entityVatNumber}][&continuationToken={continuationToken}]`
    *   Parameters:
        *   `dclid`: **Required**. Digital Client Register Record Identifier (Number). The call returns all records concerning the user with a unique entry number greater than the parameter's value.
        *   `maxdclid`: **Optional**. Maximum Digital Client Record Identifier Number. If a value is assigned, records with a dclid less than or equal to this value will be returned.
        *   `entityVatNumber`: **Optional**. Entity's VAT number. If this parameter has a value, the search will be performed for this VAT number; otherwise, it will be performed for the VAT number of the user calling the method. Only needed if called by a third party.
        *   `continuationToken`: **Optional**. Parameter for receiving results in segments.
    *   Response: An XML object `RequestedDoc` is received.
        *   It includes lists of clients, client data updates, client cancellations, and client correlations with MARK or FIM, which have a dclid greater than the parameter entered.
        *   It also includes the `continuationToken` element if the data volume exceeds the allowed limit and is segmented. The `continuationToken` is included in each segment of results and is used as a parameter in the next call to retrieve the next segment.
    *   Note: Fields with dates in the returned records are in Europe/Athens Time Zone. If an optional parameter is not entered, the search is performed for all possible values for that field.
    *   Development URL: `https://mydataapidev.aade.gr/DCL/RequestClients?DCLID={dclid}[&maxdclid={maxdclid}][&entityVatNumber={entityVatNumber}][&continuationToken={continuationToken}]`
*   **/ClientCorrelations:** Process for correlating existing Digital Client Register records with a MARK or FIM receipt.
    *   URL: `https://mydatapi.aade.gr/DCL/ClientCorrelations`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: An XML element of type `clientCorrelationType`. The structure is described in section 4.2.5.
        *   Fields: `correlateId` (filled by service), `entityVatNumber` (optional), `mark` (required choice), `FIM` (required choice, details: `FIMNumber`, `FIMAA`, `FIMIssueDate`, `FIMIssueTime` - required when FIM is sent), `correlatedDCLids` (required, list of Digital Client Register IDs to correlate).
        *   Either `mark` or `FIM` must be sent.
    *   Development URL: `https://mydataapidev.aade.gr/DCL/ClientCorrelations`

**7. myDATA API Operations**

The myDATA API provides the following functions/methods:

*   **/SendInvoices:** Process for submitting one or more documents, including corrected/modified ones.
    *   URL: `https://mydatapi.aade.gr/myDATA/SendInvoices` (Base URL `https://mydatapi.aade.gr/myDATA/` implied for most methods)
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: In XML format containing the `InvoicesDoc` element, which contains one or more documents. The structure is described by the `AadeBookInvoiceType` type. Details of this structure are in section 5.
    *   Development URL: `https://mydataapidev.aade.gr/SendInvoices`
*   **/SendIncomeClassification:** Process for submitting one or more income classifications that correspond to already submitted documents.
    *   URL: `https://mydatapi.aade.gr/myDATA/SendIncomeClassification`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: XML containing one or more `InvoiceIncomeClassificationType` elements. Key fields include `invoiceMark` (required, the MARK of the invoice being classified). Optional fields include `classificationMark` (filled by service) and `entityVatNumber` (used for third parties).
    *   Development URL: `https://mydataapidev.aade.gr/SendIncomeClassification`
*   **/SendExpensesClassification:** Process for submitting one or more expenses classifications that correspond to already submitted documents.
    *   URL: `https://mydatapi.aade.gr/myDATA/SendExpensesClassification`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: XML containing `InvoiceExpensesClassificationType` elements. Key field is `mark` (invoice MARK). User can include either `transactionMode` (1 for rejection, 2 for discrepancy) or a list of `invoicesExpensesClassificationDetails`. `invoicesExpensesClassificationDetails` contains `lineNumber` (referencing original invoice line) and a list of `expensesClassificationDetailData`. Optional field `postPerInvoice` (submit classifications at invoice level). Optional `entityVatNumber` for third parties.
    *   Development URL: `https://mydataapidev.aade.gr/SendExpensesClassification`
*   **/SendPaymentsMethod:** Process for submitting one or more payment methods corresponding to already submitted documents.
    *   URL: `https://mydatapi.aade.gr/myDATA/SendPaymentsMethod`
    *   Method: **POST**
    *   Headers: As described in section 4.1.2 (aade-user-id, ocp-apim-subscription-key).
    *   Body: XML containing one or more `PaymentMethodType` elements. Key field is `invoiceMark` (required, invoice MARK). `paymentMethodDetails` (required, list of PaymentMethodDetailType). Optional fields `paymentMethodMark` (filled by service), `entityVatNumber` (for third parties).
    *   Notes: At least one `PaymentMethodDetailType` per document must be of type POS. The sum of `amount` values for all payment methods for a document must equal the `totalGrossValue` of the corresponding document.
    *   Development URL: `https://mydataapidev.aade.gr/SendPaymentsMethod`
*   **/CancelInvoice:** Process for cancelling a document without resubmitting a new one.
    *   Method: **POST**
    *   Call: Submit the MARK of the document to be cancelled as a parameter.
    *   URL: `https://mydatapi.aade.gr/myDATA/CancelInvoice?mark={mark}[&entityVatNumber]`
    *   Parameters:
        *   `mark`: **Required**. Unique registration number of the document to be cancelled.
        *   `entityVatNumber`: **Optional**. Entity's VAT number, only required if the method is called by a third party.
    *   No XML body is required.
    *   Success: The cancellation action receives its own MARK, which is returned to the user, and the document is considered cancelled.
    *   Failure: The corresponding error message is returned.
    *   Notes: A document with MARK {mark} cannot be cancelled if it was not posted by the user's VAT number, or if it was posted by a provider or by myDATA Invoicing. It also cannot be cancelled if it is connected with an active document.
    *   Development URL: `https://mydataapidev.aade.gr/CancelInvoice?[mark][&entityVatNumber]`
*   **/RequestDocs:** Process for receiving documents, classifications, and cancellations of documents submitted by **other users** that concern the requesting user. Also provides information if a document has been rejected by a counterparty via the expenses classification method.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters acting as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestDocs?mark={mark}[&dateFrom][&dateTo][&entityVatNumber][&counterVatNumber][&invType][&maxMark][&nextPartitionKey][&nextRowKey]`
    *   Parameters: `mark` (required, returns items with MARK > parameter). Optional parameters: `dateFrom`, `dateTo` (search range for issue date), `entityVatNumber` (for third parties), `counterVatNumber`, `invType`, `maxMark`, `nextPartitionKey`, `nextRowKey` (for pagination).
    *   Response: An XML object `RequestedDoc` is received. It includes lists of invoices, income classifications, expenses classifications, and payment methods that have a mark greater than the parameter entered. Includes `nextPartitionKey` and `nextRowKey` for pagination.
    *   Development URL: `https://mydataapidev.aade.gr/RequestTransmittedDocs? mark={mark}[&dateFrom][&dateTo][&entityVatNumber][&counterVatNumber][&invType][& maxMark][&nextPartitionKey][&nextRowKey]` (Note: URL in source seems to mistakenly show RequestTransmittedDocs)
*   **/RequestTransmittedDocs:** Process for receiving documents, classifications, and cancellations of documents submitted by **the requesting user**.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters acting as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestTransmittedDocs?mark={mark}[&dateFrom][&dateTo][&entityVatNumber][&counterVatNumber][&invType][&maxMark][&nextPartitionKey][&nextRowKey`
    *   Parameters: `mark` (required, returns items with MARK > parameter). Optional parameters: `dateFrom`, `dateTo` (search range for issue date), `entityVatNumber` (for third parties), `counterVatNumber`, `invType` (document type number), `maxMark`, `nextPartitionKey`, `nextRowKey` (for pagination).
    *   Response: An XML object `RequestedDoc` is received. It includes lists of invoices, income classifications, expenses classifications, and payment methods. Includes `nextPartitionKey` and `nextRowKey` for pagination.
    *   Development URL: `https://mydataapidev.aade.gr/RequestTransmittedDocs? mark={mark}[&dateFrom][&dateTo][&entityVatNumber][&counterVatNumber][&invType][& maxMark][&nextPartitionKey][&nextRowKey]`
*   **/RequestMyIncome:** Process for receiving information about the user's income for a specific period.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestMyIncome?[dateFrom]&[dateTo]&[counterVatNumber]&[entityVatNumber]&[invType]&[nextPartitionKey]&[nextRowKey]`
    *   Parameters: `dateFrom` (required), `dateTo` (required). Date format must be dd/MM/yyyy. Optional parameters: `counterVatNumber`, `entityVatNumber` (for third parties, otherwise defaults to user's VAT), `invType`, `nextPartitionKey`, `nextRowKey` (for pagination).
    *   Response: An XML object `RequestedBookInfo` is received. It includes a list of income info and cancellations and a `continuationToken` for pagination.
    *   Development URL: `https://mydataapidev.aade.gr/RequestMyIncome?[dateFrom]&[dateTo]&[counterVatNumber]&[entityVatNumber]&[invType]&[nextPartitionKey]&[nextRowKey]`
*   **/RequestMyExpenses:** Process for receiving information about the user's expenses for a specific period.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestMyExpenses?[dateFrom]&[dateTo]&[counterVatNumber]&[entityVatNumber]&[invType]&[nextPartitionKey]&[nextRowKey]`
    *   Parameters: Same as RequestMyIncome (dateFrom, dateTo required; counterVatNumber, entityVatNumber, invType, nextPartitionKey, nextRowKey optional; date format dd/MM/yyyy).
    *   Response: An XML object `RequestedBookInfo` is received. It includes a list of expense info and cancellations and a `continuationToken` for pagination.
    *   Development URL: `https://mydataapidev.aade.gr/RequestMyExpenses?[dateFrom]&[dateTo]&[counterVatNumber]&[entityVatNumber]&[invType]&[nextPartitionKey]&[nextRowKey]`
*   **/RequestVatInfo:** Process for receiving detailed VAT information linked to an entity's VAT number for a specific period. Can retrieve info per invoice or per day.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestVatInfo?[entityVatNumber]&[dateFrom]&[dateTo]&[GroupedPerDay]&[nextPartitionKey]&[nextRowKey]`
    *   Parameters: `dateFrom` (required), `dateTo` (required). Date format must be dd/MM/yyyy. Optional parameters: `entityVatNumber` (defaults to user's VAT if not provided), `GroupedPerDay`, `nextPartitionKey`, `nextRowKey` (for pagination).
    *   Response: An XML object `RequestedVatInfo` is received. It includes a list of inflow and outflow information per document (`VatInfo`) and a `continuationToken` for pagination.
    *   Development URL: `https://mydataapidev.aade.gr/RequestVatInfo?[entityVatNumber]&[dateFrom]&[dateTo]&[GroupedPerDay]&[nextPartitionKey]&[nextRowKey]`
*   **/RequestE3Info:** Process for receiving detailed E3 information linked to an entity's VAT number for a specific period. Can retrieve info per invoice or per day.
    *   Method: **GET**
    *   Call: Made via an HTTP GET call with parameters as search criteria.
    *   URL: `https://mydatapi.aade.gr/myDATA/RequestE3Info?[entityVatNumber]&[dateFrom]&[dateTo]&[GroupedPerDay]&[nextPartitionKey]&[nextRowKey]`
    *   Parameters: `dateFrom` (required), `dateTo` (required). Date format must be dd/MM/yyyy. Optional parameters: `entityVatNumber` (defaults to user's VAT if not provided), `GroupedPerDay`, `nextPartitionKey`, `nextRowKey` (for pagination).
    *   Development URL: `https://mydataapidev.aade.gr/RequestE3Info?[entityVatNumber]&[dateFrom]&[dateTo]&[GroupedPerDay]&[nextPartitionKey]&[nextRowKey]`

**8. XML Schema Structures (Key Types)**

*   **Digital Client Register (`newDigitalClientType`):** Used in SendClient body. Contains fields like `idDcl` (filled by service), `clientServiceType` (required, 1=Rental, 2=Parking/Car Wash, 3=Garage), `creationDateTime` (filled by service unless `transmissionFailure`=1, then user sends in UTC), `entityVatNumber` (optional), `branch` (required, Installation No.), `recurringService`, `continuousService`, `fromAgreedPeriodDate`, `toAgreedPeriodDate` (for Continuous Service), `mixedService`, `customerVatNumber` (for Recurring Service), `customerCountry` (for Recurring Service), `transmissionFailure` (1=Loss of connection), `correlatedDclId` (optional), `comments` (optional). Also includes specific use case elements as choices: `rental` (type `RentalType`), `parkingcarwash` (type `ParkingCarWashType`), `garage` (type `GarageType`).
    *   **Rental (`RentalType`):** Contains `vehicleRegistrationNumber`, `foreignVehicleRegistrationNumber`, `vehicleCategory`, `vehicleFactory` (optional), `vehicleMovementPurpose` (optional, 1=Rental, 2=Own Use, 3=Free Service), `isDiffVehPickupLocation`, `vehiclePickupLocation` (optional).
    *   **Parking/Car Wash (`ParkingCarWashType`):** Contains `vehicleRegistrationNumber`, `foreignVehicleRegistrationNumber`, `vehicleCategory`, `vehicleFactory` (optional).
*   **Update Client (`updateClientType`):** Used in UpdateClient body. Contains fields as described in section 6 above.
*   **Client Correlation (`clientCorrelationType`):** Used in ClientCorrelations body. Contains fields as described in section 6 above.
*   **myDATA Invoice (`AadeBookInvoiceType`):** Used in SendInvoices body. Main structure contains `uid` (filled by service), `transmissionFailure` (values 1, 4 for ERP), `issuer` (type `PartyType`), `counterpart` (type `PartyType`), `paymentMethods` (list of `PaymentMethodDetailType`), `invoiceHeader` (required, type `InvoiceHeaderType`), `invoiceDetails` (required, list of invoice rows), `taxesTotals` (list of `TaxTotalsType`), `invoiceSummary` (required, type `InvoiceSummaryType`), `qrCodeUrl` (filled by service), `otherTransportDetails` (list of type `TransportDetailType`).
*   **Party (`PartyType`):** Used for Issuer and Counterpart. Contains `vatNumber` (required), `country` (required, 2-char ISO 3166 code), `branch` (required, Installation No., 0 for HQ/no branch), `name` (optional, not accepted for GR entities), `address` (optional, type `AddressType`, not accepted for GR issuers), `documentIdNo` (optional, for Tax Free), `supplyAccountNo` (optional, for fuel invoices), `countryDocumentId` (optional).
*   **Address (`AddressType`):** Used within `PartyType`, Loading Address, Delivery Address. Contains `street`, `number` (optional), `postalCode` (required), `city` (required).
*   **Payment Method Detail (`PaymentMethodDetailType`):** Used within `PaymentMethodType` and Invoice (`PaymentMethods`). Contains `type` (required, int, values 1-5 initially, expanded in appendix), `amount` (required, decimal, min 0, 2 decimals), `paymentMethodInfo` (optional), `tipAmount` (optional, decimal, min 0, 2 decimals), `transactionId` (optional, for type=7), `tid` (optional, POS tid, for type=7), `ProvidersSignature` (optional, type `ProviderSignatureType`, for type=7 by provider channel), `ECRToken` (optional, type `ECRTokenType`, for type=7 by ERP).
*   **Provider Signature (`ProviderSignatureType`):** Used within `PaymentMethodDetailType`. Contains `SigningAuthor` (required, YPAIES approval number), `Signature` (required, details in decision A. 1155/09-10-2023).
*   **ECR Token (`ECRTokenType`):** Used within `PaymentMethodDetailType`. Contains `SigningAuthor` (required, ECR id), `Signature` (required, details in decision A. 1155/09-10-2023).
*   **Invoice Header (`InvoiceHeaderType`):** Required within `AadeBookInvoiceType`. Contains `series`, `aa` (Invoice Number). `issueDate` (required). `invoiceType` (required, int, values in appendix). `currency`, `exchangeRate` (optional). `correlatedInvoices` (list of long, optional). `selfPricing` (optional, boolean, for self-billing invoices). `dispatchDate`, `dispatchTime`, `vehicleNumber`, `movePurpose` (int, values in appendix), `fuelInvoice` (boolean, indication for fuel invoices, allowed for ERP). `specialInvoiceCategory` (int, values in appendix). `invoiceVariationType` (int, values 1-4, not allowed for providers). `otherCorrelatedEntities` (optional, list of `EntityType`). `otherDeliveryNoteHeader` (optional, type `OtherDeliveryNoteHeaderType`). `isDeliveryNote` (optional, boolean, invoice is also delivery note). `otherMovePurposeTitle` (optional, for `movePurpose`=19). `thirdPartyCollection` (optional, boolean, for 8.4/8.5 documents). `multipleConnectedMarks` (optional, list of long). `tableAA` (required for type 8.6).
*   **Other Correlated Entities (`EntityType`):** Used within `InvoiceHeaderType`. Contains `type` (required, int, values in appendix), `entityData` (required, type `PartyType`).
*   **Other Delivery Note Header (`OtherDeliveryNoteHeaderType`):** Used within `InvoiceHeaderType`. Contains `loadingAddress` (optional, type `AddressType`), `deliveryAddress` (optional, type `AddressType`), `startShippingBranch`, `completeShippingBranch` (optional).
*   **Tax Totals (`TaxTotalsType`):** Used within `AadeBookInvoiceType`. Describes taxes for the entire document. Contains `taxType` (required, byte, 1=Withheld Tax, 2=Fees, 3=Other Taxes, 4=Stamp Duty, 5=Deductions), `taxCategory` (optional, byte, values in appendix for the specific `taxType`), `underlyingValue` (optional, decimal, min 0, 2 decimals, the value to which the tax applies), `taxAmount` (required, decimal, min 0, 2 decimals), `id` (optional, byte, line number).

**9. Response Structures**

*   **Submission Responses (SendClient, UpdateClient, CancelClient, ClientCorrelations, SendInvoices, SendIncomeClassification, SendExpensesClassification, SendPaymentsMethod):**
    *   Determined by the value of the `statusCode` field (Success or corresponding error code).
    *   Success (`statusCode = Success`): The response includes corresponding values for fields depending on the submitted entity: `newClientDclID`, `updatedClientDclID`, `cancellationID` (for DCL), `correlateId` (for DCL Correlations), `invoiceUid` (for myDATA invoice), `invoiceMark` (for myDATA invoice or classification), `classificationMark` (for myDATA classification), `cancellationMark` (for myDATA cancellation). The `qrUrl` is returned only for myDATA invoice submissions of types 1.1-11.5. `authenticationCode` is returned for myDATA submissions via provider.
    *   Failure (`statusCode` is an error code): The response includes a list of `ErrorType` elements for each entity whose submission failed.
*   **Retrieval Responses (RequestClients, RequestDocs, RequestTransmittedDocs):**
    *   An XML object `RequestedDoc` is received.
    *   It includes lists of the relevant items (clients, updates, correlations, cancellations for DCL; invoices, income classifications, expenses classifications, payment methods for myDATA).
    *   It also includes elements for **pagination** if the data volume exceeds the limit: `continuationToken` for DCL or `nextPartitionKey` and `nextRowKey` for myDATA RequestDocs/RequestTransmittedDocs. These fields are filled by the service and used in subsequent calls to fetch the next segment of results.
*   **Book Info Responses (RequestMyIncome, RequestMyExpenses):**
    *   An XML object `RequestedBookInfo` is received.
    *   It includes a list of income/expense details and cancellations and a `continuationToken` element for pagination.
*   **VAT Info Response (RequestVatInfo):**
    *   An XML object `RequestedVatInfo` is received.
    *   It includes a list of VAT inflow/outflow details per document and a `continuationToken` for pagination.
*   **E3 Info Response (RequestE3Info):**
    *   An XML object is received (structure similar to VAT Info).
    *   It includes a list of E3 details and a `continuationToken` for pagination.

**10. Error Handling**

*   Errors are elements of type `ErrorType`.
*   Each error element for an entity consists of a message describing the error (`message`) and an error code.
*   There are two categories of errors:
    *   **Technical Errors:** Result in an **HTTP Response 4xx** (e.g., 400 BAD_REQUEST). Examples provided are `HTTP 400 BAD_REQUEST` with specific messages like "Please pass mark in the request parameters or body" or "General Exception Error".
    *   **Business Errors:** Result in an **HTTP Response 200 OK**. These occur when business checks fail. The response `statusCode` indicates the error type, and an `errors` list (of type `ErrorType`) is included.
*   Examples of Business Error codes provided:
    *   Application Errors (Codes 101-105): e.g., XML Syntax Error (101), Validation Errors like VAT number not belonging to active corporation (102), missing MARK/dclid parameter (103, 43), requested invoice not found (104, 124), user not authorized for VAT number (105, 124).
    *   Digital Client Validation Errors (Codes 201-217): e.g., user not authorized (201, 43), Invalid Greek VAT number (202, 44), mandatory field missing (203, 44), conflicting elements sent (204, 44), forbidden field (205, 44), client not found (206, 44), client already cancelled (207, 44), element differs from another (208, 45), client completed (209, 45), correlations already sent (210, 45), max correlatedDCLids exceeded (211, 45), idDcl filled by service (212, 45), date errors (213, 217, 46), invalid value for service type (214, 46), field length exceeded (215, 46), recurringService forbidden for Rental/Own Use (216, 46).
    *   Invoice Validation Errors (Codes 204-280s): Many specific validation rules related to invoice structure, values, types, required/forbidden fields based on invoice type, sums not matching, country restrictions, cancellation restrictions, etc..
    *   Classification Validation Errors (Codes 300s): Validation rules specific to income and expense classifications, checking consistency with the original invoice, mandatory/forbidden fields, technical errors in classification lines, etc..
    *   Payment Validation Errors (Codes 400s): Validation rules specific to payment methods, checking consistency with the original invoice's transmission channel (provider vs ERP), requiring/forbidding provider signature or ECR token, total amount mismatch, inability to resend for myDATA invoicing created invoices, etc..

This comprehensive overview should provide a developer with the essential information from the sources to begin creating an ERP connection to the AADE Digital Client and myDATA REST APIs.