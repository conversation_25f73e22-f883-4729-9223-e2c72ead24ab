// app/admin/auth-debug/client.tsx
'use client';

import React, { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertCircle, CheckCircle, Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import type { Database } from '@/types/supabase';

// Type definitions
type DatabaseError = {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
};

type EmailMismatch = {
  auth_email: string;
  pelates_email: string;
  name: string;
  last_name: string;
  auth_user_id: string;
  pelatis_id: string;
};

type OrphanedAuthUser = {
  email: string;
  auth_email: string;
  auth_user_id: string;
  created_at: string;
  last_sign_in_at: string | null;
  pelates_email: string | null;
  pelatis_id_by_email: string | null;
};

type PelatiMissingAuth = {
  id: string;
  name: string;
  last_name: string;
  email: string;
  created_at: string | null;
};

type UserWithoutRole = {
  email: string;
  auth_user_id: string;
  created_at: string;
  last_sign_in_at: string | null;
  pelates_email: string | null;
  pelatis_id: string | null;
};

type AuthDebugData = {
  orphanedAuthUsers: OrphanedAuthUser[];
  emailMismatches: EmailMismatch[];
  pelatesMissingAuth: PelatiMissingAuth[];
  usersWithoutRoles: UserWithoutRole[];
  errors: {
    orphanedError?: string;
    mismatchError?: string;
    missingAuthError?: string;
    rolesError?: string;
  };
};

type FilterableItem = EmailMismatch | OrphanedAuthUser | PelatiMissingAuth | UserWithoutRole;

export default function AuthDebugClient({
  orphanedAuthUsers,
  emailMismatches,
  pelatesMissingAuth,
  usersWithoutRoles,
  errors
}: AuthDebugData) {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  const supabase = createClientComponentClient<Database>();
  
  const filterBySearchTerm = (item: FilterableItem): boolean => {
    const searchLower = searchTerm.toLowerCase();
    return (
      ('email' in item && item.email?.toLowerCase().includes(searchLower)) ||
      ('auth_user_id' in item && item.auth_user_id?.toLowerCase().includes(searchLower)) ||
      ('name' in item && item.name?.toLowerCase().includes(searchLower))
    );
  };
  
  const fixEmailMismatch = async (authUserId: string, pelatesId: string): Promise<void> => {
    setIsLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const { error } = await supabase
        .from('pelates')
        .update({ auth_user_id: authUserId })
        .eq('id', pelatesId);
        
      if (error) throw error;
      
      setSuccessMessage(`Successfully linked user ID ${authUserId} to pelates ID ${pelatesId}`);
      setTimeout(() => window.location.reload(), 1500);
    } catch (error: unknown) {
      const err = error as DatabaseError;
      setErrorMessage(`Error: ${err.message || 'Failed to fix link'}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const addMissingRole = async (authUserId: string, roleId: number = 1): Promise<void> => {
    setIsLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const { error } = await supabase
        .from('user_roles')
        .insert({ auth_user_id: authUserId, role_id: roleId });
        
      if (error) throw error;
      
      setSuccessMessage(`Successfully added role ${roleId} to user ID ${authUserId}`);
      setTimeout(() => window.location.reload(), 1500);
    } catch (error: unknown) {
      const err = error as DatabaseError;
      setErrorMessage(`Error: ${err.message || 'Failed to add role'}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const createPelatesForAuthUser = async (authUserId: string, email: string): Promise<void> => {
    setIsLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const namePart = email.split('@')[0];
      
      const { error } = await supabase
        .from('pelates')
        .insert({ 
          auth_user_id: authUserId, 
          email: email,
          name: namePart,
          last_name: '-'
        });
        
      if (error) throw error;
      
      await addMissingRole(authUserId);
      
      setSuccessMessage(`Successfully created pelates record for ${email}`);
      setTimeout(() => window.location.reload(), 1500);
    } catch (error: unknown) {
      const err = error as DatabaseError;
      setErrorMessage(`Error: ${err.message || 'Failed to create pelates record'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const linkUserToPelates = async (authUserId: string, pelatesId: string | null): Promise<void> => {
    if (!pelatesId) {
      setErrorMessage('No pelates ID provided for linking');
      return;
    }

    setIsLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const { error } = await supabase
        .from('pelates')
        .update({ auth_user_id: authUserId })
        .eq('id', pelatesId);
        
      if (error) throw error;
      
      setSuccessMessage(`Successfully linked auth user ${authUserId} to pelates ID ${pelatesId}`);
      setTimeout(() => window.location.reload(), 1500);
    } catch (error: unknown) {
      const err = error as DatabaseError;
      setErrorMessage(`Error: ${err.message || 'Failed to link user to pelates'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {(errors.orphanedError || errors.mismatchError || errors.missingAuthError || errors.rolesError) && (
        <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded-md">
          <h3 className="font-bold">Errors Loading Data:</h3>
          <ul className="list-disc pl-5">
            {errors.orphanedError && <li>{errors.orphanedError}</li>}
            {errors.mismatchError && <li>{errors.mismatchError}</li>}
            {errors.missingAuthError && <li>{errors.missingAuthError}</li>}
            {errors.rolesError && <li>{errors.rolesError}</li>}
          </ul>
          <p className="mt-2 text-sm">
            Note: You may need to create the necessary views in your database before using this dashboard.
          </p>
        </div>
      )}
      
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-800 p-4 rounded-md flex items-center">
          <CheckCircle className="mr-2 h-5 w-5" /> {successMessage}
        </div>
      )}
      
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded-md flex items-center">
          <AlertCircle className="mr-2 h-5 w-5" /> {errorMessage}
        </div>
      )}
      
      <div className="relative">
        <Input
          type="search"
          placeholder="Search by email or name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
        <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
      </div>
      
      <Tabs defaultValue="email-mismatches">
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="email-mismatches">
            Email Mismatches {emailMismatches.length > 0 && `(${emailMismatches.length})`}
          </TabsTrigger>
          <TabsTrigger value="orphaned-users">
            Orphaned Auth Users {orphanedAuthUsers.length > 0 && `(${orphanedAuthUsers.length})`}
          </TabsTrigger>
          <TabsTrigger value="missing-auth">
            Pelates Missing Auth {pelatesMissingAuth.length > 0 && `(${pelatesMissingAuth.length})`}
          </TabsTrigger>
          <TabsTrigger value="missing-roles">
            Missing Roles {usersWithoutRoles.length > 0 && `(${usersWithoutRoles.length})`}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="email-mismatches" className="space-y-4">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pelates Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth User ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pelatis ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {emailMismatches.filter(filterBySearchTerm).map((mismatch: EmailMismatch) => (
                  <tr key={`${mismatch.auth_user_id}-${mismatch.pelatis_id}`}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{mismatch.auth_email}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{mismatch.pelates_email}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{mismatch.name} {mismatch.last_name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-xs">{mismatch.auth_user_id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-xs">{mismatch.pelatis_id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <Button 
                        size="sm" 
                        onClick={() => fixEmailMismatch(mismatch.auth_user_id, mismatch.pelatis_id)}
                        disabled={isLoading}
                      >
                        Link
                      </Button>
                    </td>
                  </tr>
                ))}
                {emailMismatches.filter(filterBySearchTerm).length === 0 && (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchTerm ? 'No results match your search.' : 'No email mismatches found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
        
        <TabsContent value="orphaned-users" className="space-y-4">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth User ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Sign In</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email in Pelates</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth ID in Pelates</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orphanedAuthUsers.filter(filterBySearchTerm).map((user: OrphanedAuthUser) => {
                  // Check if email exists in pelates
                  const emailInPelates = user.pelates_email ? true : false;

                  
                  return (
                    <tr key={user.auth_user_id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{user.email || user.auth_email}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-xs">{user.auth_user_id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{new Date(user.created_at).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {emailInPelates ? 
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Yes</Badge> : 
                          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">No</Badge>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">No</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Button 
                          size="sm" 
                          onClick={() => createPelatesForAuthUser(user.auth_user_id, user.email || user.auth_email)}
                          disabled={isLoading}
                        >
                          Create Pelates Record
                        </Button>
                        {emailInPelates && (
                          <Button 
                            size="sm"
                            variant="outline"
                            className="ml-2"
                            onClick={() => linkUserToPelates(user.auth_user_id, user.pelatis_id_by_email)}
                            disabled={isLoading || !user.pelatis_id_by_email}
                          >
                            Link to Existing
                          </Button>
                        )}
                      </td>
                    </tr>
                  );
                })}
                {orphanedAuthUsers.filter(filterBySearchTerm).length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchTerm ? 'No results match your search.' : 'No orphaned auth users found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
        
        <TabsContent value="missing-auth" className="space-y-4">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pelatis ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pelatesMissingAuth.filter(filterBySearchTerm).map((pelati: PelatiMissingAuth) => (
                  <tr key={pelati.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{pelati.name} {pelati.last_name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{pelati.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-xs">{pelati.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{pelati.created_at ? new Date(pelati.created_at).toLocaleString() : 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <Button 
                        size="sm" 
                        disabled={!pelati.email || isLoading}
                        onClick={() => window.open(`/admin/users/invite?email=${encodeURIComponent(pelati.email || '')}&pelatis_id=${pelati.id}`, '_blank')}
                      >
                        Invite User
                      </Button>
                    </td>
                  </tr>
                ))}
                {pelatesMissingAuth.filter(filterBySearchTerm).length === 0 && (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchTerm ? 'No results match your search.' : 'No pelates missing auth records found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
        
        <TabsContent value="missing-roles" className="space-y-4">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth User ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Sign In</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email in Pelates</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth ID in Pelates</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {usersWithoutRoles.filter(filterBySearchTerm).map((user: UserWithoutRole) => {
                  const emailInPelates = user.pelates_email !== null;
                  
                  return (
                    <tr key={user.auth_user_id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{user.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-xs">{user.auth_user_id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{new Date(user.created_at).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {emailInPelates ? 
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Yes</Badge> : 
                          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">No</Badge>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">No</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm flex space-x-2">
                        <Button 
                          size="sm" 
                          onClick={() => addMissingRole(user.auth_user_id, 1)}
                          disabled={isLoading}
                        >
                          Add Client Role
                        </Button>
                        <Button 
                          size="sm" 
                          onClick={() => addMissingRole(user.auth_user_id, 2)}
                          disabled={isLoading}
                          variant="outline"
                        >
                          Add Admin Role
                        </Button>
                      </td>
                    </tr>
                  );
                })}
                {usersWithoutRoles.filter(filterBySearchTerm).length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchTerm ? 'No results match your search.' : 'No users missing roles found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
