// app/admin/auth-debug/page.tsx
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import Link from "next/link";
import AuthDebugClient from "./client";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function AuthDebugPage() {
  const supabase = createServerComponentClient({ cookies });
  
  // First check if user is an admin
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Authentication Required</h1>
        <p>Please log in to access this page.</p>
        <Link href="/auth" className="text-blue-500 hover:underline mt-4 inline-block">
          Go to Login
        </Link>
      </div>
    );
  }

  // Check if user is admin
  const { data: roles } = await supabase.rpc('getUserRoles', {
    p_user_id: session.user.id,
  });
  
  const isAdmin = roles?.includes('admin');
  
  if (!isAdmin) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Access Denied</h1>
        <p>You don&apos;t have permission to access this page.</p>
        <Link href="/" className="text-blue-500 hover:underline mt-4 inline-block">
          Go to Home
        </Link>
      </div>
    );
  }

  // 1. Get orphaned auth users (auth users without pelates records)
  const { data: orphanedAuthUsers, error: orphanedError } = await supabase.from('auth_debug_orphaned_users_view')
    .select('*');

  // 2. Get email mismatches (auth users with different email from pelates)
  const { data: emailMismatches, error: mismatchError } = await supabase.from('auth_debug_email_mismatches_view')
    .select('*');
  
  // 3. Get pelates missing auth records
  const { data: pelatesMissingAuth, error: missingAuthError } = await supabase.from('auth_debug_pelates_missing_auth_view')
    .select('*');
  
  // 4. Get users missing roles
  const { data: usersWithoutRoles, error: rolesError } = await supabase.from('auth_debug_users_without_roles_view')
    .select('*');

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Auth Debug Dashboard</h1>
      
      <AuthDebugClient 
        orphanedAuthUsers={orphanedAuthUsers || []}
        emailMismatches={emailMismatches || []}
        pelatesMissingAuth={pelatesMissingAuth || []}
        usersWithoutRoles={usersWithoutRoles || []}
        errors={{
          orphanedError: orphanedError?.message,
          mismatchError: mismatchError?.message,
          missingAuthError: missingAuthError?.message,
          rolesError: rolesError?.message
        }}
      />
    </div>
  );
}
