"use client";
import React from 'react';
import { useReconciliationRecords } from '@/hooks/useReconciliationRecords';
import ReconciliationForm from '@/components/finances/reconciliation/ReconciliationForm';

const ReconciliationPage = () => {
  const { records, loading, error } = useReconciliationRecords();

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Reconciliation</h1>
      <ReconciliationForm />

      <h2 className="text-xl font-semibold mt-8 mb-4">Reconciliation History</h2>

      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-pulse text-lg">Loading reconciliation records...</div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
          <p className="font-medium">Error loading records</p>
          <p className="text-sm">{error.message}</p>
        </div>
      )}

      {records && records.length > 0 ? (
        <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">System Balance</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual Balance</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Difference</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Adjusted</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {records.map(rec => (
                <tr key={rec.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    {rec.reconciliation_date ? new Date(rec.reconciliation_date).toLocaleDateString() : ''}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">{rec.account_name}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    € {Number(rec.system_balance).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    € {Number(rec.actual_balance).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                  </td>
                  <td className={`px-4 py-3 whitespace-nowrap text-sm ${rec.difference === 0 ? '' : 'text-red-600'}`}>
                    {Number(rec.difference).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    {rec.adjusted ? '✅' : ''}
                  </td>
                  <td className="px-4 py-3 text-sm">{rec.notes || ''}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-md">
          <p className="font-medium">No reconciliation records found</p>
          <p className="text-sm">Use the form above to create your first reconciliation record.</p>
        </div>
      )}
    </div>
  );
};

export default ReconciliationPage;