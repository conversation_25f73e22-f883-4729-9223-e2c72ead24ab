  'use client';

  import { useState } from 'react';
  import { useAccountBalances } from '@/hooks/useAccountBalances';
  import { Button } from '@/components/ui/button';
  import { Input } from '@/components/ui/input';
  import { Select, SelectTrigger, SelectContent, SelectItem } from '@/components/ui/select';
  import { Textarea } from '@/components/ui/textarea';
  import { Switch } from '@/components/ui/switch';
  import { LoadingSpinner } from '@/components/LoadingSpinner';
  import type { Database } from "@/types/supabase";

  type PaymentMethod = Database['public']['Enums']['payment_method'];

  export default function ReconciliationForm() {
    // const { reconcileAccount, loading, error } = useFinancialOperations();
    // For now, mock loading, error, and reconcileAccount
    const [loading] = useState(false);
    const [error] = useState<Error | null>(null);
    const reconcileAccount = async () => { return true; };
    const { balances, loading: balancesLoading } = useAccountBalances();
    const [accountName, setAccountName] = useState<PaymentMethod | ''>('');
    const [actualBalance, setActualBalance] = useState<string>('');
    const [adjusted, setAdjusted] = useState<boolean>(true);
    const [notes, setNotes] = useState<string>('');
    const [success, setSuccess] = useState<boolean>(false);

    // Find the selected account's current balance in the system
    const selectedAccount = balances.find(acc => acc.account_name === accountName);
    const systemBalance = selectedAccount?.current_balance || 0;
    const difference = actualBalance ? parseFloat(actualBalance) - systemBalance : 0;

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setSuccess(false);

      if (!accountName || !actualBalance || isNaN(parseFloat(actualBalance))) {
        return; // Add validation error handling
      }

      const result = await reconcileAccount();

      if (result) {
        setSuccess(true);
        // Reset form
        setActualBalance('');
        setNotes('');
      }
    };

    if (balancesLoading) {
      return <LoadingSpinner />;
    }

    return (
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Reconcile Account Balance</h1>

        {success && (
          <div className="p-4 bg-green-50 text-green-600 rounded-md mb-4">
            Account reconciled successfully!
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-600 rounded-md mb-4">
            Error: {error.message}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="accountName" className="block text-sm font-medium mb-1">
              Account
            </label>
            <Select value={accountName} onValueChange={(value) => setAccountName(value as PaymentMethod)}>
              <SelectTrigger className="w-full">{accountName || 'Select an account'}</SelectTrigger>
              <SelectContent>
                <SelectItem value="">Select an account</SelectItem>
                {balances.map((account) => (
                  <SelectItem key={account.id} value={account.account_name}>
                    {account.account_name} (System Balance: {account.current_balance.toFixed(2)} €)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {accountName && (
            <div className="p-4 bg-gray-50 rounded-md">
              <p className="font-medium">System Balance: {systemBalance.toFixed(2)} €</p>
              <p className="text-sm text-gray-500">
                Last reconciled: {selectedAccount?.last_reconciled
                  ? new Date(selectedAccount.last_reconciled).toLocaleDateString()
                  : 'Never'}
              </p>
            </div>
          )}

          <div>
            <label htmlFor="actualBalance" className="block text-sm font-medium mb-1">
              Actual Balance (€)
            </label>
            <Input
              id="actualBalance"
              type="number"
              step="0.01"
              value={actualBalance}
              onChange={(e) => setActualBalance(e.target.value)}
              placeholder="0.00"
              required
            />
          </div>

          {accountName && actualBalance && (
            <div className={`p-4 rounded-md ${
              difference === 0
                ? 'bg-green-50 text-green-700'
                : difference > 0
                  ? 'bg-blue-50 text-blue-700'
                  : 'bg-red-50 text-red-700'
            }`}>
              <p className="font-medium">
                Difference: {difference.toFixed(2)} €
                {difference === 0 && ' (Balanced)'}
                {difference > 0 && ' (More money than expected)'}
                {difference < 0 && ' (Less money than expected)'}
              </p>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              id="adjusted"
              checked={adjusted}
              onCheckedChange={setAdjusted}
            />
            <label htmlFor="adjusted" className="text-sm font-medium">
              Adjust system balance to match actual balance
            </label>
          </div>

          <div>
            <label htmlFor="notes" className="block text-sm font-medium mb-1">
              Notes
            </label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any notes about this reconciliation..."
            />
          </div>

          <Button type="submit" disabled={loading}>
            {loading ? <LoadingSpinner /> : 'Complete Reconciliation'}
          </Button>
        </form>
      </div>
    );
  }