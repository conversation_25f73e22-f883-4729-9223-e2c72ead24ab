"use client";

import React from "react";
import WinbankBalanceForm from "@/components/finances/winbank/WinbankBalanceForm";
import WinbankBalanceList from "@/components/finances/winbank/WinbankBalanceList";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

const WinbankBalancePage = () => {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Winbank Balance Records</h1>
        <Button asChild variant="outline">
          <Link href="/admin/finances">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Finances
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <WinbankBalanceForm />
        </div>
        <div>
          <WinbankBalanceList />
        </div>
      </div>
    </div>
  );
};

export default WinbankBalancePage;
