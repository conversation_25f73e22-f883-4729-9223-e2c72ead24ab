// app/admin/finances/FinancialDashboardClient.tsx
'use client';

import { useState, useEffect } from 'react';
import { useAccountBalances, AccountBalance } from '@/hooks/useAccountBalances';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import type { Database } from '@/types/supabase';
type AdminExpense = Database['public']['Tables']['admin_expenses']['Row'];
type AccountTransfer = Database['public']['Tables']['account_transfers']['Row'];

interface FinancialDashboardClientProps {
  initialBalances: AccountBalance[];
  recentExpenses: AdminExpense[];
  recentTransfers: AccountTransfer[];
}

export default function FinancialDashboardClient({
  initialBalances,
  recentExpenses,
  recentTransfers
}: FinancialDashboardClientProps) {
  // Use the hook but initialize with server data
  const { balances: liveBalances, error } = useAccountBalances();
  const [balances, setBalances] = useState<AccountBalance[]>(initialBalances);
  const [totalBalance, setTotalBalance] = useState<number>(0);

  // Update with live data when available
  useEffect(() => {
    if (liveBalances && liveBalances.length > 0) {
      setBalances(liveBalances);
    }
  }, [liveBalances]);

  useEffect(() => {
    if (balances && balances.length > 0) {
      const total = balances.reduce((sum, account) => sum + account.current_balance, 0);
      setTotalBalance(total);
    }
  }, [balances]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Financial Dashboard</h1>
        <div className="flex gap-3">
          <Button asChild>
            <Link href="/admin/finances/expenses/add">Add Expense</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/finances/transfers">Transfer Funds</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/finances/analysis">Cashflow Analysis</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/finances/winbank">Winbank Records</Link>
          </Button>
        </div>
      </div>

      {/* Total Balance Summary */}
      <Card className="p-6 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Total Balance</h2>
        <p className="text-3xl font-bold">{totalBalance.toFixed(2)} €</p>
      </Card>

      {/* Account Balances */}
      <Card className="p-6 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Account Balances</h2>
        <div className="space-y-4">
          {balances.map((account) => (
            <div
              key={account.id}
              className="flex justify-between items-center p-3 border-b last:border-0"
            >
              <div>
                <p className="font-medium">{account.account_name}</p>
                <p className="text-sm text-gray-500">
                  Last updated: {account.last_updated
                    ? new Date(account.last_updated).toLocaleDateString()
                    : 'Never'}
                </p>
              </div>
              <p className="text-xl font-semibold">{account.current_balance.toFixed(2)} €</p>
            </div>
          ))}
        </div>
      </Card>

      {/* Recent Activity */}
      <Card className="p-6 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <Tabs defaultValue="expenses">
          <TabsList className="mb-4">
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="transfers">Transfers</TabsTrigger>
          </TabsList>

          <TabsContent value="expenses">
            {recentExpenses.length > 0 ? (
              <div className="space-y-3">
                {recentExpenses.map((expense) => (
                  <div
                    key={expense.id}
                    className="flex justify-between items-center p-3 border-b last:border-0"
                  >
                    <div>
                      <p className="font-medium">{expense.description || expense.category}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(expense.date).toLocaleDateString()}
                      </p>
                    </div>
                    <p className="text-red-600 font-semibold">-{expense.amount.toFixed(2)} €</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No recent expenses</p>
            )}
          </TabsContent>

          <TabsContent value="transfers">
            {recentTransfers.length > 0 ? (
              <div className="space-y-3">
                {recentTransfers.map((transfer) => (
                  <div
                    key={transfer.id}
                    className="flex justify-between items-center p-3 border-b last:border-0"
                  >
                    <div>
                      <p className="font-medium">
                        Transfer: {transfer.from_account} → {transfer.to_account}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(transfer.initiated_date || '').toLocaleDateString()}
                        {transfer.notes && ` - ${transfer.notes}`}
                      </p>
                    </div>
                    <p className="text-amber-600 font-semibold">{transfer.amount.toFixed(2)} €</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No recent transfers</p>
            )}
          </TabsContent>
        </Tabs>

        <div className="mt-4 flex justify-end">
          <Button asChild variant="ghost" size="sm">
            <Link href="/admin/finances/transactions">View All Transactions</Link>
          </Button>
        </div>
      </Card>

      {error && (
        <div className="p-4 bg-red-50 text-red-600 rounded-md">
          <p>Error loading financial data: {error.message}</p>
        </div>
      )}
    </div>
  );
}