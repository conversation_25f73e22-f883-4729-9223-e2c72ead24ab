"use client";
import React from 'react';
import { useAccountBalances } from '@/hooks/useAccountBalances';
import CashflowSummaryCards from '@/components/finances/cashflow/CashflowSummaryCards';
import { useCashflowChartData } from '@/hooks/useCashflowChartData';
import CashflowChart from '@/components/finances/cashflow/CashflowChart';
import { useCashflowAlerts } from '@/hooks/useCashflowAlerts';
import AlertsPanel from '@/components/finances/cashflow/AlertsPanel';
import QuickActionsPanel from '@/components/finances/cashflow/QuickActionsPanel';

const CashflowDashboardPage = () => {
  const { balances, loading, error } = useAccountBalances();
  const { chartData, loading: chartLoading, error: chartError } = useCashflowChartData('30days');

  // Get both the account arrays and the counts
  const {
    unreconciledAccounts,
    largeTransfers,
    loading: alertsLoading,
    error: alertsError
  } = useCashflowAlerts();

  return (
    <div className="max-w-7xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Cashflow Dashboard</h1>
      <CashflowSummaryCards balances={balances} loading={loading} error={error ? error.message : null} />
      <div className="mb-8">
        <CashflowChart data={chartData} loading={chartLoading} error={chartError ? chartError.message : null} />
      </div>
      <div className="mb-8">
        <AlertsPanel
          unreconciledAccounts={unreconciledAccounts}
          largeTransfers={largeTransfers}
          loading={alertsLoading}
          error={alertsError ? alertsError.message : null}
        />
      </div>
      <div>
        <QuickActionsPanel />
      </div>
    </div>
  );
};

export default CashflowDashboardPage;