"use client";
import React from 'react';
import { useAccountTransfers } from '@/hooks/useAccountTransfers';
import TransferList from '@/components/finances/transfers/TransferList';
import TransferForm from '@/components/finances/transfers/TransferForm';

const TransfersPage = () => {
  const { transfers, loading, error } = useAccountTransfers();

  return (
    <div style={{ maxWidth: 1000, margin: '0 auto', padding: 32 }}>
      <h1 style={{ fontSize: 28, fontWeight: 700, marginBottom: 24 }}>Transfers</h1>
      <TransferForm />
      <TransferList transfers={transfers} loading={loading} error={error ? error.message : null} />
    </div>
  );
};

export default TransfersPage; 