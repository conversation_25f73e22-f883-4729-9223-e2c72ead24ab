// app/admin/finances/transactions/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from "@/types/supabase";
import TransactionHistory from './TransactionHistory';

export default async function TransactionHistoryPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Fetch initial expenses
  const { data: expenses } = await supabase
    .from('admin_expenses')
    .select('*')
    .order('date', { ascending: false })
    .limit(50);

  // Fetch initial transfers
  const { data: transfers } = await supabase
    .from('account_transfers')
    .select('*')
    .order('initiated_date', { ascending: false })
    .limit(50);

  return (
    <div className="container mx-auto py-6">
      <TransactionHistory
        initialExpenses={expenses || []}
        initialTransfers={transfers || []}
      />
    </div>
  );
}