'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectContent, SelectItem } from '@/components/ui/select';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Card } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import type { Database } from '@/types/supabase';

type AdminExpense = Database['public']['Tables']['admin_expenses']['Row'];
type AccountTransfer = Database['public']['Tables']['account_transfers']['Row'];

interface TransactionHistoryProps {
  initialExpenses?: AdminExpense[];
  initialTransfers?: AccountTransfer[];
}

export default function TransactionHistory({
  initialExpenses = [],
  initialTransfers = []
}: TransactionHistoryProps) {
  const [expenses, setExpenses] = useState<AdminExpense[]>(initialExpenses);
  const [transfers, setTransfers] = useState<AccountTransfer[]>(initialTransfers);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Filters
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<string | Database['public']['Enums']['payment_method']>('all');
  const [category, setCategory] = useState<string | Database['public']['Enums']['expense_category']>('all');

  const supabase = createClientComponentClient<Database>();

  const fetchTransactions = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch expenses with filters
      let expensesQuery = supabase
        .from('admin_expenses')
        .select('*')
        .order('date', { ascending: false });

      // Apply filters
      if (dateFrom) {
        expensesQuery = expensesQuery.gte('date', dateFrom);
      }

      if (dateTo) {
        expensesQuery = expensesQuery.lte('date', dateTo);
      }

      if (paymentMethod !== 'all') {
        expensesQuery = expensesQuery.eq('payment_method', paymentMethod);
      }

      if (category !== 'all') {
        expensesQuery = expensesQuery.eq('category', category);
      }

      const { data: expensesData, error: expensesError } = await expensesQuery;

      if (expensesError) throw expensesError;

      // Fetch transfers with filters
      let transfersQuery = supabase
        .from('account_transfers')
        .select('*')
        .order('initiated_date', { ascending: false });

      // Apply filters
      if (dateFrom) {
        transfersQuery = transfersQuery.gte('initiated_date', dateFrom);
      }

      if (dateTo) {
        transfersQuery = transfersQuery.lte('initiated_date', dateTo);
      }

      if (paymentMethod !== 'all') {
        transfersQuery = transfersQuery.or(`from_account.eq.${paymentMethod},to_account.eq.${paymentMethod}`);
      }

      const { data: transfersData, error: transfersError } = await transfersQuery;

      if (transfersError) throw transfersError;

      setExpenses(expensesData || []);
      setTransfers(transfersData || []);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch transactions'));
      console.error('Error fetching transactions:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, []); // Initial fetch

  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTransactions();
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Transaction History</h1>

      {/* Filters */}
      <Card className="p-6 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        <form onSubmit={handleFilterSubmit} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="dateFrom" className="block text-sm font-medium mb-1">
              Date From
            </label>
            <Input
              id="dateFrom"
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>

          <div>
            <label htmlFor="dateTo" className="block text-sm font-medium mb-1">
              Date To
            </label>
            <Input
              id="dateTo"
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>

          <div>
            <label htmlFor="paymentMethod" className="block text-sm font-medium mb-1">
              Payment Method
            </label>
            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
              <SelectTrigger className="w-full">{paymentMethod ? paymentMethod : 'All Methods'}</SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Methods</SelectItem>
                <SelectItem value="CASH">Cash</SelectItem>
                <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                <SelectItem value="VIVA">VIVA</SelectItem>
                <SelectItem value="WINBANK">Winbank</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium mb-1">
              Category
            </label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="w-full">{category ? category : 'All Categories'}</SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="IRS">IRS</SelectItem>
                <SelectItem value="SOCIAL_SECURITY">Social Security</SelectItem>
                <SelectItem value="BANK_DEPOSIT">Bank Deposit</SelectItem>
                <SelectItem value="VIVA_FEES">VIVA Fees</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="col-span-1 md:col-span-2 lg:col-span-4 flex justify-end">
            <Button type="submit" disabled={loading}>
              {loading ? <LoadingSpinner /> : 'Apply Filters'}
            </Button>
          </div>
        </form>
      </Card>

      {/* Transactions */}
      <Card className="bg-white shadow-sm">
        <Tabs defaultValue="expenses" className="w-full">
          <div className="p-6 border-b">
            <TabsList className="mb-0">
              <TabsTrigger value="expenses">Expenses</TabsTrigger>
              <TabsTrigger value="transfers">Transfers</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="expenses" className="p-0">
            {expenses.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Payment Method
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {expenses.map((expense) => (
                      <tr key={expense.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(expense.date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {expense.description || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {expense.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {expense.payment_method}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-red-600">
                          -{expense.amount.toFixed(2)} €
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500">
                No expenses found for the selected filters.
              </div>
            )}
          </TabsContent>

          <TabsContent value="transfers" className="p-0">
            {transfers.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        From Account
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        To Account
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reference
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transfers.map((transfer) => (
                      <tr key={transfer.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(transfer.initiated_date || '').toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {transfer.from_account}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {transfer.to_account}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {transfer.reference_number || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            transfer.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {transfer.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-amber-600">
                          {transfer.amount.toFixed(2)} €
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500">
                No transfers found for the selected filters.
              </div>
            )}
          </TabsContent>
        </Tabs>
      </Card>

      {error && (
        <div className="p-4 bg-red-50 text-red-600 rounded-md">
          <p>Error loading transactions: {error.message}</p>
        </div>
      )}
    </div>
  );
}