"use client";

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import AccountForm from '@/components/finances/accounts/AccountForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import type { Database } from '@/types/supabase';

// Use the proper type from the Database definition
type AccountBalance = Database['public']['Tables']['account_balances']['Row'];

const ManageAccountsPage = () => {
  const [accounts, setAccounts] = useState<AccountBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const supabase = createClientComponentClient<Database>();

      const { data, error: fetchError } = await supabase
        .from('account_balances')
        .select('*')
        .order('account_name');

      if (fetchError) {
        throw fetchError;
      }

      if (data) {
        setAccounts(data);
      }
    } catch (err) {
      console.error('Error fetching accounts:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  const handleDeleteAccount = async (id: string, accountName: string) => {
    if (!confirm(`Are you sure you want to delete the account "${accountName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const supabase = createClientComponentClient<Database>();

      const { error } = await supabase
        .from('account_balances')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      toast({
        title: "Account deleted",
        description: `The account "${accountName}" has been deleted.`,
      });

      // Refresh the accounts list
      fetchAccounts();
    } catch (err) {
      console.error('Error deleting account:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to delete account',
        variant: "destructive",
      });
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Accounts</h1>
        <Button asChild variant="outline">
          <Link href="/admin/finances/accounts">Back to Accounts</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <AccountForm />
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Existing Accounts</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <p>Loading accounts...</p>
              ) : error ? (
                <p className="text-red-500">{error}</p>
              ) : accounts.length === 0 ? (
                <p>No accounts found. Create your first account using the form.</p>
              ) : (
                <div className="space-y-4">
                  {accounts.map((account) => (
                    <div key={account.id} className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <p className="font-medium">{account.account_name}</p>
                        <p className="text-sm text-gray-500">
                          Balance: {account.current_balance.toFixed(2)} €
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteAccount(account.id, account.account_name)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ManageAccountsPage;
