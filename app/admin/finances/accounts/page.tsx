"use client";
import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import AccountTrendChart from '@/components/finances/accounts/AccountTrendChart';
import ReconciliationStatus from '@/components/finances/accounts/ReconciliationStatus';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Plus } from 'lucide-react';
import type { Database } from '@/types/supabase';

// Use the proper type from the Database definition
type AccountBalance = Database['public']['Tables']['account_balances']['Row'];

const AccountsPage = () => {
  const [balances, setBalances] = useState<AccountBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBalances = async () => {
      try {
        setLoading(true);
        const supabase = createClientComponentClient<Database>();

        const { data, error: fetchError } = await supabase
          .from('account_balances')
          .select('*');

        if (fetchError) {
          throw fetchError;
        }

        if (data) {
          setBalances(data);
        }
      } catch (err) {
        console.error('Error fetching account balances:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchBalances();
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Accounts & Balances</h1>
        <div className="flex space-x-2">
          <Button asChild variant="outline">
            <Link href="/admin/finances/reconciliation">
              Reconcile Accounts
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/finances/accounts/manage">
              <Plus className="h-4 w-4 mr-2" />
              Manage Accounts
            </Link>
          </Button>
        </div>
      </div>

      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-pulse text-lg">Loading accounts...</div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
          <p className="font-medium">Error loading accounts</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {balances.map(acc => (
          <div key={acc.id} className="bg-white shadow rounded-lg p-6 border border-gray-100">
            <div className="font-semibold text-lg">{acc.account_name}</div>
            <div className="text-2xl font-bold my-2">€ {Number(acc.current_balance).toLocaleString(undefined, { minimumFractionDigits: 2 })}</div>
            <div className="text-sm text-gray-500">Last reconciled: {acc.last_reconciled ? new Date(acc.last_reconciled).toLocaleDateString() : 'Never'}</div>
            <div className="text-sm text-gray-500 mb-4">Initial: € {Number(acc.initial_balance).toLocaleString(undefined, { minimumFractionDigits: 2 })}</div>
            <AccountTrendChart accountId={acc.id} />
            <ReconciliationStatus lastReconciled={acc.last_reconciled} />
          </div>
        ))}
      </div>

      {balances.length === 0 && !loading && !error && (
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-md">
          <p className="font-medium">No accounts found</p>
          <p className="text-sm">You need to create accounts to track your finances. Click the &quot;Manage Accounts&quot; button above to add your first account.</p>
          <div className="mt-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/admin/finances/accounts/manage">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Account
              </Link>
            </Button>
          </div>
        </div>
      )}

      {/* To-do: Add overall trend chart and reconciliation summary here */}
    </div>
  );
};

export default AccountsPage;