// app/admin/finances/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from "@/types/supabase";
import FinancialDashboardClient from './FinancialDashboardClient';

export default async function FinancialDashboardPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Fetch initial balances on the server
  const { data: balances } = await supabase
    .from('account_balances')
    .select('*');

  // Fetch recent expenses
  const { data: recentExpenses } = await supabase
    .from('admin_expenses')
    .select('*')
    .order('date', { ascending: false })
    .limit(5);

  // Fetch recent transfers
  const { data: recentTransfers } = await supabase
    .from('account_transfers')
    .select('*')
    .order('initiated_date', { ascending: false })
    .limit(5);

  return (
    <div className="container mx-auto py-6">
      <FinancialDashboardClient
        initialBalances={balances || []}
        recentExpenses={recentExpenses || []}
        recentTransfers={recentTransfers || []}
      />
    </div>
  );
}