"use client";

import React from 'react';
import CashflowAnalysis from '@/components/finances/cashflow/CashflowAnalysis';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

const CashflowAnalysisPage = () => {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Cashflow Analysis</h1>
        <Button asChild variant="outline">
          <Link href="/admin/finances">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Finances
          </Link>
        </Button>
      </div>
      
      <CashflowAnalysis />
    </div>
  );
};

export default CashflowAnalysisPage;
