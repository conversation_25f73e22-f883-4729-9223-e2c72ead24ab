// app/admin/layout.tsx (improved version)
'use client'
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";


// Admin navigation groups copied from SecondaryNavigation.tsx with added "New Members" link
const adminGroups = [
  {
    title: "Reports",
    items: [
      { title: "KPI Dashboard", href: "/admin/reports/kpis" },
      { title: "Monthly Reports", href: "/admin/reports/monthly" },
      { title: "Check-ins", href: "/admin/reports/checkins" },
      { title: "New Members", href: "/admin/reports/new-members" }
    ]
  },
  {
    title: "Coach Tools",
    items: [
      { title: "Daily Overview", href: "/admin/daily" },
      { title: "Exercise Records", href: "/admin/exercise-records" },
      { title: "Session Calendar", href: "/admin/sessions/calendar" }
    ]
  },
  {
    title: "Members",
    items: [
      { title: "View Members", href: "/admin/users/view" },
      { title: "Active Subscriptions", href: "/admin/subscriptions/active" },
      { title: "Support Tickets", href: "/admin/support" },
      { title: "Add Check-in", href: "/admin/check-ins" }
    ]
  },
  {
    title: "Expenses",
    items: [
      { title: "View Expenses", href: "/admin/expenses" }
    ]
  },
  {
    title: "Payments",
    items: [
      { title: "View Payments", href: "/admin/payments/view" },
      { title: "New Payment", href: "/admin/payments/add" }
    ]
  },
  {
    title: "Sessions",
    items: [
      { title: "Session Calendar", href: "/admin/sessions/calendar" },
      { title: "Add Session", href: "/admin/sessions/add" },
      { title: "Daily", href: "/admin/daily" }
    ]
  },
  {
    title: "WODS",
    items: [
      { title: "WODs", href: "/admin/wods" },
      { title: "Add WOD", href: "/admin/wods/add" },
      { title: "Exercise Library", href: "/admin/wods/exercises" }
    ]
  },
  {
    title: "System",
    items: [
      { title: "Auth Debug", href: "/admin/auth-debug" },
      { title: "Role Check", href: "/admin/check-role" },
      { title: "Expiring Sessions", href: "/admin/subscriptions/expiring" }
    ]
  },
  {
    title: "Notifications",
    items: [
      { title: "Dashboard", href: "/admin/notifications/dashboard" },
      { title: "Manage Notifications", href: "/admin/notifications" },
      { title: "Subscriptions", href: "/admin/notifications/subscriptions" }
    ]
  },
  {
    title: "Other",
    items: [
      { title: "CROSSFIT TIMER", href: "/admin/crossfitimer" },
      { title: "Meals Planning", href: "/meals/weekbyweek" },
      { title: "Ergo Conversions", href: "/admin/wods/conversiontable" },
      { title: "Exercises", href: "/admin/wods/exercises" },
      { title: "Merchandise", href: "/admin/merchandise" }
    ]
  }
];


export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="flex items-center gap-4 overflow-x-auto px-4 py-2 hide-scrollbar">
            {adminGroups.map((group) => (
              <DropdownMenu key={group.title}>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center gap-1">
                    {group.title}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56 max-h-[60vh] overflow-y-auto">
                  {group.items.map((item) => (
                    <DropdownMenuItem key={item.href} asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "w-full",
                          pathname === item.href ? "bg-accent text-accent-foreground" : ""
                        )}
                      >
                        {item.title}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            ))}
          </div>
        </div>
      </div>

      <main className="flex-1">
        <div className="py-4 sm:py-6">
          <div className="px-0 sm:px-4 md:px-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}