import Link from 'next/link'
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function SessionsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
   
      <div className="">
        <h1 className="text-2xl font-bold mb-4">Sessions for the Month</h1>
        <div className="mb-4 space-x-2">
          <Button asChild variant="outline">
            <Link href="/admin/sessions/calendar">Calendar</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/sessions/add">New Sessions</Link>
          </Button>
        </div>
        {children}
      </div>
 
  )
}