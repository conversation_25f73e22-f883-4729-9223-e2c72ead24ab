  import { createServerClient } from "@/utils/supabase-server";
  import SessionsAdmin from '@/components/SessionsAdmin';
  import { Locale, DEFAULT_LOCALE } from '@/app/config';

  export const dynamic = "force-dynamic";
  export const runtime = "edge";

  export default async function SessionsAdminPage() {
    const supabase = createServerClient();
    const lang: Locale = DEFAULT_LOCALE;

    // Fetch initial data
    const { data: initialSessions } = await supabase
      .from('sessions')
      .select(`
        *,
        programs (name),
        check_ins (id, pelatis_id),
        bookings (id, pelatis_id)
      `)
      .order('start_time');

    const { data: initialClients } = await supabase
      .from('pelates')
      .select('id, client_name')
      .order('client_name');

    return (
      <>
        <h1>Sessions Administration</h1>
        <SessionsAdmin 
          lang={lang} 
          initialSessions={initialSessions || []}
          initialClients={initialClients || []}
        />
      </>
    );
  }