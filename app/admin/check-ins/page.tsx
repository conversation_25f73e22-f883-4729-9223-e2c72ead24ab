'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import ClientSelector from '@/components/ClientSelector'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { toast } from '@/hooks/use-toast'
import type { Database } from '@/types/supabase'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

type Tables = Database['public']['Tables']

// Essential type definitions
interface FullClientOption {
  value: string
  label: string
  pelatis_id: string
  session_id: string
  subscription?: {
    days_until_expiration: number | null
    end_date: string | null
    subscription_status: string | null
    program_name_display: string | null
  }
}

type Session = Tables['sessions']['Row'] & {
  programs: Tables['programs']['Row']
}

type CheckIn = Tables['check_ins']['Row'] & {
  pelates: Tables['pelates']['Row'] | null
  sessions: (Tables['sessions']['Row'] & {
    programs: Tables['programs']['Row']
  }) | null
}

// Removed unused type

type MemberEngagementData = {
  pelatis_id: string
  name: string | null
  last_name: string | null
  total: number
  avg: number
  mayCheckins: number
  aprilCheckins: number
  activeMonths: number
  status: string
  lastCheckIn: string | null
}

export default function CheckInsPage() {
  const [clients, setClients] = useState<FullClientOption[]>([])
  const [sessions, setSessions] = useState<Session[]>([])
  const [selectedClients, setSelectedClients] = useState<FullClientOption[]>([])
  const [selectedSession, setSelectedSession] = useState<string>('')
  const [todayCheckIns, setTodayCheckIns] = useState<CheckIn[]>([])
  const [memberEngagementData, setMemberEngagementData] = useState<MemberEngagementData[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [activeTab, setActiveTab] = useState<string>('daily')

  const supabase = createClientComponentClient<Database>()

  // Current date to use for month filtering
  const currentYear = new Date().getFullYear()

  // Get the formatted month keys for the current and previous month
  const mayKey = `${currentYear}-05` // Format: YYYY-MM
  const aprilKey = `${currentYear}-04` // Format: YYYY-MM

  const fetchSessions = useCallback(async (date: Date) => {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        programs (*)
      `)
      .gte('start_time', startOfDay.toISOString())
      .lt('start_time', endOfDay.toISOString())
      .order('start_time')

    if (error) throw error
    return data || []
  }, [supabase])

  const fetchTodayCheckIns = useCallback(async () => {
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date()
    endOfDay.setHours(23, 59, 59, 999)

    const { data, error } = await supabase
      .from('check_ins')
      .select(`
        *,
        pelates (*),
        sessions (
          *,
          programs (*)
        )
      `)
      .gte('check_in_time', startOfDay.toISOString())
      .lt('check_in_time', endOfDay.toISOString())
      .order('check_in_time')

    if (error) throw error
    return data || []
  }, [supabase])

  const fetchMemberEngagementData = useCallback(async () => {
    try {
      // Fetch user stats from user_checkin_stats view
      const { data: userStatsData, error: userStatsError } = await supabase
        .from('user_checkin_stats')
        .select('*')

      if (userStatsError) throw userStatsError
      if (!userStatsData) return

      console.log('User stats data:', userStatsData)

      // Fetch latest check-ins to determine status and last check-in date
      const { data: latestCheckinsData, error: latestCheckinsError } = await supabase
        .from('latest_member_checkins')
        .select('*')

      if (latestCheckinsError) throw latestCheckinsError

      // Process and combine the data
      const processedData = userStatsData.map(userData => {
        // Find the latest check-in for this user
        const latestCheckin = latestCheckinsData?.find(
          checkin => checkin.pelatis_id === userData.pelatis_id
        )

        // Extract monthly breakdown data
        const monthlyBreakdown = userData.monthly_breakdown as Record<string, number> || {}

        // Calculate active months (months with at least one check-in)
        const activeMonths = Object.values(monthlyBreakdown).filter(count => count > 0).length

        // Calculate average check-ins per active month
        // Avoid division by zero
        const avg = activeMonths > 0
          ? Number((userData.total_checkins || 0) / activeMonths).toFixed(2)
          : 0

        // Determine if the user is active or inactive based on recent check-ins
        const isActive = latestCheckin?.check_in_time ?
          new Date(latestCheckin.check_in_time) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          : false

        return {
          pelatis_id: userData.pelatis_id || '',
          name: userData.name,
          last_name: userData.last_name,
          total: userData.total_checkins || 0,
          avg: Number(avg),
          mayCheckins: monthlyBreakdown[mayKey] || 0,
          aprilCheckins: monthlyBreakdown[aprilKey] || 0,
          activeMonths: activeMonths,
          status: isActive ? 'Active' : 'Inactive',
          lastCheckIn: latestCheckin?.check_in_time || null
        }
      })

      // Sort by total check-ins (descending)
      processedData.sort((a, b) => b.total - a.total)

      console.log('Processed engagement data:', processedData)
      setMemberEngagementData(processedData)
    } catch (error) {
      console.error('Error fetching member engagement data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load member engagement data',
        variant: 'destructive',
      })
    }
  }, [supabase, mayKey, aprilKey])

  const fetchClients = useCallback(async (userId: string) => {
    // Check if user is admin
    const { data: userRoles, error: rolesError } = await supabase.rpc('getUserRoles', {
      p_user_id: userId
    })
    if (rolesError) throw rolesError
    const isAdmin = Array.isArray(userRoles) && userRoles.includes('admin')

    let query = supabase
      .from('pelates')
      .select(`
        id,
        name,
        last_name,
        auth_user_id
      `)

    // If not admin, only fetch the user's own record
    if (!isAdmin) {
      query = query.eq('auth_user_id', userId)
    }

    const { data, error } = await query
    if (error) throw error

    return data.map(pelate => ({
      value: pelate.id,
      label: `${pelate.last_name}, ${pelate.name}`,
      pelatis_id: pelate.id,
      session_id: '',
    }))
  }, [supabase])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        if (userError) throw userError
        if (!user?.id) {
          console.error('No user ID found')
          return
        }

        // Fetch all data concurrently
        const [sessionsData, checkInsData, clientsData] = await Promise.all([
          fetchSessions(selectedDate),
          fetchTodayCheckIns(),
          fetchClients(user.id)
        ])

        setSessions(sessionsData)
        setTodayCheckIns(checkInsData)
        setClients(clientsData)

        // Fetch the member engagement data
        await fetchMemberEngagementData()

      } catch (error) {
        console.error('Error fetching data:', error)
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to load data',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [supabase, selectedDate, fetchSessions, fetchTodayCheckIns, fetchClients, fetchMemberEngagementData])

  // Handler for date change
  const handleDateChange = async (date: Date | undefined) => {
    if (!date) return
    setSelectedDate(date)
    setSelectedSession('') // Reset session selection when date changes

    try {
      const sessionsData = await fetchSessions(date)
      setSessions(sessionsData)
    } catch (error) {
      console.error('Error fetching sessions:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sessions for selected date',
        variant: 'destructive',
      })
    }
  }

  const handleCreateCheckIn = async () => {
    if (!selectedClients.length || !selectedSession) {
      toast({
        title: 'Error',
        description: 'Please select both a client and a session.',
        variant: 'destructive',
      })
      return
    }

    try {
      // First verify if user is admin or regular user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError) throw userError
      if (!user?.id) throw new Error('No authenticated user found')

      const { data: userRoles, error: rolesError } = await supabase.rpc('getUserRoles', {
        p_user_id: user.id
      })
      if (rolesError) throw rolesError

      const isAdmin = Array.isArray(userRoles) && userRoles.includes('admin')

      // For regular users, we need to verify they are creating a check-in for themselves
      if (!isAdmin) {
        // Get the user's pelates record
        const { data: pelatesData, error: pelatesError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', user.id)
          .single()

        if (pelatesError) throw pelatesError
        if (!pelatesData) throw new Error('No pelates record found for user')

        // Create check-in with verified pelatis_id
        const { data: newCheckIn, error: insertError } = await supabase
          .from('check_ins')
          .insert({
            pelatis_id: pelatesData.id,
            session_id: selectedSession,
          })
          .select(`
            *,
            pelates (*),
            sessions (
              *,
              programs (*)
            )
          `)
          .single()

        if (insertError) throw insertError
        if (!newCheckIn) throw new Error('Failed to create check-in')

        setTodayCheckIns(current => [...current, newCheckIn])

      } else {
        // Admin flow - can create check-ins for any user
        const { data: newCheckIn, error: insertError } = await supabase
          .from('check_ins')
          .insert({
            pelatis_id: selectedClients[0].pelatis_id,
            session_id: selectedSession,
          })
          .select(`
            *,
            pelates (*),
            sessions (
              *,
              programs (*)
            )
          `)
          .single()

        if (insertError) throw insertError
        if (!newCheckIn) throw new Error('Failed to create check-in')

        setTodayCheckIns(current => [...current, newCheckIn])
      }

      setSelectedClients([])
      setSelectedSession('')

      // Refresh the member engagement data
      await fetchMemberEngagementData()

      toast({
        title: 'Success',
        description: 'Check-in created successfully.',
      })

    } catch (error) {
      console.error('Error creating check-in:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create check-in',
        variant: 'destructive',
      })
    }
  }

  const handleDeleteCheckIn = async (id: string) => {
    try {
      const { error } = await supabase
        .from('check_ins')
        .delete()
        .eq('id', id)

      if (error) throw error

      setTodayCheckIns(current => current.filter(checkIn => checkIn.id !== id))

      // Refresh member engagement data after deletion
      await fetchMemberEngagementData()

      toast({
        title: 'Success',
        description: 'Check-in deleted successfully.',
      })
    } catch (error) {
      console.error('Error deleting check-in:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete check-in. Please try again.',
        variant: 'destructive',
      })
    }
  }

  if (loading) {
    return <div className="p-4">Loading...</div>
  }

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <Tabs defaultValue="daily" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="daily">Daily Check-ins</TabsTrigger>
          <TabsTrigger value="monthly">Member Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="daily">
          <Card>
            <CardHeader>
              <CardTitle>Manage Daily Check-ins</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <ClientSelector
                  clients={clients}
                  selectedClients={selectedClients}
                  onChange={setSelectedClients}
                />

                <div className="grid gap-2">
                  <Label>Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {format(selectedDate, 'PPP')}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={handleDateChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="session">Session</Label>
                  <Select
                    value={selectedSession}
                    onValueChange={setSelectedSession}
                  >
                    <SelectTrigger id="session">
                      <SelectValue placeholder="Select session" />
                    </SelectTrigger>
                    <SelectContent>
                      {sessions.length === 0 ? (
                        <SelectItem value="no-sessions" disabled>
                          No sessions available for this date
                        </SelectItem>
                      ) : (
                        sessions.map((session) => (
                          <SelectItem key={session.id} value={session.id}>
                            {format(new Date(session.start_time), 'HH:mm')} - {session.programs.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  onClick={handleCreateCheckIn}
                  disabled={!selectedClients.length || !selectedSession || selectedSession === 'no-sessions'}
                >
                  Create Check-in
                </Button>
              </div>

              <div className="mt-8">
                <h3 className="font-semibold mb-4">Today&apos;s Check-ins</h3>
                <div className="grid gap-4">
                  {todayCheckIns.map((checkIn) => (
                    <div
                      key={checkIn.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">
                          {checkIn.pelates && `${checkIn.pelates.last_name}, ${checkIn.pelates.name}`}
                        </p>
                        <p className="text-sm text-gray-500">
                          {checkIn.sessions && (
                            <>
                              {new Date(checkIn.sessions.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -
                              {checkIn.sessions.programs.name}
                            </>
                          )}
                        </p>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteCheckIn(checkIn.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  ))}
                  {todayCheckIns.length === 0 && (
                    <p className="text-gray-500 text-center">No check-ins for today</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly">
          <Card>
            <CardHeader>
              <CardTitle>Member Engagement - Check-ins Per User</CardTitle>
              <p className="text-sm text-gray-500">
                Check-in data for the current and previous month. Subscription status is shown in the Status column.
              </p>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                    <TableHead className="text-right">Avg</TableHead>
                    <TableHead className="text-right">Active Months</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">May 2025</TableHead>
                    <TableHead className="text-right">Apr 2025</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {memberEngagementData.length > 0 ? (
                    memberEngagementData.map((member) => (
                      <TableRow key={member.pelatis_id}>
                        <TableCell className="font-medium">{member.last_name}, {member.name}</TableCell>
                        <TableCell className="text-right">{member.total}</TableCell>
                        <TableCell className="text-right">{member.avg}</TableCell>
                        <TableCell className="text-right">{member.activeMonths}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${member.status === 'Active' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                              {member.status}
                            </span>
                            <span className="text-xs text-gray-500 mt-1">
                              {member.lastCheckIn ? `Last: ${format(new Date(member.lastCheckIn), 'dd/MM/yy')}` : 'N/A'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">{member.mayCheckins}</TableCell>
                        <TableCell className="text-right">{member.aprilCheckins}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No member engagement data found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}