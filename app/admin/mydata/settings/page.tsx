// app/admin/mydata/settings/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import MyDataSettingsClient from '@/components/mydata/MyDataSettingsClient';
import type { Database } from '@/types/supabase';
import type { MyDataEnvironmentInfo } from '@/types/company';

export const dynamic = 'force-dynamic';

export default async function MyDataSettingsPage() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  // Ensure user is authenticated and is admin
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>Please log in to access this page.</p>
      </div>
    );
  }
  
  // Check if user has admin role
  const { data: userRoles } = await supabase
    .from('user_roles')
    .select('role_id')
    .eq('auth_user_id', session.user.id);
  
  const isAdmin = userRoles?.some(ur => [1, 2].includes(ur.role_id));
  
  if (!isAdmin) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }
  
  // Get company settings
  const { data: companySettings, error } = await supabase
    .from('company_settings')
    .select('*')
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
    console.error('Error fetching company settings:', error);
  }
  
  // Check environment and credentials
  const environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';
  const hasCredentials = environment === 'production'
    ? !!process.env.MYDATA_USERNAME_PROD && !!process.env.MYDATA_SUBSCRIPTION_KEY_PROD
    : !!process.env.MYDATA_USERNAME_DEV && !!process.env.MYDATA_SUBSCRIPTION_KEY_DEV;
  
  const environmentInfo: MyDataEnvironmentInfo = {
    environment,
    hasCredentials,
    currentEnvironment: environment
  };
  
  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">myDATA Settings</h1>
      
      <MyDataSettingsClient 
        initialCompanySettings={companySettings}
        environmentInfo={environmentInfo}
      />
    </div>
  );
}