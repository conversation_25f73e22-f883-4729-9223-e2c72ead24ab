// app/admin/mydata/test/page.tsx
import MyDataNavigation from '@/components/mydata/MyDataNavigation';
import TestInvoiceForm from '@/components/mydata/TestInvoiceForm';

export default function MyDataTestPage() {
  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">myDATA Test Invoices</h1>
      
      <MyDataNavigation />
      
      <p className="text-gray-600">
        This page allows you to create test invoices and send them to the myDATA test environment.
        These invoices will be created in your local database and then sent to the AADE test API.
      </p>
      
      <TestInvoiceForm />
    </div>
  );
}