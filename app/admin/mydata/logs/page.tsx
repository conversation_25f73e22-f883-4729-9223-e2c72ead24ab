'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Search, RefreshCw } from 'lucide-react';
import MyDataNavigation from '@/components/mydata/MyDataNavigation';
import type { Database } from '@/types/supabase';

type ApiLog = Database['public']['Tables']['api_logs']['Row'];

export default function MyDataLogsPage() {
  const [logs, setLogs] = useState<ApiLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'error'>('all');
  const [requestTypeFilter, setRequestTypeFilter] = useState<string>('all');
  const [uniqueRequestTypes, setUniqueRequestTypes] = useState<string[]>([]);

  const supabase = createClientComponentClient<Database>();

  const fetchLogs = async () => {
    setLoading(true);

    try {
      let query = supabase
        .from('api_logs')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // Apply request type filter if not 'all'
      if (requestTypeFilter !== 'all') {
        query = query.eq('request_type', requestTypeFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching API logs:', error);
        return;
      }

      setLogs(data || []);

      // Extract unique request types for filter dropdown
      if (data) {
        // Use a different approach to get unique values without using Set spread
        const uniqueTypes: string[] = [];
        const typeMap: Record<string, boolean> = {};

        data.forEach(log => {
          if (!typeMap[log.request_type]) {
            typeMap[log.request_type] = true;
            uniqueTypes.push(log.request_type);
          }
        });

        setUniqueRequestTypes(uniqueTypes);
      }
    } catch (err) {
      console.error('Error in fetchLogs:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [statusFilter, requestTypeFilter]);

  // Filter logs by search term
  const filteredLogs = logs.filter(log =>
    log.request_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (log.error_message && log.error_message.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (log.request_body && log.request_body.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (log.response_body && log.response_body.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">myDATA API Logs</h1>

      <MyDataNavigation />

      <div className="flex flex-col md:flex-row gap-4 items-center justify-between mb-6">
        <div className="flex flex-1 gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search logs..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as 'all' | 'success' | 'error')}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="success">Success</SelectItem>
              <SelectItem value="error">Error</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={requestTypeFilter}
            onValueChange={setRequestTypeFilter}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Request Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Request Types</SelectItem>
              {uniqueRequestTypes.map(type => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button onClick={fetchLogs} variant="outline" className="whitespace-nowrap">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API Logs</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : filteredLogs.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Request Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Error Message</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      {log.created_at ? format(new Date(log.created_at), 'dd/MM/yyyy HH:mm:ss') : '-'}
                    </TableCell>
                    <TableCell>{log.request_type}</TableCell>
                    <TableCell>
                      <Badge variant={log.status === 'success' ? 'default' : 'destructive'}>
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {log.error_message || '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No API logs found matching your filters.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
