import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { HelpCircle } from "lucide-react";

export function MjmlHints() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm">
          <HelpCircle className="h-4 w-4" />
          <span className="ml-2">MJML Tips</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-2">
          <h4 className="font-medium">Common MJML Components</h4>
          <ul className="text-sm space-y-1">
            <li><code>{'<mj-text>'}</code> - For text content</li>
            <li><code>{'<mj-button>'}</code> - For buttons</li>
            <li><code>{'<mj-image>'}</code> - For images</li>
            <li><code>{'<mj-section>'}</code> - For layout sections</li>
            <li><code>{'<mj-column>'}</code> - For columns within sections</li>
          </ul>
          <p className="text-sm mt-2">
            Use <code>{"{{"}{"variable"}{"}}"}</code> for dynamic content
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
}
