'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { Eye, Save, Plus } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DialogFooter } from "@/components/ui/dialog";
import { MjmlHints } from "./components/MjmlHints";

// Remove the mjml2html import since we're using the API endpoint instead

// Add interface for template structure
interface Template {
  key: string;
  name: string;
  html: string;
}

interface TemplateMap {
  [key: string]: {
    name: string;
    html: string;
  };
}

const DEFAULT_MJML_TEMPLATE = `
<mjml>
  <mj-head>
    <mj-attributes>
      <mj-all font-family="Arial, sans-serif" />
    </mj-attributes>
  </mj-head>
  <mj-body>
    <mj-section>
      <mj-column>
        <mj-text font-size="20px" color="#333" align="center">
          Your Template Title
        </mj-text>
        <mj-text>
          Hello {{name}},
        </mj-text>
        <mj-text>
          Your content goes here.
        </mj-text>
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>
`;

export default function EmailTemplatesPage() {
  const [templates, setTemplates] = useState<TemplateMap>({
    subscription_reminder: {
      name: "Subscription Reminder",
      html: `<!DOCTYPE html>
<html dir="ltr" lang="el">
  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>
  <body style="background-color:#ffffff;font-family:Arial,sans-serif">
    <table align="center" width="100%" style="max-width:600px;margin:0 auto;padding:20px">
      <tr>
        <td>
          <h1 style="color:#333;text-align:center">Η Συνδρομή σας Λήγει Σύντομα</h1>
          <p>Γεια σας {{name}},</p>
          <p>Σας ενημερώνουμε ότι η συνδρομή σας <strong>{{program}}</strong> λήγει στις {{end_date}}.</p>
          <!-- Rest of your template -->
        </td>
      </tr>
    </table>
  </body>
</html>`
    },
    // Add more templates as needed
  });

  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [activeTemplate, setActiveTemplate] = useState('subscription_reminder');
  const [showNewTemplateDialog, setShowNewTemplateDialog] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    key: '',
    name: '',
    html: DEFAULT_MJML_TEMPLATE
  });

  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const response = await fetch('/api/email-templates');
        if (!response.ok) throw new Error('Failed to fetch templates');
        
        const { templates: fetchedTemplates } = await response.json() as { templates: Template[] };
        
        if (fetchedTemplates && fetchedTemplates.length > 0) {
          const templatesMap = fetchedTemplates.reduce((acc: TemplateMap, template: Template) => ({
            ...acc,
            [template.key]: {
              name: template.key.split('_').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' '),
              html: template.html
            }
          }), {});
          
          setTemplates(templatesMap);
        }
      } catch (error) {
        console.error('Error loading templates:', error);
        toast({
          title: "Error",
          description: "Failed to load templates",
          variant: "destructive"
        });
      }
    };

    loadTemplates();
  }, []);

  const handlePreview = async (templateKey: string) => {
    setIsPreviewLoading(true);
    try {
      const template = templates[templateKey].html;
      const sampleData = {
        name: 'John Doe',
        program: 'Premium Membership',
        end_date: '31 December 2024',
      };

      // Debug log
      console.log('Template contains MJML:', template.includes('<mjml'));
      console.log('Template preview:', template.substring(0, 100));

      if (template.includes('<mjml')) {
        const requestBody = {
          template,
          variables: sampleData,
        };

        // Debug log
        console.log('Sending request to MJML API:', requestBody);

        const response = await fetch('http://localhost:3000/api/mjml', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        // Debug log
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API response:', data);

        if (!data.html) {
          throw new Error('No HTML returned from MJML processing');
        }

        setPreviewContent(data.html);
      } else {
        // For regular HTML templates, just replace variables
        let preview = template;
        Object.entries(sampleData).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          preview = preview.replace(regex, String(value));
        });
        setPreviewContent(preview);
      }

      setShowPreview(true);
    } catch (error) {
      console.error('Preview error:', error);
      toast({
        title: "Error",
        description: error instanceof Error 
          ? error.message 
          : "Failed to generate preview",
        variant: "destructive"
      });
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const saveTemplate = async (key: string, html: string) => {
    try {
      const name = key.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      const response = await fetch('/api/email-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key,
          name,
          html
        }),
      });

      if (!response.ok) throw new Error('Failed to save template');

      toast({
        title: "Success",
        description: "Template saved successfully",
      });
    } catch (error) {
      console.error('Error saving template:', error);
      toast({
        title: "Error",
        description: "Failed to save template",
        variant: "destructive"
      });
    }
  };

  const handleCreateTemplate = async () => {
    try {
      const response = await fetch('/api/email-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newTemplate),
      });

      if (!response.ok) throw new Error('Failed to create template');

      // Update local state
      setTemplates(prev => ({
        ...prev,
        [newTemplate.key]: {
          name: newTemplate.name,
          html: newTemplate.html
        }
      }));

      setShowNewTemplateDialog(false);
      setNewTemplate({ key: '', name: '', html: '' });
      
      toast({
        title: "Success",
        description: "Template created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create template",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Email Templates</h1>
        <Button onClick={() => setShowNewTemplateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>
      
      <Tabs 
        defaultValue="subscription_reminder"
        onValueChange={setActiveTemplate}
      >
        <TabsList>
          {Object.entries(templates).map(([key, template]) => (
            <TabsTrigger 
              key={key} 
              value={key}
              className={activeTemplate === key ? 'font-bold' : ''}
            >
              {template.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {Object.entries(templates).map(([key, template]) => (
          <TabsContent key={key} value={key}>
            <Card>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span>{template.name}</span>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => handlePreview(key)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button 
                      onClick={() => saveTemplate(key, templates[key].html)}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Template
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={template.html}
                  onChange={(e) => setTemplates(prev => ({
                    ...prev,
                    [key]: { ...prev[key], html: e.target.value }
                  }))}
                  className="font-mono h-[600px]"
                />
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Email Preview</DialogTitle>
          </DialogHeader>
          {isPreviewLoading ? (
            <div className="flex justify-center items-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
            </div>
          ) : (
            <div 
              className="mt-4 p-4 border rounded-lg bg-white"
              dangerouslySetInnerHTML={{ __html: previewContent }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Optional: Display active template name */}
      <div className="mt-4">
        Currently editing: {templates[activeTemplate]?.name}
      </div>

      <Dialog open={showNewTemplateDialog} onOpenChange={setShowNewTemplateDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Email Template</DialogTitle>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();
            handleCreateTemplate();
          }} 
          className="space-y-4">
            <div>
              <Label htmlFor="key">Template Key</Label>
              <Input
                id="key"
                value={newTemplate.key}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  key: e.target.value.toLowerCase().replace(/\s+/g, '_')
                }))}
                placeholder="welcome_email"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Unique identifier for the template (lowercase with underscores)
              </p>
            </div>

            <div>
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  name: e.target.value
                }))}
                placeholder="Welcome Email"
                required
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="html">HTML Content (MJML supported)</Label>
                <MjmlHints />
              </div>
              <Textarea
                id="html"
                value={newTemplate.html}
                onChange={(e) => setNewTemplate(prev => ({
                  ...prev,
                  html: e.target.value
                }))}
                placeholder="<mjml>..."
                className="font-mono h-[300px]"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Use `{'{{variable}}'}`  syntax for dynamic content
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowNewTemplateDialog(false)}
              >
                Cancel
              </Button>
              <Button type="submit">
                Create Template
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
