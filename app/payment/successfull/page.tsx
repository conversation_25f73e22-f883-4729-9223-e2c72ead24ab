'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { CheckCircle } from 'lucide-react'

interface TransactionDetails {
  transactionId: string;
  orderCode: string;
  status: string;
  date: string;
}

export default function PaymentSuccessful() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [transactionDetails, setTransactionDetails] = useState<TransactionDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Extract transaction ID and order code from URL parameters with null safety
    const transactionId = searchParams?.get('t') ?? null
    const orderCode = searchParams?.get('s') ?? null

    if (transactionId && orderCode) {
      // Optional: Verify the payment server-side by calling an API
      // This is a simplified example that just displays the parameters
      setTransactionDetails({
        transactionId,
        orderCode,
        status: 'Completed',
        date: new Date().toLocaleString()
      })
    }
    
    setLoading(false)
  }, [searchParams])

  const handleReturnToHome = () => {
    router.push('/')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mx-auto"></div>
          <p className="mt-4 text-lg">Verifying your payment...</p>
        </div>
      </div>
    )
  }

  if (!transactionDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Payment Verification Failed</h1>
          <p className="text-gray-600 mb-6">
            We couldn&apos;t verify your payment details. If you believe this is an error, 
            please contact our support team.
          </p>
          <button 
            onClick={handleReturnToHome}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Successful!</h1>
          <p className="mt-2 text-gray-600">
            Thank you for your payment. Your transaction has been completed successfully.
          </p>
        </div>
        
        <div className="border-t border-gray-200 pt-4 mb-6">
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Transaction ID:</span>
            <span className="font-medium">{transactionDetails.transactionId}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Order Code:</span>
            <span className="font-medium">{transactionDetails.orderCode}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Status:</span>
            <span className="font-medium text-green-600">{transactionDetails.status}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Date:</span>
            <span className="font-medium">{transactionDetails.date}</span>
          </div>
        </div>
        
        <button
          onClick={handleReturnToHome}
          className="w-full py-3 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Return to Home
        </button>
      </div>
    </div>
  )
}
