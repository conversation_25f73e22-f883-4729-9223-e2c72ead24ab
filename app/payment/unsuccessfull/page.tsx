'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { XCircle } from 'lucide-react'

interface ErrorDetails {
  eventId?: string;
  message: string;
}

export default function PaymentUnsuccessful() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [errorDetails, setErrorDetails] = useState<ErrorDetails>({
    message: 'Your payment was not completed successfully.'
  })

  useEffect(() => {
    // Extract error information from URL parameters with null safety
    const eventId = searchParams?.get('eventId') ?? null
    
    if (eventId) {
      let errorMessage = 'Your payment was not completed successfully.'
      
      // Map common event IDs to user-friendly messages
      // Reference: https://developer.vivawallet.com/api-reference/payment-api/#event-ids-and-their-meanings
      switch (eventId) {
        case '10051':
          errorMessage = 'The payment was declined due to insufficient funds.'
          break
        case '10052':
          errorMessage = 'The payment was declined because the card is lost or stolen.'
          break
        case '10054':
          errorMessage = 'The payment was declined due to suspected fraud.'
          break
        case '10057':
          errorMessage = 'The payment was cancelled by you or timed out.'
          break
        case '10205':
          errorMessage = 'The card you used is expired. Please try again with a different card.'
          break
        default:
          errorMessage = 'The payment could not be completed. Please try again or use a different payment method.'
      }
      
      setErrorDetails({
        eventId,
        message: errorMessage
      })
    }
  }, [searchParams])

  const handleTryAgain = () => {
    router.push('/pricing')
  }

  const handleReturnToHome = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Unsuccessful</h1>
          <p className="mt-2 text-gray-600">
            {errorDetails.message}
          </p>
          {errorDetails.eventId && (
            <p className="mt-1 text-sm text-gray-500">
              Reference: {errorDetails.eventId}
            </p>
          )}
        </div>
        
        <div className="space-y-3">
          <button
            onClick={handleTryAgain}
            className="w-full py-3 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          
          <button
            onClick={handleReturnToHome}
            className="w-full py-3 px-4 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    </div>
  )
}
