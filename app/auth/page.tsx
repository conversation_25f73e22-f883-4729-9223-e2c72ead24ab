// app/auth/page.tsx
import AuthForm from '@/components/AuthForm'
import { Alert, AlertDescription } from "@/components/ui/alert"

interface PageProps {
  searchParams?: {
    error?: string
    redirect_to?: string
  }
}

export default function AuthPage({ searchParams }: PageProps) {
  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <div className="p-8 max-w-sm w-full bg-white shadow-lg rounded-lg">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold">Welcome</h1>
          <p className="text-gray-600 mt-2">
            Sign in with magic link
          </p>
        </div>

        {searchParams?.error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>
              {searchParams.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Include iOS detection in the auth form */}
        <AuthForm redirectTo={searchParams?.redirect_to} />
        
        {/* Add iOS-specific guidance */}
        <div className="mt-6 text-xs text-gray-500">
          <p>Having trouble signing in? Try these steps:</p>
          <ul className="list-disc pl-5 mt-1 space-y-1">
            <li>Ensure cookies are enabled in your browser</li>
            <li>If using iOS Safari, open the magic link directly in Safari</li>
            <li>Avoid using private/incognito browsing mode</li>
          </ul>
        </div>
      </div>
    </div>
  )
}