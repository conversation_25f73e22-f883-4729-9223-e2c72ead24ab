import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'  // Remove 'type' from import
import { type Database } from '@/types/supabase'

export const dynamic = 'force-dynamic'

export async function POST() {
  const cookieStore = cookies()
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore })

  try {
    // Get current session first
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Session error:', sessionError)
      return NextResponse.json(
        { error: 'Session error' },
        { status: 400 }
      )
    }

    // If no session, return success (already signed out)
    if (!session) {
      return NextResponse.json(
        { message: 'Already signed out' },
        { status: 200 }
      )
    }

    // Proceed with sign out
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      console.error('Sign out error:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    // Create response
    const response = NextResponse.json(
      { message: 'Signed out successfully' },
      { status: 200 }
    )

    // Clear auth cookie
    response.cookies.set(`sb-${process.env.NEXT_PUBLIC_SUPABASE_URL}-auth-token`, '', {
      path: '/',
      expires: new Date(0),
    })

    return response

  } catch (error) {
    console.error('Sign out error:', error)
    return NextResponse.json(
      { 
        error: 'Error signing out',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}