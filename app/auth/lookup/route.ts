// app/api/auth/lookup/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { Database } from '@/types/supabase';

export async function POST(request: Request) {
  const { email } = await request.json();
  const supabase = createRouteHandlerClient<Database>({ cookies });

  try {
    const { data: adminData } = await supabase.auth.admin.listUsers();
    const existingUser = adminData?.users?.find(u => u.email === email);

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ userId: existingUser.id });
  } catch (error) {
    console.error('Auth lookup error:', error);
    return NextResponse.json({ error: 'Failed to lookup user' }, { status: 500 });
  }
}