// app/auth/callback/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from '@/types/supabase'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const redirectTo = requestUrl.searchParams.get('redirect_to')
  const deviceType = requestUrl.searchParams.get('device') // Get device type

  if (!code) {
    return NextResponse.redirect(new URL('/auth?error=No code provided', requestUrl))
  }

  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient<Database>({
      cookies: () => cookieStore
    })

    // Try to capture auth state before exchange
    let preExchangeSession = null;
    try {
      const { data } = await supabase.auth.getSession();
      preExchangeSession = data.session;
    } catch (e) {
      console.log('Pre-exchange session check failed:', e);
    }

    // Exchange code for session
    const { error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)
    if (exchangeError) {
      // Log detailed error for debugging
      console.error('Code exchange error:', {
        error: exchangeError.message,
        deviceType,
        hasExistingSession: !!preExchangeSession?.user?.id,
        timestamp: new Date().toISOString(),
        url: request.url
      })

      // If we already have a session despite the exchange error, we might still be authenticated
      if (preExchangeSession?.user?.id) {
        console.log('Exchange failed but existing session found, proceeding with current session');
        // Continue with existing session
      } else {
        return NextResponse.redirect(
          new URL(`/auth?error=${encodeURIComponent(exchangeError.message)}`, requestUrl)
        )
      }
    }

    // Get the current session (either new or existing)
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.error('Session retrieval error:', sessionError);
      throw sessionError;
    }

    if (!session) {
      console.error('No session established after code exchange');
      throw new Error('No session established');
    }

    try {
      // Use maybeSingle instead of single to avoid errors when no role is found
      const { error: roleError } = await supabase
        .from('user_roles')
        .select('role_id')
        .eq('auth_user_id', session.user.id)
        .maybeSingle()

      if (roleError) {
        console.error('Role check error:', roleError, 'User ID:', session.user.id)
        return NextResponse.redirect(new URL(redirectTo || '/user/front-page', requestUrl))
      }

      // If we have a specific redirect from the auth request, prioritize that
      if (redirectTo) {
        return NextResponse.redirect(new URL(redirectTo, requestUrl))
      }

      // Always redirect to front-page, which will handle the routing logic
      return NextResponse.redirect(new URL('/user/front-page', requestUrl))
    } catch (dbError) {
      // If there's any database error, still allow the user to log in
      console.error('Database error during role check:', dbError)
      return NextResponse.redirect(new URL('/user/front-page', requestUrl))
    }
  } catch (error) {
    console.error('Auth callback error:', error)
    // Create a more detailed error message for mobile devices
    const errorMsg = deviceType ?
      `Authentication failed on ${deviceType}. Please try again or use a different browser.` :
      'Authentication failed. Please try again.';

    return NextResponse.redirect(new URL(`/auth?error=${encodeURIComponent(errorMsg)}`, requestUrl))
  }
}
