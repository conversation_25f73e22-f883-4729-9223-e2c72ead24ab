// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from '@/types/supabase'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const path = req.nextUrl.pathname
  
  // Skip middleware for callback routes to avoid interference with the auth flow
  if (path.includes('/auth/callback')) {
    return res
  }

  try {
    // Correctly create middleware client
    const supabase = createMiddlewareClient<Database>({ req, res })
    
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Middleware session error:', error)
      // Handle auth errors gracefully
      if (path.startsWith('/user') || path.startsWith('/admin')) {
        const redirectUrl = new URL('/auth', req.url)
        redirectUrl.searchParams.set('redirect_to', path)
        redirectUrl.searchParams.set('error', 'Session expired. Please sign in again.')
        return NextResponse.redirect(redirectUrl)
      }
    }

    // Simple auth check for all protected routes
    if (path.startsWith('/user') || path.startsWith('/admin')) {
      if (!session) {
        const redirectUrl = new URL('/auth', req.url)
        redirectUrl.searchParams.set('redirect_to', path)
        return NextResponse.redirect(redirectUrl)
      }
      
      // Only perform role check for admin routes
      if (path.startsWith('/admin') && session) {
        try {
          const { data: roleData, error: roleError } = await supabase
            .from('user_roles')
            .select('role_id')
            .eq('auth_user_id', session.user.id)
            .maybeSingle()
            
          const isAdmin = roleData?.role_id === 1
          
          if (!isAdmin || roleError) {
            // Not an admin or error occurred, redirect to user section
            console.log('Admin access denied:', session.user.email)
            return NextResponse.redirect(new URL('/user/front-page', req.url))
          }
        } catch (error) {
          console.error('Admin role check error:', error)
          // Any role check error means access is denied to admin section
          return NextResponse.redirect(new URL('/user/front-page', req.url))
        }
      }
    }

    return res
  } catch (error) {
    console.error('Middleware error:', error)
    // Continue the request even if there's an auth error
    // This prevents the entire site from breaking if there's an auth issue
    return res
  }
}

export const config = {
  matcher: [
    '/auth',
    '/auth/callback',
    '/user/:path*',
    '/admin/:path*'
  ]
}