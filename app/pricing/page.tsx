
'use client'

import { useRouter } from 'next/navigation'
import { CheckIcon } from 'lucide-react'

interface PricingOption {
  title: string
  slug: string
  price: number
  originalPrice?: number
  description: string
  features: string[]
  duration?: string
  checkoutAmount: number
  highlight?: boolean
}

interface PricingData {
  subscriptions: PricingOption[]
  specialPrograms: PricingOption[]
  healthPrograms: PricingOption[]
}

// All pricing data
const pricingData: PricingData = {
  subscriptions: [
    {
      title: '8 Συνεδρίες',
      slug: '8-sessions-monthly',
      price: 50,
      description: 'Μηνιαίο πρόγραμμα',
      features: [
        '8 συνεδρίες το μήνα',
        'Ισχύς: 30 ημέρες',
        'Ιδανικό για αρχάριους',
      ],
      duration: '1 month',
      checkoutAmount: 5000,
    },
    {
      title: '12 Συνεδρίες',
      slug: '12-sessions-monthly',
      price: 60,
      description: 'Μηνιαίο πρόγραμμα',
      features: [
        '12 συνεδρίες το μήνα',
        'Ισχύς: 30 ημέρες',
        'Ο πιο δημοφιλής τύπος συνδρομής',
      ],
      duration: '1 month',
      checkoutAmount: 6000,
    },
    {
      title: 'Απεριόριστες Συνεδρίες',
      slug: 'unlimited-monthly',
      price: 70,
      description: 'Μηνιαίο πρόγραμμα',
      features: [
        'Απεριόριστες συνεδρίες',
        'Ισχύς: 30 ημέρες',
        'Για μέγιστα αποτελέσματα',
      ],
      duration: '1 month',
      checkoutAmount: 7000,
      highlight: true,
    },
    {
      title: 'Απεριόριστες Συνεδρίες',
      slug: 'unlimited-quarterly',
      price: 180,
      description: 'Τριμηνιαίο πρόγραμμα',
      features: [
        'Απεριόριστες συνεδρίες',
        'Ισχύς: 90 ημέρες',
        'Η καλύτερη τιμή μακροπρόθεσμα',
      ],
      duration: '3 months',
      checkoutAmount: 18000,
    },
  ],
  specialPrograms: [
    {
      title: 'LIFT GLUTES',
      slug: 'lift-glutes-12',
      price: 100,
      description: '12 συνεδρίες - 6 εβδομάδες',
      features: [
        '12 εξειδικευμένες προπονήσεις',
        '2 προπονήσεις την εβδομάδα',
        'Διάρκεια 6 εβδομάδες',
      ],
      checkoutAmount: 10000,
    },
    {
      title: 'LIFT GLUTES',
      slug: 'lift-glutes-1',
      price: 10,
      description: 'Μία συνεδρία',
      features: [
        '1 εξειδικευμένη προπόνηση',
      ],
      checkoutAmount: 1000,
    },
    {
      title: 'PUMP MY LIFT',
      slug: 'pump-my-lift-12',
      price: 100,
      description: '12 συνεδρίες - 6 εβδομάδες',
      features: [
        '12 εξειδικευμένες προπονήσεις',
        '2 προπονήσεις την εβδομάδα',
        'Διάρκεια 6 εβδομάδες',
      ],
      checkoutAmount: 10000,
      highlight: true,
    },
    {
      title: 'PUMP MY LIFT',
      slug: 'pump-my-lift-1',
      price: 10,
      description: 'Μία συνεδρία',
      features: [
        '1 εξειδικευμένη προπόνηση',
      ],
      checkoutAmount: 1000,
    },
  ],
  healthPrograms: [
    {
      title: 'LIFT YOUR HEALTH',
      slug: 'lift-your-health',
      price: 100,
      description: 'Εξειδικευμένο πρόγραμμα υγείας',
      features: [
        'Εξατομικευμένο πρόγραμμα',
        'Παρακολούθηση προόδου',
      ],
      checkoutAmount: 10000,
      highlight: true,
    },
    {
      title: 'LIFT MY BACK',
      slug: 'lift-my-back',
      price: 100,
      description: 'Εξειδικευμένο πρόγραμμα για την πλάτη',
      features: [
        'Εξατομικευμένο πρόγραμμα',
        'Παρακολούθηση προόδου',
      ],
      checkoutAmount: 10000,
    },
  ],
}

export default function PricingPage() {
  const router = useRouter()

  const handleProgramSelection = (option: PricingOption, programType: string) => {
    const formattedDescription = `${option.title} - ${option.duration || 'One-time payment'}`
    router.push(`/checkout?amount=${option.checkoutAmount}&description=${encodeURIComponent(formattedDescription)}&programType=${programType}`)
  }

  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-indigo-600">Pricing</h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Επιλέξτε το πρόγραμμά σας
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Βρείτε το ιδανικό πρόγραμμα που ταιριάζει στους στόχους σας
          </p>
        </div>
        
        {/* Monthly Subscriptions */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Μηνιαίες Συνδρομές</h3>
          <div className="isolate mx-auto grid max-w-7xl grid-cols-1 gap-8 lg:grid-cols-4">
            {pricingData.subscriptions.map((option) => (
              <PricingCard 
                key={option.slug} 
                option={option} 
                onSubscribe={() => handleProgramSelection(option, 'monthly')}
              />
            ))}
          </div>
        </div>
        
        {/* Special Programs */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Ειδικά Προγράμματα</h3>
          <div className="isolate mx-auto grid max-w-7xl grid-cols-1 gap-8 lg:grid-cols-4">
            {pricingData.specialPrograms.map((option) => (
              <PricingCard 
                key={option.slug} 
                option={option} 
                onSubscribe={() => handleProgramSelection(option, 'special')}
              />
            ))}
          </div>
        </div>
        
        {/* Health Programs */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Προγράμματα Υγείας</h3>
          <div className="isolate mx-auto grid max-w-7xl grid-cols-1 gap-8 lg:grid-cols-4">
            {pricingData.healthPrograms.map((option) => (
              <PricingCard 
                key={option.slug} 
                option={option} 
                onSubscribe={() => handleProgramSelection(option, 'health')}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function PricingCard({ 
  option, 
  onSubscribe 
}: { 
  option: PricingOption
  onSubscribe: () => void 
}) {
  return (
    <div className={`flex flex-col rounded-3xl p-6 ring-1 ring-gray-200 ${
      option.highlight 
        ? 'bg-indigo-600 ring-indigo-600 shadow-md' 
        : 'bg-white'
    }`}>
      <div className="flex flex-1 flex-col">
        <h3 className={`text-xl font-bold ${option.highlight ? 'text-white' : 'text-gray-900'}`}>
          {option.title}
        </h3>
        <p className={`mt-2 flex-1 text-sm ${option.highlight ? 'text-indigo-200' : 'text-gray-600'}`}>
          {option.description}
          {option.duration && (
            <span className="block mt-1 text-sm opacity-80">
              Διάρκεια: {option.duration === '1 month' ? '28 μέρες (4 εβδομάδες)' : '90 μέρες (12 εβδομάδες)'}
            </span>
          )}
        </p>
        
        <div className={`mt-4 ${option.highlight ? 'text-white' : 'text-gray-900'}`}>
          {option.originalPrice ? (
            <div className="flex items-baseline gap-2">
              <span className="text-4xl font-bold">{option.price}€</span>
              <span className={`text-xl line-through ${option.highlight ? 'text-indigo-200' : 'text-gray-500'}`}>
                {option.originalPrice}€
              </span>
            </div>
          ) : (
            <span className="text-4xl font-bold">{option.price}€</span>
          )}
        </div>
        
        <ul role="list" className={`mt-6 space-y-3 text-sm ${option.highlight ? 'text-indigo-100' : 'text-gray-600'}`}>
          {option.features.map((feature) => (
            <li key={feature} className="flex gap-x-3">
              <CheckIcon 
                className={`h-5 w-5 flex-none ${option.highlight ? 'text-indigo-200' : 'text-indigo-600'}`} 
                aria-hidden="true" 
              />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </div>
      
      <button
        onClick={onSubscribe}
        className={`mt-6 w-full rounded-md py-2.5 px-3.5 text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
          option.highlight
            ? 'bg-white text-indigo-600 hover:bg-indigo-50 focus-visible:outline-white'
            : 'bg-indigo-600 text-white hover:bg-indigo-500 focus-visible:outline-indigo-600'
        }`}
      >
        Εγγραφή Τώρα
      </button>
    </div>
  )
}
