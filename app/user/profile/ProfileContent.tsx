"use client";

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { format, parseISO } from 'date-fns';
import { useRouter } from 'next/navigation';
import ReactMarkdown from 'react-markdown';
import { startOfMonth, endOfMonth } from 'date-fns';

// Extract Json type from Database type
type Json = Database['public']['Tables']['notifications']['Row']['metadata'];

// For database row types
type PelatesRow = Database['public']['Tables']['pelates']['Row']
type CheckInsRow = Database['public']['Tables']['check_ins']['Row']
type ActiveSubscriptionsRow = Database['public']['Views']['active_subscriptions']['Row']
type UserCheckinStats = Database['public']['Views']['user_checkin_stats']['Row']
type PliromesRow = Database['public']['Tables']['pliromes']['Row']

// Fixed interface extending CheckInsRow
interface Checkin extends CheckInsRow {
  sessions?: { 
    start_time: string | null;
    program_id: string;
    program?: {
      name: string | null;
    } | null;
  } | null;
  session_start_time?: string;
}

// Payment interface
interface PaymentWithProgram extends PliromesRow {
  programs?: {
    name: string | null;
  } | null;
}

// Wod types
type UserWodRow = {
  check_in_id: string | null;
  created_at: string | null;
  id: string;
  wod_id: string | null;
  wod?: {
    id: string;
    date: string;
    content: string;
    exercises: string[] | null;
    warmup: string | null;
    is_published: boolean | null;
    published_at: string | null;
    created_at: string | null;
  } | null;
  check_ins?: {
    check_in_time: string | null;
  } | null;
}

type WodRow = {
  id: string;
  content: string;
  created_at: string | null;
  published_at: string | null;
  is_published: boolean | null;
  date: string;
  warmup: string | null;
  exercises: string[] | null;
}

type ProfileWithSettings = PelatesRow & {
  notifications?: {
    id: string;
    message: string;
    type: string | null;
    metadata: Json | null;
    created_at: string;
    read: boolean;
    expires_at: string | null;
    link: string | null;
    // Note: client_id is missing in the actual data
  }[];
  onesignal_subscriptions?: {
    id: string;
    player_id: string;
    enabled: boolean;
    created_at: string;
    updated_at: string;
  }[];
};

export default function ProfileContent({ userId }: { userId: string }) {
  const [profile, setProfile] = useState<ProfileWithSettings | null>(null);
  const [activeSubscription, setActiveSubscription] = useState<ActiveSubscriptionsRow | null>(null);
  const [checkinStats, setCheckinStats] = useState<UserCheckinStats | null>(null);
  const [checkins, setCheckins] = useState<Checkin[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [selectedCheckin, setSelectedCheckin] = useState<Checkin | null>(null);
  const [payments, setPayments] = useState<PaymentWithProgram[]>([]);
  const [allWods, setAllWods] = useState<WodRow[]>([]);
  const [displayMonth, setDisplayMonth] = useState(new Date());
  const [selectedWod, setSelectedWod] = useState<UserWodRow['wod'] | null>(null);
  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  const isInSelectedMonth = (dateStr: string | null) => {
    if (!dateStr) return false;
    const date = new Date(dateStr);
    return date.getMonth() === displayMonth.getMonth() && 
           date.getFullYear() === displayMonth.getFullYear();
  };

  useEffect(() => {
    async function fetchData() {
      try {
        setError(null);
    
        // First get the auth session to get the email
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) throw sessionError;
        if (!session?.user?.email) {
          setError('No authenticated user found');
          return;
        }
      
        // Then fetch user profile using email
        const { data: profileData, error: profileError } = await supabase
          .from('pelates')
          .select(`
            *,
            email_consent,
            notification_consent,
            onesignal_subscriptions!left (
              id,
              player_id,
              enabled,
              created_at,
              updated_at
            ),
            notifications!left (
              id,
              message,
              type,
              metadata,
              created_at,
              read,
              expires_at,
              link
            )
          `)
          .eq('email', session.user.email)
          .maybeSingle();
    
        if (profileError) throw profileError;
        if (!profileData) {
          setError('Profile not found');
          return;
        }

        // Type assertion to ensure the structure matches our ProfileWithSettings type
        const typedProfile: ProfileWithSettings = {
          ...profileData,
          notifications: profileData.notifications || [],
          onesignal_subscriptions: profileData.onesignal_subscriptions || []
        };
        
        setProfile(typedProfile);
    
        const pelatesId = profileData.id;

        const { data: statsData, error: statsError } = await supabase
          .from('user_checkin_stats')
          .select('*')
          .eq('pelatis_id', pelatesId)
          .maybeSingle();
  
        if (statsError) throw statsError;
        setCheckinStats(statsData);
    
        const { data: wodsData, error: wodsError } = await supabase
          .from('wod') // the table name is 'wod' not 'wods'
          .select('*')  // remove the incorrect type annotation
          .gte('date', format(startOfMonth(displayMonth), 'yyyy-MM-dd'))
          .lte('date', format(endOfMonth(displayMonth), 'yyyy-MM-dd'))
          .eq('is_published', true)
          .order('date', { ascending: false });
      
        if (wodsError) throw wodsError;
        setAllWods(wodsData || []);
    
        // Active subscription
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('active_subscriptions')
          .select('*')
          .eq('client_id', pelatesId)
          .maybeSingle();
    
        if (subscriptionError && subscriptionError.code !== 'PGRST116') {
          throw subscriptionError;
        }
        setActiveSubscription(subscriptionData);
    
        // Updated payments query to use pelatesId
        const { data: paymentsData, error: paymentsError } = await supabase
          .from('pliromes')
          .select(`
            *,
            programs:course (
              name
            )
          `)
          .eq('pelates_id', pelatesId)
          .order('date_money_gave', { ascending: false });
    
        if (paymentsError) {
          console.error('Payments error:', paymentsError);
          throw paymentsError;
        }
        setPayments(paymentsData);
    
        // Get detailed check-ins with session info
        const { data: detailedCheckinsData, error: detailedCheckinsError } = await supabase
          .from('check_ins')
          .select(`
            *,
            sessions (
              start_time,
              program_id,
              program:programs (
                name
              )
            )
          `)
          .eq('pelatis_id', pelatesId)
          .order('check_in_time', { ascending: false });

        if (detailedCheckinsError) throw detailedCheckinsError;
        setCheckins(detailedCheckinsData);
    
      } catch (error) {
        console.error('Error fetching data:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      }
    }
    
    fetchData();
  }, [supabase, userId, displayMonth]);

  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '-';
    return format(parseISO(dateString), 'dd/MM/yyyy');
  };

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <p className="text-red-500">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <h2 className="text-xl font-bold">Personal Information</h2>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/user/profile/edit')}
            >
              Edit Profile
            </Button>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><span className="font-semibold">Name:</span> {profile?.name} {profile?.last_name}</p>
            <p><span className="font-semibold">Email:</span> {profile?.email || '-'}</p>
            <p><span className="font-semibold">Phone:</span> {profile?.phone || '-'}</p>
            <p><span className="font-semibold">Instagram:</span> {profile?.instagram || '-'}</p>
          </CardContent>
        </Card>

        {/* Membership Information */}
        <Card >
          <CardHeader>
            <h2 className="text-xl font-bold">Membership Status</h2>
          </CardHeader>
          <CardContent className="space-y-2">
            <p>
              <span className="font-semibold">Status:</span>
              <span className={`ml-2 px-2 py-1 rounded ${
                activeSubscription?.subscription_status === 'Active' 
                ? 'bg-green-500' 
                : activeSubscription?.subscription_status === 'In Grace Period'
                ? 'bg-yellow-500'
                : 'bg-red-500'
              } text-white`}>
                {activeSubscription?.subscription_status || 'Inactive'}
              </span>
            </p>
            <p><span className="font-semibold">Program:</span> {activeSubscription?.program_name_display || '-'}</p>
            <p><span className="font-semibold">Expires:</span> {formatDate(activeSubscription?.end_date)}</p>
            <p><span className="font-semibold">Days until expiration:</span> {activeSubscription?.days_until_expiration || 0}</p>
          </CardContent>
        </Card>
      </div>

      {/* Check-in Statistics */}
      <Card className="mt-6">
        <CardHeader>
          <h2 className="text-xl font-bold">Check-in Statistics</h2>
        </CardHeader>
        <CardContent className="space-y-2">
        <p><span className="font-semibold">Total Check-ins:</span> {checkinStats?.total_checkins || 0}</p>
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card className="mt-6">
        <CardHeader>
          <h2 className="text-xl font-bold">Payment History</h2>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {payments.length === 0 ? (
              <p>No payment history available</p>
            ) : (
              <div className="grid gap-4">
                {payments.map((payment) => (
                  <Card key={payment.id} className="p-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div>
                        <p className="font-semibold">Date</p>
                        <p>{formatDate(payment.date_money_gave)}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Amount</p>
                        <p>{payment.money_gave}€</p>
                      </div>
                      <div>
                        <p className="font-semibold">Program</p>
                        <p>{payment.programs?.name || payment.course}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Payment Method</p>
                        <p>{payment.way_of_payment || '-'}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period</p>
                        <p>{formatDate(payment.start_program)} - {formatDate(payment.end_date)}</p>
                      </div>
                      {payment.receipt && (
                        <div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(payment.receipt!, '_blank')}
                          >
                            View Receipt
                          </Button>
                        </div>
                      )}
                      {payment.debt && payment.debt > 0 && (
                        <div className="col-span-2 md:col-span-3">
                          <p className="text-red-500">
                            <span className="font-semibold">Outstanding Balance:</span> {payment.debt}€
                          </p>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Training History Section */}
      <Card className="mt-6">
        <CardHeader>
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Training History</h2>
            <div className="flex items-center gap-2">
              <Button 
                onClick={() => setDisplayMonth(new Date(displayMonth.setMonth(displayMonth.getMonth() - 1)))} 
                variant="outline" 
                size="sm"
              >
                ←
              </Button>
              <span className="font-medium">
                {format(displayMonth, 'MMMM yyyy')}
              </span>
              <Button 
                onClick={() => setDisplayMonth(new Date(displayMonth.setMonth(displayMonth.getMonth() + 1)))} 
                variant="outline" 
                size="sm"
              >
                →
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {checkins
              .filter(checkin => isInSelectedMonth(checkin.check_in_time))
              .map((checkin, index) => {
                const checkInDate = checkin.check_in_time ? format(parseISO(checkin.check_in_time), 'yyyy-MM-dd') : '';
                const wodForDate = allWods.find(wod => wod.date === checkInDate);
                
                return (
                  <Card key={index} className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-semibold">
                          {checkin.check_in_time ? format(parseISO(checkin.check_in_time), 'dd/MM/yyyy HH:mm') : 'Unknown date'}
                        </p>
                        <p className="text-sm text-gray-600">
                          {checkin.sessions?.program?.name || 'General Training'}
                        </p>
                      </div>
                      {wodForDate && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedWod(wodForDate)}
                        >
                          WOD
                        </Button>
                      )}
                    </div>
                  </Card>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Add WOD Dialog */}
      {selectedWod && (
        <Dialog open={!!selectedWod} onOpenChange={() => setSelectedWod(null)}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Workout of the Day - {formatDate(selectedWod.date)}</DialogTitle>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Workout</h3>
                <ReactMarkdown className="prose dark:prose-invert">
                  {selectedWod.content}
                </ReactMarkdown>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setSelectedWod(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      
      {/* Check-in Dialog */}
      {selectedCheckin && (
        <Dialog open={!!selectedCheckin} onOpenChange={() => setSelectedCheckin(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Check-in Details</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p><span className="font-semibold">Date:</span> {formatDate(selectedCheckin.check_in_time)}</p>
              {selectedCheckin.sessions?.start_time && (
                <p><span className="font-semibold">Session Time:</span> {format(parseISO(selectedCheckin.sessions.start_time), 'HH:mm')}</p>
              )}
            </div>
            <DialogFooter>
              <Button onClick={() => setSelectedCheckin(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}













