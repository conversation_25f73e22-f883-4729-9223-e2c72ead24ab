'use client';

import { useState, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useRouter } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SignaturePad from 'react-signature-canvas';
import { isValidPhoneNumber, AsYouType } from 'libphonenumber-js';
import Image from 'next/image';
import { format, parse } from 'date-fns';
import { useOneSignal } from '@/contexts/OneSignalContext';
import waiverTextGreek from '@/utils/waiverText';

type Profile = Database['public']['Tables']['pelates']['Row'];

interface ProfileEditFormProps {
  initialData: Profile;
}

interface FormData {
  name: string;
  last_name: string;
  email: string;
  phone: string;
  instagram: string;
  height: string;
  date_birth: string;
  sex: string;
  find_us: string;
  email_consent: boolean;
  notification_consent: boolean;
}

interface ValidationErrors {
  [key: string]: string | undefined;
  consent?: string;
}

// Enums for select options
const FIND_US_OPTIONS = [
  { value: 'instagram', label: 'Instagram' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'google', label: 'Google Search' },
  { value: 'friend', label: 'Friend Referral' },
  { value: 'other', label: 'Other' },
] as const;

const SEX_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
] as const;


const formatDateForDisplay = (isoDate: string | null): string => {
  if (!isoDate) return '';
  try {
    return format(new Date(isoDate), 'dd/MM/yyyy');
  } catch {
    return '';
  }
};



const formatDateForInput = (displayDate: string): string | null => {
  if (!displayDate) return null;
  try {
    // If already in ISO format, just return it
    if (displayDate.includes('T')) return displayDate;

    const parsedDate = parse(displayDate, 'dd/MM/yyyy', new Date());
    return parsedDate.toISOString();
  } catch (e) {
    console.error('Date format error:', e);
    return null;
  }
};

export default function ProfileEditForm({ initialData }: ProfileEditFormProps) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [showWaiverDialog, setShowWaiverDialog] = useState<boolean>(false);
  const signatureRef = useRef<SignaturePad>(null);
  const [signatureImage, setSignatureImage] = useState<string | null>(initialData?.waiver_signature || null);
  const [incompleteFields, setIncompleteFields] = useState<string[]>([]);
  const {  isSubscribed, requestSubscription, updateSubscriptionStatus } = useOneSignal();

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  const [formData, setFormData] = useState<FormData>({
    name: initialData?.name || '',
    last_name: (initialData?.last_name || '').toUpperCase(),
    email: initialData?.email || '',
    phone: initialData?.phone || '+30',
    instagram: initialData?.instagram || '',
    height: initialData?.height?.toString() || '',
    date_birth: initialData?.date_birth
    ? formatDateForDisplay(initialData.date_birth)
    : '',
    sex: initialData?.sex || '',
    find_us: initialData?.find_us || '',
    email_consent: initialData?.email_consent || false,
    notification_consent: initialData?.notification_consent || false,
  });


  const supabase = createClientComponentClient<Database>();

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      setSignatureImage(null);
    }
  };
  const formatPhoneNumber = (value: string): string => {
    try {
      const cleanedValue = value.replace(/[^\d+]/g, '');
      const asYouType = new AsYouType();
      return asYouType.input(cleanedValue);
    } catch (error) {
      return value;
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhoneNumber = (phone: string): boolean => {
    try {
      return isValidPhoneNumber(phone);
    } catch (error) {
      return false;
    }
  };

  const handleSaveSignature = () => {
    if (!signatureRef.current || signatureRef.current.isEmpty()) {
      setError('Please provide your signature');
      return;
    }

    try {
      const signatureData = signatureRef.current.getTrimmedCanvas().toDataURL('image/png');
      setSignatureImage(signatureData);
      setShowWaiverDialog(false);
      setError(null);

      // Remove the waiver error if it exists
      if (validationErrors.waiver) {
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.waiver;
          return newErrors;
        });
      }
    } catch (err) {
      console.error('Error saving signature:', err);
      setError('Error saving signature. Please try again.');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    let processedValue = value;

    if (name === 'last_name') {
      processedValue = value.toUpperCase();
    }
    if (name === 'phone') {
      processedValue = value.startsWith('+') ? value : '+30' + value.replace(/^\+30/, '');
      processedValue = formatPhoneNumber(processedValue);
    }
    if (name === 'date_birth' && value) {
      processedValue = formatDateForDisplay(value);
    }

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : processedValue
    }));

    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (name: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};
    const currentIncompleteFields: string[] = [];

    // Validation logic remains the same, but now with type-safe access
    if (!formData.name.trim()) errors.name = 'Name is required';
    if (!formData.last_name.trim()) errors.last_name = 'Last name is required';
    if (!formData.email.trim() || !validateEmail(formData.email)) {
      errors.email = 'Valid email is required';
    }
    if (!formData.phone.trim() || !validatePhoneNumber(formData.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }
    if (!formData.height.trim() || isNaN(Number(formData.height)) || Number(formData.height) <= 0) {
      errors.height = 'Valid height in cm is required';
    }
    if (!formData.date_birth) errors.date_birth = 'Date of birth is required';
    if (!formData.sex) errors.sex = 'Sex is required';
    if (!formData.find_us) errors.find_us = 'This field is required';

    // Waiver validation
    if (!signatureImage) {
      errors.waiver = 'Waiver signature is required';
      currentIncompleteFields.push('Waiver');
    }

    // Communication consent validation
    if (!formData.email_consent || !formData.notification_consent) {
      errors.consent = 'Please review communication preferences';
      currentIncompleteFields.push('Communication Preferences');
    }

    setValidationErrors(errors);
    setIncompleteFields(currentIncompleteFields);

    if (Object.keys(errors).length > 0) {
      const firstIncompleteField = document.querySelector(`[data-field="${currentIncompleteFields[0]}"]`);
      if (firstIncompleteField) {
        firstIncompleteField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }

    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      const firstIncompleteField = document.querySelector(`[data-field="${incompleteFields[0]}"]`);
      if (firstIncompleteField) {
        firstIncompleteField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      setError('Please fix the validation errors before submitting');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get the current authenticated user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // First get the pelatis record
      const { data: pelatis, error: pelatesError } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', user.id)
        .single();

      if (pelatesError) throw new Error(`Failed to get pelatis: ${pelatesError.message}`);
      if (!pelatis) throw new Error('Pelatis record not found');

      const heightNum = formData.height ? parseFloat(formData.height) : null;
      const dateOfBirth = formData.date_birth ? formatDateForInput(formData.date_birth) : null;

      // Now update using the pelatis id
      const { error: updateError } = await supabase
        .from('pelates')
        .update({
          name: formData.name,
          last_name: formData.last_name,
          phone: formData.phone,
          email: formData.email,
          instagram: formData.instagram,
          height: heightNum,
          date_birth: dateOfBirth,
          sex: formData.sex,
          find_us: formData.find_us,
          email_consent: formData.email_consent,
          notification_consent: formData.notification_consent,
          waiver_signed: !!signatureImage,
          waiver_signature: signatureImage,
          waiver_signed_date: signatureImage ? new Date().toISOString() : null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', pelatis.id) // Use pelatis.id instead of auth_user_id
        .select();

      if (updateError) throw new Error(`Update failed: ${updateError.message}`);

      setSuccess(true);
      router.refresh();
    } catch (err) {
      console.error('Form submission error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during the update');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Incomplete Fields Summary */}
        {incompleteFields.length > 0 && (
          <div
            className="bg-yellow-50 border-l-4 border-yellow-400 p-4"
            role="alert"
          >
            <p className="font-bold">Please complete the following:</p>
            <ul className="list-disc list-inside">
              {incompleteFields.map(field => (
                <li key={field} className="text-yellow-700">{field}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className={validationErrors.name ? 'border-red-500' : ''}
            />
            {validationErrors.name && (
              <p className="text-red-500 text-sm">{validationErrors.name}</p>
            )}
          </div>

          {/* Last Name Field */}
          <div className="space-y-2">
            <Label htmlFor="last_name">Last Name *</Label>
            <Input
              id="last_name"
              name="last_name"
              value={formData.last_name}
              onChange={handleInputChange}
              required
              className={validationErrors.last_name ? 'border-red-500' : ''}
            />
            {validationErrors.last_name && (
              <p className="text-red-500 text-sm">{validationErrors.last_name}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className={validationErrors.email ? 'border-red-500' : ''}
            />
            {validationErrors.email && (
              <p className="text-red-500 text-sm">{validationErrors.email}</p>
            )}
          </div>

          {/* Phone Field */}
          <div className="space-y-2">
            <Label htmlFor="phone">Phone *</Label>
            <div className="relative">
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                placeholder="+30 XXX XXX XXXX"
                className={`pl-10 ${validationErrors.phone ? 'border-red-500' : ''}`}
              />
              <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                {!formData.phone.startsWith('+') && '+'}
              </div>
            </div>
            {validationErrors.phone && (
              <p className="text-red-500 text-sm">{validationErrors.phone}</p>
            )}
            <p className="text-xs text-gray-500">
              Default: Greece (+30). For other countries, include country code (e.g., +1 for US)
            </p>
          </div>

          {/* Height Field */}
          <div className="space-y-2">
            <Label htmlFor="height">Height (cm) *</Label>
            <Input
              id="height"
              name="height"
              type="number"
              value={formData.height}
              onChange={handleInputChange}
              required
              min="1"
              className={validationErrors.height ? 'border-red-500' : ''}
            />
            {validationErrors.height && (
              <p className="text-red-500 text-sm">{validationErrors.height}</p>
            )}
          </div>

          {/* Date of Birth Field */}
          <div className="space-y-2">
            <Label htmlFor="date_birth">Date of Birth * (DD/MM/YYYY)</Label>
            <Input
              id="date_birth"
              name="date_birth"
              type="text"
              placeholder="DD/MM/YYYY"
              value={formData.date_birth || ''}
              onChange={(e) => {
                const value = e.target.value;
                // Allow typing of numbers and forward slashes
                if (!/^[\d/]*$/.test(value)) return;

                // Auto-add slashes
                let formattedValue = value.replace(/[^0-9]/g, '');
                if (formattedValue.length > 2) {
                  formattedValue = formattedValue.slice(0, 2) + '/' + formattedValue.slice(2);
                }
                if (formattedValue.length > 5) {
                  formattedValue = formattedValue.slice(0, 5) + '/' + formattedValue.slice(5, 9);
                }

                setFormData(prev => ({
                  ...prev,
                  date_birth: formattedValue
                }));
              }}
              required
              className={validationErrors.date_birth ? 'border-red-500' : ''}
            />
            {validationErrors.date_birth && (
              <p className="text-red-500 text-sm">{validationErrors.date_birth}</p>
            )}
          </div>

          {/* Sex Select Field */}
          <div className="space-y-2">
            <Label>Sex *</Label>
            <Select
              value={formData.sex}
              onValueChange={(value) => handleSelectChange('sex', value)}
            >
              <SelectTrigger className={validationErrors.sex ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select sex" />
              </SelectTrigger>
              <SelectContent>
                {SEX_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.sex && (
              <p className="text-red-500 text-sm">{validationErrors.sex}</p>
            )}
          </div>

          {/* Find Us Select Field */}
          <div className="space-y-2">
            <Label>How did you find us? *</Label>
            <Select
              value={formData.find_us}
              onValueChange={(value) => handleSelectChange('find_us', value)}
            >
              <SelectTrigger className={validationErrors.find_us ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                {FIND_US_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.find_us && (
              <p className="text-red-500 text-sm">{validationErrors.find_us}</p>
            )}
          </div>

          {/* Instagram Field (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="instagram">Instagram</Label>
            <Input
              id="instagram"
              name="instagram"
              value={formData.instagram}
              onChange={handleInputChange}
              placeholder="@username (optional)"
            />
          </div>
        </div>

        {/* Waiver Section with Enhanced Guidance */}
        <div
          className={`space-y-4 border rounded-lg p-4 ${
            incompleteFields.includes('Waiver')
              ? 'bg-yellow-50 border-yellow-300'
              : 'bg-gray-50'
          }`}
          data-field="Waiver"
        >
          <h3 className="text-lg font-semibold flex items-center">
            Liability Waiver
            {incompleteFields.includes('Waiver') && (
              <span className="ml-2 text-yellow-600 font-bold">
                ⚠️ Requires Attention
              </span>
            )}
          </h3>

          {signatureImage ? (
            <div className="space-y-2">
              <p className="text-green-600">
                Waiver signed on {initialData?.waiver_signed_date &&
                  new Date(initialData.waiver_signed_date).toLocaleDateString()}
              </p>
              <div className="border h-20 relative">
                <Image
                  src={signatureImage}
                  alt="Signature"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowWaiverDialog(true)}
                >
                  Update Signature
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => {
                    setShowWaiverDialog(true);
                    if (signatureRef.current) {
                      signatureRef.current.fromDataURL(signatureImage);
                    }
                  }}
                >
                  View Waiver
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Button
                type="button"
                variant="destructive"
                onClick={() => setShowWaiverDialog(true)}
                className="w-full"
              >
                ⚠️ Sign Required Waiver
              </Button>
              {validationErrors.waiver && (
                <p className="text-red-500 text-sm">{validationErrors.waiver}</p>
              )}
            </div>
          )}
        </div>

        {/* Communication Preferences with Enhanced Guidance */}
        <div
          className={`space-y-4 border rounded-lg p-4 ${
            incompleteFields.includes('Communication Preferences')
              ? 'bg-yellow-50 border-yellow-300'
              : 'bg-gray-50'
          }`}
          data-field="Communication Preferences"
        >
          <h3 className="text-lg font-semibold flex items-center">
            Communication Preferences
            {incompleteFields.includes('Communication Preferences') && (
              <span className="ml-2 text-yellow-600 font-bold">
                ⚠️ Requires Attention
              </span>
            )}
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-semibold">Push Notifications</p>
                <p className="text-sm text-gray-600">
                  Receive instant updates about your sessions, payments, and new features
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {isSubscribed ? (
                  <>
                    <span className="text-green-500">Enabled</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateSubscriptionStatus(false)}
                    >
                      Disable
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={requestSubscription}
                    disabled={isSubscribed}
                  >
                    {isSubscribed ? 'Notifications Enabled' : 'Enable Push Notifications'}
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="email_consent"
                checked={formData.email_consent}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({
                    ...prev,
                    email_consent: checked as boolean
                  }))
                }
              />
              <Label htmlFor="email_consent" className="text-sm">
                I agree to receive email communications
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="notification_consent"
                checked={formData.notification_consent}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({
                    ...prev,
                    notification_consent: checked as boolean
                  }))
                }
              />
              <Label htmlFor="notification_consent" className="text-sm">
                I agree to receive notifications
              </Label>
            </div>

            {validationErrors.consent && (
              <p className="text-red-500 text-sm">{validationErrors.consent}</p>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button variant="outline" onClick={() => router.back()} type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Saving...' : 'Save'}
          </Button>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {success && (
          <Alert className="bg-green-50 text-green-700">
            <AlertDescription>Profile updated successfully!</AlertDescription>
          </Alert>
        )}
      </form>

      {/* Waiver Dialog */}
      <Dialog open={showWaiverDialog} onOpenChange={setShowWaiverDialog}>
  <DialogContent className="max-w-2xl">
    <DialogHeader>
      <DialogTitle>Liability Waiver</DialogTitle>
      <DialogDescription>
        Please read and sign the following waiver
      </DialogDescription>
    </DialogHeader>

    <div className="space-y-4">
      <div className="h-64 overflow-y-auto p-4 border rounded bg-gray-50 text-sm">
        {waiverTextGreek.split('\n\n').map((paragraph, index) => (
          <p key={index} className="mb-2">{paragraph}</p>
        ))}
      </div>

      <div className="space-y-2">
        <Label>Sign below</Label>
        <div className="border rounded bg-white">
          <SignaturePad
            ref={signatureRef}
            canvasProps={{
              className: 'signature-canvas w-full h-40'
            }}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={clearSignature} type="button">
            Clear
          </Button>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={() => setShowWaiverDialog(false)}>
          Cancel
        </Button>
        <Button onClick={handleSaveSignature}>
          Save Signature
        </Button>
      </div>
    </div>
  </DialogContent>
</Dialog>
    </>
  );
}




