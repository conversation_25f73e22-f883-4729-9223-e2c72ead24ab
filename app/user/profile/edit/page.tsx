import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import ProfileEditForm from './ProfileEditForm';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from 'lucide-react'; // Import the arrow icon

export default async function ProfileEditPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/auth/signin');
  }

  // Check if user has email
  if (!user.email) {
    return (
      <div className="min-h-screen bg-gray-100 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white shadow rounded-lg p-6">
            <h1 className="text-xl font-semibold text-gray-900 mb-4">Error</h1>
            <p className="text-red-600">Email is required to create a profile.</p>
          </div>
        </div>
      </div>
    );
  }

  // Try to get profile by user ID first
  const { data: profileById } = await supabase
    .from('pelates')
    .select('*')
    .eq('id', user.id)
    .single();


 // If no profile found by ID, try to find by email
 if (!profileById) {
  const { data: profileByEmail } = await supabase
    .from('pelates')
    .select('*')
    .eq('email', user.email)
    .single();

  if (profileByEmail) {
    // Your existing email profile logic
    if (profileByEmail.id !== user.id) {
      const { error: updateError } = await supabase
        .from('pelates')
        .update({ id: user.id })
        .eq('id', profileByEmail.id);

      if (updateError) {
        console.error('Error updating profile ID:', updateError);
      }
    }
      return (
        <div className="min-h-screen bg-gray-100 py-8">
          <div className="max-w-3xl mx-auto">
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200 flex items-center gap-4">
                <Link href="/user/profile">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                </Link>
                <h1 className="text-xl font-semibold text-gray-900">Edit Profile</h1>
              </div>
              <div className="p-6">
                <ProfileEditForm initialData={profileByEmail} />
              </div>
            </div>
          </div>
        </div>
      );
    }
  }

  // If we have a profile by ID, use that
  if (profileById) {
    return (
      <div className="min-h-screen bg-gray-100 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center gap-4">
              <Link href="/user/profile">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">Edit Profile</h1>
            </div>
            <div className="p-6">
              <ProfileEditForm initialData={profileById} />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If no profile exists at all, create one
  const { data: newProfile, error: createError } = await supabase
    .from('pelates')
    .insert([{
      id: user.id,
      email: user.email,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }])
    .select()
    .single();

    if (createError) {
      return (
        <div className="min-h-screen bg-gray-100 py-8">
          <div className="max-w-3xl mx-auto">
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200 flex items-center gap-4">
                <Link href="/user/profile">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                </Link>
                <h1 className="text-xl font-semibold text-gray-900">Error</h1>
              </div>
              <div className="p-6">
                <p className="text-red-600">{createError.message}</p>
              </div>
            </div>
          </div>
        </div>
      );
    }
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center gap-4">
            <Link href="/user/profile">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900">Create Profile</h1>
          </div>
          <div className="p-6">
            <ProfileEditForm initialData={newProfile} />
          </div>
        </div>
      </div>
    </div>
  );
}