// app/user/profile/onboarding/page.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import type { Database } from '@/types/supabase';
import OnboardingTypeform from '@/components/OnboardingTypeform';

export default function OnboardingPage() {
  const [loading, setLoading] = useState<boolean>(true);
  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      try {
        setLoading(true);
        
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !session?.user) {
          router.push('/auth');
          return;
        }
        
        const { data: profileData, error: profileError } = await supabase
          .from('pelates')
          .select('height, date_birth, sex, name, last_name, phone, email')
          .eq('auth_user_id', session.user.id)
          .maybeSingle();
        
        // Check if profile is complete
        if (!profileError && 
            profileData?.height && 
            profileData?.date_birth && 
            profileData?.sex &&
            profileData?.name &&
            profileData?.last_name &&
            profileData?.phone &&
            profileData?.email) {
          router.push('/user/profile');
          return;
        }
        
      } catch (error) {
        console.error('Error checking auth:', error);
      } finally {
        setLoading(false);
      }
    }
    
    checkAuth();
  }, [supabase, router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return <OnboardingTypeform />;
}
