// app/user/profile/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import type { Database } from '@/types/supabase'
import { Card, CardContent } from "@/components/ui/card"
import ProfileContent from './ProfileContent'

export default async function ProfilePage() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    redirect('/auth')
  }

  const { data: profile } = await supabase
    .from('pelates')
    .select('*')
    .eq('auth_user_id', session.user.id)
    .maybeSingle()

  if (!profile) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6 space-y-4">
            <h1 className="text-2xl font-bold">Profile Not Found</h1>
            <p className="text-center text-gray-600">
              Please contact the gym staff to link your account to your profile.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Check for required fields
  const hasRequiredFields = 
    profile.height && 
    profile.date_birth && 
    profile.sex &&
    profile.name &&
    profile.last_name &&
    profile.phone &&
    profile.email;

  // Redirect to onboarding if any required field is missing
  if (!hasRequiredFields) {
    redirect('/user/profile/onboarding')
  }

  return <ProfileContent userId={session.user.id} />
}
