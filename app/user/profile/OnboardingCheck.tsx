// app/user/profile/OnboardingCheck.tsx
import { useRouter } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface OnboardingCheckProps {
  missingFields: string[]
}

export default function OnboardingCheck({ missingFields }: OnboardingCheckProps) {
  const router = useRouter()

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6 space-y-4">
          <h1 className="text-2xl font-bold">Complete Your Profile</h1>
          <div className="text-center space-y-2">
            <p className="text-gray-600">
              Please complete your profile by providing the following information:
            </p>
            <ul className="list-disc list-inside text-left">
              {missingFields.map((field) => (
                <li key={field} className="text-gray-700">{field}</li>
              ))}
            </ul>
          </div>
          <Button 
            onClick={() => router.push('/user/profile/onboarding')}
            className="mt-4"
          >
            Complete Profile
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
