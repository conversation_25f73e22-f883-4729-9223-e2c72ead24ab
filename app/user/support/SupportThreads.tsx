'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/hooks/useSupabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import type { Database } from '@/types/supabase';

type Thread = Database['public']['Tables']['support_threads']['Row'] & {
  support_messages: Array<
    Database['public']['Tables']['support_messages']['Row'] & {
      sender: {
        name: string | null;
        last_name: string | null;
      } | null;
    }
  >;
};

interface SupportThreadsProps {
  initialThreads: Thread[] | null;
  clientId: string;
}

// Define getStatusColor function
function getStatusColor(status: Database['public']['Enums']['message_status'] | null): string {
  switch (status) {
    case 'open':
      return 'bg-blue-500';
    case 'in_progress':
      return 'bg-yellow-500';
    case 'resolved':
      return 'bg-green-500';
    case 'closed':
      return 'bg-gray-500';
    case null:
      return 'bg-gray-300';
    default:
      return 'bg-gray-300';
  }
}

export function SupportThreads({ initialThreads, clientId }: SupportThreadsProps) {
  const [threads, setThreads] = useState<Thread[]>(initialThreads || []);
  const router = useRouter();
  const { supabase } = useSupabase();

  useEffect(() => {
    const channel = supabase
      .channel('support_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'support_threads',
          filter: `client_id=eq.${clientId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setThreads(prev => [payload.new as Thread, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setThreads(prev => 
              prev.map(thread => 
                thread.id === payload.new.id 
                  ? { ...thread, ...payload.new } 
                  : thread
              )
            );
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase, clientId]);

  return (
    <div className="space-y-4">
      {threads.map((thread) => (
        <Card 
          key={thread.id}
          onClick={() => router.push(`/support/${thread.id}`)}
          className="cursor-pointer hover:bg-gray-50"
        >
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">{thread.title}</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{thread.category}</Badge>
              <div className={`px-2 py-1 rounded-full text-xs text-white ${getStatusColor(thread.status)}`}>
                {thread.status}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-500">
              {thread.support_messages?.[0]?.content.slice(0, 100)}
              {thread.support_messages?.[0]?.content.length > 100 ? '...' : ''}
            </div>
            <div className="text-xs text-gray-400 mt-2">
              Created {formatDistanceToNow(new Date(thread.created_at || ''), { addSuffix: true })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}