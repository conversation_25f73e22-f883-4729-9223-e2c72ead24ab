// app/user/support/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { getServerUser } from '@/utils/auth.server';
import { CreateThreadButton } from './CreateThreadButton';
import { SupportTable } from './SupportTable';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function SupportPage() {
  const { clientId } = await getServerUser();
  const supabase = createServerComponentClient<Database>({ cookies });

  const { data: threads } = await supabase
    .from('support_threads')
    .select(`
      *,
      support_messages (
        *,
        sender:pelates!support_messages_sender_id_fkey (
          name,
          last_name
        )
      )
    `)
    .eq('client_id', clientId)
    .order('created_at', { ascending: false });

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Support Tickets</h1>
        <CreateThreadButton clientId={clientId} />
      </div>

      <SupportTable threads={threads} />
    </div>
  );
}