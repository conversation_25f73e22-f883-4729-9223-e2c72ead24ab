'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useSupabase } from '@/hooks/useSupabase';
import { useToast } from "@/hooks/use-toast";
import { Plus, Loader2 } from 'lucide-react';
import type { Database } from '@/types/supabase';
import { PostgrestError } from '@supabase/supabase-js';

const CATEGORIES = [
  { value: 'injury' as const, label: 'Injury/Pain' },
  { value: 'nutrition' as const, label: 'Nutrition' },
  { value: 'training' as const, label: 'Training' },
  { value: 'general' as const, label: 'General' },
] satisfies { value: Database['public']['Enums']['message_category']; label: string }[];

interface CreateThreadButtonProps {
  clientId: string;
}

// Type guard function
function isPostgrestError(error: unknown): error is PostgrestError {
  return error !== null && 
         typeof error === 'object' && 
         'message' in error && 
         'details' in error && 
         'hint' in error;
}

export function CreateThreadButton({ clientId }: CreateThreadButtonProps) {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    category: '' as Database['public']['Enums']['message_category'],
    message: '',
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate form data
      if (!formData.category) {
        throw new Error('Please select a category');
      }

      
      // Start a transaction using .single() to ensure we get the thread back
      const { data: thread, error: threadError } = await supabase
        .from('support_threads')
        .insert({
          title: formData.title,
          category: formData.category,
          client_id: clientId,
          priority: 'medium',
          status: 'open'
        })
        .select()
        .single();

      if (threadError) throw threadError;

      // Now create the message
      const { error: messageError } = await supabase
        .from('support_messages')
        .insert({
          thread_id: thread.id,
          content: formData.message,
          is_coach_reply: false,
          sender_id: clientId // This matches the pelates.id
        });

      if (messageError) throw messageError;

      setOpen(false);
      router.push(`/support/${thread.id}`); // Navigate to the new thread
      
      toast({
        title: "Thread created",
        description: "Your support request has been sent to the coaches.",
      });

    } catch (error) {
      // Type-safe error handling
      const errorMessage = error instanceof Error 
      ? error.message 
      : isPostgrestError(error)
        ? error.message || 'Database error occurred'
        : 'An unexpected error occurred';

      console.error('Error creating thread:', error);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          New Thread
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Support Thread</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Title</label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Brief description of your issue"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <Select
              value={formData.category}
              onValueChange={(value: Database['public']['Enums']['message_category']) => 
                setFormData(prev => ({ ...prev, category: value }))
              }
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {CATEGORIES.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Message</label>
            <Textarea
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Describe your issue in detail"
              required
              className="min-h-[100px]"
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Create Thread'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}