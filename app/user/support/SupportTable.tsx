'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from 'date-fns';
import type { Database } from '@/types/supabase';

type Thread = Database['public']['Tables']['support_threads']['Row'] & {
  support_messages: Array<{
    sender: {
      name: string | null;
      last_name: string | null;
    } | null;
  }>;
};

interface SupportTableProps {
  threads: Thread[] | null;
}

function getStatusVariant(status: Database['public']['Enums']['message_status'] | null): 
  | "default" 
  | "destructive" 
  | "outline" 
  | "secondary" {
  switch (status) {
    case 'open':
      return 'default';
    case 'closed':
      return 'secondary';
    case 'in_progress':
      return 'outline';
    case 'resolved':
      return 'default';
    case null:
      return 'outline';
    default:
      return 'outline';
  }
}

function getStatusColor(status: Database['public']['Enums']['message_status'] | null): string {
  switch (status) {
    case 'open':
      return 'bg-blue-500';
    case 'in_progress':
      return 'bg-yellow-500';
    case 'resolved':
      return 'bg-green-500';
    case 'closed':
      return 'bg-gray-500';
    case null:
      return 'bg-gray-300';
    default:
      return 'bg-gray-300';
  }
}

export function SupportTable({ threads }: SupportTableProps) {
  const router = useRouter();

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Title</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Last Reply</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {threads?.map((thread) => (
          <TableRow 
            key={thread.id}
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => router.push(`/user/support/${thread.id}`)}
          >
            <TableCell>
              <Link 
                href={`/user/support/${thread.id}`}
                className="text-blue-600 hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {thread.title}
              </Link>
            </TableCell>
            <TableCell>
              <Badge variant="outline">{thread.category}</Badge>
            </TableCell>
            <TableCell>
              <Badge variant={getStatusVariant(thread.status)}>
                <div className={`px-2 py-1 rounded-full text-xs text-white ${getStatusColor(thread.status)}`}>
                  {thread.status}
                </div>
              </Badge>
            </TableCell>
            <TableCell>
              {format(new Date(thread.created_at || ''), 'PP')}
            </TableCell>
            <TableCell>
              {thread.last_reply_at ? 
                format(new Date(thread.last_reply_at), 'PP') : 
                'No replies'}
            </TableCell>
          </TableRow>
        ))}
        {!threads?.length && (
          <TableRow>
            <TableCell colSpan={5} className="text-center py-8 text-gray-500">
              No support tickets found. Create one to get started!
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}