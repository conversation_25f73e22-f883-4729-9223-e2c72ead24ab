// app/support/[threadId]/ReplyForm.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useSupabase } from '@/hooks/useSupabase'
import { useToast } from '@/hooks/use-toast'
import { Loader2 } from 'lucide-react'

export function ReplyForm({ 
  threadId, 
  clientId 
}: { 
  threadId: string
  clientId: string 
}) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()
  const [message, setMessage] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim()) return

    setLoading(true)
    try {
      const { error } = await supabase
        .from('support_messages')
        .insert({
          thread_id: threadId,
          content: message.trim(),
          sender_id: clientId,
          is_coach_reply: false
        })

      if (error) throw error

      // Update thread's last_reply_at
      await supabase
        .from('support_threads')
        .update({ 
          last_reply_at: new Date().toISOString(),
          status: 'open'
        })
        .eq('id', threadId)

      setMessage('')
      router.refresh()
      toast({ title: 'Reply sent successfully' })
    } catch (error) {
      console.error('Error sending reply:', error)
      toast({ 
        title: 'Error sending reply',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Type your reply..."
        className="min-h-[100px]"
        disabled={loading}
      />
      <Button type="submit" disabled={loading || !message.trim()}>
        {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Send Reply'}
      </Button>
    </form>
  )
}