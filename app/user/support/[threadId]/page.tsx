// app/support/[threadId]/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import { getServerUser } from '@/utils/auth.server';
import type { Database } from '@/types/supabase';
import { Card } from '@/components/ui/card';
import { ReplyForm } from './ReplyForm';

export const dynamic = 'force-dynamic';

async function getThread(threadId: string) {
  const supabase = createServerComponentClient<Database>({ cookies });

  const { data: thread } = await supabase
    .from('support_threads')
    .select(`
      *,
      support_messages (
        *,
        sender:pelates!support_messages_sender_id_fkey (
          name,
          last_name
        )
      )
    `)
    .eq('id', threadId)
    .single();

  if (!thread) {
    notFound();
  }

  return thread;
}

// Define a type for the bodyParts metadata
type BodyPart = {
  side: string;
  slug: string;
  painType: string;
  intensity: number;
};

// Define a type for the metadata
type Metadata = {
  duration?: string;
  bodyParts?: BodyPart[];
  painLevel?: number;
  reportedAt?: string;
  impactOnTraining?: string;
};

// Helper function to format metadata
function formatMetadata(metadata: Metadata) {
  if (metadata.bodyParts) {
    return (
      <div>
        <h4 className="text-sm font-medium text-gray-600">Body Parts:</h4>
        <ul className="mt-2 space-y-2">
          {metadata.bodyParts.map((bodyPart, index) => (
            <li key={index} className="text-sm text-gray-700">
              <strong>Side:</strong> {bodyPart.side}<br />
              <strong>Slug:</strong> {bodyPart.slug}<br />
              <strong>Pain Type:</strong> {bodyPart.painType}<br />
              <strong>Intensity:</strong> {bodyPart.intensity}
            </li>
          ))}
        </ul>
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-600">Other Details:</h4>
          <pre className="text-sm text-gray-700 bg-gray-100 p-3 rounded-md overflow-auto">
            {JSON.stringify({
              duration: metadata.duration,
              painLevel: metadata.painLevel,
              reportedAt: metadata.reportedAt,
              impactOnTraining: metadata.impactOnTraining
            }, null, 2)}
          </pre>
        </div>
      </div>
    );
  }

  // Default JSON display for other metadata
  return (
    <pre className="text-sm text-gray-700 bg-gray-100 p-3 rounded-md overflow-auto">
      {JSON.stringify(metadata, null, 2)}
    </pre>
  );
}

export default async function ThreadPage({
  params
}: {
  params: { threadId: string }
}) {
  const { clientId } = await getServerUser();
  const thread = await getThread(params.threadId);

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{thread.title}</h1>
        <div className="flex items-center gap-2">
          <span className="px-2 py-1 rounded-full text-sm bg-gray-100">
            {thread.category}
          </span>
          <span className="px-2 py-1 rounded-full text-sm bg-gray-100">
            {thread.status}
          </span>
        </div>
      </div>

      {/* Display Metadata */}
      {thread.metadata && (
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-2">Ticket Metadata</h3>
          {formatMetadata(thread.metadata as Metadata)}
        </Card>
      )}

      <div className="space-y-4">
        {thread.support_messages.map((message) => (
          <Card key={message.id} className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <div className="font-medium">
                  {message.sender?.name} {message.sender?.last_name}
                </div>
                <div className="text-sm text-gray-500">
                  {new Date(message.created_at!).toLocaleString()}
                </div>
              </div>
              {message.is_coach_reply && (
                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                  Coach
                </span>
              )}
            </div>
            <div className="mt-2 text-gray-700">{message.content}</div>

            {/* Display attachments if they exist */}
            {message.attachments && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-600">Attachments:</h4>
                <div className="mt-2 space-y-2">
                  {(JSON.parse(message.attachments as string) as Array<{ url: string; name?: string }>).map((attachment, index) => (
                    <div key={index} className="text-sm text-blue-600 hover:underline">
                      <a href={attachment.url} target="_blank" rel="noopener noreferrer">
                        {attachment.name || `Attachment ${index + 1}`}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      <ReplyForm threadId={thread.id} clientId={clientId} />
    </div>
  );
}