'use client';

import { UserGoalsManager } from '@/components/goals_badges/UserGoalsManager';

// Define categories statically to avoid database queries
export const GOAL_CATEGORIES = [
  { value: 'strength', label: 'Strength' },
  { value: 'endurance', label: 'Endurance' },
  { value: 'flexibility', label: 'Flexibility' },
  { value: 'weight_management', label: 'Weight Management' },
  { value: 'skill_acquisition', label: 'Skill Acquisition' },
  { value: 'attendance', label: 'Attendance' },
  { value: 'nutrition', label: 'Nutrition' },
] as const;

interface GoalsClientProps {
  userId: string;
}

export function GoalsClient({ userId }: GoalsClientProps) {
  return (
    <UserGoalsManager 
      pelatisId={userId} 
      categories={GOAL_CATEGORIES}
    />
  );
}
