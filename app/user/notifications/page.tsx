'use client';

import { useState, useEffect } from 'react';
import { format, parseISO } from 'date-fns';
import { AlertTriangle, AlertCircle, Info } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useSupabase } from '@/hooks/useSupabase';
import type { Database } from '@/types/supabase';

type DbNotification = Database['public']['Tables']['notifications']['Row'];
type NotificationMetadata = DbNotification['metadata'];

interface FrontendNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | null;
  message: string;
  created_at: string;
  read: boolean;
  link: string | null;
  client_id: string | null;
  admin_impersonation: boolean | null;
  expires_at: string | null;
  metadata: NotificationMetadata;
}

function isValidNotificationType(
  type: string | null
): type is 'info' | 'warning' | 'error' {
  return type === 'info' || type === 'warning' || type === 'error';
}

function convertDatabaseNotification(
  dbNotification: DbNotification
): FrontendNotification {
  const type = isValidNotificationType(dbNotification.type) ? dbNotification.type : 'info';
  
  return {
    ...dbNotification,
    type,
    metadata: dbNotification.metadata as NotificationMetadata | null
  };
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<FrontendNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const { supabase, user } = useSupabase();

  useEffect(() => {
    async function fetchNotifications() {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        
        const convertedNotifications = (data || []).map(convertDatabaseNotification);
        setNotifications(convertedNotifications);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchNotifications();
  }, [supabase, user]);

  const markAsRead = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, read: true } : n)
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const getIcon = (type: 'info' | 'warning' | 'error' | null) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-4">
      <h1 className="text-2xl font-bold mb-6">Notifications</h1>
      {notifications.length === 0 ? (
        <p className="text-gray-500">No notifications</p>
      ) : (
        notifications.map((notification) => (
          <Alert
            key={notification.id}
            className={`${notification.read ? 'bg-gray-50' : 'bg-white'}`}
            variant={notification.type === 'error' ? 'destructive' : 'default'}
          >
            <div className="flex items-start">
              {getIcon(notification.type)}
              <div className="ml-4 flex-1">
                <AlertTitle>
                  {format(parseISO(notification.created_at), 'PPp')}
                </AlertTitle>
                <AlertDescription>{notification.message}</AlertDescription>
                {notification.link && (
                  <Button 
                    variant="link" 
                    className="p-0 h-auto mt-2"
                    onClick={() => window.location.href = notification.link!}
                  >
                    View Details
                  </Button>
                )}
              </div>
              {!notification.read && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => markAsRead(notification.id)}
                >
                  Mark as Read
                </Button>
              )}
            </div>
          </Alert>
        ))
      )}
    </div>
  );
}
