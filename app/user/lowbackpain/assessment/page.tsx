"use client";

import React, { useState } from 'react';
import ABCScaleGreek from '@/components/assessment/ABCScaleGreek';
import FABQGreek from '@/components/assessment/FABQGreek';
import PCSGreek from '@/components/assessment/PCSGreek';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function AssessmentPage() {
  const [patientData, setPatientData] = useState({
    name: "",
    date: "",
    age: "",
    assessor: ""
  });

  // Συνάρτηση για κοινά δεδομένα ασθενή μεταξύ των φορμών
  const handlePatientDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPatientData({
      ...patientData,
      [name]: value
    });
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">LIFT Gym - Αξιολόγηση Πόνου</h1>
      
      {/* Κοινά στοιχεία ασθενή */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Στοιχεία Ασθενή</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Ονοματεπώνυμο</label>
            <input 
              type="text" 
              name="name" 
              value={patientData.name} 
              onChange={handlePatientDataChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Ημερομηνία</label>
            <input 
              type="date" 
              name="date" 
              value={patientData.date} 
              onChange={handlePatientDataChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Ηλικία</label>
            <input 
              type="number" 
              name="age" 
              value={patientData.age} 
              onChange={handlePatientDataChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Αξιολογητής</label>
            <input 
              type="text" 
              name="assessor" 
              value={patientData.assessor} 
              onChange={handlePatientDataChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>
        </div>
      </div>
      
      {/* Καρτέλες για τα διαφορετικά εργαλεία αξιολόγησης */}
      <Tabs defaultValue="abc" className="bg-white p-6 rounded-lg shadow-md">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="abc">Κλίμακα ABC</TabsTrigger>
          <TabsTrigger value="fabq">Ερωτηματολόγιο FABQ</TabsTrigger>
          <TabsTrigger value="pcs">Κλίμακα PCS</TabsTrigger>
        </TabsList>
        
        <TabsContent value="abc">
          <ABCScaleGreek patientData={patientData} />
        </TabsContent>
        
        <TabsContent value="fabq">
          <FABQGreek patientData={patientData} />
        </TabsContent>
        
        <TabsContent value="pcs">
          <PCSGreek patientData={patientData} />
        </TabsContent>
      </Tabs>
    </div>
  );
}