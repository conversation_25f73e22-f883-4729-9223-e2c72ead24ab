'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Pencil, Trash2 } from "lucide-react"
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { format } from 'date-fns'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Check, ChevronDown, ChevronsUpDown, <PERSON><PERSON><PERSON>, <PERSON>, CalendarDays } from "lucide-react"
import QuickAddForm from './QuickAddForm'
import type { Database } from '@/types/supabase'

// Calculate One Rep Max using Brzycki formula
function calculateOneRepMax(weight: number, reps: number = 1): number {
  if (reps === 1) return weight;
  return Math.round(weight * (36 / (37 - reps)) * 10) / 10;
}

type ExerciseRecord = Database['public']['Tables']['exercise_records']['Row'] & {
  exercise: Database['public']['Tables']['exercise_movements']['Row']
}

interface Props {
  initialRecords: ExerciseRecord[] | null
  exercises: Database['public']['Tables']['exercise_movements']['Row'][]
  todayWod: Database['public']['Tables']['wod']['Row'] | null
  userId: string
  pelatisId: string
}

const supabase = createClientComponentClient<Database>()

export function ExerciseRecordsClient({ initialRecords, exercises = [], todayWod, pelatisId }: Props) {
  // State management
  const [selectedWod, setSelectedWod] = useState(todayWod)
  const [openExercises, setOpenExercises] = useState<Record<string, boolean>>({})
  const [popoverOpen, setPopoverOpen] = useState(false)
  const [filteredExercises, setFilteredExercises] = useState<typeof exercises>(exercises)
  const [editingRecord, setEditingRecord] = useState<ExerciseRecord | null>(null);
  
  
  const filterExercises = (search: string) => {
    if (!search.trim()) {
      setFilteredExercises(exercises);
      return;
    }
  
    const searchLower = search.toLowerCase();
    const filtered = exercises.filter(exercise => {
      const exerciseName = exercise.exercise_name.toLowerCase();
      const equipment = (exercise.equipment || '').toLowerCase();
      const bodyPart = (exercise.body_part || '').toLowerCase();
      
      return exerciseName.includes(searchLower) ||
             equipment.includes(searchLower) ||
             bodyPart.includes(searchLower);
    });
  
    setFilteredExercises(filtered);
  }

  const [formData, setFormData] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '00:00',
    exerciseId: '',
    weight: '',
    reps: '1',
    sets: '1',
    notes: '',
    calories: ''
  })

  const [loading, setLoading] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [records, setRecords] = useState<ExerciseRecord[] | null>(initialRecords)

  // Initialize WOD
  useEffect(() => {
    if (formData.date) {
      fetchWodForDate(formData.date)
    }
  }, [formData.date])

  // Group records by exercise
  const groupedRecords = records?.reduce((acc, record) => {
    const exerciseName = record.exercise?.exercise_name || 'Unknown'
    if (!acc[exerciseName]) {
      acc[exerciseName] = []
    }
    acc[exerciseName].push(record)
    return acc
  }, {} as Record<string, ExerciseRecord[]>) || {}

  // Handler for toggling exercise sections
  const toggleExercise = (exerciseName: string) => {
    setOpenExercises(prev => ({
      ...prev,
      [exerciseName]: !prev[exerciseName]
    }))
  }

  const handleEdit = (record: ExerciseRecord) => {
    setEditingRecord(record);
    setFormData({
      date: format(new Date(record.date_achieved!), 'yyyy-MM-dd'),
      time: format(new Date(record.date_achieved!), 'HH:mm'),
      exerciseId: record.exercise_id,
      weight: record.weight?.toString() || '',
      reps: record.reps?.toString() || '1',
      sets: record.sets?.toString() || '1',
      notes: record.notes || '',
      calories: record.calories?.toString() || ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    // Validate exercise selection
    if (!formData.exerciseId) {
      console.error('Please select an exercise');
      setLoading(false);
      return;
    }
  
    // Validate time format if entered
    if (formData.time && !/^\d{2}:\d{2}$/.test(formData.time)) {
      console.error('Invalid time format. Use mm:ss');
      setLoading(false);
      return;
    }
    
    try {
      // Existing submission logic remains the same
      if (editingRecord) {
        // Update existing record
        const { data, error } = await supabase
          .from('exercise_records')
          .update({
            exercise_id: formData.exerciseId,
            date_achieved: `${formData.date}T${formData.time}`,
            weight: formData.weight ? Number(formData.weight) : null,
            reps: formData.reps ? Number(formData.reps) : null,
            sets: formData.sets ? Number(formData.sets) : null,
            notes: formData.notes || null,
            calories: formData.calories ? Number(formData.calories) : null
          })
          .eq('id', editingRecord.id)
          .select('*, exercise:exercise_movements(*)')
          .single();
    
        if (error) throw error;
        
        setRecords(prev => 
          prev ? prev.map(r => r.id === data.id ? data : r) : [data]
        );
      } else {
        // Create new record
        const dateTime = `${formData.date}T${formData.time}`;
        const { data, error } = await supabase
          .from('exercise_records')
          .insert({
            exercise_id: formData.exerciseId,
            pelatis_id: pelatisId,
            date_achieved: dateTime,
            weight: formData.weight ? Number(formData.weight) : null,
            reps: formData.reps ? Number(formData.reps) : null,
            sets: formData.sets ? Number(formData.sets) : null,
            notes: formData.notes || null,
            calories: formData.calories ? Number(formData.calories) : null
          })
          .select('*, exercise:exercise_movements(*)')
          .single();
    
        if (error) throw error;
    
        setRecords(prev => prev ? [data, ...prev] : [data]);
      }
    
      // Reset form 
      setEditingRecord(null);
      setFormData({
        date: format(new Date(), 'yyyy-MM-dd'),
        time: '00:00',
        exerciseId: '',
        weight: '',
        reps: '1',
        sets: '1',
        notes: '',
        calories: ''
      });
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAdd = async (exerciseId: string, formData: {
    date: string;
    weight: string;
    reps: string;
    sets: string;
    time: string;
    calories: string;
    notes: string;
  }) => {
    try {
      const dateTime = `${formData.date}T${formData.time}`
      
      const { data, error } = await supabase
        .from('exercise_records')
        .insert({
          exercise_id: exerciseId,
          pelatis_id: pelatisId,
          date_achieved: dateTime,
          weight: formData.weight ? Number(formData.weight) : null,
          reps: formData.reps ? Number(formData.reps) : null,
          sets: formData.sets ? Number(formData.sets) : null,
          notes: formData.notes || null,
          calories: formData.calories ? Number(formData.calories) : null
        })
        .select('*, exercise:exercise_movements(*)')
        .single()

      if (error) throw error

      setRecords(prev => prev ? [data, ...prev] : [data])
      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 3000)
    } catch (error) {
      console.error('Error:', error)
    }
  }

  const fetchWodForDate = async (date: string) => {
    try {
      const { data: wodData, error } = await supabase
        .from('wod')
        .select('*')
        .eq('date', date)
        .single()

      if (error) {
        console.error('Error fetching WOD:', error)
        setSelectedWod(null)
        return
      }

      setSelectedWod(wodData)
    } catch (error) {
      console.error('Error:', error)
      setSelectedWod(null)
    }
  }

  const handleDateChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value
    setFormData(d => ({ ...d, date: newDate }))
    await fetchWodForDate(newDate)
  }

  const deleteRecord = async (recordId: string) => {
    try {
      const { error } = await supabase
        .from('exercise_records')
        .delete()
        .eq('id', recordId)

      if (error) throw error

      setRecords(prev => 
        prev ? prev.filter(record => record.id !== recordId) : null
      )
    } catch (error) {
      console.error('Error deleting record:', error)
    }
  }

  return (
<div className="max-w-7xl mx-auto py-6 px-4 sm:px-6">

      {showSuccess && (
        <Alert className="mb-4 bg-green-50">
          <AlertDescription>
            Exercise record added successfully!
          </AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content Area */}
        <div className="lg:col-span-2 space-y-8">
          {/* Exercise Form Card */}
          <Card className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Selected Exercise Display */}
              {formData.exerciseId && (
                <div className="p-3 bg-primary/10 rounded-lg border border-primary/20 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Dumbbell className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-semibold text-lg text-primary">
                        {exercises.find(e => e.id === formData.exerciseId)?.exercise_name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {exercises.find(e => e.id === formData.exerciseId)?.body_part} • 
                        {exercises.find(e => e.id === formData.exerciseId)?.equipment}
                      </div>
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setFormData(d => ({ ...d, exerciseId: '' }))}
                  >
                    Change
                  </Button>
                </div>
              )}

              {/* Date and Exercise Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2 bg-muted/50 rounded-lg px-3 py-2 ">
                  <CalendarDays className="h-5 w-5 text-muted-foreground" />
                  <Input
                    type="date"
                    value={formData.date}
                    onChange={handleDateChange}
                    className="border-0 bg-transparent focus-visible:ring-0"
                  />
                </div>

                {!formData.exerciseId && (
                  <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        className="w-full justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <Dumbbell className="h-4 w-4 text-muted-foreground" />
                          <span>Select exercise...</span>
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
  <Command>
    <CommandInput 
      placeholder="Search exercises..."
      onValueChange={filterExercises}
    />
    <CommandList>
      <CommandEmpty>No exercises found.</CommandEmpty>
      {filteredExercises.map((exercise) => (
        <CommandItem
          key={exercise.id}
          value={exercise.exercise_name}
          onSelect={() => {
            setFormData(d => ({ ...d, exerciseId: exercise.id }))
            setPopoverOpen(false)
          }}
        >
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <Check
                className={cn(
                  "mr-2 h-4 w-4",
                  formData.exerciseId === exercise.id ? "opacity-100" : "opacity-0"
                )}
              />
              <span>{exercise.exercise_name}</span>
            </div>
            <span className="text-xs text-muted-foreground">
              {exercise.equipment}
            </span>
          </div>
        </CommandItem>
      ))}
    </CommandList>
  </Command>
</PopoverContent>
                  </Popover>
                )}
              </div>

              {/* Exercise Metrics */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Weight (kg)</label>
                  <Input
                    type="number"
                    value={formData.weight}
                    onChange={e => setFormData(d => ({ ...d, weight: e.target.value }))}
                    className="bg-muted/50"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Reps</label>
                  <Input
                    type="number"
                    value={formData.reps}
                    onChange={e => setFormData(d => ({ ...d, reps: e.target.value }))}
                    className="bg-muted/50"
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Sets</label>
                  <Input
                    type="number"
                    value={formData.sets}
                    onChange={e => setFormData(d => ({ ...d, sets: e.target.value }))}
                    className="bg-muted/50"
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Time</label>
                  <div className="relative">
                    <Clock className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                    <Input
                      type="text"
                      value={formData.time}
                      onChange={e => {
                        const value = e.target.value
                        if (value.length <= 5) {
                          const cleaned = value.replace(/[^\d:]/g, '')
                          if (cleaned.length === 2 && !value.includes(':')) {
                            setFormData(d => ({ ...d, time: cleaned + ':' }))
                          } else {
                            setFormData(d => ({ ...d, time: cleaned }))
                          }
                        }
                      }}
                      className="bg-muted/50 pl-9"
                      placeholder="mm:ss"
                    />
                  </div>
                </div>
                <div className="space-y-2">
    <label className="text-sm font-medium">Calories</label>
    <Input
      type="number"
      value={formData.calories}
      onChange={e => setFormData(d => ({ ...d, calories: e.target.value }))}
      className="bg-muted/50"
      min="0"
      placeholder="Optional"
    />
  </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Input
                  placeholder="Add notes..."
                  value={formData.notes}
                  onChange={e => setFormData(d => ({ ...d, notes: e.target.value }))}
                  className="bg-muted/50"
                />
              </div>

              <Button 
                type="submit" 
                disabled={loading || !formData.exerciseId}
                className="w-full"
              >
                Add Record
              </Button>
            </form>
          </Card>

          {/* Exercise History */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Exercise History</h2>
            {Object.entries(groupedRecords).map(([exerciseName, exerciseRecords]) => {
              // Calculate max weight and its date
              const recordsWithWeight = exerciseRecords.filter(r => r.weight);
              const maxWeight = Math.max(...recordsWithWeight.map(r => r.weight || 0));
              
              // Calculate 1RM only for records with weight
              const oneRepMaxes = recordsWithWeight
                .map(r => ({
                  value: calculateOneRepMax(r.weight!, r.reps || 1),
                  date: r.date_achieved
                }))
                .sort((a, b) => b.value - a.value);
              
              const maxOneRepMax = oneRepMaxes[0];

              return (
                <Collapsible 
                  key={exerciseName} 
                  open={openExercises[exerciseName]}
                  onOpenChange={() => toggleExercise(exerciseName)}
                >
                  <Card className="mb-6">
                    <CollapsibleTrigger className="w-full">
                      <div className="p-4 flex items-center justify-between">
                      <div className="flex items-center gap-4">
  <div>
    <div className="flex items-baseline gap-2">
      <h3 className="text-lg font-medium">{exerciseName}</h3>
      {maxOneRepMax && (
        <span className="text-lg font-bold text-primary">
          {maxOneRepMax.value}kg
        </span>
      )}
    </div>
    {maxOneRepMax && (
      <div className="text-xs text-muted-foreground">
        {format(new Date(maxOneRepMax.date!), 'dd/MM/yyyy')}
      </div>
    )}
  </div>
</div>
                        <ChevronDown className={cn(
  "h-5 w-5 transition-transform duration-200",
  openExercises[exerciseName] ? "transform rotate-180" : ""
)} />
                      </div>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent>
                      <div className="p-4 pt-0">
                      <QuickAddForm 
      exerciseId={exerciseRecords[0].exercise_id}
      onSubmit={(formData) => handleQuickAdd(exerciseRecords[0].exercise_id, formData)}
    />

                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Weight</TableHead>
                              <TableHead>1RM (est.)</TableHead>
                              <TableHead>Reps</TableHead>
                              <TableHead>Sets</TableHead>
                              <TableHead>Time</TableHead>
                              <TableHead>Cals</TableHead>
                              <TableHead>Notes</TableHead>
                              <TableHead className="w-[50px]"></TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {exerciseRecords.map((record) => {
                              const oneRepMax = record.weight ? 
                                calculateOneRepMax(record.weight, record.reps || 1) : 
                                null;

                              return (
                                <TableRow 
                                  key={record.id} 
className={cn(
  "group",
  record.weight === maxWeight ? "bg-primary/5" : ""
)}
                                >
                                  <TableCell>
                                    {format(new Date(record.date_achieved!), 'MMM d, yyyy')}
                                  </TableCell>
                                  <TableCell>
                                  {record.weight ? (
  <span className={cn(
    record.weight === maxWeight ? "text-primary font-medium" : ""
  )}>
    {record.weight}kg
  </span>
) : '-'}
                                  </TableCell>
                                  <TableCell>
                                  {oneRepMax ? (
  <span className={cn(
    oneRepMax === maxOneRepMax?.value ? "text-primary font-medium" : ""
  )}>
    {oneRepMax}kg
  </span>
) : '-'}
                                  </TableCell>
                                  <TableCell>{record.reps || '1'}</TableCell>
                                  <TableCell>{record.sets || '1'}</TableCell>
                                  <TableCell>{record.time || '-'}</TableCell>
                                  <TableCell>{record.calories || '-'}</TableCell>
                                  <TableCell className="max-w-xs truncate">
                                    {record.notes || '-'}
                                  </TableCell>
                                  <TableCell>
  <div className="flex items-center gap-2">
    <Button
      variant="ghost"
      size="icon"
      onClick={() => deleteRecord(record.id)}
      className="h-8 w-8 p-0 text-destructive hover:text-destructive/90"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
    <Button
      variant="ghost"
      size="icon"
      onClick={() => handleEdit(record)}
      className="h-8 w-8 p-0"
    >
      <Pencil className="h-4 w-4" />
    </Button>
  </div>
</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    </CollapsibleContent>
                  </Card>
                </Collapsible>
              );
            })}
          </div>
        </div>

        {/* WOD Section */}
        <div className="order-first lg:order-none mb-6 lg:mb-0">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">
              WOD for {format(new Date(formData.date), 'MMM d, yyyy')}
            </h2>
            
            {selectedWod ? (
              <>
                {/* Exercise Tags */}
                {selectedWod.exercises && selectedWod.exercises.length > 0 && (
                  <div className="mb-4 flex flex-wrap gap-2">
                    {selectedWod.exercises.map((exerciseId) => {
                      const exercise = exercises?.find(e => e.id === exerciseId);
                      return exercise && (
                        <Badge
                          key={exerciseId}
                          variant="outline"
                          className={cn(
                            "cursor-pointer transition-colors",
                            formData.exerciseId === exerciseId 
                              ? "bg-primary/20 hover:bg-primary/30 border-primary" 
                              : "hover:bg-primary/10"
                          )}
                          onClick={() => setFormData(d => ({ ...d, exerciseId }))}
                        >
                          <Dumbbell className="h-3 w-3 mr-1" />
                          {exercise.exercise_name}
                        </Badge>
                      );
                    })}
                  </div>
                )}


                {/* Workout Section */}
                <div className="bg-blue-50 dark:bg-blue-950/10 rounded-lg p-4 border border-blue-100 dark:border-blue-900/50">
                  <h3 className="font-medium text-lg mb-2 text-blue-700 dark:text-blue-400 flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                    Workout
                  </h3>
                  <div className="space-y-1">
                    {selectedWod.content.split('\n').map((line, i) => {
                      // Check if line is a section header (e.g., "A) something" or "A. something")
                      const isSection = /^[A-Z][).]\s/.test(line);
                      return (
                        <p 
                          key={i} 
                          className={cn(
                            "text-blue-800 dark:text-blue-200",
                            isSection ? "font-medium mt-3" : ""
                          )}
                        >
                          {line}
                        </p>
                      );
                    })}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-muted-foreground text-center p-8 bg-muted/10 rounded-lg border border-dashed">
                <CalendarDays className="h-8 w-8 mb-2 mx-auto text-muted-foreground/50" />
                No WOD scheduled for this date
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}