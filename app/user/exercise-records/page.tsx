// page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { ExerciseRecordsClient } from './client'
import { redirect } from 'next/navigation'
import type { Database } from '@/types/supabase'


export default async function ExerciseRecordsPage() {
  const supabase = createServerComponentClient<Database>({
    cookies
  })
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user || !user.email) {
    redirect('/auth')
  }

  const today = new Date().toISOString().split('T')[0]
  
  const { data: pelatis, error: pelatisError } = await supabase
    .from('pelates')
    .select('id')
    .eq('email', user.email)
    .single()

  if (pelatisError || !pelatis) {
    redirect('/user/profile')
  }

  const [exercisesResponse, recordsResponse, wodResponse] = await Promise.all([
    supabase.from('exercise_movements').select().order('exercise_name'),
    supabase
      .from('exercise_records')
      .select('*, exercise:exercise_movements(*)')
      .eq('pelatis_id', pelatis.id)  
      .order('date_achieved', { ascending: false }),
    supabase.from('wod').select('*').eq('date', today).single()
  ])

  return (
    <ExerciseRecordsClient 
      initialRecords={recordsResponse.data} 
      exercises={exercisesResponse.data ?? []} // Provide default empty array
      todayWod={wodResponse.data}
      userId={user.id}
      pelatisId={pelatis.id}
    />
  )
}