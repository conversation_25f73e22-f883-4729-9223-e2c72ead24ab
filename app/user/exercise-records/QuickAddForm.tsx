import React, { useState } from 'react';
import { TableCell, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Loader2, X } from "lucide-react";
import { format } from 'date-fns';
import { Card } from '@/components/ui/card';

interface QuickAddFormProps {
  exerciseId: string;
  onSubmit: (data: {
    date: string;
    weight: string;
    reps: string;
    sets: string;
    time: string;
    calories: string;
    notes: string;
  }) => Promise<void>;
}

export const QuickAddForm = ({ onSubmit }: QuickAddFormProps) => {
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    weight: '',
    reps: '',
    sets: '',
    time: '00:00',
    calories: '',
    notes: ''
  });

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
      setShowForm(false);
      setFormData({
        date: format(new Date(), 'yyyy-MM-dd'),
        weight: '',
        reps: '',
        sets: '',
        time: '00:00',
        calories: '',
        notes: ''
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mb-4">
      {/* Toggle Button */}
      <div className="flex justify-end mb-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowForm(!showForm)}
          className="flex items-center gap-2"
        >
          {showForm ? (
            <>
              <X className="h-4 w-4" />
              <span className="hidden sm:inline">Close Quick Add</span>
              <span className="sm:hidden">Close</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Quick Add</span>
              <span className="sm:hidden">Add</span>
            </>
          )}
        </Button>
      </div>

      {/* Form Content */}
      {showForm && (
        <>
          {/* Mobile View */}
          <Card className="p-4 mb-4 lg:hidden">
            <div className="grid grid-cols-2 gap-3">
              <div className="col-span-2">
                <Input
                  type="date"
                  value={formData.date}
                  onChange={e => setFormData(d => ({ ...d, date: e.target.value }))}
                  className="w-full"
                />
              </div>
              <Input
                type="number"
                placeholder="Weight (kg)"
                value={formData.weight}
                onChange={e => setFormData(d => ({ ...d, weight: e.target.value }))}
              />
              <Input
                type="number"
                placeholder="Reps"
                value={formData.reps}
                onChange={e => setFormData(d => ({ ...d, reps: e.target.value }))}
              />
              <Input
                type="number"
                placeholder="Sets"
                value={formData.sets}
                onChange={e => setFormData(d => ({ ...d, sets: e.target.value }))}
              />
              <Input
                type="text"
                placeholder="Time (mm:ss)"
                pattern="[0-9]{2}:[0-9]{2}"
                value={formData.time}
                onChange={e => {
                  const value = e.target.value;
                  if (value.length <= 5) {
                    const cleaned = value.replace(/[^\d:]/g, '');
                    if (cleaned.length === 2 && !value.includes(':')) {
                      setFormData(d => ({ ...d, time: cleaned + ':' }));
                    } else {
                      setFormData(d => ({ ...d, time: cleaned }));
                    }
                  }
                }}
              />
              <Input
                type="number"
                placeholder="Calories"
                value={formData.calories}
                onChange={e => setFormData(d => ({ ...d, calories: e.target.value }))}
              />
              <div className="col-span-2">
                <Input
                  placeholder="Notes"
                  value={formData.notes}
                  onChange={e => setFormData(d => ({ ...d, notes: e.target.value }))}
                />
              </div>
              <Button 
                onClick={handleSubmit}
                disabled={loading}
                className="col-span-2"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
                Quick Add Record
              </Button>
            </div>
          </Card>

          {/* Desktop View */}
          <div className="hidden lg:block">
            <TableRow className="bg-muted/50">
              <TableCell>
                <Input
                  type="date"
                  value={formData.date}
                  onChange={e => setFormData(d => ({ ...d, date: e.target.value }))}
                  className="w-32"
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  placeholder="Weight"
                  value={formData.weight}
                  onChange={e => setFormData(d => ({ ...d, weight: e.target.value }))}
                  className="w-20"
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  placeholder="Reps"
                  value={formData.reps}
                  onChange={e => setFormData(d => ({ ...d, reps: e.target.value }))}
                  className="w-16"
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  placeholder="Sets"
                  value={formData.sets}
                  onChange={e => setFormData(d => ({ ...d, sets: e.target.value }))}
                  className="w-16"
                />
              </TableCell>
              <TableCell>
                <Input
                  type="text"
                  placeholder="mm:ss"
                  pattern="[0-9]{2}:[0-9]{2}"
                  value={formData.time}
                  onChange={e => {
                    const value = e.target.value;
                    if (value.length <= 5) {
                      const cleaned = value.replace(/[^\d:]/g, '');
                      if (cleaned.length === 2 && !value.includes(':')) {
                        setFormData(d => ({ ...d, time: cleaned + ':' }));
                      } else {
                        setFormData(d => ({ ...d, time: cleaned }));
                      }
                    }
                  }}
                  className="w-20"
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  placeholder="Calories"
                  value={formData.calories}
                  onChange={e => setFormData(d => ({ ...d, calories: e.target.value }))}
                  className="w-20"
                />
              </TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Input
                    placeholder="Notes"
                    value={formData.notes}
                    onChange={e => setFormData(d => ({ ...d, notes: e.target.value }))}
                  />
                  <Button 
                    onClick={handleSubmit}
                    disabled={loading}
                    size="sm"
                  >
                    {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Add'}
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </div>
        </>
      )}
    </div>
  );
};

export default QuickAddForm;