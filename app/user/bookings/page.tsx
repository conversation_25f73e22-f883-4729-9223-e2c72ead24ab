// app/user/bookings/page.tsx
'use client'

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, Loader2, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

type DbBooking = Database['public']['Tables']['bookings']['Row'];

interface BookingWithSession extends DbBooking {
  sessions: {
    start_time: string;
    program_id: string;
    programs: {
      name: string;
    };
  } | null;
}

export default function UserBookingsPage() {
  const [bookings, setBookings] = useState<BookingWithSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    fetchUserBookings();
  }, []);

  const fetchUserBookings = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data: profile } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', user.id)
        .single();

      if (!profile) throw new Error('Profile not found');

      const now = new Date();
      now.setHours(0, 0, 0, 0);

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          created_at,
          booked_session_id,
          sessions!inner (
            start_time,
            program_id,
            programs!inner (
              name
            )
          )
        `)
        .eq('pelatis_id', profile.id)
        .gte('sessions.start_time', now.toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBookings(data as BookingWithSession[]);

    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError('Failed to fetch bookings');
    } finally {
      setLoading(false);
    }
  };

  const cancelBooking = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);

      if (error) throw error;
      
      toast.success('Booking cancelled successfully');
      fetchUserBookings();
    } catch (err) {
      console.error('Error cancelling booking:', err);
      toast.error('Failed to cancel booking');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <h1 className="text-2xl font-bold">My Bookings</h1>
      
      {error && (
        <div className="p-4 rounded-md bg-red-100 text-red-700">{error}</div>
      )}

      <Card>
        <CardContent className="p-4">
          {bookings && bookings.length > 0 ? (
            <div className="space-y-3">
              {bookings.map((booking) => (
                <div key={booking.id} 
                    className="p-3 rounded-lg border bg-white shadow-sm hover:bg-gray-50 transition-colors
                              flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-blue-100">
                      <Calendar className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">
                        {booking.sessions ? (
                          <>
                            {new Date(booking.sessions.start_time).toLocaleDateString('el-GR')}
                            {' '}
                            <span className="text-gray-600">
                              {new Date(booking.sessions.start_time).toLocaleTimeString('el-GR', { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </span>
                          </>
                        ) : (
                          'Session details not available'
                        )}
                      </p>
                      <p className="text-xs text-gray-600">
                        {booking.sessions?.programs.name}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => cancelBooking(booking.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600 font-medium">No future bookings found</p>
              <p className="text-sm text-gray-400 mt-1">
                Book a session from the calendar
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}