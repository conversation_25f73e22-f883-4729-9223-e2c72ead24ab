// app/user/fitness/weight-tracker/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { format } from 'date-fns';
import { Pencil, Trash2 } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type FitnessMeasurement = Database['public']['Tables']['fitness_measurements']['Row'];

export default function WeightTrackerPage() {
  const [measurements, setMeasurements] = useState<FitnessMeasurement[]>([]);
  const [formData, setFormData] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '00:00',
    weight: '',
    resting_heart_rate: '0',
    waist_circumference: '0'
  });
  const [error, setError] = useState<string | null>(null);
  const [editingMeasurement, setEditingMeasurement] = useState<FitnessMeasurement | null>(null);
  const [loading, setLoading] = useState(false);
  const supabase = createClientComponentClient<Database>();

  // Fetch measurements on component mount
  useEffect(() => {
    const fetchMeasurements = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user) {
          throw new Error('No authenticated user');
        }

        const { data: pelatesData } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', session.user.id)
          .single();

        if (!pelatesData) {
          throw new Error('Could not find user profile');
        }

        const { data, error } = await supabase
          .from('fitness_measurements')
          .select('*')
          .eq('pelatis_id', pelatesData.id)
          .order('measurement_date', { ascending: false });

        if (error) throw error;
        setMeasurements(data || []);
      } catch (err) {
        console.error('Error fetching measurements:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      }
    };

    fetchMeasurements();
  }, [supabase]);

  // Handle measurement submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error('No authenticated user');
      }

      const { data: pelatesData } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', session.user.id)
        .single();

      if (!pelatesData) {
        throw new Error('Could not find user profile');
      }

      const measurementDate = `${formData.date}T${formData.time}`;

      if (editingMeasurement) {
        // Update existing measurement
        const { data, error } = await supabase
          .from('fitness_measurements')
          .update({
            weight: parseFloat(formData.weight),
            measurement_date: measurementDate,
            resting_heart_rate: parseFloat(formData.resting_heart_rate),
            waist_circumference: parseFloat(formData.waist_circumference)
          })
          .eq('id', editingMeasurement.id)
          .select()
          .single();

        if (error) throw error;

        // Update local state
        setMeasurements(prev =>
          prev.map(m => m.id === data.id ? data : m)
        );
        setEditingMeasurement(null);
      } else {
        // Create new measurement
        const { data, error } = await supabase
          .from('fitness_measurements')
          .insert({
            pelatis_id: pelatesData.id,
            weight: parseFloat(formData.weight),
            measurement_date: measurementDate,
            resting_heart_rate: parseFloat(formData.resting_heart_rate),
            waist_circumference: parseFloat(formData.waist_circumference)
          })
          .select()
          .single();

        if (error) throw error;

        // Update local state
        setMeasurements(prev => [data, ...prev]);
      }

      // Reset form
      setFormData({
        date: format(new Date(), 'yyyy-MM-dd'),
        time: '00:00',
        weight: '',
        resting_heart_rate: '0',
        waist_circumference: '0'
      });
    } catch (err) {
      console.error('Error submitting measurement:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle editing a measurement
  const handleEdit = (measurement: FitnessMeasurement) => {
    setEditingMeasurement(measurement);
    setFormData({
      date: format(new Date(measurement.measurement_date || ''), 'yyyy-MM-dd'),
      time: format(new Date(measurement.measurement_date || ''), 'HH:mm'),
      weight: measurement.weight?.toString() || '',
      resting_heart_rate: measurement.resting_heart_rate?.toString() || '0',
      waist_circumference: measurement.waist_circumference?.toString() || '0'
    });
  };

  // Handle deleting a measurement
  const handleDelete = async (measurementId: string) => {
    try {
      const { error } = await supabase
        .from('fitness_measurements')
        .delete()
        .eq('id', measurementId);

      if (error) throw error;

      // Update local state
      setMeasurements(prev => prev.filter(m => m.id !== measurementId));
    } catch (err) {
      console.error('Error deleting measurement:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  // Prepare data for chart
  const chartData = [...measurements]
    .sort((a, b) =>
      new Date(a.measurement_date || '').getTime() -
      new Date(b.measurement_date || '').getTime()
    )
    .map(m => ({
      date: format(new Date(m.measurement_date || ''), 'dd/MM/yyyy'),
      weight: m.weight
    }));

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Weight Tracker</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  type="date"
                  id="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="time">Time</Label>
                <Input
                  type="time"
                  id="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="weight">Weight (kg)</Label>
                <Input
                  type="number"
                  id="weight"
                  value={formData.weight}
                  onChange={(e) => setFormData(prev => ({ ...prev, weight: e.target.value }))}
                  step="0.1"
                  required
                  placeholder="Enter your current weight"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="resting_heart_rate">Resting Heart Rate</Label>
                <Input
                  type="number"
                  id="resting_heart_rate"
                  value={formData.resting_heart_rate}
                  onChange={(e) => setFormData(prev => ({ ...prev, resting_heart_rate: e.target.value }))}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="waist_circumference">Waist Circumference (cm)</Label>
                <Input
                  type="number"
                  id="waist_circumference"
                  value={formData.waist_circumference}
                  onChange={(e) => setFormData(prev => ({ ...prev, waist_circumference: e.target.value }))}
                  step="0.1"
                  className="mt-1"
                />
              </div>
            </div>
            <Button
              type="submit"
              disabled={loading}
              className="w-full"
            >
              {editingMeasurement ? 'Update Measurement' : 'Add Measurement'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Weight History</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis label={{ value: 'Weight (kg)', angle: -90, position: 'insideLeft' }} />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="weight"
                stroke="#8884d8"
                activeDot={{ r: 8 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Detailed Weight Log</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Weight (kg)</TableHead>
                <TableHead>Resting HR</TableHead>
                <TableHead>Waist (cm)</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {measurements.map((measurement) => (
                <TableRow key={measurement.id}>
                  <TableCell>
                    {format(new Date(measurement.measurement_date || ''), 'dd/MM/yyyy')}
                  </TableCell>
                  <TableCell>
                    {format(new Date(measurement.measurement_date || ''), 'HH:mm')}
                  </TableCell>
                  <TableCell>{measurement.weight}</TableCell>
                  <TableCell>{measurement.resting_heart_rate}</TableCell>
                  <TableCell>{measurement.waist_circumference}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(measurement.id)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive/90"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(measurement)}
                        className="h-8 w-8 p-0"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}