// app/user/achievements/client.tsx
'use client';

import dynamic from 'next/dynamic';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';

interface LoadingCardProps {
  title: string;
}

function LoadingCard({ title }: LoadingCardProps) {
  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center justify-center space-x-4">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900" />
          <p>{title}</p>
        </div>
      </div>
    </Card>
  );
}

const BadgeDisplay = dynamic<{ userId: string }>(() => 
  import('@/components/goals_badges/BadgeDisplay').then(mod => mod.default), {
  loading: () => <LoadingCard title="Loading Badges..." />
});

const BadgeAnalytics = dynamic<{ userId: string }>(() => 
  import('@/components/goals_badges/BadgeAnalytics').then(mod => mod.default), {
  loading: () => <LoadingCard title="Loading Analytics..." />
});

const GoalsManager = dynamic<{ userId: string }>(() => 
  import('@/components/goals_badges/GoalsManager').then(mod => mod.default), {
  loading: () => <LoadingCard title="Loading Goals..." />
});

interface AchievementsClientProps {
  userId: string;
}

export function AchievementsClient({ userId }: AchievementsClientProps) {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Achievements & Goals</h1>

      <Tabs defaultValue="badges" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="badges">Badges</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
        </TabsList>

        <TabsContent value="badges" className="mt-6">
          <BadgeDisplay userId={userId} />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <BadgeAnalytics userId={userId} />
        </TabsContent>

        <TabsContent value="goals" className="mt-6">
          <GoalsManager userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}