// app/user/achievements/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { AchievementsClient } from './client';
import type { Database } from '@/types/supabase';

export default async function AchievementsPage() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  // Check for session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return redirect('/auth');
  }

  // Get the pelatis_id for the current user
  const { data: pelatis, error } = await supabase
    .from('pelates')
    .select('id')
    .eq('auth_user_id', session.user.id)
    .single();

  if (error || !pelatis) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-red-50 text-red-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Profile Not Found</h2>
          <p>Please complete your profile setup to access achievements.</p>
        </div>
      </div>
    );
  }

  return <AchievementsClient userId={pelatis.id} />;
}