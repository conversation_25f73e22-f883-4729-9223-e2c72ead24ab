
'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Loader2, Trash2, ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import MembershipStatus from '@/components/MembershipStatus';

type DbSession = Database['public']['Tables']['sessions']['Row'];
type DbProgram = Database['public']['Tables']['programs']['Row'];
type DbBooking = Database['public']['Tables']['bookings']['Row'];
type DbPelatis = Database['public']['Tables']['pelates']['Row'];



interface BookingWithSession extends DbBooking {
  sessions: {
    start_time: string;
    program_id: string;
    programs: {
      name: string;
    };
  } | null;
}







  
  interface SessionWithRelations extends DbSession {
    programs: Pick<DbProgram, 'name'> | null;
    bookings: Pick<DbBooking, 'id'>[] | null;
    program_name: string;
    total_bookings: number;
    available_slots: number;
  }

type ActiveSubscriptionsRow = Database['public']['Views']['active_subscriptions']['Row'];


const SessionsPage = () => {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(getWeekStart(new Date()));
  const [sessions, setSessions] = useState<SessionWithRelations[]>([]);
  const [bookings, setBookings] = useState<BookingWithSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<SessionWithRelations | null>(null);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<DbPelatis | null>(null);
  const [activeSubscription, setActiveSubscription] = useState<ActiveSubscriptionsRow | null>(null);
  const supabase = createClientComponentClient<Database>();

  // Debug Supabase client
console.log('Supabase client config:', {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  hasAnon: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
});


useEffect(() => {
  const debugTypes = () => {
    type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
    const sampleBooking: BookingInsert = {
      booked_session_id: 'some-uuid',
      pelatis_id: 'some-uuid',
      created_at: new Date().toISOString()
    };
    console.log('Sample booking type:', sampleBooking);
  };

  debugTypes();
}, []); // Run once on mount

  // Fetch user profile once on mount
  useEffect(() => {
// Inside fetchUserProfile function, modify it to handle the profile data properly
const fetchUserProfile = async () => {
  try {
    console.log('Fetching user profile...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Auth error:', authError);
      throw authError;
    }
    
    if (!user?.id) {
      console.error('No user ID found in auth data:', user);
      throw new Error('No authenticated user found');
    }

    if (!user.email) {
      console.error('No email found in auth data:', user);
      throw new Error('No email found for authenticated user');
    }

    console.log('Auth user found:', { id: user.id, email: user.email });

    // First try to find by auth_user_id
    const { data: profile, error: profileError } = await supabase
      .from('pelates')
      .select('*')
      .eq('auth_user_id', user.id)
      .single();

    console.log('Profile lookup by auth_user_id result:', { profile, error: profileError });

    let foundProfile: DbPelatis | null = null;

    if (profileError) {
      console.log('Profile not found by auth_user_id, trying email...');
      // If not found by auth_user_id, try by email
      const { data: emailProfile, error: emailError } = await supabase
        .from('pelates')
        .select('*')
        .eq('email', user.email)
        .single();

      console.log('Profile lookup by email result:', { emailProfile, error: emailError });

      if (emailError) throw emailError;
      
      // Update the pelates record with the auth_user_id if found by email
      if (emailProfile) {
        console.log('Updating pelates record with auth_user_id:', user.id);
        const { error: updateError } = await supabase
          .from('pelates')
          .update({ auth_user_id: user.id })
          .eq('id', emailProfile.id);
        
        if (updateError) {
          console.error('Error updating pelates record:', updateError);
          throw updateError;
        }
        
        foundProfile = emailProfile;
      }
    } else {
      foundProfile = profile;
    }

    if (foundProfile) {
      setUserProfile(foundProfile);

      // Now fetch subscription data using the found profile
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('active_subscriptions')
        .select('*')
        .eq('client_id', foundProfile.id)
        .maybeSingle();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        console.error('Error fetching subscription:', subscriptionError);
      } else {
        setActiveSubscription(subscriptionData);
      }
    }

  } catch (err) {
    console.error('Error in fetchUserProfile:', err);
    setError('Failed to fetch user profile');
  }
};

    fetchUserProfile();
  }, [supabase]);

// Update the fetchSessionsForWeek function
const fetchSessionsForWeek = useCallback(async (startDate: Date) => {
  try {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 7);

    const { data: sessionsData, error: sessionsError } = await supabase
      .from('sessions')
      .select(`
        *,
        programs (name),
        bookings (id),
        session_booking_counts!left (total_bookings)
      `)
      .gte('start_time', startDate.toISOString())
      .lt('start_time', endDate.toISOString());

    if (sessionsError) throw sessionsError;

    const formattedSessions: SessionWithRelations[] = (sessionsData || []).map(session => ({
      ...session,
      bookings: session.bookings || [],
      program_name: session.programs?.name || 'N/A',
      total_bookings: session.session_booking_counts?.[0]?.total_bookings || 0,
      available_slots: session.max_participants - (session.session_booking_counts?.[0]?.total_bookings || 0)
    }));

    setSessions(formattedSessions);

  } catch (err) {
    console.error('Error:', err);
    setError('Failed to fetch sessions');
  }
}, [supabase]);
  
  const fetchUserBookings = useCallback(async () => {
    console.log('Starting fetchUserBookings...');
    
    if (!userProfile) {
      console.log('No userProfile available, skipping fetch');
      return;
    }
  
    try {
      const now = new Date();
      // Set time to start of day to include today's bookings
      now.setHours(0, 0, 0, 0);
      
      console.log('Fetching bookings for user:', userProfile.id);
      
      // Using the bookings_view instead and fixing the order syntax
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          created_at,
          booked_session_id,
          sessions!inner (
            start_time,
            program_id,
            programs!inner (
              name
            )
          )
        `)
        .eq('pelatis_id', userProfile.id)
        .gte('sessions.start_time', now.toISOString())
        .order('created_at', { ascending: false }); // Fixed order syntax
  
      if (error) {
        console.error('Error fetching bookings:', error);
        throw error;
      }
      
      console.log('Bookings fetched successfully:', data);
      setBookings(data as BookingWithSession[]);
    } catch (err) {
      console.error('Error in fetchUserBookings:', err);
      setError('Failed to fetch bookings');
    }
  }, [supabase, userProfile]);
  // Update getWeekStart to ensure correct week calculation
  function getWeekStart(date: Date): Date {
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0); // Reset time to start of day
    const day = newDate.getDay();
    const diff = newDate.getDate() - day + (day === 0 ? -6 : 1);
    newDate.setDate(diff);
    return newDate;
  }
  
  // Update useEffect to handle week changes properly
  useEffect(() => {
    if (userProfile) {
      console.log('Fetching data for week starting:', currentWeekStart);
      fetchSessionsForWeek(currentWeekStart);
      fetchUserBookings();
    }
  }, [currentWeekStart, fetchSessionsForWeek, fetchUserBookings, userProfile]);
  
  // Update week navigation to properly handle date changes
  // Navigation handlers
  const handlePreviousWeek = () => {
    const newWeekStart = new Date(currentWeekStart);
    newWeekStart.setDate(newWeekStart.getDate() - 7);
    if (newWeekStart >= getWeekStart(new Date())) {
      setCurrentWeekStart(newWeekStart);
    }
  };
  
  const handleNextWeek = () => {
    const newWeekStart = new Date(currentWeekStart);
    newWeekStart.setDate(newWeekStart.getDate() + 7);
    setCurrentWeekStart(newWeekStart);
  };

  


  useEffect(() => {
    if (userProfile) {
      fetchSessionsForWeek(currentWeekStart);
      fetchUserBookings();
    }
  }, [currentWeekStart, fetchSessionsForWeek, fetchUserBookings, userProfile]);




  const bookSession = async () => {
    if (!selectedSession || !userProfile) return;
    
    setLoading(true);
    try {
      const { error } = await supabase.rpc('create_booking', {
        p_booked_session_id: selectedSession.id,
        p_pelatis_id: userProfile.id
      });
  
      if (error) throw error;
  
      setShowModal(false);
      toast.success('Η κράτηση σας ολοκληρώθηκε με επιτυχία!');
      await Promise.all([
        fetchSessionsForWeek(currentWeekStart),
        fetchUserBookings()
      ]);
  
    } catch (err) {
      console.error('Error in bookSession:', err);
      toast.error('Αποτυχία κράτησης');
    } finally {
      setLoading(false);
    }
  };
  



  const cancelBooking = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);

      if (error) throw error;
      
      toast.success('Booking cancelled successfully');
      
      await Promise.all([
        fetchSessionsForWeek(currentWeekStart),
        fetchUserBookings()
      ]);
    } catch (err) {
      console.error('Error cancelling booking:', err);
      toast.error('Failed to cancel booking');
    }
  };



  function formatDate(date: Date): string {
    return date.toLocaleDateString('el-GR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    });
  }


  function showSessionDetails(session: SessionWithRelations) {
    console.log('Selected session details:', {
      id: session.id,
      startTime: session.start_time,
      programName: session.program_name,
      availableSlots: session.available_slots
    });
    setSelectedSession(session);
    setShowModal(true);
    setError(null);
  }
  // Calendar rendering logic
function renderCalendar() {
 const dayNames = ['Δευ', 'Τρι', 'Τετ', 'Πεμ', 'Παρ', 'Σαβ', 'Κυρ'];
 const calendar = [];
 const today = new Date();
 today.setHours(0, 0, 0, 0);

 for (let i = 0; i < 7; i++) {
   const date = new Date(currentWeekStart);
   date.setDate(date.getDate() + i);
   const isToday = date.toDateString() === today.toDateString();
   
   const daySessions = sessions.filter(session => {
     const sessionDate = new Date(session.start_time);
     return sessionDate.toDateString() === date.toDateString();
   }).sort((a, b) => 
     new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
   );

   calendar.push(
     <Card 
       key={i} 
       className={`flex flex-col ${isToday ? 'border-blue-500 border-2' : ''}`}
     >
       <CardHeader className={`p-2 mb-2 ${isToday ? 'bg-blue-100' : 'bg-gray-100'}`}>
         <div className="flex flex-col items-center">
           <div className="font-bold text-lg">{dayNames[i]}</div>
           <div className="text-sm text-gray-600">{formatDate(date)}</div>
         </div>
       </CardHeader>
       <CardContent className="p-2 flex-grow overflow-y-auto max-h-[400px]">
         <div className="space-y-2">
           {daySessions.length === 0 ? (
             <div className="text-center py-4 text-gray-400 text-sm">
               No sessions
             </div>
           ) : (
             daySessions.map(session => {
               const sessionDate = new Date(session.start_time);
               const totalBookings = session.bookings?.length || 0;
               const isBooked = bookings.some(b => b.booked_session_id === session.id);
               const isDisabled = sessionDate < today || totalBookings >= session.max_participants || isBooked;
               
               return (
                 <Button
                   key={session.id}
                   variant={isBooked ? "secondary" : isDisabled ? "ghost" : "default"}
                   size="sm"
                   className={`w-full p-2 grid-cols-2 md:grid-cols-1 grid gap-0.5 h-auto
                     ${isBooked ? 'bg-green-50 hover:bg-green-100' : ''}
                     ${isDisabled && !isBooked ? 'opacity-80' : ''}`}
                   onClick={() => !isDisabled && !isBooked && showSessionDetails(session)}
                   disabled={isDisabled}
                 >
                   <div className="font-bold text-lg row-span-2 md:row-span-1">
                     {sessionDate.toLocaleTimeString('el-GR', { 
                       hour: '2-digit', 
                       minute: '2-digit' 
                     })}
                   </div>
                   <div className="text-sm text-gray-400 truncate col-start-2 md:col-start-1 text-center">
                     {session.program_name}
                   </div>
                   <div className="text-sm text-gray-400 truncate text-center flex justify-center">
  <div className={`text-[10px] px-1.5 py-0.5 rounded-full col-start-2 w-14 flex justify-center items-center
    ${isBooked ? 'bg-green-200 text-green-800' : 
      session.available_slots <= 0 ? 'bg-red-200 text-red-800' : 
      'bg-blue-100 text-blue-800'}`}>
    {isBooked ? '✓' : `${session.available_slots}/${session.max_participants}`}
  </div>
</div>
                 </Button>
               );
             })
           )}
         </div>
       </CardContent>
     </Card>
   );
 }
 return calendar;
}

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
    <h1 className="text-2xl font-bold">Πρόγραμμα</h1>
    <MembershipStatus activeSubscription={activeSubscription} />
    {error && (
      <div className="p-4 mb-4 rounded-md bg-red-100 text-red-700">{error}</div>
    )}

<div className="mb-6 flex flex-col space-y-4">
<div className="mb-6 flex flex-col space-y-2">
  <h2 className="text-lg font-semibold text-center">
    Week of {formatDate(currentWeekStart)}
  </h2>
  <div className="flex items-center justify-between gap-2">
    <Button 
      variant="outline"
      onClick={handlePreviousWeek}
      disabled={currentWeekStart <= getWeekStart(new Date())}
      className="flex-1 flex items-center justify-center space-x-2"
    >
      <ChevronLeft className="h-4 w-4" />
      <span className="sm:inline hidden">Previous Week</span>
      <span className="sm:hidden inline">Prev</span>
    </Button>
    <Button 
      variant="outline"
      onClick={handleNextWeek}
      className="flex-1 flex items-center justify-center space-x-2"
    >
      <span className="sm:inline hidden">Next Week</span>
      <span className="sm:hidden inline">Next</span>
      <ChevronRight className="h-4 w-4" />
    </Button>
  </div>
</div>
</div>

      <div className="grid grid-cols-1 sm:grid-cols-7 gap-3 "> {/* Added fixed height */}
  {renderCalendar()}
</div>

<div className="mt-8 space-y-4">
  <Card>
    <CardHeader>
      <h2 className="text-xl font-bold">Οι Κρατήσεις μου</h2>
    </CardHeader>
    <CardContent>
      {bookings && bookings.length > 0 ? (
        <div className="space-y-3">
          {bookings.map((booking) => (
            <div key={booking.id} 
                className="p-4 rounded-lg border bg-white shadow-sm hover:bg-gray-50 transition-colors
                          flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <div className="p-2 rounded-full bg-blue-100">
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">
                    {booking.sessions ? (
                      <>
                        {new Date(booking.sessions.start_time).toLocaleDateString('el-GR')}
                        {' '}
                        <span className="text-gray-600">
                          {new Date(booking.sessions.start_time).toLocaleTimeString('el-GR', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </>
                    ) : (
                      'Session details not available'
                    )}
                  </p>
                  <p className="text-sm text-gray-600">
                    {booking.sessions?.programs.name}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => cancelBooking(booking.id)}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600 font-medium">No future bookings found</p>
          <p className="text-sm text-gray-400 mt-1">
            Book a session from the calendar above
          </p>
        </div>
      )}
    </CardContent>
  </Card>
</div>

<Dialog open={showModal} onOpenChange={setShowModal}>
  <DialogContent className="sm:max-w-[425px]">
    <DialogHeader>
      <DialogTitle className="flex items-center gap-2">
        <Calendar className="h-5 w-5" />
        Book Session
      </DialogTitle>
    </DialogHeader>
    {selectedSession && (
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">Date</label>
            <p className="mt-1">{new Date(selectedSession.start_time).toLocaleDateString()}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Time</label>
            <p className="mt-1">
              {new Date(selectedSession.start_time).toLocaleTimeString('el-GR', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Program</label>
            <p className="mt-1">{selectedSession.program_name}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Available Slots</label>
            <p className="mt-1">{selectedSession.available_slots}</p>
          </div>
        </div>
      </div>
    )}
    <DialogFooter>
      <Button onClick={() => setShowModal(false)} variant="outline">
        Cancel
      </Button>
      <Button 
        onClick={bookSession} 
        disabled={loading}
        className="ml-2"
      >
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Booking...
          </>
        ) : (
          'Confirm Booking'
        )}
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
    </div>
  );
};

export default SessionsPage;