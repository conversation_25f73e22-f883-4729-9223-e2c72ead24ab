// page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { ExerciseRecordsClient } from './client'
import { redirect } from 'next/navigation'
import type { Database } from '@/types/supabase'


export default async function ExerciseRecordsPage() {
  const supabase = createServerComponentClient<Database>({
    cookies
  })
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user || !user.email) {
    redirect('/auth')
  }

  
  const { data: pelatis, error: pelatisError } = await supabase
    .from('pelates')
    .select('id')
    .eq('email', user.email)
    .single()

  if (pelatisError || !pelatis) {
    redirect('/user/profile')
  }

  const fetchTodayWod = async () => {
    const today = new Date().toISOString().split('T')[0];
    
    const { data: wodResponse, error } = await supabase
      .from('wod')
      .select('*')
      .eq('date', today)
      .eq('is_published', true) // Only fetch if published
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return { data: null, error: null };
      }
      return { data: null, error };
    }

    return { data: wodResponse, error: null };
  };

  const [exercisesResponse, recordsResponse, wodResponse] = await Promise.all([
    supabase.from('exercise_movements').select().order('exercise_name'),
    supabase
      .from('exercise_records')
      .select('*, exercise:exercise_movements(*)')
      .eq('pelatis_id', pelatis.id)  
      .order('date_achieved', { ascending: false }),
    fetchTodayWod()
  ]);

  if (!wodResponse.data) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-4">
          <h2 className="text-xl font-semibold mb-2">No workout available for today</h2>
          <p className="text-gray-600">Check back later for updates</p>
        </div>
      </div>
    );
  }

  return (
    <ExerciseRecordsClient 
      initialRecords={recordsResponse.data} 
      exercises={exercisesResponse.data ?? []} // Provide default empty array
      todayWod={wodResponse.data}
      userId={user.id}
      pelatisId={pelatis.id}
    />
  )
}
