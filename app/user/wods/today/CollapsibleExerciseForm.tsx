import { ReactNode } from 'react';
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Plus, X } from "lucide-react";
import { 
  Collapsible,
  CollapsibleContent
} from "@/components/ui/collapsible";

interface CollapsibleExerciseFormProps {
  children: ReactNode;
}

export const CollapsibleExerciseForm = ({ children }: CollapsibleExerciseFormProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-6">
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2"
        >
          {isOpen ? (
            <>
              <X className="h-4 w-4" />
              <span>Close Form</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span>Add Exercise Record</span>
            </>
          )}
        </Button>
      </div>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent>
          <Card className="p-6">
            {children}
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default CollapsibleExerciseForm;