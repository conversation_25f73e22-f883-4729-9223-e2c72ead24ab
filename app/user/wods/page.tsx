'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import ReactMarkdown from 'react-markdown';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Wod {
  id: number;
  date: string;
  content: string;
  is_published: boolean;
}

const WodPage: React.FC = () => {
  const [wods, setWods] = useState<Wod[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [message, setMessage] = useState({ text: '', isError: false });
  const [viewMode, setViewMode] = useState<'swipe' | 'list'>('swipe');

  const supabase = createClientComponentClient();

  const getWeekBounds = (date: Date) => {
    const start = new Date(date);
    start.setDate(start.getDate() - start.getDay() - 1); // Previous Saturday
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setDate(end.getDate() + 13); // Next Saturday
    end.setHours(23, 59, 59, 999);

    return { start, end };
  };

  const fetchWods = useCallback(async () => {
    try {
      const today = new Date();
      const bounds = getWeekBounds(today);
      
      const { data, error } = await supabase
        .from('wod')
        .select('*')
        .gte('date', bounds.start.toISOString())
        .lte('date', bounds.end.toISOString())
        .order('date', { ascending: false });

      if (error) throw error;

      const sortedWods = (data || []).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      setWods(sortedWods);

      // Set current index to today's workout
      const todayIndex = sortedWods.findIndex(wod => 
        new Date(wod.date).toDateString() === today.toDateString()
      );
      setCurrentIndex(todayIndex !== -1 ? todayIndex : 0);

    } catch (error) {
      console.error('Error fetching WODs:', error);
      showMessage('Failed to fetch WODs: ' + (error as Error).message, true);
    }
  }, [supabase]);

  useEffect(() => {
    fetchWods();
  }, [fetchWods]);

  const showMessage = (text: string, isError = false) => {
    setMessage({ text, isError });
    setTimeout(() => setMessage({ text: '', isError: false }), 5000);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const weekday = weekdays[date.getDay()];
    return `${weekday}, ${date.toLocaleDateString()}`;
  };

  const isToday = (dateString: string) => {
    const today = new Date();
    const date = new Date(dateString);
    return date.toDateString() === today.toDateString();
  };

  // Swipe handling functions
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.touches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentIndex < wods.length - 1) {
      setCurrentIndex(curr => curr + 1);
    }
    if (isRightSwipe && currentIndex > 0) {
      setCurrentIndex(curr => curr - 1);
    }

    setTouchStart(0);
    setTouchEnd(0);
  };

  const navigate = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentIndex > 0) {
      setCurrentIndex(curr => curr - 1);
    }
    if (direction === 'next' && currentIndex < wods.length - 1) {
      setCurrentIndex(curr => curr + 1);
    }
  };

  // Render swipeable view
  const renderSwipeableView = () => {
    if (!wods.length) return <div className="p-4">Loading...</div>;

    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <button 
            onClick={() => setViewMode('list')}
            className="text-blue-500 hover:text-blue-700"
          >
            Switch to List View
          </button>
          <div className="flex gap-2">
            <button 
              onClick={() => navigate('prev')}
              disabled={currentIndex === 0}
              className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button 
              onClick={() => navigate('next')}
              disabled={currentIndex === wods.length - 1}
              className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronRight className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div 
          className="flex-1 relative overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div 
            className="absolute w-full h-full transition-transform duration-300"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            <div className="flex absolute left-0 h-full" style={{ width: `${wods.length * 100}%` }}>
              {wods.map((wod) => (
                <div 
                  key={wod.id} 
                  className="h-full" 
                  style={{ width: `${100 / wods.length}%` }}
                >
                  <Card 
                    className={`mx-4 h-full flex flex-col ${
                      isToday(wod.date) ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <CardHeader>
                      <CardTitle>{formatDate(wod.date)}</CardTitle>
                    </CardHeader>
                    <CardContent className="flex-1 overflow-y-auto">
                      <div className="prose max-w-none">
                        <ReactMarkdown>{wod.content}</ReactMarkdown>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-center gap-2 mt-4">
          {wods.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full ${
                currentIndex === index ? 'bg-blue-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    );
  };

  // Render list view (original view)
  const renderListView = () => {
    const groupedWods = groupWodsByWeek(wods);

    return (
      <div>
        <div className="flex justify-between mb-4">
          <h1 className="text-2xl font-bold">WODs</h1>
          <button 
            onClick={() => setViewMode('swipe')}
            className="text-blue-500 hover:text-blue-700"
          >
            Switch to Swipe View
          </button>
        </div>
        {Object.entries(groupedWods).map(([weekStart, weekWods]) => (
          <div key={weekStart} className="mb-8">
            <h2 className="text-xl font-semibold mb-4">{getWeekLabel(weekStart)}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {weekWods.map((wod) => (
                <Card 
                  key={wod.id} 
                  className={`flex flex-col ${isToday(wod.date) ? 'bg-blue-50' : ''}`}
                >
                  <CardHeader>
                    <CardTitle>{formatDate(wod.date)}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow overflow-auto">
                    <div className="mb-2">
                      Status: {wod.is_published ? 'Published' : 'Draft'}
                    </div>
                    <div className="mb-4 prose max-w-none">
                      <ReactMarkdown>{wod.content}</ReactMarkdown>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Helper functions for list view
  const getWeekLabel = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const weekStart = getWeekBounds(date).start;
    const thisWeekStart = getWeekBounds(today).start;

    if (weekStart.toISOString() === thisWeekStart.toISOString()) {
      return "This Week";
    }
    return "Last Week";
  };

  const groupWodsByWeek = (wods: Wod[]) => {
    return wods.reduce((acc: { [key: string]: Wod[] }, wod) => {
      const weekStart = getWeekBounds(new Date(wod.date)).start.toISOString().split('T')[0];
      if (!acc[weekStart]) {
        acc[weekStart] = [];
      }
      acc[weekStart].push(wod);
      return acc;
    }, {});
  };

  return (
    <div className="flex h-screen">
      <div className="p-4 w-full">
        {message.text && (
          <div className={`mb-4 p-2 ${message.isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message.text}
          </div>
        )}
        {viewMode === 'swipe' ? renderSwipeableView() : renderListView()}
      </div>
    </div>
  );
};

export default WodPage;