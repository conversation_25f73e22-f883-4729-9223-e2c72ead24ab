"use client";

import React, { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';


function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}

type ExerciseRecord = {
  id: string;
  exercise_id: string;
  exercise_name: string;
  weight: number;
  sets: number;
  reps: number;
  date_performed: string;
  is_pr: boolean;
  workout_date: string;
  workout_type: string;
  week_number: number;
};

type ExerciseSummary = {
  exercise_id: string;
  exercise_name: string;
  records: ExerciseRecord[];
  max_weight: number;
  first_weight: number;
  current_weight: number;
  progress_percentage: number;
  total_volume: number;
};


export default function StrengthProgramLogPage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [exerciseSummaries, setExerciseSummaries] = useState<ExerciseSummary[]>([]);
  const [selectedExercise, setSelectedExercise] = useState<string | null>(null);
  
  useEffect(() => {
    let isMounted = true;
    
    const fetchExerciseLog = async () => {
      try {
        setLoading(true);
        
        // Step 1: Check auth session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          throw new Error(`Session error: ${sessionError.message}`);
        }
        
        if (!sessionData.session) {
          setErrorMsg('You must be logged in to view your exercise log.');
          return;
        }
        
        // Step 2: Get pelati ID for the authenticated user
        const { data: pelatiData, error: pelatiError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', sessionData.session.user.id)
          .maybeSingle();
        
        if (pelatiError) {
          throw new Error(`User lookup error: ${pelatiError.message}`);
        }
        
        if (!pelatiData) {
          setErrorMsg('Unable to find your profile information.');
          return;
        }
        
        const pelatiId = pelatiData.id;
        
        // Step 3: Get active plan
        const { data: planData, error: planError } = await supabase
          .from('strength_training_plans')
          .select('id')
          .eq('pelatis_id', pelatiId)
          .eq('is_active', true)
          .maybeSingle();
        
        if (planError) {
          throw new Error(`Plan lookup error: ${planError.message}`);
        }
        
        if (!planData) {
          setErrorMsg('No active strength training program found.');
          return;
        }
        
        // Step 4: Get exercise records with workout details
        // First, find all workouts for this plan to get their IDs
        const { data: workoutsData, error: workoutsError } = await supabase
          .from('strength_workouts')
          .select('id, week_number, workout_type, scheduled_date')
          .eq('plan_id', planData.id);
        
        if (workoutsError) {
          throw new Error(`Workouts lookup error: ${workoutsError.message}`);
        }
        
        if (!workoutsData || workoutsData.length === 0) {
          setErrorMsg('No workouts found for your program.');
          return;
        }
        
        // Create a map of workout IDs to their metadata for easy lookup
        const workoutsMap = new Map<string, {
          week_number: number;
          workout_type: string;
          scheduled_date: string;
        }>();
        
        workoutsData.forEach(workout => {
          workoutsMap.set(workout.id, {
            week_number: workout.week_number,
            workout_type: workout.workout_type,
            scheduled_date: workout.scheduled_date
          });
        });
        
        // Get all exercise records for these workouts
        const { data: recordsData, error: recordsError } = await supabase
          .from('strength_exercise_records')
          .select(`
            id,
            exercise_id,
            weight,
            sets,
            reps,
            date_performed,
            is_pr,
            is_warmup,
            workout_id,
            pelatis_id
          `)
          .eq('pelatis_id', pelatiId)
          .eq('is_warmup', false);
        
        if (recordsError) {
          throw new Error(`Records lookup error: ${recordsError.message}`);
        }
        
        if (!recordsData || recordsData.length === 0) {
          if (isMounted) {
            setExerciseSummaries([]);
            setLoading(false);
          }
          return;
        }
        
        // Get the exercise movement details for IDs in the records
        // Use a more compatible approach to get unique exercise IDs
        const exerciseIdsMap: { [key: string]: boolean } = {};
        recordsData.forEach(record => {
          if (record.exercise_id) {
            exerciseIdsMap[record.exercise_id] = true;
          }
        });
        const exerciseIds = Object.keys(exerciseIdsMap);
        
        const { data: exercisesData, error: exercisesError } = await supabase
          .from('exercise_movements')
          .select('id, exercise_name')
          .in('id', exerciseIds);
        
        if (exercisesError) {
          throw new Error(`Exercise lookup error: ${exercisesError.message}`);
        }
        
        // Create a map of exercise IDs to names
        const exerciseMap = new Map<string, string>();
        if (exercisesData) {
          exercisesData.forEach(exercise => {
            exerciseMap.set(exercise.id, exercise.exercise_name);
          });
        }
        
        // Transform records with additional data from maps
        const transformedRecords: ExerciseRecord[] = recordsData
          .filter(record => record.workout_id && workoutsMap.has(record.workout_id)) // Only include records with valid workout IDs
          .map(record => {
            const workout = workoutsMap.get(record.workout_id || '') || {
              week_number: 0,
              workout_type: 'Unknown',
              scheduled_date: new Date().toISOString()
            };
            
            const exerciseName = (record.exercise_id && exerciseMap.get(record.exercise_id)) || 'Unknown Exercise';
            
            return {
              id: record.id,
              exercise_id: record.exercise_id,
              exercise_name: exerciseName,
              weight: record.weight,
              sets: record.sets,
              reps: record.reps,
              date_performed: record.date_performed,
              is_pr: record.is_pr || false,
              workout_date: workout.scheduled_date,
              workout_type: workout.workout_type,
              week_number: workout.week_number
            };
          });
        
        // Group records by exercise ID
        const exerciseRecordsMap = new Map<string, ExerciseRecord[]>();
        transformedRecords.forEach(record => {
          if (!exerciseRecordsMap.has(record.exercise_id)) {
            exerciseRecordsMap.set(record.exercise_id, []);
          }
          const recordsArray = exerciseRecordsMap.get(record.exercise_id);
          if (recordsArray) {
            recordsArray.push(record);
          }
        });
        
        // Calculate summaries for each exercise
        const summaries: ExerciseSummary[] = [];
        exerciseRecordsMap.forEach((records, exerciseId) => {
          if (records.length === 0) return;
          
          const exerciseName = records[0].exercise_name;
          
          // Sort records by date (oldest to newest)
          const sortedByDate = [...records].sort((a, b) => 
            new Date(a.date_performed).getTime() - new Date(b.date_performed).getTime()
          );
          
          const maxWeight = Math.max(...records.map(r => r.weight));
          const firstWeight = sortedByDate[0]?.weight || 0;
          const currentWeight = sortedByDate[sortedByDate.length - 1]?.weight || 0;
          
          // Calculate progress percentage
          const progressPercentage = firstWeight > 0 
            ? ((currentWeight - firstWeight) / firstWeight) * 100 
            : 0;
          
          // Calculate total volume (weight × sets × reps)
          const totalVolume = records.reduce((sum, record) => 
            sum + (record.weight * record.sets * record.reps), 0
          );
          
          summaries.push({
            exercise_id: exerciseId,
            exercise_name: exerciseName,
            records: sortedByDate.reverse(), // Reverse to show newest first in UI
            max_weight: maxWeight,
            first_weight: firstWeight,
            current_weight: currentWeight,
            progress_percentage: progressPercentage,
            total_volume: totalVolume
          });
        });
        
        if (isMounted) {
          setExerciseSummaries(summaries);
          
          // Set initial selected exercise
          if (summaries.length > 0) {
            setSelectedExercise(summaries[0].exercise_id);
          }
        }
      } catch (err) {
        console.error('Error fetching exercise log:', err);
        if (isMounted) {
          setErrorMsg(err instanceof Error ? err.message : 'An unexpected error occurred.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    fetchExerciseLog();
    
    return () => {
      isMounted = false;
    };
  }, [supabase]);
  
  // Get records for selected exercise
  const selectedExerciseRecords = exerciseSummaries.find(
    s => s.exercise_id === selectedExercise
  )?.records || [];
  
  // Get summary for selected exercise
  const selectedSummary = exerciseSummaries.find(
    s => s.exercise_id === selectedExercise
  );
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Exercise Log</h1>
        <Button 
          variant="outline"
          onClick={() => router.push('/user/strength-program')}
        >
          Back to Program
        </Button>
      </div>
      
      {exerciseSummaries.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <p>No exercise records found. Complete some workouts to see your progress here.</p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="mb-6">
            <Select
              value={selectedExercise || ''}
              onValueChange={setSelectedExercise}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an exercise" />
              </SelectTrigger>
              <SelectContent>
                {exerciseSummaries.map(summary => (
                  <SelectItem key={summary.exercise_id} value={summary.exercise_id}>
                    {summary.exercise_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {selectedSummary && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>{selectedSummary.exercise_name} Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium">Starting Weight</h3>
                    <p className="text-xl">{selectedSummary.first_weight} kg</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">Current Weight</h3>
                    <p className="text-xl">{selectedSummary.current_weight} kg</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">Max Weight</h3>
                    <p className="text-xl">{selectedSummary.max_weight} kg</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">Progress</h3>
                    <p className="text-xl">
                      {selectedSummary.progress_percentage.toFixed(1)}%
                    </p>
                  </div>
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium">Total Volume</h3>
                    <p className="text-xl">
                      {selectedSummary.total_volume.toLocaleString()} kg
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          <Card>
            <CardHeader>
              <CardTitle>Exercise History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {selectedExerciseRecords.map((record) => (
                  <div 
                    key={record.id} 
                    className="border rounded-md p-4 flex flex-col md:flex-row md:items-center md:justify-between"
                  >
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">
                          Week {record.week_number}, Workout {record.workout_type}
                        </h3>
                        {record.is_pr && (
                          <Badge variant="default">PR</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {new Date(record.date_performed).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="mt-2 md:mt-0 text-right">
                      <p className="text-lg font-semibold">
                        {record.weight} kg
                      </p>
                      <p className="text-sm text-gray-500">
                        {record.sets} sets × {record.reps} reps
                      </p>
                    </div>
                  </div>
                ))}
                
                {selectedExerciseRecords.length === 0 && (
                  <p>No records found for this exercise.</p>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}