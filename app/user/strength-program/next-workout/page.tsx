"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/hooks/useSupabase';
import { useStrengthProgram } from '@/hooks/useStrengthProgram';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarIcon, ArrowLeftIcon } from 'lucide-react';
import PlateCalculator from '@/components/startingStrength/PlateCalculator';
import WarmupSets from '@/components/startingStrength/WarmupSets';
import WorkoutPlateGuide from '@/components/startingStrength/WorkoutPlateGuide';
import WorkoutCompletionForm from '@/components/startingStrength/WorkoutCompletionForm';
import type { TodaysWorkout, Exercise, CompletedExercise } from '@/types/strength-program';


function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}

export default function NextWorkoutPage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { getActivePlan, completeWorkout } = useStrengthProgram();
  
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [nextWorkout, setNextWorkout] = useState<TodaysWorkout | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [showCompletionForm, setShowCompletionForm] = useState(false);
  const [completedExercises, setCompletedExercises] = useState<Record<string, boolean>>({});
  
  // Generate workout exercises - moved to useCallback to include in dependencies
  const generateWorkoutExercises = useCallback(async (
    workoutType: string,
    weekNumber: number,
    startingWeights: Record<string, number>,
    incrementSettings: Record<string, number>
  ): Promise<Exercise[]> => {
    const exercises: Exercise[] = [];
    
    // Define the exercises for each workout type
    let exerciseNames: string[] = [];
    
    if (workoutType === 'A') {
      exerciseNames = ['Squat', 'Bench Press', 'Deadlift'];
    } else if (workoutType === 'B') {
      exerciseNames = ['Squat', 'Press', 'Power Clean'];
    }
    
    // For each exercise, get its ID and calculate weight
    for (const exerciseName of exerciseNames) {
      // Get exercise ID
      const { data } = await supabase
        .from('exercise_movements')
        .select('id')
        .eq('exercise_name', exerciseName)
        .maybeSingle();
      
      const exerciseId = data ? data.id : exerciseName.toLowerCase().replace(/\s/g, '');
      
      // Map exercise name to key in starting weights
      const keyMapping: Record<string, string> = {
        'Squat': 'squat',
        'Bench Press': 'bench',
        'Press': 'press',
        'Deadlift': 'deadlift',
        'Power Clean': 'powerClean'
      };
      
      const key = keyMapping[exerciseName];
      
      if (!key) continue;
      
      const startWeight = startingWeights[key] || 20; // Default to bar weight
      const increment = incrementSettings[key] || 2.5; // Default increment
      
      // Calculate weight based on week number
      const incrementWeeks = weekNumber - 1;
      let currentWeight = startWeight + (increment * incrementWeeks);
      
      // Round to nearest 2.5kg
      currentWeight = Math.round(currentWeight / 2.5) * 2.5;
      
      // Determine sets and reps based on exercise
      let sets = 3;
      let reps = 5;
      
      // Deadlift is typically 1 set of 5 reps in Starting Strength
      if (exerciseName === 'Deadlift') {
        sets = 1;
      }
      
      // Power Clean is typically 5 sets of 3 reps
      if (exerciseName === 'Power Clean') {
        sets = 5;
        reps = 3;
      }
      
      exercises.push({
        exercise_id: exerciseId,
        exercise_name: exerciseName,
        weight: currentWeight,
        sets,
        reps,
        is_warmup: false
      });
    }
    
    return exercises;
  }, [supabase]);
  
  useEffect(() => {
    let isMounted = true;
    
    const fetchNextWorkout = async () => {
      try {
        setLoading(true);
        
        // Get current user
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          setErrorMsg('You must be logged in to view your workouts.');
          setLoading(false);
          return;
        }
        
        // Get pelatis ID
        const { data: pelatiData } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', sessionData.session.user.id)
          .single();
        
        if (!pelatiData) {
          setErrorMsg('User profile not found.');
          setLoading(false);
          return;
        }
        
        setUserId(pelatiData.id);
        
        // Get active plan
        const plan = await getActivePlan(pelatiData.id);
        if (!plan) {
          setErrorMsg('No active training plan found.');
          setLoading(false);
          return;
        }
        
        // Get next scheduled workout
        const { data: nextWorkoutData } = await supabase
          .from('strength_workouts')
          .select('*')
          .eq('plan_id', plan.id)
          .eq('status', 'scheduled')
          .order('scheduled_date', { ascending: true })
          .limit(1)
          .single();
        
        if (!nextWorkoutData) {
          setErrorMsg('No upcoming workouts found.');
          setLoading(false);
          return;
        }
        
        // Calculate exercises for this workout
        const exercises = await generateWorkoutExercises(
          nextWorkoutData.workout_type,
          nextWorkoutData.week_number,
          plan.starting_weights || {}, // Add fallback to empty object
          plan.increment_settings || {} // Add fallback to empty object
        );
        
        const workout: TodaysWorkout = {
          workout_id: nextWorkoutData.id,
          week_number: nextWorkoutData.week_number,
          workout_type: nextWorkoutData.workout_type,
          exercises,
          scheduled_date: nextWorkoutData.scheduled_date
        };
        
        if (isMounted) {
          setNextWorkout(workout);
          setLoading(false);
        }
      } catch (err) {
        console.error('Error fetching next workout:', err);
        if (isMounted) {
          setErrorMsg((err as Error).message || 'An error occurred while fetching your next workout.');
          setLoading(false);
        }
      }
    };
    
    fetchNextWorkout();
    
    return () => {
      isMounted = false;
    };
  }, [supabase, getActivePlan, generateWorkoutExercises]);
  
  // Mark an exercise as completed
  const markExerciseCompleted = (exerciseId: string) => {
    setCompletedExercises(prev => ({
      ...prev,
      [exerciseId]: true
    }));
  };
  
  // Check if all exercises are completed
  const allExercisesCompleted = () => {
    if (!nextWorkout?.exercises) return false;
    
    const workSets = nextWorkout.exercises.filter(ex => !ex.is_warmup);
    return workSets.every(exercise => 
      completedExercises[exercise.exercise_id]
    );
  };
  
  // Open workout completion form
  const handleStartCompletion = () => {
    setShowCompletionForm(true);
  };
  
  // Handle workout completion from form
// Update the function signature
const handleWorkoutComplete = async (workoutId: string, completedExercises: CompletedExercise[]) => {
  if (!workoutId || !userId) return;
  
  try {
    // Complete the workout
    await completeWorkout(workoutId, completedExercises);
    
    // Close the form
    setShowCompletionForm(false);
    
    // Redirect back to main program page
    router.push('/user/strength-program');
  } catch (err) {
    console.error('Error completing workout:', err);
    setErrorMsg((err as Error).message || 'Failed to complete workout');
  }
};
  
  // Get only work sets (not warmup sets)
  const getWorkSets = (exercises: Exercise[]) => {
    return exercises.filter(ex => !ex.is_warmup);
  };
  
  if (loading) return <LoadingSpinner />;
  
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="mb-6 flex justify-between items-center">
        <Button 
          variant="ghost"
          onClick={() => router.push('/user/strength-program')}
          className="inline-flex items-center"
        >
          <ArrowLeftIcon className="mr-2 h-4 w-4" />
          Back to Program
        </Button>
        
        <h1 className="text-xl font-bold">Next Scheduled Workout</h1>
      </div>
      
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      {nextWorkout ? (
        <>
          {/* Date information */}
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center">
              <CalendarIcon className="mr-2 h-5 w-5 text-gray-500" />
              <span className="text-lg font-medium">
                Scheduled for {new Date(nextWorkout.scheduled_date).toLocaleDateString()}
              </span>
            </div>
            <Badge>Week {nextWorkout.week_number}</Badge>
          </div>
          
          {/* Plate Guide - Show at the top for better planning */}
          {nextWorkout.exercises && nextWorkout.exercises.length > 0 && (
            <WorkoutPlateGuide exercises={getWorkSets(nextWorkout.exercises)} />
          )}
          
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Workout {nextWorkout.workout_type}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Group exercises by name to show warmup + work sets together */}
                {Array.from(new Set(nextWorkout.exercises.map(ex => ex.exercise_id))).map((exerciseId) => {
                  const exerciseGroup = nextWorkout.exercises.filter(ex => ex.exercise_id === exerciseId);
                  const workSets = exerciseGroup.filter(ex => !ex.is_warmup);
                  
                  if (workSets.length === 0) return null;
                  
                  const mainExercise = workSets[0];
                  
                  return (
                    <div key={exerciseId} className="border rounded-lg p-4">
                      <div className="flex flex-row items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold">{mainExercise.exercise_name}</h3>
                        {completedExercises[exerciseId] ? (
                          <Badge>Completed</Badge>
                        ) : (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => markExerciseCompleted(exerciseId)}
                          >
                            Mark as Done
                          </Button>
                        )}
                      </div>
                      
                      <Tabs defaultValue="warmup">
                        <TabsList className="mb-2">
                          <TabsTrigger value="warmup">Warm-up Sets</TabsTrigger>
                          <TabsTrigger value="work">Work Sets</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="warmup">
                          <WarmupSets 
                            workWeight={mainExercise.weight} 
                            workReps={mainExercise.reps}
                            showPlateCalculations={true}
                          />
                        </TabsContent>
                        
                        <TabsContent value="work">
                          <div className="space-y-2">
                            <div className="text-blue-600 font-medium">
                              {mainExercise.weight} kg × {mainExercise.reps} reps × {mainExercise.sets} sets
                            </div>
                            
                            <PlateCalculator 
                              targetWeight={mainExercise.weight} 
                              showVisualization={true}
                            />
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  );
                })}
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                className="w-full"
                onClick={handleStartCompletion}
                disabled={allExercisesCompleted()}
              >
                {allExercisesCompleted() ? "All Exercises Completed" : "Start Workout Completion"}
              </Button>
            </CardFooter>
          </Card>
        </>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <p>No upcoming workouts found. Your program might be completed or needs to be set up again.</p>
            <Button 
              className="mt-4"
              onClick={() => router.push('/user/strength-program/setup')}
            >
              Set Up Program
            </Button>
          </CardContent>
        </Card>
      )}
      
      {/* Workout Completion Dialog */}
      {nextWorkout && (
        <Dialog open={showCompletionForm} onOpenChange={setShowCompletionForm}>
          <DialogContent className="sm:max-w-[600px]">
            <WorkoutCompletionForm
              workoutId={nextWorkout.workout_id}
              exercises={nextWorkout.exercises}
              onWorkoutComplete={handleWorkoutComplete}
              onCancel={() => setShowCompletionForm(false)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}