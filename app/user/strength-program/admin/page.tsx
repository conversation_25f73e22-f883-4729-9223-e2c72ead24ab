"use client";

import React, { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { useRouter } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>T<PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
// Define proper types for our status objects
interface TableStatus {
  exists: boolean;
  count?: number;
  error?: string;
}

interface ExerciseStatus {
  count: number;
  missing: string[];
  names: string[];
}

interface StatusState {
  tables: Record<string, TableStatus>;
  exercises: ExerciseStatus;
}

// Define the tables we want to check
const TABLES_TO_CHECK = [
  'strength_training_plans',
  'strength_workouts',
  'strength_exercise_records',
  'exercise_movements'
] as const;

// Type for our known tables
type KnownTable = typeof TABLES_TO_CHECK[number];

// Check if a string is a known table name
function isKnownTable(tableName: string): tableName is KnownTable {
  return (TABLES_TO_CHECK as readonly string[]).includes(tableName);
}

// Type for exercise data
interface ExerciseMovement {
  id?: string;
  exercise_name: string;
  body_part: string;
  equipment: string;
  expertise_level: string;
  movement_category: string;
  movement_pattern?: string[];
  created_at?: string;
}

// List of required exercises
const REQUIRED_EXERCISES: ExerciseMovement[] = [
  {
    exercise_name: 'Squat',
    body_part: 'legs',
    equipment: 'barbell',
    expertise_level: 'intermediate',
    movement_category: 'compound'
  },
  {
    exercise_name: 'Bench Press',
    body_part: 'chest',
    equipment: 'barbell',
    expertise_level: 'intermediate',
    movement_category: 'compound'
  },
  {
    exercise_name: 'Deadlift',
    body_part: 'back',
    equipment: 'barbell',
    expertise_level: 'intermediate',
    movement_category: 'compound'
  },
  {
    exercise_name: 'Press',
    body_part: 'shoulders',
    equipment: 'barbell',
    expertise_level: 'intermediate',
    movement_category: 'compound'
  },
  {
    exercise_name: 'Power Clean',
    body_part: 'full body',
    equipment: 'barbell',
    expertise_level: 'advanced',
    movement_category: 'compound'
  }
];

export default function StrengthProgramDebug() {
  const router = useRouter();
  const { supabase } = useSupabase();
  
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<StatusState>({
    tables: {},
    exercises: { count: 0, missing: [], names: [] }
  });
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [successMsg, setSuccessMsg] = useState<string | null>(null);
  
  /**
   * Check if a table exists by attempting to query it
   */
  const checkTableExists = async (tableName: string): Promise<TableStatus> => {
    try {
      if (!isKnownTable(tableName)) {
        return { exists: false, error: 'Unknown table name' };
      }

      // Now TypeScript knows this is a valid table name
      const { error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        // PGRST116 is the "relation does not exist" error code from PostgREST
        if (error.code === 'PGRST116') {
          return { exists: false };
        }
        return { exists: false, error: error.message };
      }
      
      // If we got here, the table exists - now get the count
      return { 
        exists: true, 
        count: count ?? 0
      };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      return { exists: false, error: error.message };
    }
  };
  
  /**
   * Refresh the status of all tables and exercises
   */
  const refreshStatus = async () => {
    setLoading(true);
    setErrorMsg(null);
    
    try {
      // Check each table
      const tableResults: Record<string, TableStatus> = {};
      
      for (const table of TABLES_TO_CHECK) {
        tableResults[table] = await checkTableExists(table);
      }
      
      // Get exercises if the table exists
      let exerciseStatus: ExerciseStatus = { count: 0, missing: [], names: [] };
      
      if (tableResults['exercise_movements']?.exists) {
        try {
          const { data, error } = await supabase
            .from('exercise_movements')
            .select('exercise_name');
          
          if (error) {
            setErrorMsg(`Error getting exercises: ${error.message}`);
          } else if (data) {
            const foundExercises = data.map(e => e.exercise_name);
            const missingExercises = REQUIRED_EXERCISES
              .map(e => e.exercise_name)
              .filter(name => !foundExercises.includes(name));
              
            exerciseStatus = {
              count: foundExercises.length,
              missing: missingExercises,
              names: foundExercises
            };
          }
        } catch (err) {
          const error = err instanceof Error ? err : new Error(String(err));
          setErrorMsg(`Error getting exercises: ${error.message}`);
        }
      }
      
      // Update status
      setStatus({
        tables: tableResults,
        exercises: exerciseStatus
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error checking status: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    refreshStatus();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  /**
   * Create the exercise_movements table using Supabase management functions
   * Since we can't run direct SQL, we'll use appropriate warnings
   */
  const createExerciseMovementsTable = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    try {
      // Check if the table already exists
      const { exists } = await checkTableExists('exercise_movements');
      
      if (exists) {
        setSuccessMsg('Exercise movements table already exists');
      } else {
        setErrorMsg(`Unable to create table directly via the client. 
          Please run the following SQL in your Supabase dashboard SQL editor:
          
          CREATE TABLE IF NOT EXISTS public.exercise_movements (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            exercise_name TEXT NOT NULL,
            body_part TEXT NOT NULL,
            equipment TEXT NOT NULL,
            expertise_level TEXT NOT NULL,
            movement_category TEXT NOT NULL,
            movement_pattern TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );`);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error creating table: ${error.message}`);
    } finally {
      setLoading(false);
      await refreshStatus();
    }
  };
  
  /**
   * Create the strength_training_plans table using Supabase management functions
   */
  const createTrainingPlansTable = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    try {
      // Check if the table already exists
      const { exists } = await checkTableExists('strength_training_plans');
      
      if (exists) {
        setSuccessMsg('Strength training plans table already exists');
      } else {
        setErrorMsg(`Unable to create table directly via the client. 
          Please run the following SQL in your Supabase dashboard SQL editor:
          
          CREATE TABLE IF NOT EXISTS public.strength_training_plans (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            pelatis_id UUID NOT NULL,
            program_id UUID NOT NULL,
            start_date DATE NOT NULL,
            is_active BOOLEAN DEFAULT true,
            starting_weights JSONB NOT NULL,
            increment_settings JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );`);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error creating table: ${error.message}`);
    } finally {
      setLoading(false);
      await refreshStatus();
    }
  };
  
  /**
   * Create the strength_workouts table using Supabase management functions
   */
  const createWorkoutsTable = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    try {
      // Check if the table already exists
      const { exists } = await checkTableExists('strength_workouts');
      
      if (exists) {
        setSuccessMsg('Strength workouts table already exists');
      } else {
        setErrorMsg(`Unable to create table directly via the client. 
          Please run the following SQL in your Supabase dashboard SQL editor:
          
          CREATE TABLE IF NOT EXISTS public.strength_workouts (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            plan_id UUID NOT NULL,
            workout_type TEXT NOT NULL,
            scheduled_date DATE NOT NULL,
            day_of_week TEXT NOT NULL,
            week_number INTEGER NOT NULL,
            status TEXT NOT NULL DEFAULT 'scheduled',
            completed_date TIMESTAMP WITH TIME ZONE,
            CONSTRAINT fk_plan FOREIGN KEY (plan_id) REFERENCES public.strength_training_plans(id) ON DELETE CASCADE
          );`);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error creating table: ${error.message}`);
    } finally {
      setLoading(false);
      await refreshStatus();
    }
  };
  
  /**
   * Create the strength_exercise_records table using Supabase management functions
   */
  const createExerciseRecordsTable = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    try {
      // Check if the table already exists
      const { exists } = await checkTableExists('strength_exercise_records');
      
      if (exists) {
        setSuccessMsg('Strength exercise records table already exists');
      } else {
        setErrorMsg(`Unable to create table directly via the client. 
          Please run the following SQL in your Supabase dashboard SQL editor:
          
          CREATE TABLE IF NOT EXISTS public.strength_exercise_records (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            workout_id UUID,
            pelatis_id UUID,
            exercise_id UUID,
            weight NUMERIC NOT NULL,
            sets INTEGER NOT NULL,
            reps INTEGER NOT NULL,
            date_performed TIMESTAMP WITH TIME ZONE NOT NULL,
            is_pr BOOLEAN DEFAULT false,
            is_warmup BOOLEAN DEFAULT false,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            CONSTRAINT fk_workout FOREIGN KEY (workout_id) REFERENCES public.strength_workouts(id) ON DELETE SET NULL,
            CONSTRAINT fk_pelatis FOREIGN KEY (pelatis_id) REFERENCES public.pelates(id) ON DELETE CASCADE,
            CONSTRAINT fk_exercise FOREIGN KEY (exercise_id) REFERENCES public.exercise_movements(id) ON DELETE CASCADE
          );`);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error creating table: ${error.message}`);
    } finally {
      setLoading(false);
      await refreshStatus();
    }
  };
  
  /**
   * Create required exercises in the exercise_movements table
   */
  const createRequiredExercises = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    try {
      // If the exercise_movements table doesn't exist, we can't add exercises
      const { exists } = await checkTableExists('exercise_movements');
      
      if (!exists) {
        setErrorMsg('Cannot add exercises: exercise_movements table does not exist');
        setLoading(false);
        return;
      }
      
      let createdCount = 0;
      
      for (const exercise of REQUIRED_EXERCISES) {
        // Check if exercise exists
        const { data: existingExercise } = await supabase
          .from('exercise_movements')
          .select('id')
          .eq('exercise_name', exercise.exercise_name)
          .maybeSingle();
        
        if (!existingExercise) {
          // Insert the exercise
          // TypeScript safety - directly map our exercise to match the table structure
          const { error } = await supabase
            .from('exercise_movements')
            .insert({
              exercise_name: exercise.exercise_name,
              body_part: exercise.body_part,
              equipment: exercise.equipment,
              expertise_level: exercise.expertise_level,
              movement_category: exercise.movement_category,
              movement_pattern: exercise.movement_pattern || null
            });
          
          if (error) {
            console.error(`Error creating ${exercise.exercise_name}:`, error);
          } else {
            createdCount++;
          }
        }
      }
      
      setSuccessMsg(`Required exercises created (${createdCount} new exercises)`);
      await refreshStatus();
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setErrorMsg(`Error creating exercises: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Create enum types - since we can't run direct SQL, we'll provide instructions
   */
  const createEnumTypes = async () => {
    setLoading(true);
    setSuccessMsg(null);
    setErrorMsg(null);
    
    setErrorMsg(`Unable to create enum types directly via the client. 
      Please run the following SQL in your Supabase dashboard SQL editor:
      
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'workout_status') THEN
          CREATE TYPE workout_status AS ENUM ('scheduled', 'completed', 'skipped');
        END IF;
      END
      $$;`);
    
    setLoading(false);
  };
  
  // JSX render
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Strength Program Admin</h1>
        <Button 
          variant="outline"
          onClick={() => router.push('/user/strength-program')}
        >
          Back to Program
        </Button>
      </div>
      
      {errorMsg && (
        <Alert className="mb-6 whitespace-pre-line" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      {successMsg && (
        <Alert className="mb-6">
          <AlertDescription>{successMsg}</AlertDescription>
        </Alert>
      )}
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Database Status</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tables">
            <TabsList>
              <TabsTrigger value="tables">Tables</TabsTrigger>
              <TabsTrigger value="exercises">Exercises</TabsTrigger>
            </TabsList>
            <TabsContent value="tables">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(status.tables).map(([table, info]) => (
                    <div key={table} className="border p-4 rounded-md">
                      <h3 className="font-medium">{table}</h3>
                      <p>Status: {info.exists ? 'Exists' : 'Missing'}</p>
                      {info.exists && (
                        <p>Count: {typeof info.count === 'number' ? info.count : 'Unknown'}</p>
                      )}
                      {info.error && (
                        <p className="text-red-500 text-sm">{info.error}</p>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="flex flex-wrap gap-4">
                  <Button
                    onClick={createEnumTypes}
                    disabled={loading}
                  >
                    Create Enum Types
                  </Button>
                  
                  <Button
                    onClick={createExerciseMovementsTable}
                    disabled={loading}
                  >
                    Create Exercise Movements Table
                  </Button>
                  
                  <Button
                    onClick={createTrainingPlansTable}
                    disabled={loading}
                  >
                    Create Training Plans Table
                  </Button>
                  
                  <Button
                    onClick={createWorkoutsTable}
                    disabled={loading}
                  >
                    Create Workouts Table
                  </Button>
                  
                  <Button
                    onClick={createExerciseRecordsTable}
                    disabled={loading}
                  >
                    Create Exercise Records Table
                  </Button>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="exercises">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Exercise Status</h3>
                  <p>Total Exercises: {status.exercises.count}</p>
                  
                  {status.exercises.missing.length > 0 && (
                    <div className="mt-2">
                      <p className="text-red-500">Missing Exercises:</p>
                      <ul className="list-disc pl-6">
                        {status.exercises.missing.map((name) => (
                          <li key={name}>{name}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {status.exercises.names.length > 0 && (
                    <div className="mt-2">
                      <p>Available Exercises:</p>
                      <div className="max-h-48 overflow-y-auto">
                        <ul className="list-disc pl-6">
                          {status.exercises.names.map((name) => (
                            <li key={name}>{name}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
                
                <Button
                  onClick={createRequiredExercises}
                  disabled={loading}
                >
                  Create Required Exercises
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={refreshStatus}
            disabled={loading}
            className="mr-4"
          >
            Refresh Status
          </Button>
          
          <div className="flex gap-4 mt-4">
            <Button
              onClick={() => router.push('/user/strength-program/setup')}
            >
              Go to Setup
            </Button>
            
            <Button
              onClick={() => router.push('/user/strength-program/schedule')}
            >
              Go to Schedule
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}



