// app/user/strength-program/page.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/hooks/useSupabase';
import { useStrengthProgram } from '@/hooks/useStrengthProgram';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarIcon, PlusIcon } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import PlateCalculator from '@/components/startingStrength/PlateCalculator';
import WarmupSets from '@/components/startingStrength/WarmupSets';
import WorkoutPlateGuide from '@/components/startingStrength/WorkoutPlateGuide';
import WorkoutCompletionForm from '@/components/startingStrength/WorkoutCompletionForm';
import type { TodaysWorkout, Exercise, PlanData  } from '@/types/strength-program';


type ErrorType = {
  message: string;
};

type ExerciseRecord = {
  exercise_id: string;
  weight: number;
  reps: number;
  sets: number;
  is_pr?: boolean;
  notes?: string;
};

// If this is handling Supabase data for a strength program, create an interface:


// Use a simple loading spinner instead of importing a component
function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}



export default function StrengthProgramPage() {
  const router = useRouter();
  const { supabase } = useSupabase(); // Remove session from destructuring
  const [session, setSession] = useState<import('@supabase/supabase-js').Session | null>(null);
  
  const { 
    loading, 
    getTodaysWorkout,
    getActivePlan,
    completeWorkout
  } = useStrengthProgram();
  
  const [userId, setUserId] = useState<string | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [workoutData, setWorkoutData] = useState<TodaysWorkout | null>(null);
  const [activePlan, setActivePlan] = useState<PlanData | null>(null);
  const [completedExercises, setCompletedExercises] = useState<Record<string, boolean>>({});
  const [programProgress, setProgramProgress] = useState(0);
  const [showCompletionForm, setShowCompletionForm] = useState(false);
  

  
   // Add an effect to get the session
   useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      setSession(data.session);
    };
    
    getSession();
  }, [supabase]);
  
  // Fetch user ID and today's workout on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      if (session?.user?.id) {
        try {
          // Get the pelates ID for the authenticated user
          const { data: pelatesData } = await supabase
            .from('pelates')
            .select('id')
            .eq('auth_user_id', session.user.id)
            .single();
          
          if (pelatesData) {
            setUserId(pelatesData.id);
            
            // Get active plan
            const plan = await getActivePlan(pelatesData.id);
            setActivePlan(plan);

            if (!plan) {
              // No active plan, redirect to setup
              router.push('/user/strength-program/setup');
              return;
            }
            
            // Get today's workout
            setWorkoutData(await getTodaysWorkout(pelatesData.id));
            
            // Calculate program progress
            if (plan) {
              const { data: workoutsData } = await supabase
                .from('strength_workouts')
                .select('status')
                .eq('plan_id', plan.id);
              
              if (workoutsData) {
                const total = workoutsData.length;
                const completed = workoutsData.filter(w => w.status === 'completed').length;
                setProgramProgress(Math.round((completed / total) * 100));
              }
            }
          }
        } catch (err: unknown) {
          console.error('Error fetching data:', err);
          const error = err as ErrorType;
          setErrorMsg(error.message || 'Failed to load program data');
        }
      }
    };
    
    fetchUserData();
  }, [session, supabase, getActivePlan, getTodaysWorkout, router]);
  
  // Mark an exercise as completed
  const markExerciseCompleted = (exerciseId: string) => {
    setCompletedExercises(prev => ({
      ...prev,
      [exerciseId]: true
    }));
  };
  
  // Check if all exercises are completed
  const allExercisesCompleted = () => {
    if (!workoutData?.exercises) return false;
    
    const workSets = workoutData.exercises.filter(ex => !ex.is_warmup);
    return workSets.every(exercise => 
      completedExercises[exercise.exercise_id]
    );
  };
  
  // Open workout completion form
  const handleStartCompletion = () => {
    console.log("Opening workout completion form");
    setShowCompletionForm(true);
  };
  
  // Handle workout completion from form
  const handleWorkoutComplete = async (workoutId: string, completedExerciseData: ExerciseRecord[]) => {
    if (!workoutId || !userId) return;
    
    try {
      // Complete the workout with the workout ID
      await completeWorkout(workoutId, completedExerciseData);
      
      // Close the form
      setShowCompletionForm(false);
      
      // Refresh data
      if (userId) {
        const workout = await getTodaysWorkout(userId);
        setWorkoutData(workout);
        const plan = await getActivePlan(userId);
        setActivePlan(plan);
      }
      
      // Update UI
      router.refresh();
    } catch (err: unknown) {
      console.error('Error completing workout:', err);
      const error = err as ErrorType;
      setErrorMsg(error.message || 'Failed to complete workout');
    }
  };

  // Filters just the work sets (not warmup sets)
  const getWorkSets = (exercises: Exercise[]) => {
    return exercises.filter(ex => !ex.is_warmup);
  };
  
  // Render component
  if (loading) return <LoadingSpinner />;
  
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 gap-6">
        {/* Program Overview Card */}
        <Card>
          <CardHeader>
            <CardTitle>Starting Strength Program</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-1">Program Progress</h3>
                <Progress value={programProgress} className="h-2" />
                <p className="text-sm text-muted-foreground mt-1">{programProgress}% complete</p>
              </div>
              
              {activePlan && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-1">Start Date</h3>
                    <p>{new Date(activePlan.start_date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">Status</h3>
                    <Badge variant={activePlan.is_active ? "default" : "secondary"}>
                      {activePlan.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        {/* Today's Workout Card */}
        {workoutData ? (
          <>
            {/* Plate Guide - Show at the top for better planning */}
            {workoutData.exercises && workoutData.exercises.length > 0 && (
              <WorkoutPlateGuide exercises={getWorkSets(workoutData.exercises)} />
            )}
            
            <Card>
              <CardHeader>
                <CardTitle>Today&apos;s Workout: {workoutData.workout_type}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Group exercises by name to show warmup + work sets together */}
                  {Array.from(new Set(workoutData.exercises.map(ex => ex.exercise_id))).map((exerciseId) => {
                    const exerciseGroup = workoutData.exercises.filter(ex => ex.exercise_id === exerciseId);
                    const workSets = exerciseGroup.filter(ex => !ex.is_warmup);
                    
                    if (workSets.length === 0) return null;
                    
                    const mainExercise = workSets[0];
                    
                    return (
                      <div key={exerciseId} className="border rounded-lg p-4">
                        <div className="flex flex-row items-center justify-between mb-2">
                          <h3 className="text-lg font-semibold">{mainExercise.exercise_name}</h3>
                          {completedExercises[exerciseId] ? (
                            <Badge>Completed</Badge>
                          ) : (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => markExerciseCompleted(exerciseId)}
                            >
                              Mark as Done
                            </Button>
                          )}
                        </div>
                        
                        <Tabs defaultValue="warmup">
                          <TabsList className="mb-2">
                            <TabsTrigger value="warmup">Warm-up Sets</TabsTrigger>
                            <TabsTrigger value="work">Work Sets</TabsTrigger>
                          </TabsList>
                          
                          <TabsContent value="warmup">
                            <WarmupSets 
                              workWeight={mainExercise.weight} 
                              workReps={mainExercise.reps}
                              showPlateCalculations={true}
                            />
                          </TabsContent>
                          
                          <TabsContent value="work">
                            <div className="space-y-2">
                              <div className="text-blue-600 font-medium">
                                {mainExercise.weight} kg × {mainExercise.reps} reps × {mainExercise.sets} sets
                              </div>
                              
                              <PlateCalculator 
                                targetWeight={mainExercise.weight} 
                                showVisualization={true}
                              />
                            </div>
                          </TabsContent>
                        </Tabs>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={handleStartCompletion}
                  disabled={allExercisesCompleted()}
                >
                  {allExercisesCompleted() ? "All Exercises Completed" : "Start Today&apos;s Workout"}
                </Button>
              </CardFooter>
            </Card>
          </>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>No Workout Today</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">You don&apos;t have a scheduled workout for today, or you&apos;ve already completed it.</p>
              
              <div className="border rounded-md p-4 bg-blue-50 mb-4">
                <h3 className="font-medium text-blue-700 mb-2">Want to train anyway?</h3>
                <p className="text-gray-600 mb-3">You can choose to do your next scheduled workout early or log a custom workout.</p>
                
                <div className="flex flex-col space-y-2">
                  <Button 
                    variant="default"
                    onClick={() => router.push('/user/strength-program/next-workout')}
                    className="w-full justify-start"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    Do Next Scheduled Workout
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/user/strength-program/custom-workout')}
                    className="w-full justify-start"
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Create Custom Workout
                  </Button>
                </div>
              </div>
              
              <div className="mt-4">
                <Button 
                  variant="outline"
                  onClick={() => router.push('/user/strength-program/schedule')}
                >
                  View Program Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* Quick Links */}
        <div className="grid grid-cols-2 gap-4">
          <Button 
            variant="outline"
            onClick={() => router.push('/user/strength-program/log')}
          >
            View Exercise Log
          </Button>
          <Button 
            variant="outline"
            onClick={() => router.push('/user/strength-program/schedule')}
          >
            Program Schedule
          </Button>
        </div>
      </div>
      
      {/* Workout Completion Dialog */}
      {workoutData && (
        <Dialog open={showCompletionForm} onOpenChange={setShowCompletionForm}>
          <DialogContent className="sm:max-w-[600px]">
            <WorkoutCompletionForm
              workoutId={workoutData.workout_id}
              exercises={workoutData.exercises}
              onWorkoutComplete={handleWorkoutComplete}
              onCancel={() => setShowCompletionForm(false)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}