// app/user/strength-program/schedule/page.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge, type BadgeProps } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import PlateCalculator from '@/components/startingStrength/PlateCalculator';
import WarmupSets from '@/components/startingStrength/WarmupSets';
import WorkoutPlateGuide from '@/components/startingStrength/WorkoutPlateGuide';
import { ChevronDownIcon } from 'lucide-react';

interface Exercise {
  exercise_name: string;
  sets: number;
  reps: number;
  weight: number;
  is_warmup: boolean;
}

interface ScheduleWorkout {
  id: string;
  week_number: number;
  day_of_week: string;
  workout_type: string;
  scheduled_date: string;
  status: 'scheduled' | 'completed' | 'skipped';
  exercises: Exercise[];
}

interface GroupedWorkouts {
  [key: string]: {
    [key: string]: ScheduleWorkout[];
  };
}

interface WorkoutData {
  id: string;
  week_number: number;
  day_of_week: string;
  workout_type: string;
  scheduled_date: string;
  status: 'scheduled' | 'completed' | 'skipped';
  plan_id: string;
}

interface PlanData {
  id: string;
  pelatis_id: string;
  is_active: boolean;
  start_date: string;
  starting_weights: Record<string, number> | null;
  increment_settings: Record<string, number> | null;
  created_at?: string;
  program_id?: string;
}

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}

export default function StrengthProgramSchedulePage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [groupedWorkouts, setGroupedWorkouts] = useState<GroupedWorkouts>({});
  const [selectedWorkout, setSelectedWorkout] = useState<string | null>(null);
  
  const statusColor: Record<'scheduled' | 'completed' | 'skipped', BadgeProps['variant']> = {
    scheduled: 'default',
    completed: 'outline',  // Changed from 'success' to 'outline'
    skipped: 'secondary'
  } as const;
  
  useEffect(() => {
    let isMounted = true;
    
    const fetchSchedule = async () => {
      try {
        if (!isMounted) return;
        setLoading(true);
        
        // Step 1: Check auth state
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !sessionData.session) {
          setErrorMsg('You must be logged in to view your schedule.');
          setLoading(false);
          return;
        }
        
        // Step 2: Get user's pelatis ID
        const { data: pelatiData, error: pelatiError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', sessionData.session.user.id)
          .maybeSingle();
        
        if (pelatiError || !pelatiData) {
          setErrorMsg('Unable to find your profile information.');
          setLoading(false);
          return;
        }
        
        // Step 3: Get active strength training plan
        const { data: planData, error: planError } = await supabase
          .from('strength_training_plans')
          .select('*')
          .eq('pelatis_id', pelatiData.id)
          .eq('is_active', true)
          .maybeSingle();

        if (planError || !planData) {
          setErrorMsg('No active strength training program found.');
          setLoading(false);
          return;
        }

        // Parse JSON fields and create a properly typed PlanData object
        const typedPlanData: PlanData = {
          ...planData,
          starting_weights: planData.starting_weights ? JSON.parse(planData.starting_weights as string) : null,
          increment_settings: planData.increment_settings ? JSON.parse(planData.increment_settings as string) : null
        };

        // Step 4: Get workouts for this plan
        const { data: workoutsData, error: workoutsError } = await supabase
          .from('strength_workouts')
          .select('*')
          .eq('plan_id', typedPlanData.id)
          .order('scheduled_date', { ascending: true });
        
        if (workoutsError || !workoutsData || workoutsData.length === 0) {
          setErrorMsg('No workouts found for your program.');
          setLoading(false);
          return;
        }
        
        // Step 5: Process workouts with exercise information
        const processedWorkouts = processWorkouts(workoutsData, typedPlanData);
        
        // Step 6: Group workouts by week and date
        const grouped = groupWorkoutsByWeekAndDate(processedWorkouts);
        
        if (isMounted) {
          setGroupedWorkouts(grouped);
          setLoading(false);
        }
        
      } catch (err) {
        console.error("Error in fetchSchedule:", err);
        if (isMounted) {
          setErrorMsg('An unexpected error occurred.');
          setLoading(false);
        }
      }
    };
    
    // Helper function to process workouts and add exercise information
    const processWorkouts = (workouts: WorkoutData[], plan: PlanData): ScheduleWorkout[] => {
      // Create safe versions of potentially null objects
      const safeStartingWeights = plan.starting_weights || {};
      const safeIncrementSettings = plan.increment_settings || {};
      
      return workouts.map(workout => {
        // Define exercises array
        const exercises: Exercise[] = [];
        
        // Determine workout exercises based on workout type
        const exerciseNames = workout.workout_type === 'A' 
          ? ['Squat', 'Bench Press', 'Deadlift']
          : ['Squat', 'Press', 'Power Clean'];

        exerciseNames.forEach(exerciseName => {
          const exerciseKey = exerciseName.toLowerCase().replace(/\s+/g, '');
          const keyMapping: Record<string, string> = {
            'powerclean': 'powerClean',
            'benchpress': 'bench'
          };
          
          const lookupKey = keyMapping[exerciseKey] || exerciseKey;
          
          // Calculate weight using safe objects
          const baseWeight = safeStartingWeights[lookupKey] || 20;
          const increment = safeIncrementSettings[lookupKey] || 2.5;
          const calculatedWeight = baseWeight + (increment * (workout.week_number - 1));
          
          exercises.push({
            exercise_name: exerciseName,
            sets: exerciseName === 'Deadlift' ? 1 : 3,
            reps: 5,
            weight: Math.round(calculatedWeight / 2.5) * 2.5,
            is_warmup: false
          });
        });

        return {
          id: workout.id,
          week_number: workout.week_number,
          day_of_week: workout.day_of_week,
          workout_type: workout.workout_type,
          scheduled_date: workout.scheduled_date,
          status: workout.status,
          exercises
        };
      });
    };

    // Helper function to group workouts by week and date
    const groupWorkoutsByWeekAndDate = (workouts: ScheduleWorkout[]): GroupedWorkouts => {
      const grouped: GroupedWorkouts = {};
      
      workouts.forEach(workout => { // Changed from '_' to 'workout'
        const weekLabel = `Week ${workout.week_number}`;
        
        // Format the date for display
        const date = new Date(workout.scheduled_date);
        const dateLabel = date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'short', 
          day: 'numeric'
        });
        
        // Initialize nested objects if they don't exist
        if (!grouped[weekLabel]) {
          grouped[weekLabel] = {};
        }
        
        if (!grouped[weekLabel][dateLabel]) {
          grouped[weekLabel][dateLabel] = [];
        }
        
        grouped[weekLabel][dateLabel].push(workout);
      });
      
      return grouped;
    };
    
    fetchSchedule();
    
    return () => {
      isMounted = false;
    };
  }, [supabase]);
  
  const toggleWorkoutDetails = (workoutId: string) => {
    setSelectedWorkout(prev => prev === workoutId ? null : workoutId);
  };
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Program Schedule</h1>
        <Button 
          variant="outline"
          onClick={() => router.push('/user/strength-program')}
        >
          Back to Program
        </Button>
      </div>

      {Object.keys(groupedWorkouts).length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <p>No workout schedule found. Your program might need to be set up again.</p>
            <Button 
              className="mt-4"
              onClick={() => router.push('/user/strength-program/setup')}
            >
              Set Up Program
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue={Object.keys(groupedWorkouts)[0] || "Week 1"}>
          <TabsList className="mb-4 flex overflow-x-auto">
            {Object.keys(groupedWorkouts).map((week) => (
              <TabsTrigger key={week} value={week}>
                {week}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {Object.keys(groupedWorkouts).map((week) => (
            <TabsContent key={week} value={week} className="space-y-4">
              {Object.keys(groupedWorkouts[week]).map((date) => (
                <Card key={date}>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>{date}</CardTitle>
                      {groupedWorkouts[week][date].map((workout) => (
                        <Badge key={workout.id} variant={statusColor[workout.status]}>
                          {workout.status.charAt(0).toUpperCase() + workout.status.slice(1)}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>
                  <CardContent>
                    {groupedWorkouts[week][date].map((workout) => (
                      <div key={workout.id} className="space-y-3">
                        <h3 className="font-medium">Workout {workout.workout_type}</h3>
                        
                        {/* Plate Guide */}
                        <WorkoutPlateGuide exercises={workout.exercises} />
                        
                        <div className="grid grid-cols-1 gap-3">
                          {workout.exercises.map((exercise, idx) => (
                            <Collapsible 
                              key={idx} 
                              open={selectedWorkout === `${workout.id}-${idx}`}
                              onOpenChange={() => toggleWorkoutDetails(`${workout.id}-${idx}`)}
                              className="border rounded-md p-3"
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <span className="font-medium">{exercise.exercise_name}</span>
                                  <div className="text-sm text-gray-500">
                                    {exercise.sets} sets × {exercise.reps} reps × {exercise.weight} kg
                                  </div>
                                </div>
                                <CollapsibleTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <ChevronDownIcon className={`h-4 w-4 transition-transform ${
                                      selectedWorkout === `${workout.id}-${idx}` ? 'rotate-180' : ''
                                    }`} />
                                  </Button>
                                </CollapsibleTrigger>
                              </div>
                              
                              <CollapsibleContent className="mt-2 pt-2 border-t">
                                <div className="space-y-3">
                                  <div>
                                    <h4 className="text-sm font-medium mb-1">Warmup Sets</h4>
                                    <WarmupSets 
                                      workWeight={exercise.weight} 
                                      workReps={exercise.reps}
                                      showPlateCalculations={true}
                                    />
                                  </div>
                                  
                                  <div>
                                    <h4 className="text-sm font-medium mb-1">Work Sets</h4>
                                    <div className="bg-blue-50 p-2 rounded">
                                      <div className="text-blue-600 font-medium">
                                        {exercise.weight} kg × {exercise.reps} reps × {exercise.sets} sets
                                      </div>
                                      
                                      <PlateCalculator 
                                        targetWeight={exercise.weight} 
                                        showVisualization={true}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </CollapsibleContent>
                            </Collapsible>
                          ))}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}
