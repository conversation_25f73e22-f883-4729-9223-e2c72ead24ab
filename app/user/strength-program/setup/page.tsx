"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/hooks/useSupabase';
import { useStrengthProgram } from '@/hooks/useStrengthProgram';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Use a simple loading spinner instead of importing a component
function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}

// This function is defined but not used - keeping it in case it's needed in the future
/* 
const resetUserProgram = async (pelatiId: string, supabaseClient: any) => {
  try {
    console.log("Resetting user program for pelatiId:", pelatiId);
    
    // Step 1: Delete all exercise records for this user
    const { error: recordsError } = await supabaseClient
      .from('strength_exercise_records')
      .delete()
      .eq('pelatis_id', pelatiId);
      
    if (recordsError) console.error("Error deleting exercise records:", recordsError);
    
    // Step 2: Get all plans for this user
    const { data: plans } = await supabaseClient
      .from('strength_training_plans')
      .select('id')
      .eq('pelatis_id', pelatiId);
      
    if (plans && plans.length > 0) {
      // Step 3: Delete all workouts for these plans
      const planIds = plans.map(p => p.id);
      
      const { error: workoutsError } = await supabaseClient
        .from('strength_workouts')
        .delete()
        .in('plan_id', planIds);
        
      if (workoutsError) console.error("Error deleting workouts:", workoutsError);
      
      // Step 4: Delete all plans
      const { error: plansError } = await supabaseClient
        .from('strength_training_plans')
        .delete()
        .eq('pelatis_id', pelatiId);
        
      if (plansError) console.error("Error deleting plans:", plansError);
    }
    
    console.log("Reset complete");
    return true;
  } catch (err) {
    console.error("Error in resetUserProgram:", err);
    return false;
  }
};
*/

export default function StrengthProgramSetup() {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { 
    loading: programLoading, 
    createStrengthTrainingPlan, 
    generateAndStoreWorkoutPlan,
    getActivePlan
  } = useStrengthProgram();
  
  const [userId, setUserId] = useState<string | null>(null);
  // In your StrengthProgramSetup component
  const [startingWeights, setStartingWeights] = useState({
    squat: 20, // ~45 lbs in kg
    bench: 20, // ~45 lbs in kg
    press: 20, // ~45 lbs in kg
    deadlift: 40, // ~95 lbs in kg
    powerClean: 20 // ~45 lbs in kg
  });
  
  const [incrementSettings, setIncrementSettings] = useState({
    squat: 2.5, // 5 lbs in kg
    bench: 1, // 2.5 lbs in kg
    press: 1, // 2.5 lbs in kg
    deadlift: 5, // 10 lbs in kg
    powerClean: 2.5 // 5 lbs in kg
  });

  const [startDate, setStartDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  
  const [setupComplete, setSetupComplete] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);


  // Fetch user data after authentication is confirmed
  const fetchUserData = async (userId: string) => {
    try {
      console.log("Fetching user data for ID:", userId);
      
      // Call this function to ensure exercise movements exist
      await ensureExerciseMovementsExist();

      // Get the pelates ID for the authenticated user
      const { data: pelatesData, error: pelatesError } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', userId)
        .single();
      
      if (pelatesError) {
        console.error("Error fetching pelates data:", pelatesError);
        setErrorMsg('User profile not found. Please contact support.');
        setLoading(false);
        return;
      }
      
      if (!pelatesData) {
        console.log("No pelates record found for user");
        setErrorMsg('Your user profile is not set up correctly. Please contact support.');
        setLoading(false);
        return;
      }
      
      setUserId(pelatesData.id);
      console.log("Found pelati_id:", pelatesData.id);
      
      // Get the Starting Strength program ID
      const { data: ssProgram, error: programError } = await supabase
        .from('programs')
        .select('id')
        .eq('name', 'Starting Strength')
        .maybeSingle();
      
      if (programError) {
        console.error("Error fetching Starting Strength program:", programError);
        setErrorMsg('Error finding the Starting Strength program. Please contact support.');
        setLoading(false);
        return;
      }
      
      console.log("Starting Strength program:", ssProgram);
      
      if (!ssProgram) {
        console.log("Starting Strength program not found");
        setErrorMsg('Starting Strength program not found in the system. Please contact support.');
        setLoading(false);
        return;
      }
      
      // Check if the user is enrolled in Starting Strength program
      const { data: programData, error: enrollmentError } = await supabase
        .from('pliromes')
        .select('id')
        .eq('pelates_id', pelatesData.id)
        .eq('course', ssProgram.id);
      
      if (enrollmentError) {
        console.error("Error checking enrollment:", enrollmentError);
        setErrorMsg('Failed to verify program enrollment. Please try again.');
        setLoading(false);
        return;
      }
      
      console.log("Program data:", programData);
      
      const enrolledInSS = programData && programData.length > 0;
      console.log("Is enrolled in Starting Strength:", enrolledInSS);
      setIsEnrolled(enrolledInSS);
      
      if (enrolledInSS) {
        // Check if user already has an active plan
        const activePlan = await getActivePlan(pelatesData.id);
        console.log("Active plan check result:", activePlan);
        
        if (activePlan) {
          // User already has a plan, redirect to program page
          console.log("User has an active plan, redirecting to program page");
          router.push('/user/strength-program');
          return;
        }
      } else {
        setErrorMsg('You need to be enrolled in the Starting Strength program to access this feature.');
      }
      
      setLoading(false);
    } catch (err) {
      console.error("Error in fetchUserData:", err);
      setErrorMsg('An unexpected error occurred. Please try again.');
      setLoading(false);
    }
  };
  
  // Check if user is authenticated
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Manually check session to ensure we have the latest state
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();
        
        console.log("Current session:", currentSession ? "Authenticated" : "Not authenticated");
        
        if (error) {
          console.error("Auth check error:", error);
          setErrorMsg("Authentication error. Please try refreshing the page.");
          setLoading(false);
          return;
        }
        
        if (!currentSession) {
          console.log("No active session found");
          setErrorMsg("You must be logged in to access this page.");
          setLoading(false);
          return;
        }
        
        setAuthChecked(true);
        
        // Continue with user data fetching once we confirmed auth
        fetchUserData(currentSession.user.id);
      } catch (err) {
        console.error("Error in auth check:", err);
        setLoading(false);
        setErrorMsg("Authentication check failed. Please try again.");
      }
    };
    
    checkAuth();
  }, [supabase, router]); // Added fetchUserData to dependencies
  
  // Add this to your setup page right after the auth check
  const ensureExerciseMovementsExist = async () => {
    console.log("Ensuring exercise movements exist...");
    
    const requiredExercises = [
      {
        exercise_name: 'Squat',
        body_part: 'legs',
        equipment: 'barbell',
        expertise_level: 'intermediate',
        movement_category: 'compound'
      },
      {
        exercise_name: 'Bench Press',
        body_part: 'chest',
        equipment: 'barbell',
        expertise_level: 'intermediate',
        movement_category: 'compound'
      },
      {
        exercise_name: 'Deadlift',
        body_part: 'back',
        equipment: 'barbell',
        expertise_level: 'intermediate',
        movement_category: 'compound'
      },
      {
        exercise_name: 'Press',
        body_part: 'shoulders',
        equipment: 'barbell',
        expertise_level: 'intermediate',
        movement_category: 'compound'
      },
      {
        exercise_name: 'Power Clean',
        body_part: 'full body',
        equipment: 'barbell',
        expertise_level: 'advanced',
        movement_category: 'compound'
      }
    ];
    
    // Check if Starting Strength program exists
    const { data: ssProgram } = await supabase
      .from('programs')
      .select('id')
      .eq('name', 'Starting Strength')
      .maybeSingle();
      
    if (!ssProgram) {
      console.log("Creating Starting Strength program...");
      // Create the Starting Strength program
      await supabase
        .from('programs')
        .insert({
          name: 'Starting Strength',
          description: 'A simple, effective strength training program for beginners focusing on compound barbell movements.'
        });
    }
    
    // Check for each exercise
    for (const exercise of requiredExercises) {
      const { data: existingExercise } = await supabase
        .from('exercise_movements')
        .select('id')
        .eq('exercise_name', exercise.exercise_name)
        .maybeSingle();
        
      if (!existingExercise) {
        console.log(`Creating exercise: ${exercise.exercise_name}`);
        // Insert the exercise
        await supabase
          .from('exercise_movements')
          .insert(exercise);
      }
    }
    
    console.log("Exercise setup complete");
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg(null);
    
    if (!userId) {
      setErrorMsg('User ID not found. Please try again later.');
      return;
    }
    
    try {
      setLoading(true);
      console.log("Creating strength training plan...");
      
      // Create a new strength training plan
      const planId = await createStrengthTrainingPlan(
        userId,
        startingWeights,
        incrementSettings,
        startDate
      );
      
      console.log("Plan created with ID:", planId);
      
      if (!planId) {
        throw new Error('Failed to create training plan');
      }
      
      // Generate workout plan for 12 weeks
      console.log("Generating workout plan...");
      await generateAndStoreWorkoutPlan(
        planId,
        userId,
        startingWeights,
        incrementSettings,
        new Date(startDate),
        12
      );
      
      console.log("Workout plan generated successfully!");
      setSetupComplete(true);
      
      // Redirect to strength program page after short delay
      setTimeout(() => {
        router.push('/user/strength-program');
      }, 2000);
      
    } catch (err: unknown) {
      console.error('Error setting up program:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to set up program. Please try again.';
      setErrorMsg(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // Show loading state
  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4">
        <LoadingSpinner />
        <p className="text-center mt-4">
          {authChecked ? "Checking enrollment status..." : "Verifying authentication..."}
        </p>
      </div>
    );
  }

  // If the user is not authenticated, show login message
  if (!authChecked && errorMsg === "You must be logged in to access this page.") {
    return (
      <div className="w-full max-w-4xl mx-auto p-4">
        <Card>
          <CardContent className="pt-6">
            <Alert variant="destructive">
              <AlertDescription>{errorMsg}</AlertDescription>
            </Alert>
            <div className="mt-4">
              <Button 
                onClick={() => router.push('/auth')}
                className="w-full"
              >
                Log In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Render component
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      {setupComplete ? (
        <Alert className="mb-6">
          <AlertDescription>
            Your Starting Strength program has been set up successfully! Redirecting to your program...
          </AlertDescription>
        </Alert>
      ) : !isEnrolled ? (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Starting Strength Program</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              You need to be enrolled in the Starting Strength program to access this feature.
              Please contact your coach for more information.
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Starting Strength Program Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Starting Weights (Kgrs)</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                    <label className="block text-sm font-medium mb-1">Squat (kg)</label>
                      <Input
                        type="number"
                        value={startingWeights.squat}
                        onChange={(e) => setStartingWeights(prev => ({ ...prev, squat: parseFloat(e.target.value) || 0 }))}
                        placeholder="Starting weight"
                        min="20"
                        step="4"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Bench Press</label>
                      <Input
                        type="number"
                        value={startingWeights.bench}
                        onChange={(e) => setStartingWeights(prev => ({ ...prev, bench: parseFloat(e.target.value) || 0 }))}
                        placeholder="Starting weight"
                        min="20"
                        step="4"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Press</label>
                      <Input
                        type="number"
                        value={startingWeights.press}
                        onChange={(e) => setStartingWeights(prev => ({ ...prev, press: parseFloat(e.target.value) || 0 }))}
                        placeholder="Starting weight"
                        min="20"
                        step="4"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Deadlift</label>
                      <Input
                        type="number"
                        value={startingWeights.deadlift}
                        onChange={(e) => setStartingWeights(prev => ({ ...prev, deadlift: parseFloat(e.target.value) || 0 }))}
                        placeholder="Starting weight"
                        min="20"
                        step="5"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Power Clean</label>
                      <Input
                        type="number"
                        value={startingWeights.powerClean}
                        onChange={(e) => setStartingWeights(prev => ({ ...prev, powerClean: parseFloat(e.target.value) || 0 }))}
                        placeholder="Starting weight"
                        min="20"
                        step="5"
                        required
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Weight Progression</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    These are the recommended increments for each workout. You can adjust them if needed.
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                    <label className="block text-sm font-medium mb-1">Squat (kg/workout)</label>
                    <Input
                      type="number"
                      value={incrementSettings.squat} // Corrected to incrementSettings
                      onChange={(e) => setIncrementSettings(prev => ({
                        ...prev,
                        squat: parseFloat(e.target.value) || 0
                      }))}
                      placeholder="Weight increment"
                      min="2.5"
                      step="4"
                      required
                    />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Bench (kgrs/workout)</label>
                      <Input
                        type="number"
                        value={incrementSettings.bench}
                        onChange={(e) => setIncrementSettings(prev => ({ ...prev, bench: parseFloat(e.target.value) || 0 }))}
                        placeholder="Weight increment"
                        min="1"
                        step="1"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Press (kgrs/workout)</label>
                      <Input
                        type="number"
                        value={incrementSettings.press}
                        onChange={(e) => setIncrementSettings(prev => ({ ...prev, press: parseFloat(e.target.value) || 0 }))}
                        placeholder="Weight increment"
                        min="1"
                        step="1"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Deadlift (kgrs/workout)</label>
                      <Input
                        type="number"
                        value={incrementSettings.deadlift}
                        onChange={(e) => setIncrementSettings(prev => ({ ...prev, deadlift: parseFloat(e.target.value) || 0 }))}
                        placeholder="Weight increment"
                        min="5"
                        step="5"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Power Clean (kgrs/workout)</label>
                      <Input
                        type="number"
                        value={incrementSettings.powerClean}
                        onChange={(e) => setIncrementSettings(prev => ({ ...prev, powerClean: parseFloat(e.target.value) || 0 }))}
                        placeholder="Weight increment"
                        min="2.5"
                        step="4"
                        required
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Program Start Date</h3>
                  <div className="mb-4">
                    <Input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      required
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Your program will start on the next Monday after this date.
                    </p>
                  </div>
                </div>
                
                <Button 
                  type="submit"
                  className="w-full"
                  disabled={programLoading || loading}
                >
                  {programLoading || loading ? "Setting up..." : "Generate Program"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}