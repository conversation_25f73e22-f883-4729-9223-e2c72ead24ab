"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/hooks/useSupabase';
import { useStrengthProgram } from '@/hooks/useStrengthProgram';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { PlusIcon, MinusIcon, ArrowLeftIcon } from 'lucide-react';
import PlateCalculator from '@/components/startingStrength/PlateCalculator';
import WorkoutCompletionForm from '@/components/startingStrength/WorkoutCompletionForm';
import type { Exercise } from '@/types/strength-program';
import { TablesInsert } from '@/types/supabase';

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}

// Define the CompletedExercise type based on the strength_exercise_records table type
// but only include the fields that WorkoutCompletionForm will provide
type CompletedExercise = {
  exercise_id: string;
  weight: number;
  sets: number;
  reps: number;
  is_pr: boolean;
  is_warmup: boolean;
  notes?: string;
};

// Type for strength exercise records to insert into the database
type StrengthExerciseRecord = TablesInsert<'strength_exercise_records'>;



// Extend the strength_workouts table type to include our custom is_custom field

// Define a more flexible type for insert operations
type StrengthWorkoutInsert = {
  plan_id: string;
  workout_type: string;
  scheduled_date: string;
  day_of_week: string;
  week_number: number;
  status: "scheduled" | "completed" | "skipped";
  completed_date?: string | null;
  is_custom?: boolean;
};

// Workaround for optional fields that need to be strings
const UUID_PLACEHOLDER = '00000000-0000-0000-0000-000000000000';

export default function CustomWorkoutPage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { completeCustomWorkout } = useStrengthProgram();
  
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [availableExercises, setAvailableExercises] = useState<{id: string, name: string}[]>([]);
  const [showCompletionForm, setShowCompletionForm] = useState(false);
  const [workoutType, setWorkoutType] = useState<string>('');
  const [createdWorkoutId, setCreatedWorkoutId] = useState<string | null>(null);
  
  useEffect(() => {
    let isMounted = true;
    
    const fetchUserAndExercises = async () => {
      try {
        setLoading(true);
        
        // Get current user
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          setErrorMsg('You must be logged in to create workouts.');
          setLoading(false);
          return;
        }
        
        // Get pelatis ID
        const { data: pelatiData } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', sessionData.session.user.id)
          .single();
        
        if (!pelatiData) {
          setErrorMsg('User profile not found.');
          setLoading(false);
          return;
        }
        
        setUserId(pelatiData.id);
        
        // Get all available exercises
        const { data: exercisesData } = await supabase
          .from('exercise_movements')
          .select('id, exercise_name')
          .order('exercise_name');
        
        if (exercisesData) {
          if (isMounted) {
            setAvailableExercises(exercisesData.map(ex => ({
              id: ex.id,
              name: ex.exercise_name
            })));
            
            // Pre-populate with main Starting Strength exercises
            const ssExercises = ['Squat', 'Bench Press', 'Press', 'Deadlift', 'Power Clean'];
            const initialExercises = exercisesData
              .filter(ex => ssExercises.includes(ex.exercise_name))
              .slice(0, 3) // Take only first 3
              .map(ex => ({
                exercise_id: ex.id,
                exercise_name: ex.exercise_name,
                weight: ex.exercise_name === 'Deadlift' ? 40 : 20, // Default weights
                sets: ex.exercise_name === 'Deadlift' ? 1 : 3, // Default sets
                reps: 5,
                is_warmup: false
              }));
            
            setExercises(initialExercises);
          }
        }
        
        if (isMounted) {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        if (isMounted) {
          setErrorMsg((err as Error).message || 'An error occurred while fetching data.');
          setLoading(false);
        }
      }
    };
    
    fetchUserAndExercises();
    
    return () => {
      isMounted = false;
    };
  }, [supabase]);
  
  // Add a new exercise
  const addExercise = () => {
    // Default to an exercise that's not already in the list
    const unusedExercises = availableExercises.filter(ex => 
      !exercises.some(e => e.exercise_id === ex.id)
    );
    
    if (unusedExercises.length === 0) return;
    
    const newExercise: Exercise = {
      exercise_id: unusedExercises[0].id,
      exercise_name: unusedExercises[0].name,
      weight: 20, // Default to bar weight
      sets: 3,
      reps: 5,
      is_warmup: false
    };
    
    setExercises(prev => [...prev, newExercise]);
  };
  
  // Remove an exercise
  const removeExercise = (index: number) => {
    setExercises(prev => prev.filter((_, i) => i !== index));
  };
  
  // Update exercise details
  const updateExercise = (index: number, field: keyof Exercise, value: number | string | boolean) => {
    setExercises(prev => {
      const updated = [...prev];
      
      if (field === 'exercise_id') {
        // If updating the exercise, also update the name
        const exercise = availableExercises.find(ex => ex.id === value);
        if (exercise) {
          updated[index] = {
            ...updated[index],
            exercise_id: value as string,
            exercise_name: exercise.name
          };
        }
      } else {
        updated[index] = {
          ...updated[index],
          [field]: value
        };
      }
      
      return updated;
    });
  };
  
  // Handle form submission
  const handleCreateWorkout = async () => {
    if (!userId || exercises.length === 0 || !workoutType) {
      setErrorMsg('Please select a workout type and add at least one exercise.');
      return;
    }
    
    try {
      setLoading(true);
      
      // Find an active training plan to associate this workout with
      // If none exists, we'll use a placeholder UUID
      const { data: planData } = await supabase
        .from('strength_training_plans')
        .select('id')
        .eq('pelatis_id', userId)
        .eq('is_active', true)
        .maybeSingle();
      
      // Create a custom workout entry with type-safe data
      const workoutData: StrengthWorkoutInsert = {
        plan_id: planData?.id || UUID_PLACEHOLDER, // Use actual plan or placeholder UUID
        workout_type: workoutType,
        scheduled_date: new Date().toISOString().split('T')[0],
        day_of_week: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][new Date().getDay()],
        week_number: 0, // Custom workouts don't have a week number
        status: 'scheduled',
        completed_date: null, // Explicitly set as null for scheduled workouts
        is_custom: true
      };
      
      const { data: newWorkout, error: workoutError } = await supabase
        .from('strength_workouts')
        .insert(workoutData)
        .select('id')
        .single();
      
      if (workoutError || !newWorkout) {
        throw new Error(workoutError?.message || 'Failed to create workout');
      }
      
      // Store the workout ID for completion
      setCreatedWorkoutId(newWorkout.id);
      
      // Open the completion form for the newly created workout
      setShowCompletionForm(true);
      
    } catch (err) {
      console.error('Error creating custom workout:', err);
      setErrorMsg((err as Error).message || 'Failed to create workout');
      setLoading(false);
    }
  };
  
  // Handle workout completion
  const handleWorkoutComplete = async (workoutId: string, completedExercises: CompletedExercise[]) => {
    if (!workoutId || !userId) return;
    
    try {
      // Convert CompletedExercise array to the format expected by completeCustomWorkout
      // which likely expects structures matching strength_exercise_records table
      const exerciseRecords = completedExercises.map(exercise => {
        // Create a properly typed record based on Supabase schema
        const record: Partial<StrengthExerciseRecord> = {
          workout_id: workoutId,
          pelatis_id: userId,
          exercise_id: exercise.exercise_id,
          weight: exercise.weight,
          sets: exercise.sets,
          reps: exercise.reps,
          date_performed: new Date().toISOString(),
          is_pr: exercise.is_pr,
          is_warmup: exercise.is_warmup
        };
        
        // Only add notes if provided
        if (exercise.notes) {
          record.notes = exercise.notes;
        }
        
        return record;
      });
      
      // Now pass the properly typed records to completeCustomWorkout
      await completeCustomWorkout(workoutId, userId, exerciseRecords);
      
      // Close the form
      setShowCompletionForm(false);
      
      // Redirect back to main program page
      router.push('/user/strength-program');
    } catch (err) {
      console.error('Error completing workout:', err);
      setErrorMsg((err as Error).message || 'Failed to complete workout');
    }
  };
  
  if (loading && !showCompletionForm) return <LoadingSpinner />;
  
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="mb-6 flex justify-between items-center">
        <Button 
          variant="ghost"
          onClick={() => router.push('/user/strength-program')}
          className="inline-flex items-center"
        >
          <ArrowLeftIcon className="mr-2 h-4 w-4" />
          Back to Program
        </Button>
        
        <h1 className="text-xl font-bold">Create Custom Workout</h1>
      </div>
      
      {errorMsg && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{errorMsg}</AlertDescription>
        </Alert>
      )}
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Workout Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="workout-type">Workout Type</Label>
              <Select 
                value={workoutType} 
                onValueChange={setWorkoutType}
              >
                <SelectTrigger id="workout-type">
                  <SelectValue placeholder="Select workout type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Workout A</SelectItem>
                  <SelectItem value="B">Workout B</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Separator />
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Exercises</h3>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={addExercise}
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Exercise
                </Button>
              </div>
              
              {exercises.map((exercise, index) => (
                <div key={index} className="border rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <Label htmlFor={`exercise-${index}`}>Exercise</Label>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => removeExercise(index)}
                    >
                      <MinusIcon className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <Select 
                        value={exercise.exercise_id} 
                        onValueChange={(value) => updateExercise(index, 'exercise_id', value)}
                      >
                        <SelectTrigger id={`exercise-${index}`}>
                          <SelectValue placeholder="Select exercise" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableExercises.map(ex => (
                            <SelectItem key={ex.id} value={ex.id}>{ex.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor={`weight-${index}`}>Weight (kg)</Label>
                      <Input 
                        id={`weight-${index}`}
                        type="number"
                        value={exercise.weight.toString()}
                        onChange={(e) => updateExercise(index, 'weight', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="2.5"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`sets-${index}`}>Sets</Label>
                      <Input 
                        id={`sets-${index}`}
                        type="number"
                        value={exercise.sets.toString()}
                        onChange={(e) => updateExercise(index, 'sets', parseInt(e.target.value) || 1)}
                        min="1"
                        max="10"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`reps-${index}`}>Reps</Label>
                      <Input 
                        id={`reps-${index}`}
                        type="number"
                        value={exercise.reps.toString()}
                        onChange={(e) => updateExercise(index, 'reps', parseInt(e.target.value) || 1)}
                        min="1"
                        max="20"
                      />
                    </div>
                  </div>
                  
                  {exercise.weight > 0 && (
                    <div className="mt-4">
                      <PlateCalculator 
                        targetWeight={exercise.weight} 
                        showVisualization={true}
                      />
                    </div>
                  )}
                </div>
              ))}
              
              {exercises.length === 0 && (
                <div className="border rounded-md p-4 text-center text-gray-500">
                  No exercises added. Click &ldquo;Add Exercise&rdquo; to begin.
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            className="w-full"
            disabled={exercises.length === 0 || !workoutType || loading}
            onClick={handleCreateWorkout}
          >
            {loading ? <LoadingSpinner /> : "Continue to Workout"}
          </Button>
        </CardFooter>
      </Card>
      
      {/* Workout Completion Dialog */}
      <Dialog open={showCompletionForm} onOpenChange={setShowCompletionForm}>
        <DialogContent className="sm:max-w-[600px]">
          <WorkoutCompletionForm
            workoutId={createdWorkoutId || "custom"}
            exercises={exercises}
            onWorkoutComplete={handleWorkoutComplete}
            onCancel={() => setShowCompletionForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}