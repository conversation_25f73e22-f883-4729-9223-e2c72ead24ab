'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import type { Database } from "@/types/supabase";
import type { SelectedBodyPart } from '@/data/body-parts/types';
import { BodyHighlighter } from '@/components/BodyHighlighter';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

type SerializedBodyPart = Pick<SelectedBodyPart, 'slug' | 'side' | 'painType' | 'intensity' | 'notes'>;

type SupportThreadMetadata = {
  reportedAt: string;
  painLevel: number;
  duration: string;
  impactOnTraining: string;
  bodyParts: SerializedBodyPart[];
}

export default function InjuryReportForm() {
  const router = useRouter();
  const [selectedParts, setSelectedParts] = useState<SelectedBodyPart[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    painLevel: 5,
    duration: '',
    impactOnTraining: '',
  });

  const supabase = createClientComponentClient<Database>();


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedParts.length) {
      setError('Please select at least one pain point');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user?.email) throw new Error('Authentication required');

      const { data: pelatis, error: pelatesError } = await supabase
        .from('pelates')
        .select('id')
        .eq('email', user.email)
        .single();

      if (pelatesError || !pelatis) {
        throw new Error('Could not find your profile');
      }

      const metadata: SupportThreadMetadata = {
        reportedAt: new Date().toISOString(),
        painLevel: formData.painLevel,
        duration: formData.duration,
        impactOnTraining: formData.impactOnTraining,
        bodyParts: selectedParts.map(({ slug, side, painType, intensity, notes }) => ({
          slug,
          side,
          painType,
          intensity,
          notes
        }))
      };

      const { error: threadError } = await supabase
        .from('support_threads')
        .insert({
          client_id: pelatis.id,
          title: 'Injury Report',
          category: 'injury',
          priority: 'medium',
          status: 'open',
          metadata // Supabase will handle the JSON conversion
        });

      if (threadError) throw threadError;

      router.push('/user/support');
      router.refresh();

    } catch (err) {
      console.error('Submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit report');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSelectPart = (part: SelectedBodyPart) => {
    setSelectedParts(prev => {
      const exists = prev.some(p => p.slug === part.slug && p.side === part.side);
      return exists ? prev : [...prev, part];
    });
  };



  return (
    <div className="container max-w-2xl py-8">
      <form onSubmit={handleSubmit} className="space-y-8">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Select Pain Points</h2>
          <BodyHighlighter
            onSelectPart={handleSelectPart}
            selectedParts={selectedParts}
          />

          {error && (
            <div className="text-red-500 mt-2 text-sm">{error}</div>
          )}

          <div className="mt-4 space-y-2">
            {selectedParts.map((part, i) => (
              <div key={`${part.slug}-${part.side}-${i}`} className="flex items-center gap-2">
                <Badge>
                  {part.slug} {part.side && `(${part.side})`}
                </Badge>
                <button
                  type="button"
                  onClick={() => setSelectedParts(prev =>
                    prev.filter(p => !(p.slug === part.slug && p.side === part.side))
                  )}
                  className="text-sm text-red-500"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>

          <div className="space-y-4 mt-6">
            <div>
              <Label htmlFor="duration">When did this start?</Label>
              <Input
                required
                id="duration"
                value={formData.duration}
                onChange={e => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                placeholder="e.g. 2 days ago"
              />
            </div>

            <div>
              <Label htmlFor="impact">Impact on Training</Label>
              <Textarea
                required
                id="impact"
                value={formData.impactOnTraining}
                onChange={e => setFormData(prev => ({ ...prev, impactOnTraining: e.target.value }))}
                placeholder="How does this affect your workouts?"
              />
            </div>
          </div>

          <Button
            type="submit"
            className="mt-6 w-full"
            disabled={isSubmitting || !selectedParts.length}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Report'}
          </Button>
        </Card>
      </form>
    </div>
  );
}