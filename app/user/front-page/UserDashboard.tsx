import Link from 'next/link'
import { 
  User as UserIcon,
  ClipboardList as <PERSON>lipboardListIcon,
  Ticket,
  CalendarCheck as CalendarCheckIcon,
  Weight as WeightIcon,
  TicketCheck
} from 'lucide-react'
import type { LucideIcon } from 'lucide-react'

type LinkItem = {
  href: string
  title: string
  description: string
}

const iconMap: Record<string, LucideIcon> = {
  '/user/profile': UserIcon,
  '/user/exercise-records': ClipboardListIcon,
  '/user/session-book': Ticket,
  '/user/bookings': TicketCheck,
  '/user/wods': CalendarCheckIcon,
  '/user/weight-tracker': WeightIcon
}

const clientLinks: LinkItem[] = [
  {
    href: '/user/session-book',
    title: 'Κράτηση Συνεδριών',
    description: 'Κλείστε τις προπονήσεις σας'
  },
  {
    href: '/user/wods',
    title: "WOD's",
    description: 'Δείτε τις προπονήσεις της ημέρας'
  },
  {
    href: '/user/exercise-records',
    title: 'Ημερολόγιο Προπόνησης',
    description: 'Καταγραφή των ρεκόρ σας'
  },
  {
    href: '/user/bookings',
    title: 'Οι Κρατήσεις μου',
    description: 'Διαχειριστείτε τις κρατήσεις σας'
  },
  {
    href: '/user/profile',
    title: 'Το Προφίλ μου',
    description: 'Διαχειριστείτε τα στοιχεία σας'
  },
  {
    href: '/user/weight-tracker',
    title: 'Καταγραφή Βάρους',
    description: 'Παρακολουθήστε το βάρος σας'
  }
]

export default function UserDashboard() {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6 text-center">LIFT GYM</h1>
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
        {clientLinks.map((link) => {
          const Icon = iconMap[link.href]
          return (
            <Link 
              key={link.href} 
              href={link.href}
              className="bg-white border rounded-lg shadow-md hover:shadow-xl transition-all 
                         flex flex-col items-center justify-center p-4 
                         space-y-2 text-center group"
            >
              <div className="bg-primary/10 p-3 rounded-full group-hover:bg-primary/20 transition-colors">
                {Icon && (
                  <Icon 
                    className="w-8 h-8 text-primary group-hover:text-primary/80 transition-colors" 
                    strokeWidth={1.5} 
                  />
                )}
              </div>
              <h2 className="font-semibold">{link.title}</h2>
              <p className="text-sm text-muted-foreground">{link.description}</p>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
