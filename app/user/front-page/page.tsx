import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import type { Database } from '@/types/supabase'
import UserDashboard from './UserDashboard'
import AdminDashboard from './AdminDashboard'

export default async function FrontPage() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    redirect('/auth')
  }

  // Check if user is admin
  const { data: roleData } = await supabase
    .from('user_roles')
    .select('role_id')
    .eq('auth_user_id', session.user.id)
    .maybeSingle()

  // If admin, show admin dashboard
  if (roleData?.role_id === 1) {
    return <AdminDashboard />
  }

  // For regular users, check profile completeness
  const { data: profile } = await supabase
    .from('pelates')
    .select('height, date_birth, sex, name, last_name, phone, email')
    .eq('auth_user_id', session.user.id)
    .maybeSingle()

  const hasRequiredFields = 
    profile?.height && 
    profile?.date_birth && 
    profile?.sex &&
    profile?.name &&
    profile?.last_name &&
    profile?.phone &&
    profile?.email

  if (!hasRequiredFields) {
    redirect('/user/profile/onboarding')
  }

  return <UserDashboard />
}
