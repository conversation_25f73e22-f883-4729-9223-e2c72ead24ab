import Link from 'next/link'
import { 
  CreditCard,
  Calendar,
  ClipboardList,
  Users,
  Activity,
  Bell,
  DollarSign,
  Dumbbell
} from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

const adminLinks = [
  {
    href: '/admin/payments/add',
    title: 'New Payment',
    description: 'Create a new payment record',
    icon: CreditCard,
    color: 'text-green-600'
  },
  {
    href: '/admin/daily',
    title: 'Daily Overview',
    description: 'View today\'s sessions and attendance',
    icon: Calendar,
    color: 'text-blue-600'
  },
  {
    href: '/admin/exercise-records',
    title: 'Exercise Records',
    description: 'Manage member exercise records',
    icon: ClipboardList,
    color: 'text-purple-600'
  },
  {
    href: '/admin/users/view',
    title: 'Members',
    description: 'View and manage gym members',
    icon: Users,
    color: 'text-orange-600'
  },
  {
    href: '/admin/reports/kpis',
    title: 'KPI Dashboard',
    description: 'View key performance metrics',
    icon: Activity,
    color: 'text-red-600'
  },
  {
    href: '/admin/notifications/dashboard',
    title: 'Notifications',
    description: 'Manage system notifications',
    icon: Bell,
    color: 'text-yellow-600'
  },
  {
    href: '/admin/expenses',
    title: 'Expenses',
    description: 'Track gym expenses',
    icon: DollarSign,
    color: 'text-emerald-600'
  },
  {
    href: '/admin/wods',
    title: 'WODs',
    description: 'Manage workout programs',
    icon: Dumbbell,
    color: 'text-indigo-600'
  }
]

export default function AdminDashboard() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Quick access to important admin tools
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {adminLinks.map((link) => {
          const Icon = link.icon
          return (
            <Link key={link.href} href={link.href}>
              <Card className="h-full hover:bg-accent/5 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Icon className={`h-5 w-5 ${link.color}`} />
                    <CardTitle className="text-lg">{link.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription>{link.description}</CardDescription>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>
    </div>
  )
}