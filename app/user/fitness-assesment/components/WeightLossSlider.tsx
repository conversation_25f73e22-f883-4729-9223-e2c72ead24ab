import React, { useState } from 'react';

interface WeightLossSliderProps {
  overweightAmount: number;
}

const WeightLossSlider: React.FC<WeightLossSliderProps> = ({ overweightAmount }) => {
  const [calorieDeficit, setCalorieDeficit] = useState<number>(250);

  const updateWeightLossTime = (): string => {
    const monthsToLoseWeight = ((overweightAmount * 7700) / (calorieDeficit * 30)).toFixed(1);
    return `Χρόνος για απώλεια βάρους: ${monthsToLoseWeight} μήνες`;
  };

  return (
    <div className="mt-8">
      <h3 className="text-xl font-bold mb-2">Πλάνο Απώλειας Βάρους</h3>
      <div>
        <label htmlFor="calorieDeficit" className="block text-sm font-medium text-gray-700">
          Ημερήσιο έλλειμμα θερμίδων:
        </label>
        <input
          type="range"
          id="calorieDeficit"
          name="calorieDeficit"
          min="0"
          max="500"
          step="10"
          value={calorieDeficit}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCalorieDeficit(parseInt(e.target.value, 10))}
          className="mt-1 block w-full"
        />
        <span className="text-sm text-gray-600">{calorieDeficit} kcal</span>
      </div>
      <p className="mt-2 font-bold">{updateWeightLossTime()}</p>
    </div>
  );
};

export default WeightLossSlider;