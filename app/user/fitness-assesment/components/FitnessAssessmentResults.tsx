import React from 'react';
import { usePDF } from 'react-to-pdf';
import { calculateFitnessMetrics, getBMICategory } from '@/utils/calculations';
import type { AssessmentData } from '@/types/fitness';


interface MetricBoxProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const MetricBox: React.FC<MetricBoxProps> = ({ title, children, className }) => (
  <div className={`p-4 rounded-lg border border-gray-200 ${className}`}>
    <h2 className="text-lg font-semibold mb-3">{title}</h2>
    {children}
  </div>
);

const BMISlider: React.FC<{ value: number }> = ({ value }) => {
  const position = Math.min((value / 40) * 100, 100);
  
  return (
    <div className="relative w-full h-5 mt-2">
      <div className="absolute w-1/3 h-full bg-green-500 rounded-l-md" />
      <div className="absolute left-1/3 w-1/3 h-full bg-yellow-500" />
      <div className="absolute left-2/3 w-1/3 h-full bg-red-500 rounded-r-md" />
      <div 
        className="absolute w-4 h-4 bg-black rounded-full -mt-0.5 transform -translate-x-1/2"
        style={{ left: `${position}%` }}
      />
    </div>
  );
};

export const FitnessAssessmentResults: React.FC<{ data: AssessmentData }> = ({ data }) => {
  const { toPDF, targetRef } = usePDF({ filename: 'fitness-assessment-results.pdf' });
  const metrics = calculateFitnessMetrics(data);

  return (
    <div ref={targetRef} className="space-y-6 p-6">
      <MetricBox title="ΔΜΣ (BMI)">
        <p className="text-2xl font-bold mb-2">{metrics.bmi}</p>
        <BMISlider value={metrics.bmi} />
        <p className="mt-2">Τρέχον Βάρος: {data.weight} kg</p>
        <p>Κατηγορία BMI: {getBMICategory(metrics.bmi)}</p>
      </MetricBox>

      <MetricBox title="Συνιστώμενο Ιδανικό Βάρος (Hamwi)">
        <p className="text-xl mb-2">{metrics.idealWeight} kg</p>
        <p className="text-gray-600">Υπέρβαρο: {metrics.overweight} kg</p>
      </MetricBox>

      <MetricBox title="Βασικός Μεταβολικός Ρυθμός (BMR)">
        <p className="text-xl mb-2">{metrics.bmr} kcal/ημέρα</p>
        <p className="text-gray-600">
          Επιπλέον θερμίδες ανά ημέρα (για άρση βαρών 3 φορές/εβδομάδα): {metrics.additionalCalories} kcal/ημέρα
        </p>
      </MetricBox>

      <button
        onClick={() => toPDF()}
        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Download PDF
      </button>
    </div>
  );
};

export default FitnessAssessmentResults;