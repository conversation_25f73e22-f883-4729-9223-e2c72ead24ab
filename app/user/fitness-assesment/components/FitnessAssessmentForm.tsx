  // components/FitnessAssessmentForm.tsx
  import React from 'react';
  import { Label } from '@/components/ui/label';
  import { Input } from '@/components/ui/input';
  import { Button } from '@/components/ui/button';
  import { format } from 'date-fns';

  interface FitnessAssessmentFormProps {
    onSubmit: (data: { weight: number; restingHeartRate: number }) => void;
  }

  const FitnessAssessmentForm: React.FC<FitnessAssessmentFormProps> = ({ onSubmit }) => {
    const today = format(new Date(), 'yyyy-MM-dd');

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      const data = {
        weight: Number(formData.get('weight')),
        restingHeartRate: Number(formData.get('restingHeartRate')),
      };
      onSubmit(data);
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="measurementDate">Ημερομηνία Μέτρησης</Label>
          <Input
            type="date"
            id="measurementDate"
            name="measurementDate"
            value={today}
            disabled
            className="mt-1 bg-gray-50"
          />
        </div>

        <div>
          <Label htmlFor="weight">Βάρος (kg)</Label>
          <Input
            type="number"
            id="weight"
            name="weight"
            step="0.1"
            required
            placeholder="Εισάγετε το τρέχον βάρος σας"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="restingHeartRate">Καρδιακός Ρυθμός Ηρεμίας (bpm)</Label>
          <Input
            type="number"
            id="restingHeartRate"
            name="restingHeartRate"
            required
            placeholder="Εισάγετε τον καρδιακό ρυθμό ηρεμίας"
            className="mt-1"
          />
        </div>

        <Button type="submit" className="w-full">
          Υπολογισμός
        </Button>
      </form>
    );
  };

  export default FitnessAssessmentForm;