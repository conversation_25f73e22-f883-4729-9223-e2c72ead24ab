// FitnessAssessmentResults.test.tsx
import { render, screen } from '@testing-library/react';
import { calculateFitnessMetrics } from '@/utils/calculations';
import FitnessAssessmentResults from './FitnessAssessmentResults';

describe('FitnessAssessmentResults', () => {
  const mockData = {
    name: '<PERSON>',
    age: 30,
    sex: 'male' as const,
    weight: 80,
    height: 180,
    restingHeartRate: 70
  };

  test('calculates metrics correctly', () => {
    const metrics = calculateFitnessMetrics(mockData);
    expect(metrics.bmi).toBeCloseTo(24.69, 2);
  });

  test('renders all metric boxes', () => {
    render(<FitnessAssessmentResults data={mockData} />);
    expect(screen.getByText(/ΔΜΣ \(BMI\)/)).toBeInTheDocument();
    expect(screen.getByText(/Συνιστώμενο Ιδανικό Βάρος/)).toBeInTheDocument();
  });
});