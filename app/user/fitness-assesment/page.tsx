'use client'

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import FitnessAssessmentForm from './components/FitnessAssessmentForm';
import FitnessAssessmentResults from './components/FitnessAssessmentResults';
import WeightLossSlider from './components/WeightLossSlider';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from '@/components/ui/button';
import Link from 'next/link';

type Profile = Database['public']['Tables']['pelates']['Row'];

// Match the expected type in FitnessAssessmentResults
type Sex = "male" | "female";

export interface AssessmentData {
  name: string;
  age: number;
  sex: Sex;
  weight: number;
  height: number;
  restingHeartRate: number;
}

interface FormMeasurementData {
  weight: number;
  restingHeartRate: number;
}

export interface ErrorState {
  message: string;
  type: 'error' | 'warning' | 'info' | 'success';
}

export default function FitnessAssessmentPage() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ErrorState | null>(null);
  const [assessmentData, setAssessmentData] = useState<AssessmentData | null>(null);
  const [overweightAmount, setOverweightAmount] = useState<number>(0);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function loadProfile() {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError) throw userError;
        if (!user || !user.email) {
          setError({ message: 'No authenticated user found or email missing', type: 'error' });
          setLoading(false);
          return;
        }

        const { data: profileData, error: profileError } = await supabase
          .from('pelates')
          .select('*')
          .eq('email', user.email)
          .single();

        if (profileError) throw profileError;
        setProfile(profileData);
      } catch (err) {
        console.error('Error loading profile:', err);
        setError({
          message: err instanceof Error ? err.message : 'An error occurred',
          type: 'error'
        });
      } finally {
        setLoading(false);
      }
    }

    loadProfile();
  }, [supabase]);

  const handleAssessmentSubmit = async (formData: FormMeasurementData) => {
    if (!profile) {
      setError({ message: 'Profile not found', type: 'error' });
      return;
    }

    // Validate required fields and their types
    if (!profile.name || !profile.sex || !profile.height) {
      setError({ message: 'Missing required profile information', type: 'error' });
      return;
    }

    // Validate sex is either 'male' or 'female'
    if (profile.sex !== 'male' && profile.sex !== 'female') {
      setError({ message: 'Invalid sex value in profile', type: 'error' });
      return;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Save measurement to fitness_measurements table
      const { error: measurementError } = await supabase
        .from('fitness_measurements')
        .insert({
          pelatis_id: profile.id,
          measurement_date: new Date().toISOString(),
          weight: formData.weight,
          resting_heart_rate: formData.restingHeartRate,
          waist_circumference: 0 // Default value since not collected in form
        });

      if (measurementError) throw measurementError;

      const birthDate = profile.date_birth ? new Date(profile.date_birth) : null;
      const age = birthDate ? calculateAge(birthDate) : 0;

      const newAssessmentData: AssessmentData = {
        name: profile.name,
        age,
        sex: profile.sex as Sex, // Type assertion is safe here due to validation above
        weight: formData.weight,
        height: profile.height,
        restingHeartRate: formData.restingHeartRate
      };

      setAssessmentData(newAssessmentData);
      
      if (profile.height) {
        const heightInMeters = profile.height / 100;
        const idealWeight = 22 * (heightInMeters * heightInMeters);
        setOverweightAmount(Math.max(formData.weight - idealWeight, 0));
      }
    } catch (err) {
      console.error('Error in assessment submission:', err);
      setError({
        message: err instanceof Error ? err.message : 'Error processing assessment',
        type: 'error'
      });
    }
  };

  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const missingFields: string[] = [];
  if (!profile?.height) missingFields.push('height');
  if (!profile?.date_birth) missingFields.push('date of birth');
  if (!profile?.sex) missingFields.push('sex');

  if (missingFields.length > 0) {
    return (
      <div className="bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <Alert variant="destructive">
            <AlertTitle>Missing Profile Information</AlertTitle>
            <AlertDescription className="mt-2">
              <p>To proceed with the fitness assessment, we need the following information:</p>
              <ul className="list-disc pl-4 mt-2 mb-4">
                {missingFields.map(field => (
                  <li key={field} className="capitalize">{field}</li>
                ))}
              </ul>
              <Link href="/user/profile">
                <Button variant="secondary">
                  Complete Profile
                </Button>
              </Link>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6">Fitness Assessment</h1>
        <FitnessAssessmentForm onSubmit={handleAssessmentSubmit} />
        {assessmentData && (
          <>
            <FitnessAssessmentResults data={assessmentData} />
            {overweightAmount > 0 && (
              <WeightLossSlider overweightAmount={overweightAmount} />
            )}
          </>
        )}
      </div>
    </div>
  );
}