'use client'

import React, { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Trash2, CheckCircle } from 'lucide-react';
import { useLocalStorage } from '@hooks/useLocalStorage';

interface Booking {
  id: string;
  booked_session_id: string;
  sessions: Array<{
    start_time: string;
    programs: Array<{
      name: string;
    }>;
  }>;
}

const UserBookings: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(getWeekStart(new Date()));
  const [email, setEmail] = useLocalStorage<string>('userEmail', '');
  const [isValidEmail, setIsValidEmail] = useState(false);
  const supabase = createClientComponentClient();

  const validateEmail = useCallback(async (email: string) => {
    if (!email || !email.includes('@')) {
      setIsValidEmail(false);
      return;
    }
    try {
      const { data, error } = await supabase
        .from('pelates')
        .select('id')
        .eq('email', email)
        .single();

      setIsValidEmail(!!data && !error);
    } catch (error) {
      console.error('Error validating email:', error);
      setIsValidEmail(false);
    }
  }, [supabase]);

  useEffect(() => {
    validateEmail(email);
  }, [email, validateEmail]);

  const fetchBookings = useCallback(async () => {
    if (!email || !isValidEmail) {
      setError('Παρακαλώ εισάγετε ένα έγκυρο email.');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      // First, get the user's ID from the pelates table
      const { data: pelatesData, error: pelatesError } = await supabase
        .from('pelates')
        .select('id')
        .eq('email', email)
        .single();
  
      if (pelatesError) {
        console.error('Supabase error (pelates):', pelatesError);
        throw new Error(`Supabase error: ${pelatesError.message}`);
      }
  
      if (!pelatesData) {
        throw new Error('User not found');
      }
  
      // Then, use the user's ID to fetch bookings
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          booked_session_id,
          sessions (
            start_time,
            programs (name)
          )
        `)
        .eq('pelatis_id', pelatesData.id)
        .gte('sessions.start_time', new Date().toISOString());
  
      if (bookingsError) {
        console.error('Supabase error (bookings):', bookingsError);
        throw new Error(`Supabase error: ${bookingsError.message}`);
      }
      
      if (!bookingsData) {
        throw new Error('No bookings data returned from Supabase');
      }
      
 // Sort the data after fetching
 const sortedData = bookingsData.sort((a, b) => {
  const dateA = new Date(a.sessions[0]?.start_time || 0);
  const dateB = new Date(b.sessions[0]?.start_time || 0);
  return dateA.getTime() - dateB.getTime();
});


      setBookings(sortedData as Booking[]);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setError(`Αποτυχία ανάκτησης κρατήσεων: ${error instanceof Error ? error.message : 'Άγνωστο σφάλμα'}`);
    } finally {
      setLoading(false);
    }
  }, [email, isValidEmail, supabase]);

  useEffect(() => {
    if (isValidEmail) {
      fetchBookings();
    }
  }, [isValidEmail, fetchBookings]);



  const cancelBooking = useCallback(async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);

      if (error) throw error;
      
      setBookings(prevBookings => prevBookings.filter(booking => booking.id !== bookingId));
    } catch (error) {
      setError('Failed to cancel booking. Please try again later.');
      console.error('Error cancelling booking:', error);
    }
  }, [supabase]);


  function getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(d.setDate(diff));
  }

  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
  }


  function renderCalendar() {
    const dayNames = ['Δευ', 'Τρι', 'Τετ', 'Πεμ', 'Παρ', 'Σαβ', 'Κυρ'];
    const calendar = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(currentWeekStart);
      date.setDate(date.getDate() + i);
      const dayBookings = bookings.filter(booking => 
        booking.sessions[0] && new Date(booking.sessions[0].start_time).toDateString() === date.toDateString()
      );

      calendar.push(
        <Card key={i} className="min-h-[100px]">
          <CardHeader className="p-2">
            <div className="font-bold">{dayNames[i]}</div>
            <div>{formatDate(date)}</div>
          </CardHeader>
          <CardContent className="p-2">
            {dayBookings.map(booking => (
              <div key={booking.id} className="mb-2 p-2 bg-gray-100 rounded">
                <p>{booking.sessions[0] && new Date(booking.sessions[0].start_time).toLocaleTimeString('el-GR', { hour: '2-digit', minute: '2-digit' })}</p>
                <p>{booking.sessions[0]?.programs[0]?.name}</p>
                {booking.sessions[0] && new Date(booking.sessions[0].start_time) >= new Date() && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => cancelBooking(booking.id)}
                    className="mt-1"
                  >
                    <Trash2 size={16} />
                  </Button>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      );
    }

    return calendar;
  }

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold mb-4">Οι Κρατήσεις μου</h1>
      
      <div className="mb-4 text-sm">
        <h2 className="text-lg font-semibold mb-2">Οδηγίες:</h2>
        <ul className="list-disc pl-5">
          <li>Εισάγετε τη διεύθυνση email σας στο παρακάτω πεδίο.</li>
          <li>Θα εμφανιστεί ένα πράσινο εικονίδιο όταν το email επιβεβαιωθεί.</li>
          <li>Οι κρατήσεις σας θα εμφανιστούν αυτόματα σε εβδομαδιαίο ημερολόγιο.</li>
          <li>Μπορείτε να διαγράψετε μια κράτηση πατώντας το εικονίδιο κάδου δίπλα στην κράτηση.</li>
        </ul>
      </div>

      <div className="mb-4 relative">
        <label htmlFor="userEmail" className="block mb-2 text-sm font-medium text-gray-900">Διεύθυνση Email:</label>
        <div className="flex items-center">
          <input
            type="email"
            id="userEmail"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Εισάγετε το email σας"
            className="w-full p-2 border rounded pr-10"
          />
          {isValidEmail && (
            <CheckCircle className="absolute right-3 text-green-500" size={20} />
          )}
        </div>
      </div>

      {loading && <div className="flex justify-center items-center h-64"><Loader2 className="animate-spin" /></div>}

      {error && <div className="text-red-500 text-center mb-4">{error}</div>}

      <div className="mb-4 flex justify-between items-center">
        <Button onClick={() => setCurrentWeekStart(new Date(currentWeekStart.setDate(currentWeekStart.getDate() - 7)))}>
          Προηγούμενη Εβδομάδα
        </Button>
        <span className="text-xl font-semibold">
          Εβδομάδα {formatDate(currentWeekStart)}
        </span>
        <Button onClick={() => setCurrentWeekStart(new Date(currentWeekStart.setDate(currentWeekStart.getDate() + 7)))}>
          Επόμενη Εβδομάδα
        </Button>
      </div>

      <h2 className="text-lg font-semibold mb-2">
        Κρατήσεις ({bookings.filter(b => new Date(b.sessions[0]?.start_time) >= new Date()).length})
      </h2>

      <div className="grid grid-cols-7 gap-2">
        {renderCalendar()}
      </div>

      {!loading && bookings.length === 0 && (
        <p className="text-center mt-4">Δεν βρέθηκαν κρατήσεις για αυτή την εβδομάδα.</p>
      )}
    </div>
  );
};

export default UserBookings;