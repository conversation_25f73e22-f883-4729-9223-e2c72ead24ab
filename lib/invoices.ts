// lib/invoices.ts
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

// Generate next invoice number for a given series
export async function generateInvoiceNumber(
  supabase: ReturnType<typeof createClient<Database>>,
  series: string
): Promise<string> {
  // Get the last invoice number for the series
  const { data: lastInvoice } = await supabase
    .from('invoices')
    .select('invoice_number')
    .eq('invoice_series', series)
    .order('created_at', { ascending: false })
    .limit(1);

  // Get the current date
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  // Determine if we need to reset numbering (new year)
  const yearPrefix = `${currentYear}`;

  // Parse the last invoice number
  let lastNumber = 0;
  if (lastInvoice && lastInvoice.length > 0) {
    const lastInvoiceNumber = lastInvoice[0].invoice_number;

    // Check if the last invoice number has the current year prefix
    if (lastInvoiceNumber.startsWith(yearPrefix)) {
      // Extract the number part
      const numberPart = lastInvoiceNumber.substring(yearPrefix.length);
      lastNumber = parseInt(numberPart);
    }
  }

  // Generate the next number
  const nextNumber = lastNumber + 1;

  // Format with year prefix and padding
  return `${yearPrefix}${nextNumber.toString().padStart(4, '0')}`;
}

// Format currency
export function formatCurrency(amount?: number): string {
  if (amount === undefined || amount === null) return '€0.00';
  return new Intl.NumberFormat('el-GR', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
}