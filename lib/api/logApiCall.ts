// lib/api/logApiCall.ts
import { supabase } from '@/lib/supabase';
import { ApiLogType } from '@/types/api';

interface LogApiCallParams {
  endpoint: string;
  method: string;
  requestBody: string | null; // Make requestBody nullable
  responseBody: string | null; // Make responseBody nullable
  statusCode: number;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export async function logApiCall({
  endpoint,
  method,
  requestBody,
  responseBody,
  statusCode,
  userId,
  metadata
}: LogApiCallParams) {
  try {
    // Create log object with defaults for null values
    const logEntry: ApiLogType = {
      endpoint,
      method,
      request_body: requestBody || '', // Use empty string as default if null
      response_body: responseBody || '', // Use empty string as default if null
      status_code: statusCode,
      created_at: new Date().toISOString(),
      user_id: userId || null,
      metadata: metadata || null
    };

    const { error } = await supabase
      .from('api_logs')
      .insert(logEntry);

    if (error) {
      console.error('Error inserting API log:', error);
    }
  } catch (err) {
    console.error('Exception in logApiCall:', err);
  }
}