/**
 * @file /lib/utils.ts
 * @description Utility function for conditionally combining Tailwind CSS classes
 *
 * @overview
 * Provides a robust class name composition utility that:
 * - Merges multiple class inputs
 * - Handles conditional and dynamic class applications
 * - Resolves Tailwind class conflicts
 *
 * @dependencies
 * - clsx: Conditional class name generation
 * - tailwind-merge: Intelligent Tailwind class merging
 *
 * @features
 * - Supports multiple input types (strings, objects, arrays)
 * - Eliminates conflicting Tailwind utility classes
 * - Enables dynamic class composition
 *
 * @usageExamples
 * ```typescript
 * // Simple usage
 * cn('text-red-500', 'font-bold')
 *
 * // Conditional classes
 * cn('base-class', isActive && 'text-blue-500')
 *
 * // Object-based conditionals
 * cn({
 *   'text-red-500': hasError,
 *   'text-green-500': isSuccess
 * })
 * ```
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */

// lib/utils.ts
export const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

export const navigationStyles = {
  menuTrigger: cn(
    "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium",
    "transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none",
    "disabled:pointer-events-none disabled:opacity-50"
  ),
  active: "bg-accent text-accent-foreground",
};

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('el-GR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}
