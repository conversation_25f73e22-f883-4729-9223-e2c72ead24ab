// lib/mydata/xmlParser.ts
import { XMLParser } from 'fast-xml-parser';
import type { ParsedMyDataResponse, MyDataErrorDetail } from '@/types/mydata';

export function parseXmlResponse(xmlString: string): ParsedMyDataResponse {
  const parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "@_",
    textNodeName: "#text",
    isArray: (name) => {
      // Ensure these elements are always treated as arrays even if there's only one
      const alwaysArrayElements = [
        'errors',
        'error',
        'invoices',
        'invoice',
        'invoiceDetails',
        'invoiceDetail',
        'paymentMethodDetails',
        'paymentMethodDetail',
        'classificationDetails',
        'classificationDetail'
      ];
      return alwaysArrayElements.includes(name);
    }
  });

  try {
    // First, handle the wrapped string response format from myDATA
    let actualXml = xmlString;

    // Check if the response is wrapped in a <string> element (common with .NET services)
    const stringWrapperMatch = xmlString.match(/<string[^>]*>(.*?)<\/string>/s);
    if (stringWrapperMatch) {
      console.log('Detected wrapped XML response, extracting and decoding...');

      // Extract the content and decode HTML entities
      actualXml = stringWrapperMatch[1]
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#x27;/g, "'")
        .replace(/&#39;/g, "'");

      console.log('Decoded XML:', actualXml.substring(0, 200) + '...');
    }

    const result = parser.parse(actualXml);
    console.log('Parsed XML structure:', Object.keys(result));

    // Handle different response formats
    if (result.ResponseDoc) {
      // This is the correct format for myDATA responses
      const responses = Array.isArray(result.ResponseDoc.response)
        ? result.ResponseDoc.response
        : [result.ResponseDoc.response];

      const response = responses[0] || {};
      console.log('Processing ResponseDoc with statusCode:', response.statusCode);

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          invoiceMark: response.invoiceMark,
          invoiceUid: response.invoiceUid,
          qrUrl: response.qrUrl,
          cancellationMark: response.cancellationMark,
          classificationMark: response.classificationMark
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.InvoicesDoc) {
      // Legacy format support
      const response = result.InvoicesDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          invoiceMark: response.invoiceMark,
          qrUrl: response.qrUrl,
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.ClassificationDoc) {
      // Parse classification response
      const response = result.ClassificationDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          classificationMark: response.classificationMark
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.PaymentMethodsDoc) {
      // Parse payment methods response
      const response = result.PaymentMethodsDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success'
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.RequestedDoc) {
      // Parse requested documents response
      return {
        statusCode: 'Success'
      };
    } else {
      // Unknown response format
      console.error('Unknown response format. Available keys:', Object.keys(result));
      console.error('Full parsed result:', JSON.stringify(result, null, 2));

      return {
        statusCode: 'Error',
        errors: [{
          code: 'UNKNOWN',
          message: `Unknown response format. Available keys: ${Object.keys(result).join(', ')}`
        }]
      };
    }
  } catch (error) {
    console.error('Error parsing XML response:', error);
    console.error('Original XML string (first 500 chars):', xmlString.substring(0, 500));

    return {
      statusCode: 'Error',
      errors: [{
        code: 'PARSE_ERROR',
        message: `Failed to parse XML response: ${error instanceof Error ? error.message : 'Unknown error'}`
      }]
    };
  }
}

function parseErrors(errors: unknown): MyDataErrorDetail[] {
  if (!errors) return [];

  const errorsObj = errors as Record<string, unknown>;

  // Handle single error (not in array)
  if (errorsObj.error && !Array.isArray(errorsObj.error)) {
    const error = errorsObj.error as Record<string, unknown>;
    return [{
      code: (error.code as string) || 'UNKNOWN',
      message: (error.message as string) || 'Unknown error'
    }];
  }

  // Handle array of errors
  if (Array.isArray(errorsObj.error)) {
    return errorsObj.error.map((error: unknown) => {
      const errorObj = error as Record<string, unknown>;
      return {
        code: (errorObj.code as string) || 'UNKNOWN',
        message: (errorObj.message as string) || 'Unknown error'
      };
    });
  }

  // Handle direct error array
  if (Array.isArray(errors)) {
    return errors.map((error: unknown) => {
      const errorObj = error as Record<string, unknown>;
      return {
        code: (errorObj.code as string) || 'UNKNOWN',
        message: (errorObj.message as string) || 'Unknown error'
      };
    });
  }

  return [];
}