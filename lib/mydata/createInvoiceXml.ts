// lib/mydata/createInvoiceXml.ts
import { XMLBuilder } from 'fast-xml-parser';
import type { Invoice, InvoiceLine, PaymentMethod } from '@/types/mydata';

export function createInvoiceXml(
  invoice: Invoice,
  lines: InvoiceLine[],
  paymentMethods: PaymentMethod[],
  companySettings: {
    vatNumber: string;
    country?: string;
    branch?: string;
    defaultClassificationType?: string;
    defaultClassificationCategory?: string;
  }
) {
  // Determine which elements to include based on invoice type
  const isRetailReceipt = invoice.invoice_type === '11.1' || invoice.invoice_type === '11.2';
  const includeCounterpart = !isRetailReceipt; // For retail receipts, counterpart is forbidden
  const includeQuantity = !isRetailReceipt; // For retail receipts, quantity is forbidden
  // Ensure numeric invoice number for Greek issuers
let invoiceNumber = invoice.invoice_number;
if (companySettings.country === 'GR' || !companySettings.country) {
  // Extract only numeric parts of the invoice number or use a fallback
  const numericOnly = invoiceNumber.replace(/\D/g, '');
  invoiceNumber = numericOnly || '1'; // Fallback to '1' if no numbers found
}

  // Create the basic invoice structure
  const invoiceObj: Record<string, unknown> = {
    'issuer': {
      'vatNumber': companySettings.vatNumber,
      'country': companySettings.country || 'GR',
      'branch': companySettings.branch || '0'
    },
'invoiceHeader': {
  'series': invoice.invoice_series,
  'aa': invoiceNumber, // Use the numeric-only invoice number
  'issueDate': formatDate(invoice.issue_date),
  'invoiceType': invoice.invoice_type,
  'currency': invoice.currency || 'EUR',
}
  };

  // Add counterpart if applicable
  if (includeCounterpart && invoice.client_vat) {
    invoiceObj.counterpart = buildCounterpart(invoice);
  }

  // Add payment methods if available
  if (paymentMethods && paymentMethods.length > 0) {
    invoiceObj.paymentMethods = {
      'paymentMethodDetails': paymentMethods.map(method => ({
        'type': method.payment_type,
        'amount': method.amount.toFixed(2),
        ...(method.payment_info ? {'paymentMethodInfo': method.payment_info} : {})
      }))
    };
  }

  // Prepare line classifications for later use in summary
  const lineClassifications = [];

  // Add invoice details (line items)
  invoiceObj.invoiceDetails = lines.map(line => {
    // Track classifications for summary
    lineClassifications.push({
      classificationType: line.income_classification_type || companySettings.defaultClassificationType,
      classificationCategory: line.income_classification_category || companySettings.defaultClassificationCategory,
      amount: line.net_value
    });

    // Build the line item with appropriate fields
    const lineItem: Record<string, unknown> = {
      'lineNumber': line.line_number,
      'netValue': line.net_value.toFixed(2),
      'vatCategory': line.vat_category,
      'vatAmount': line.vat_amount.toFixed(2),
      'lineComments': line.description
    };

    // Add quantity and measurement unit if applicable
    if (includeQuantity) {
      lineItem.quantity = line.quantity.toFixed(2);
      lineItem.measurementUnit = 1; // 1 = Pieces (default)
    }

    // Add discount option only if true
    if (line.discount_option === true) {
      lineItem.discountOption = true;
    }

    // Add income classification (with proper namespace prefix)
    lineItem.incomeClassification = {
      'icls:classificationType': line.income_classification_type || companySettings.defaultClassificationType,
      'icls:classificationCategory': line.income_classification_category || companySettings.defaultClassificationCategory,
      'icls:amount': line.net_value.toFixed(2)
    };

    return lineItem;
  });

  // Calculate totals
  const totalNetValue = calculateTotalNetValue(lines);
  const totalVatAmount = calculateTotalVatAmount(lines);
  const totalGrossValue = totalNetValue + totalVatAmount;

  // Add invoice summary with classifications
  invoiceObj.invoiceSummary = {
    'totalNetValue': totalNetValue.toFixed(2),
    'totalVatAmount': totalVatAmount.toFixed(2),
    'totalWithheldAmount': '0.00',
    'totalFeesAmount': '0.00',
    'totalStampDutyAmount': '0.00',
    'totalOtherTaxesAmount': '0.00',
    'totalDeductionsAmount': '0.00',
    'totalGrossValue': totalGrossValue.toFixed(2)
  };

  // Add income classifications to summary (required to match line items)
  // Group by classification type and category to consolidate
  const consolidatedClassifications = consolidateClassifications(lineClassifications);

  // If we have classifications, add them to the summary
  if (consolidatedClassifications.length > 0) {
    // For single classification
    if (consolidatedClassifications.length === 1) {
      const classification = consolidatedClassifications[0];
      invoiceObj.invoiceSummary.incomeClassification = {
        'icls:classificationType': classification.classificationType,
        'icls:classificationCategory': classification.classificationCategory,
        'icls:amount': classification.amount.toFixed(2)
      };
    }
    // For multiple classifications
    else {
      invoiceObj.invoiceSummary.incomeClassification = consolidatedClassifications.map(classification => ({
        'icls:classificationType': classification.classificationType,
        'icls:classificationCategory': classification.classificationCategory,
        'icls:amount': classification.amount.toFixed(2)
      }));
    }
  }

  // Create the XML structure
  const invoiceData = {
    'InvoicesDoc': {
      '@_xmlns': 'http://www.aade.gr/myDATA/invoice/v1.0',
      '@_xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
      '@_xmlns:icls': 'https://www.aade.gr/myDATA/incomeClassificaton/v1.0',
      '@_xmlns:ecls': 'https://www.aade.gr/myDATA/expensesClassificaton/v1.0',
      'invoice': invoiceObj
    }
  };

  // Generate XML with proper formatting
  const builder = new XMLBuilder({
    format: true,
    ignoreAttributes: false,
    suppressEmptyNode: true,
  });

  // Add XML declaration manually since the builder doesn't add it
  const xmlString = builder.build(invoiceData);
  return `<?xml version="1.0" encoding="UTF-8"?>\n${xmlString}`;
}

// Helper function to consolidate classifications
function consolidateClassifications(classifications: Array<{
  classificationType: string;
  classificationCategory: string;
  amount: number;
}>) {
  const consolidated: Record<string, {
    classificationType: string;
    classificationCategory: string;
    amount: number;
  }> = {};

  classifications.forEach(cls => {
    const key = `${cls.classificationType}|${cls.classificationCategory}`;
    if (consolidated[key]) {
      consolidated[key].amount += cls.amount;
    } else {
      consolidated[key] = { ...cls };
    }
  });

  return Object.values(consolidated);
}

// Helper function to build counterpart data
function buildCounterpart(invoice: Invoice) {
  const counterpart: {
    vatNumber: string;
    country: string;
    branch: number;
    address?: {
      street: string;
      number: string;
      postalCode: string;
      city: string;
    };
    name?: string;
  } = {
    'vatNumber': invoice.client_vat,
    'country': invoice.client_country || 'GR',
    'branch': 0,
  };

  // Add address if available
  if (invoice.client_address) {
    counterpart.address = {
      'street': invoice.client_address.street,
      'number': invoice.client_address.number,
      'postalCode': invoice.client_address.postal_code,
      'city': invoice.client_address.city
    };
  }

  // Add name if available - only for non-Greek counterparts
  if (invoice.client_name && invoice.client_country !== 'GR') {
    counterpart.name = invoice.client_name;
  }

  return counterpart;
}

// Format date to YYYY-MM-DD
function formatDate(date: string | Date): string {
  const d = new Date(date);
  return d.toISOString().split('T')[0];
}

// Calculate total net value
function calculateTotalNetValue(lines: InvoiceLine[]): number {
  return lines.reduce((sum, line) => sum + Number(line.net_value), 0);
}

// Calculate total VAT amount
function calculateTotalVatAmount(lines: InvoiceLine[]): number {
  return lines.reduce((sum, line) => sum + Number(line.vat_amount), 0);
}