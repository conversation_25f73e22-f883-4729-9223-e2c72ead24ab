// lib/mydata/service.ts
import axios, { AxiosRequestConfig } from 'axios';
import { supabase } from '@/lib/supabase';

// Define environment types
type Environment = 'development' | 'production';

// Define API endpoints
const API_ENDPOINTS = {
  development: {
    sendInvoice: 'https://mydataapidev.aade.gr/SendInvoices',
    cancelInvoice: 'https://mydataapidev.aade.gr/CancelInvoice',
    getTransmittedDocs: 'https://mydataapidev.aade.gr/RequestTransmittedDocs',
    sendIncomeClassification: 'https://mydataapidev.aade.gr/SendIncomeClassification',
    sendExpensesClassification: 'https://mydataapidev.aade.gr/SendExpensesClassification',
    requestDocs: 'https://mydataapidev.aade.gr/RequestDocs',
    requestTransmittedDocs: 'https://mydataapidev.aade.gr/RequestTransmittedDocs',
    requestMyIncome: 'https://mydataapidev.aade.gr/RequestMyIncome',
    requestMyExpenses: 'https://mydataapidev.aade.gr/RequestMyExpenses'
  },
  production: {
    sendInvoice: 'https://mydatapi.aade.gr/myDATA/SendInvoices',
    cancelInvoice: 'https://mydatapi.aade.gr/myDATA/CancelInvoice',
    getTransmittedDocs: 'https://mydatapi.aade.gr/myDATA/RequestTransmittedDocs'
  }
};


// Define user credentials
interface UserCredentials {
  username: string;
  password: string;
}

export class MyDataService {
  private baseUrls: {
    sendInvoice: string;
    cancelInvoice: string;
    getTransmittedDocs: string;
  };
  private credentials: UserCredentials | null = null;

  constructor(environment: Environment = 'development') {
    this.baseUrls = API_ENDPOINTS[environment];

    // Load credentials from environment variables
    this.loadCredentialsFromEnv();
  }

  /**
   * Load credentials from environment variables
   * This is called automatically in the constructor
   * but can also be called explicitly to test credentials
   */
  async loadCredentials(): Promise<void> {
    const username = process.env.NODE_ENV === 'production'
      ? process.env.MYDATA_USERNAME_PROD
      : process.env.MYDATA_USERNAME_DEV;

    const subscriptionKey = process.env.NODE_ENV === 'production'
      ? process.env.MYDATA_SUBSCRIPTION_KEY_PROD
      : process.env.MYDATA_SUBSCRIPTION_KEY_DEV;

    if (!username || !subscriptionKey) {
      throw new Error('MyDATA credentials not found in environment variables');
    }

    this.credentials = {
      username,
      password: subscriptionKey // Still using password internally for backward compatibility
    };

    // Validate credentials by checking they're not empty
    if (!this.credentials.username.trim() || !this.credentials.password.trim()) {
      throw new Error('MyDATA credentials cannot be empty');
    }
  }

  /**
   * Private method to load credentials from environment variables
   * Used internally by the constructor
   */private loadCredentialsFromEnv(): void {
  const username = process.env.NODE_ENV === 'production'
  ? process.env.MYDATA_USERNAME_PROD
  : process.env.MYDATA_USERNAME_DEV;

const subscriptionKey = process.env.NODE_ENV === 'production'
  ? process.env.MYDATA_SUBSCRIPTION_KEY_PROD
  : process.env.MYDATA_SUBSCRIPTION_KEY_DEV;

if (username && subscriptionKey) {
  this.credentials = {
    username,
    password: subscriptionKey // Still using password internally
  };
} else {
  console.warn('MyDATA credentials not found in environment variables');
}
}

  /**
   * Send invoice XML to myDATA API
   * @param invoiceXml The XML document to send
   * @returns The response XML from myDATA
   */
  async sendInvoice(invoiceXml: string): Promise<string> {
    // At the start of sendInvoice
    console.log('sendInvoice called, XML length:', invoiceXml?.length || 0);

    if (!this.credentials) {
      throw new Error('MyDATA credentials not configured');
    }

    // Check credentials
    console.log('Using credentials:', {
      username: this.credentials?.username ? 'Present' : 'Missing',
      password: this.credentials?.password ? 'Present' : 'Missing'
    });

    // Configure request
    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'application/xml',
        'Accept': 'application/xml',
        'aade-user-id': this.credentials.username,
        'ocp-apim-subscription-key': this.credentials.password
      },
      timeout: 30000 // 30 seconds timeout
    };

    // Before making API call
    console.log('Making API call to:', this.baseUrls.sendInvoice);

    // Additional debug logs
    console.log('Headers:', JSON.stringify(config.headers, null, 2));
    console.log('XML Payload (first 500 chars):', invoiceXml.substring(0, 500));

    // Log the request (for debugging)
    await this.logApiCall('request', invoiceXml);

    // Implement retry logic with exponential backoff
    const maxRetries = 3;
    let retryCount = 0;
    let delay = 1000; // Start with 1 second delay

    while (retryCount <= maxRetries) {
      try {
        // Send request
        const response = await axios.post(this.baseUrls.sendInvoice, invoiceXml, config);

        // Log the response (for debugging)
        await this.logApiCall('response', response.data);

        return response.data;
      } catch (error) {
        // Enhanced error logging
        console.error('Error in sendInvoice:', error);

        if (axios.isAxiosError(error)) {
          console.error('Status:', error.response?.status);
          console.error('Status Text:', error.response?.statusText);
          console.error('Headers:', JSON.stringify(error.response?.headers, null, 2));
          console.error('Response Data:', error.response?.data);

          // Special handling for rate limiting
          if (error.response?.status === 429) {
            console.error('Rate limited by myDATA API');

            if (retryCount < maxRetries) {
              // Rate limited - wait and retry
              console.log(`Rate limited. Retrying in ${delay}ms... (Attempt ${retryCount + 1}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, delay));
              retryCount++;
              delay *= 2; // Exponential backoff
              continue; // Skip to next iteration
            } else {
              // If we've exhausted retries
              throw new Error(`MyDATA API Error: Rate limit exceeded after ${maxRetries} retries. Please try again later.`);
            }
          }

          // Log the error response
          if (error.response?.data) {
            await this.logApiCall('error',
              typeof error.response.data === 'string'
                ? error.response.data
                : JSON.stringify(error.response.data)
            );
          }

          // Extract error message from response if available
          if (typeof error.response?.data === 'string') {
            const errorMessage = this.extractErrorMessage(error.response.data);
            throw new Error(`MyDATA API Error: ${errorMessage}`);
          }

          // Otherwise use the error message
          throw new Error(`MyDATA API Error: ${error.message}`);
        }

        // Re-throw other errors
        throw error;
      }
    }

    // This should never be reached due to the throw in the loop, but TypeScript needs it
    throw new Error(`Rate limited by myDATA API after ${maxRetries} retries`);
  }
  /**
   * Cancel an invoice in myDATA API
   * @param mark The invoice mark to cancel
   * @param entityVatNumber Optional VAT number if called by a third party
   * @returns The response XML from myDATA
   */
  async cancelInvoice(mark: string, entityVatNumber?: string): Promise<string> {
    try {
      if (!this.credentials) {
        throw new Error('MyDATA credentials not configured');
      }

      if (!mark) {
        throw new Error('Invoice mark is required for cancellation');
      }

      // Build URL with parameters
      let url = `${this.baseUrls.cancelInvoice}?mark=${mark}`;
      if (entityVatNumber) {
        url += `&entityVatNumber=${entityVatNumber}`;
      }

      // Configure request
      const config: AxiosRequestConfig = {
        headers: {
          'Accept': 'application/xml',
          'aade-user-id': this.credentials.username,
          'ocp-apim-subscription-key': this.credentials.password
        },
        timeout: 30000 // 30 seconds timeout
      };

      // Log the request (for debugging)
      await this.logApiCall('request', `Cancelling invoice with mark: ${mark}`);

      // Send request - no body needed for cancellation
      const response = await axios.post(url, null, config);

      // Log the response (for debugging)
      await this.logApiCall('response', response.data);

      return response.data;
    } catch (error) {
      // Handle errors
      if (axios.isAxiosError(error)) {
        const responseData = error.response?.data;

        // Log the error response
        if (responseData) {
          await this.logApiCall('error', responseData);
        }

        // Extract error message from response if available
        if (typeof responseData === 'string') {
          const errorMessage = this.extractErrorMessage(responseData);
          throw new Error(`MyDATA API Error: ${errorMessage}`);
        }

        // Otherwise use the error message
        throw new Error(`MyDATA API Error: ${error.message}`);
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Log API calls to database for debugging and auditing
   */
  private async logApiCall(type: 'request' | 'response' | 'error', data: string): Promise<void> {
    try {
      // Console log for immediate visibility
      console.log(`[MYDATA ${type.toUpperCase()}]`, data.substring(0, 1000) + (data.length > 1000 ? '...' : ''));

      // Insert log entry with appropriate values based on type
      const logEntry: Record<string, unknown> = {
        request_type: 'SendInvoices',
        status: type === 'error' ? 'error' : type === 'response' ? 'success' : 'pending',
      };

      // Always provide a non-null value for request_body (required by schema)
      if (type === 'request') {
        logEntry.request_body = data;
      } else {
        // For response or error types, use placeholder for request_body
        logEntry.request_body = '[See previous request]'; // Non-null placeholder
        logEntry.response_body = data;
      }

      // Add error message for error type
      if (type === 'error') {
        logEntry.error_message = data.substring(0, 500);
      }

      const { error } = await supabase.from('api_logs').insert(logEntry);

      if (error) {
        console.error('Error inserting API log:', error);
      }
    } catch (err) {
      // Don't let logging errors affect the main flow
      console.error('Error during API call logging:', err);
    }
  }
  // Improved error message extraction
  private extractErrorMessage(responseXml: string): string {
    try {
      // Look for full error structure - using a workaround for the 's' flag
      // Replace all newlines with a special character, then use a regular regex
      const normalizedXml = responseXml.replace(/\n/g, '§');
      const errorSection = normalizedXml.match(/<errors>(.*?)<\/errors>/);
      if (errorSection) {
        // Extract all error messages
        const errorMessages: string[] = [];
        // Use global flag only, not 's' flag
        const errorRegex = /<e>(.*?)<\/error>/g;
        let match;

        while ((match = errorRegex.exec(errorSection[1])) !== null) {
          const errorContent = match[1];
          const codeMatch = errorContent.match(/<code>(.*?)<\/code>/);
          const messageMatch = errorContent.match(/<message>(.*?)<\/message>/);

          if (codeMatch && messageMatch) {
            errorMessages.push(`${codeMatch[1]}: ${messageMatch[1]}`);
          }
        }

        if (errorMessages.length > 0) {
          return errorMessages.join('\n');
        }
      }

      // Fallback to simpler extraction if structured approach failed
      const messageMatch = responseXml.match(/<message>(.*?)<\/message>/);
      const codeMatch = responseXml.match(/<code>(.*?)<\/code>/);

      if (messageMatch && codeMatch) {
        return `${codeMatch[1]}: ${messageMatch[1]}`;
      } else if (messageMatch) {
        return messageMatch[1];
      }

      // Last resort: check for HTTP error
      const statusMatch = responseXml.match(/<statusCode>(.*?)<\/statusCode>/);
      if (statusMatch && statusMatch[1] !== 'Success') {
        return `Status: ${statusMatch[1]}`;
      }

      return 'Unknown error';
    } catch (error) {
      console.error('Error parsing error message:', error);
      return 'Error parsing response';
    }
  }

  // Extract invoice ID from request body (if possible)
  private extractInvoiceId(requestBody: string): string | null {
    try {
      // Only attempt to extract from XML bodies
      if (!requestBody.startsWith('<')) {
        return null;
      }

      // Try to extract from URL parameters
      if (requestBody.includes('invoiceId=')) {
        const match = requestBody.match(/invoiceId=([^&]+)/);
        if (match) return match[1];
      }

      // This is a very basic extraction and might need to be enhanced
      // based on your actual XML structure and how invoice IDs are represented
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get transmitted documents from myDATA API
   * This method is used to test the connection and retrieve documents
   * @param mark The mark to start from, use '0' to get the first batch
   * @returns The response XML from myDATA
   */
  async getTransmittedDocs(mark: string): Promise<string> {
    try {
      if (!this.credentials) {
        throw new Error('MyDATA credentials not configured');
      }

      // Build URL with parameters
      const url = `${this.baseUrls.getTransmittedDocs}?mark=${mark}`;

      // Configure request
      const config: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'text/xml; charset=UTF-8',  // Changed from 'application/xml'
          'Accept': 'application/xml',
          'aade-user-id': this.credentials.username,
          'ocp-apim-subscription-key': this.credentials.password
        },
        timeout: 30000 // 30 seconds timeout
      };

      // Log the request (for debugging)
      await this.logApiCall('request', `Requesting transmitted docs from mark: ${mark}`);

      // Send request
      const response = await axios.get(url, config);

      // Log the response (for debugging)
      await this.logApiCall('response', response.data);

      return response.data;
    } catch (error) {
      // Handle errors
      if (axios.isAxiosError(error)) {
        const responseData = error.response?.data;

        // Log the error response
        if (responseData) {
          await this.logApiCall('error', responseData);
        }

        // Extract error message from response if available
        if (typeof responseData === 'string') {
          const errorMessage = this.extractErrorMessage(responseData);
          throw new Error(`MyDATA API Error: ${errorMessage}`);
        }

        // Otherwise use the error message
        throw new Error(`MyDATA API Error: ${error.message}`);
      }

      // Re-throw other errors
      throw error;
    }
  }
}
