// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Get the site URL based on environment
const getSiteUrl = () => {
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    // Use explicit site URL if defined
    return process.env.NEXT_PUBLIC_SITE_URL
  }
  if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    // Vercel provides this automatically
    return `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
  }
  // Fallback to localhost
  return 'http://localhost:3000'
}

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Export the site URL for use in auth redirects
export const siteUrl = getSiteUrl()