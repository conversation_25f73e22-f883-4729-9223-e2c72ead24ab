// lib/services/support.ts
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

export type SupportThreadData = {
  title: string;
  category: Database['public']['Enums']['message_category'];
  message: string;
  userId: string; // Add this to identify the user
}

export const createSupportThread = async (
  supabase: SupabaseClient<Database>,
  data: SupportThreadData
) => {
  // First, get the pelates ID for the user
  const { data: pelates, error: pelatesError } = await supabase
    .from('pelates')
    .select('id')
    .eq('auth_user_id', data.userId)
    .single();

  if (pelatesError) {
    throw new Error(`Failed to get user profile: ${pelatesError.message}`);
  }

  // Create the thread with the correct client_id
  const { data: thread, error: threadError } = await supabase
    .from('support_threads')
    .insert({
      title: data.title,
      category: data.category,
      priority: 'medium',
      status: 'open',
      client_id: pelates.id  // This is crucial for R<PERSON>
    })
    .select()
    .single();

  if (threadError) throw threadError;

  // Create the initial message
  const { error: messageError } = await supabase
    .from('support_messages')
    .insert({
      thread_id: thread.id,
      content: data.message,
      is_coach_reply: false,
      sender_id: pelates.id  // Also set the sender_id
    });

  if (messageError) throw messageError;

  return thread;
};