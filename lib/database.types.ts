export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface GoalsDatabase {
  public: {
    Tables: {
      goals: {
        Row: {
          id: string
          created_at: string
          title: string
          description: string | null
          target_date: string | null
          status: string
          user_id: string
          template_id: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          title: string
          description?: string | null
          target_date?: string | null
          status: string
          user_id: string
          template_id?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          title?: string
          description?: string | null
          target_date?: string | null
          status?: string
          user_id?: string
          template_id?: string | null
        }
      }
      goal_templates: {
        Row: {
          id: string
          created_at: string
          title: string
          description: string | null
          category: string
          is_active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          title: string
          description?: string | null
          category: string
          is_active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          title?: string
          description?: string | null
          category?: string
          is_active?: boolean
        }
      }
      goal_assignments: {
        Row: {
          id: string
          created_at: string
          user_id: string
          template_id: string
          assigned_by: string
          status: string
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          template_id: string
          assigned_by: string
          status: string
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          template_id?: string
          assigned_by?: string
          status?: string
        }
      }
      assigned_goals: {
        Row: {
          id: string
          created_at: string
          title: string
          description: string | null
          category: string
          assigned_by: string
          due_date: string | null
          start_date: string
          target_value: number
          current_value: number
          status: string
          template_id: string | null
          pelatis_id: string
          updated_at: string
        }
        Insert: {
          id?: string
          created_at?: string
          title: string
          description?: string | null
          category: string
          assigned_by: string
          due_date?: string | null
          start_date: string
          target_value: number
          current_value?: number
          status: string
          template_id?: string | null
          pelatis_id: string
          updated_at?: string
        }
        Update: {
          id?: string
          created_at?: string
          title?: string
          description?: string | null
          category?: string
          assigned_by?: string
          due_date?: string | null
          start_date?: string
          target_value?: number
          current_value?: number
          status?: string
          template_id?: string | null
          pelatis_id?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}