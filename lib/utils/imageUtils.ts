/**
 * Utility functions for handling images in the application
 */

/**
 * Constructs a URL for an image stored in Supabase Storage
 *
 * @param imageSource - The ID, path, or full URL of the image
 * @param bucket - The storage bucket name (default: 'exercises')
 * @returns The full URL to the image
 */
export function getImageUrl(imageSource: string, bucket: string = 'exercises'): string {
  if (!imageSource || typeof imageSource !== 'string') {
    console.debug('getImageUrl: Invalid imageSource provided:', imageSource);
    return getFallbackImageUrl('Invalid+Source');
  }

  // If the imageSource is already a full URL, return it as is
  if (imageSource.startsWith('http://') || imageSource.startsWith('https://')) {
    console.debug('getImageUrl: Full URL provided, returning as-is:', imageSource);
    return imageSource;
  }

  // Use environment variable or fallback to the known Supabase URL
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://eebyxmqdzvutxakzijbu.supabase.co';
  if (!supabaseUrl) {
    console.error('NEXT_PUBLIC_SUPABASE_URL is not defined and fallback URL is missing');
    return getFallbackImageUrl('Config+Error');
  }

  // Handle case where imageSource might include the bucket name already
  let finalImageSource = imageSource.trim();
  let finalBucket = bucket;

  // If imageSource contains a slash, it might include a path or bucket
  if (finalImageSource.includes('/')) {
    const parts = finalImageSource.split('/');
    // If it's a path with more than one segment, the first might be the bucket
    if (parts.length > 1) {
      // Check if the first part matches a known bucket pattern
      const possibleBucket = parts[0].toLowerCase();
      if (possibleBucket === 'exercises' || possibleBucket === 'recipes' ||
          possibleBucket === 'profiles' || possibleBucket === 'receipts') {
        finalBucket = parts[0];
        finalImageSource = parts.slice(1).join('/');
        console.debug('Extracted bucket from imageSource:', { finalBucket, finalImageSource });
      }
    }
  }

  // Clean up the image source - remove any leading/trailing slashes
  finalImageSource = finalImageSource.replace(/^\/+|\/+$/g, '');

  // Construct the final URL - no extension manipulation needed
  // Supabase storage should serve files regardless of extension
  const fullUrl = `${supabaseUrl}/storage/v1/object/public/${finalBucket}/${finalImageSource}`;
  
  console.debug('getImageUrl generated:', {
    originalImageSource: imageSource,
    finalImageSource,
    originalBucket: bucket,
    finalBucket,
    fullUrl
  });
  
  return fullUrl;
}

/**
 * Constructs a URL for an image using the image_url field directly
 * This is preferred when you have the full image_url stored in the database
 *
 * @param imageUrl - The full image URL from the database
 * @returns The image URL or fallback if invalid
 */
export function getDirectImageUrl(imageUrl: string | null | undefined): string {
  if (!imageUrl || typeof imageUrl !== 'string') {
    return getFallbackImageUrl('No+URL');
  }

  // If it's already a full URL, return as-is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a relative path, construct the full URL
  if (imageUrl.startsWith('/storage/') || imageUrl.startsWith('storage/')) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://eebyxmqdzvutxakzijbu.supabase.co';
    const cleanPath = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
    return `${supabaseUrl}${cleanPath}`;
  }

  // If it's just a filename, construct the exercises bucket URL
  return getImageUrl(imageUrl, 'exercises');
}

/**
 * Checks if a string is a valid image ID or URL
 *
 * @param value - The string to check
 * @returns True if the string is a valid image ID or URL
 */
export function isValidImageSource(value: string | null | undefined): boolean {
  if (!value || typeof value !== 'string') return false;

  // Check if it's a URL
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return true;
  }

  // Check if it's a valid image ID (non-empty string)
  return value.trim().length > 0;
}

/**
 * Gets a fallback image URL for when an image fails to load
 *
 * @param text - Optional text to display on the placeholder image
 * @returns A URL to a placeholder image
 */
export function getFallbackImageUrl(text: string = 'No+Image'): string {
  // Use a more reliable placeholder service
  return `https://via.placeholder.com/100x100/e2e8f0/64748b?text=${encodeURIComponent(text)}`;
}

/**
 * Attempts to load an image and returns the working URL or fallback
 * Useful for testing multiple potential URLs
 *
 * @param potentialUrls - Array of potential image URLs to try
 * @returns Promise that resolves to the first working URL or fallback
 */
export async function findWorkingImageUrl(potentialUrls: string[]): Promise<string> {
  for (const url of potentialUrls) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (response.ok) {
        return url;
      }
    } catch (error) {
      console.debug('URL failed:', url, error);
    }
  }
  return getFallbackImageUrl('Not+Found');
}