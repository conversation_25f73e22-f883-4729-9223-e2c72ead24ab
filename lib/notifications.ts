/**
 * @file /lib/notifications.ts
 * @description Comprehensive Notification Service for managing user notifications
 * 
 * @overview
 * A robust, type-safe notification management service that provides:
 * - Centralized notification creation
 * - Flexible metadata support
 * - Automatic expiration handling
 * - Multiple notification management methods
 * 
 * @dependencies
 * - Supabase JavaScript Client
 * - Custom Supabase Database Types
 * 
 * @features
 * - Singleton service pattern
 * - Type-safe notification creation
 * - Multiple notification operations
 * - Error handling and logging
 * - Automatic notification expiration
 * 
 * @supabaseInteractions
 * - Notifications table operations
 * - Complex insert and update queries
 * 
 * @performanceConsiderations
 * - Efficient database operations
 * - Minimal overhead notification management
 * - Built-in expiration mechanism
 * 
 * @securityConsiderations
 * - Validates and sanitizes notification inputs
 * - Uses environment-based client creation
 * - Provides granular notification management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

type NotificationInsert = Database['public']['Tables']['notifications']['Insert'];
type Json = Database['public']['Tables']['notifications']['Row']['metadata'];

export class NotificationService {
  private supabase;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    
    this.supabase = createClient<Database>(supabaseUrl, supabaseKey);
  }

  async createNotification({
    clientId,
    message,
    type = 'info',
    metadata = null,
  }: {
    clientId: string;
    message: string;
    type?: 'info' | 'warning' | 'error';
    metadata?: Json;
  }) {
    const notification: NotificationInsert = {
      client_id: clientId,
      message,
      type,
      metadata,
      created_at: new Date().toISOString(),
      read: false,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days expiry
    };

    const { data, error } = await this.supabase
      .from('notifications')
      .insert(notification)
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      throw error;
    }

    return data;
  }

  async markAsRead(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAllAsRead(clientId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ read: true })
      .eq('client_id', clientId)
      .eq('read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  async deleteNotification(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  async cleanupExpiredNotifications() {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .lt('expires_at', new Date().toISOString());

    if (error) {
      console.error('Error cleaning up expired notifications:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const notificationService = new NotificationService();

// Usage example:
// import { notificationService } from '@/lib/notifications';
// 
// await notificationService.createNotification({
//   clientId: 'user123',
//   message: 'Your session has been booked',
//   type: 'info',
//   metadata: {
//     sessionId: 'session123',
//     date: '2024-01-01'
//   }
// });