/**
 * @file /lib/useNotifications.ts
 * @description Custom React hook for managing real-time notifications in a Supabase-powered application
 * 
 * @overview
 * A comprehensive notifications management hook that provides:
 * - Real-time notification fetching
 * - Automatic updates via Supabase Realtime
 * - Efficient state management
 * - Ability to mark notifications as read
 * 
 * @dependencies
 * - React (useState, useEffect)
 * - Supabase Auth Helpers
 * - Supabase Realtime
 * 
 * @supabaseInteractions
 * - Queries 'notifications' table
 * - Sets up real-time subscription
 * - Performs update operations
 * 
 * @databaseTables
 * - public.notifications
 * 
 * @performanceConsiderations
 * - Efficient real-time updates
 * - Minimal state mutations
 * - Automatic cleanup of subscriptions
 * 
 * @errorHandling
 * - Comprehensive error logging
 * - Graceful error management
 * - Fallback mechanisms
 * 
 * @accessibilityConsiderations
 * - Provides loading state
 * - Enables marking notifications as read
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */
// hooks/useNotifications.ts
import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

type Notification = Database['public']['Tables']['notifications']['Row'];

export function useNotifications(clientId: string) {
  // State management for notifications
  // Tracks both notification data and loading status
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  // Create a Supabase client for database operations
  // Ensures type-safe interactions with the database
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    // Declare channel variable in outer scope for cleanup
    let channel: RealtimeChannel;

    // Async function to fetch initial notifications
    const fetchNotifications = async () => {
      try {
        // Fetch notifications for specific client
        // Ordered by most recent first
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('client_id', clientId)
          .order('created_at', { ascending: false });

        // Handle potential errors
        if (error) throw error;

        // Update state with fetched notifications
        // Provides empty array as fallback
        setNotifications(data || []);
      } catch (error) {
        // Log any errors during fetching
        console.error('Error fetching notifications:', error);
      } finally {
        // Ensure loading state is resolved
        setLoading(false);
      }
    };

    // Setup real-time subscription for new notifications
    const setupSubscription = () => {
      // Create a Supabase real-time channel
      channel = supabase
        .channel('notifications')
        .on(
          'postgres_changes',
          {
            // Listen only for INSERT events
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            // Filter to only receive notifications for this client
            filter: `client_id=eq.${clientId}`,
          },
          // Callback to handle new notifications
          (payload) => {
            // Prepend new notification to existing list
            // Ensures most recent notifications appear first
            setNotifications((current) => [payload.new as Notification, ...current]);
          }
        )
        .subscribe();
    };

    // Execute initial fetch and setup subscription
    fetchNotifications();
    setupSubscription();

    // Cleanup function to remove subscription
    // Prevents memory leaks and unnecessary subscriptions
    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [clientId, supabase]); // Re-run effect if client ID changes

  // Method to mark a specific notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      // Update notification read status in database
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      // Throw error if update fails
      if (error) throw error;

      // Optimistically update local state
      // Marks specific notification as read
      setNotifications((current) =>
        current.map((n) =>
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
    } catch (error) {
      // Log any errors during marking as read
      console.error('Error marking notification as read:', error);
    }
  };

  // Return hook values for consumption
  return { notifications, loading, markAsRead };
}