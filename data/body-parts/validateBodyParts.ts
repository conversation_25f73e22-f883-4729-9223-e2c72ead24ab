import { BodyPartName } from './types';
import { bodyBack } from './bodyBack';
import { bodyFront } from './bodyFront';
import { bodyFemaleBack } from './bodyFemaleBack';
import { bodyFemaleFront } from './bodyFemaleFront';

const validateBodyPartData = (data: any[], source: string) => {
  const invalidSlugs = data.filter(part => {
    const isValid = isValidBodyPartName(part.slug);
    if (!isValid) {
      console.error(`Invalid slug "${part.slug}" found in ${source}`);
    }
    return !isValid;
  });
  return invalidSlugs.length === 0;
};

// Type guard to check if a string is a valid BodyPartName
function isValidBodyPartName(part: string): part is BodyPartName {
  const validParts = [
    'head', 'hair', 'neck', 'trapezius', 'deltoids', 'chest',
    'biceps', 'triceps', 'forearms', 'hands', 'upper-back',
    'lower-back', 'obliques', 'abs', 'gluteal', 'adductors',
    'hamstring', 'quadriceps', 'knees', 'calves', 'ankles', 'feet'
  ];
  return validParts.includes(part as BodyPartName);
}

// Validate all data files
console.log('Validating body part data...');
const isBackValid = validateBodyPartData(bodyBack, 'bodyBack.ts');
const isFrontValid = validateBodyPartData(bodyFront, 'bodyFront.ts');
const isFemaleBackValid = validateBodyPartData(bodyFemaleBack, 'bodyFemaleBack.ts');
const isFemaleFrontValid = validateBodyPartData(bodyFemaleFront, 'bodyFemaleFront.ts');

if (isBackValid && isFrontValid && isFemaleBackValid && isFemaleFrontValid) {
  console.log('All body part data is valid!');
} else {
  console.error('Invalid body part data found. Please fix the errors above.');
}