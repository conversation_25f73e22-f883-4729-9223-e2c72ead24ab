export type BodyPartName = 
  | 'neck'
  | 'shoulders' // maps to deltoids/trapezius area
  | 'chest'
  | 'abdomen'  // maps to abs/obliques area
  | 'biceps'
  | 'forearms'
  | 'wrists'
  | 'hands'
  | 'quadriceps'
  | 'knees'
  | 'calves'
  | 'ankles'
  | 'feet';

export type BodySide = 'front' | 'back';

export type PainType = 
  | 'sharp'
  | 'dull'
  | 'burning'
  | 'tingling'
  | 'numbness'
  | 'stiffness'
  | 'soreness'
  | 'throbbing';

export type IntensityLevel = 1 | 2;

export type Gender = 'male' | 'female';

export interface BodyPartData {
  slug: string; // Allow internal SVG data to use more specific names
  color: string;
  path: {
    left?: string[];
    right?: string[];
    common?: string[];
  };
}

export interface SelectedBodyPart {
  slug: BodyPartName;
  intensity: IntensityLevel;
  side?: 'left' | 'right';
  painType: PainType;
  notes?: string;
}

// Helper function to map detailed anatomical names to simplified BodyPartName
export function mapToBodyPartName(anatomicalName: string): BodyPartName {
  const mapping: { [key: string]: BodyPartName } = {
    'trapezius': 'shoulders',
    'deltoids': 'shoulders',
    'abs': 'abdomen',
    'obliques': 'abdomen',
    'tibialis': 'calves',
    'triceps': 'biceps',
    'head': 'neck',
    'adductors': 'quadriceps',
  };
  
  return (mapping[anatomicalName] || anatomicalName) as BodyPartName;
}