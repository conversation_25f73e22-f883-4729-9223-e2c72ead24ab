import React from 'react';
import { useMonthlySummary } from '../hooks/useMonthlySummary';
import { useIncomeBreakdown } from '../hooks/useIncomeBreakdown';
import { useExpenseAnalysis } from '../hooks/useExpenseAnalysis';
import MonthlySummary from '../components/reports/MonthlySummary';
import IncomeBreakdown from '../components/reports/IncomeBreakdown';
import ExpenseAnalysis from '../components/reports/ExpenseAnalysis';

const AnalyticsPage = () => {
  const monthly = useMonthlySummary();
  const income = useIncomeBreakdown();
  const expenses = useExpenseAnalysis();

  return (
    <div style={{ maxWidth: 1000, margin: '0 auto', padding: 32 }}>
      <h1 style={{ fontSize: 28, fontWeight: 700, marginBottom: 24 }}>Advanced Analytics</h1>
      <div style={{ background: '#e3f2fd', padding: 24, borderRadius: 12, marginBottom: 32 }}>
        <h2 style={{ fontSize: 20, fontWeight: 600, marginBottom: 8 }}>Monthly Summary</h2>
        <MonthlySummary data={monthly.data} loading={monthly.loading} error={monthly.error} />
      </div>
      <div style={{ background: '#fffde7', padding: 24, borderRadius: 12, marginBottom: 32 }}>
        <h2 style={{ fontSize: 20, fontWeight: 600, marginBottom: 8 }}>Income Breakdown</h2>
        <IncomeBreakdown data={income.data} loading={income.loading} error={income.error} />
      </div>
      <div style={{ background: '#fbe9e7', padding: 24, borderRadius: 12, marginBottom: 32 }}>
        <h2 style={{ fontSize: 20, fontWeight: 600, marginBottom: 8 }}>Expense Analysis</h2>
        <ExpenseAnalysis data={expenses.data} loading={expenses.loading} error={expenses.error} />
      </div>
    </div>
  );
};

export default AnalyticsPage; 