// contexts/OneSignalContext.tsx - Updated version

'use client';

import { createContext, useContext, useEffect, useState, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

// Define a safe type for window.OneSignal access
interface OneSignalInstance {
  init: (config: any) => Promise<void>;
  getNotificationPermission: () => Promise<string>;
  getUserId: () => Promise<string>;
  showNativePrompt: () => Promise<void>;
  on: (event: string, callback: (value: any) => void) => void;
  off: (event: string, callback?: (value: any) => void) => void;
  initialized: boolean;
  push: (func: any) => void;
}

interface OneSignalContextType {
  isInitialized: boolean;
  isSubscribed: boolean;
  playerId: string | null;
  requestSubscription: () => Promise<void>;
  updateSubscriptionStatus: (enabled: boolean) => Promise<void>;
}

const OneSignalContext = createContext<OneSignalContextType>({
  isInitialized: false,
  isSubscribed: false,
  playerId: null,
  requestSubscription: async () => {},
  updateSubscriptionStatus: async () => {},
});

// Type guard to check if OneSignal is properly initialized
function isInitializedOneSignal(oneSignal: any): oneSignal is OneSignalInstance {
  return (
    oneSignal &&
    typeof oneSignal === 'object' &&
    !Array.isArray(oneSignal) &&
    'initialized' in oneSignal &&
    oneSignal.initialized === true
  );
}

export function OneSignalProvider({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const hasInitialized = useRef(false);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    // Prevent multiple initialization attempts
    if (hasInitialized.current) return;
    
    hasInitialized.current = true;
    let subscriptionChangeHandler: ((isSubscribed: boolean) => void) | null = null;

    const initializeOneSignal = async () => {
      if (typeof window === 'undefined') return;
      
      // Check if OneSignal is already initialized by the browser
      const oneSignalAny = (window as any).OneSignal;
      
      if (isInitializedOneSignal(oneSignalAny)) {
        console.log('OneSignal already initialized');
        setIsInitialized(true);
        
        try {
          // Get current state if already initialized
          const state = await oneSignalAny.getNotificationPermission();
          setIsSubscribed(state === 'granted');
          
          if (state === 'granted') {
            const id = await oneSignalAny.getUserId();
            setPlayerId(id);
          }
        } catch (error) {
          console.error('Error getting OneSignal state:', error);
        }
        
        return;
      }

      // Wait for OneSignal to be available with a more robust approach
      const waitForOneSignal = () => {
        return new Promise<void>((resolve, reject) => {
          if ((window as any).OneSignal) {
            resolve();
            return;
          }

          // Check every 200ms for up to 5 seconds
          let attempts = 0;
          const maxAttempts = 25;
          const interval = setInterval(() => {
            if ((window as any).OneSignal) {
              clearInterval(interval);
              resolve();
              return;
            }

            attempts++;
            if (attempts >= maxAttempts) {
              clearInterval(interval);
              reject(new Error('OneSignal failed to load after maximum attempts'));
            }
          }, 200);
        });
      };

      try {
        await waitForOneSignal();
        
        // Ensure OneSignal array is properly initialized
        (window as any).OneSignal = (window as any).OneSignal || [];
        
        const oneSignal = (window as any).OneSignal;
        
        // Only initialize if not already initialized
        if (Array.isArray(oneSignal)) {
          oneSignal.push(function() {
            (oneSignal as any).init({
              appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID || '',
              allowLocalhostAsSecureOrigin: process.env.NODE_ENV === 'development',
              notifyButton: {
                enable: false, // We'll handle this ourselves
              },
            });
          });
        } else if (!isInitializedOneSignal(oneSignal)) {
          await oneSignal.init({
            appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID || '',
            allowLocalhostAsSecureOrigin: process.env.NODE_ENV === 'development',
            notifyButton: {
              enable: false, // We'll handle this ourselves
            },
          });
        }

        setIsInitialized(true);

        // Get initial subscription state
        let state = 'default';
        if (!Array.isArray(oneSignal) && typeof oneSignal.getNotificationPermission === 'function') {
          state = await oneSignal.getNotificationPermission();
        }
        
        setIsSubscribed(state === 'granted');

        if (state === 'granted' && !Array.isArray(oneSignal) && 
            typeof oneSignal.getUserId === 'function') {
          const id = await oneSignal.getUserId();
          setPlayerId(id);
          
          // Get user session and save subscription
          const { data: { session } } = await supabase.auth.getSession();
          if (session?.user?.id && id) {
            await saveSubscription(id);
          }
        }

        // Listen for subscription changes
        subscriptionChangeHandler = async (isSubscribed: boolean) => {
          setIsSubscribed(isSubscribed);
          const currentOneSignal = (window as any).OneSignal;
          
          if (isSubscribed && !Array.isArray(currentOneSignal) && 
              typeof currentOneSignal.getUserId === 'function') {
            const id = await currentOneSignal.getUserId();
            setPlayerId(id);
            await saveSubscription(id);
          } else {
            await updateDatabaseSubscription(false);
            setPlayerId(null);
          }
        };
        
        // Only attach event listener if not already attached
        if (!Array.isArray(oneSignal) && typeof oneSignal.on === 'function') {
          oneSignal.on('subscriptionChange', subscriptionChangeHandler);
        }
      } catch (error) {
        console.error('OneSignal initialization error:', error);
      }
    };

    initializeOneSignal();

    return () => {
      // Cleanup subscription change listener if it exists
      const oneSignal = (window as any).OneSignal;
      if (oneSignal && !Array.isArray(oneSignal) && 
          subscriptionChangeHandler && 
          typeof oneSignal.off === 'function') {
        oneSignal.off('subscriptionChange', subscriptionChangeHandler);
      }
    };
  }, []);

// Modified saveSubscription function to handle admins without pelatis_id
const saveSubscription = async (playerSignalId: string) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user?.id) return;
    
    // Check if user is admin
    const { data: roles } = await supabase.rpc('getUserRoles', {
      p_user_id: session.user.id
    });
    
    const isAdmin = Array.isArray(roles) && roles.includes('admin');
    
    if (isAdmin) {
      // For admin users, try first to find if they have a pelates record
      const { data: adminProfile } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', session.user.id)
        .maybeSingle();
      
      if (adminProfile?.id) {
        // Admin has a pelates record, use regular approach
        await supabase
          .from('onesignal_subscriptions')
          .upsert({
            pelatis_id: adminProfile.id,
            player_id: playerSignalId,
            enabled: true,
          }, {
            onConflict: 'pelatis_id, player_id',
          });
          
        console.log('Admin subscription saved using existing pelates record');
      } else {
        // Admin doesn't have a pelates record, create one
        const { data: newProfile, error: createError } = await supabase
          .from('pelates')
          .insert({
            auth_user_id: session.user.id,
            name: session.user.email ? session.user.email.split('@')[0] : 'Admin',
            last_name: 'User',
            email: session.user.email,
            notification_consent: true,
            email_consent: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('id')
          .single();
          
        if (createError) {
          console.error('Error creating admin profile:', createError);
          return;
        }
        
        // Use the newly created pelates record
        await supabase
          .from('onesignal_subscriptions')
          .upsert({
            pelatis_id: newProfile.id,
            player_id: playerSignalId,
            enabled: true,
          }, {
            onConflict: 'pelatis_id, player_id',
          });
          
        console.log('Created admin pelates record and saved subscription');
      }
    } else {
      // Regular user flow
      const { data: profile } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', session.user.id)
        .single();

      if (profile) {
        await supabase
          .from('onesignal_subscriptions')
          .upsert({
            pelatis_id: profile.id,
            player_id: playerSignalId,
            enabled: true,
          }, {
            onConflict: 'pelatis_id, player_id',
          });
          
        console.log('Regular user subscription saved');
      } else {
        console.error('No pelates record found for user');
      }
    }
  } catch (error) {
    console.error('Error saving subscription:', error);
  }
};

  const updateDatabaseSubscription = async (enabled: boolean) => {
    if (!playerId) return;
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) return;
      
      const { data: profile } = await supabase
        .from('pelates')
        .select('id')
        .eq('auth_user_id', session.user.id)
        .single();

      if (profile) {
        await supabase
          .from('onesignal_subscriptions')
          .update({ enabled })
          .match({ pelatis_id: profile.id, player_id: playerId });
      }
    } catch (error) {
      console.error('Error updating subscription:', error);
    }
  };

  const updateSubscriptionStatus = async (enabled: boolean) => {
    await updateDatabaseSubscription(enabled);
  };

  const requestSubscription = async () => {
    if (!isInitialized) return;
    
    const oneSignal = (window as any).OneSignal;
    
    if (!oneSignal || Array.isArray(oneSignal) || 
        typeof oneSignal.showNativePrompt !== 'function') {
      return;
    }
    
    try {
      await oneSignal.showNativePrompt();
    } catch (error) {
      console.error('Error requesting subscription:', error);
    }
  };

  return (
    <OneSignalContext.Provider value={{ 
      isInitialized, 
      isSubscribed, 
      playerId,
      requestSubscription,
      updateSubscriptionStatus 
    }}>
      {children}
    </OneSignalContext.Provider>
  );
}

export const useOneSignal = () => useContext(OneSignalContext);

