// contexts/AuthContext.tsx
'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabaseClient } from '@/lib/supabase-client'
import type { Database } from '@/types/supabase'

// Debug logging
console.log('AuthContext: supabaseClient loaded:', !!supabaseClient)
console.log('AuthContext: supabaseClient.auth exists:', !!supabaseClient?.auth)

interface UserRole {
  isAdmin: boolean
  isPelatis: boolean
  roles: string[]
  pelatisId: string | null
}

interface AuthContextType {
  user: User | null
  session: Session | null
  userRole: UserRole
  loading: boolean
  signOut: () => Promise<void>
  refreshUserRole: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Circuit breaker for auth operations to prevent rate limiting
class AuthCircuitBreaker {
  private failures = 0
  private lastFailTime = 0
  private readonly maxFailures = 3
  private readonly resetTimeout = 30000 // 30 seconds

  canExecute(): boolean {
    if (this.failures >= this.maxFailures) {
      if (Date.now() - this.lastFailTime > this.resetTimeout) {
        this.reset()
        return true
      }
      console.warn('Auth circuit breaker activated - skipping request')
      return false
    }
    return true
  }

  onSuccess(): void {
    this.reset()
  }

  onFailure(): void {
    this.failures++
    this.lastFailTime = Date.now()
    console.warn(`Auth circuit breaker failure ${this.failures}/${this.maxFailures}`)
  }

  private reset(): void {
    this.failures = 0
    this.lastFailTime = 0
  }
}

const authCircuitBreaker = new AuthCircuitBreaker()

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userRole, setUserRole] = useState<UserRole>({
    isAdmin: false,
    isPelatis: false,
    roles: [],
    pelatisId: null
  })
  const [loading, setLoading] = useState(true)

  const fetchUserRole = async (userId: string): Promise<UserRole> => {
    if (!authCircuitBreaker.canExecute()) {
      return { isAdmin: false, isPelatis: false, roles: [], pelatisId: null }
    }

    try {
      // Parallel fetch for roles and pelatis data
      const [rolesResult, pelatesResult] = await Promise.allSettled([
        supabaseClient.rpc('getUserRoles', { p_user_id: userId }),
        supabaseClient.from('pelates').select('id').eq('auth_user_id', userId).maybeSingle()
      ])

      let roles: string[] = []
      let pelatisId: string | null = null

      // Handle roles result
      if (rolesResult.status === 'fulfilled' && !rolesResult.value.error) {
        roles = Array.isArray(rolesResult.value.data) ? rolesResult.value.data : []
      }

      // Handle pelatis result
      if (pelatesResult.status === 'fulfilled' && !pelatesResult.value.error && pelatesResult.value.data) {
        pelatisId = pelatesResult.value.data.id
      }

      const result = {
        isAdmin: roles.includes('admin'),
        isPelatis: !!pelatisId,
        roles,
        pelatisId
      }

      authCircuitBreaker.onSuccess()
      return result
    } catch (error) {
      console.error('Error fetching user roles:', error)
      authCircuitBreaker.onFailure()
      return { isAdmin: false, isPelatis: false, roles: [], pelatisId: null }
    }
  }

  const refreshUserRole = async () => {
    if (user) {
      const roles = await fetchUserRole(user.id)
      setUserRole(roles)
    }
  }

  const signOut = async () => {
    try {
      await supabaseClient.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  useEffect(() => {
    let mounted = true

    // Validate supabaseClient before using
    if (!supabaseClient || !supabaseClient.auth) {
      console.error('AuthContext: supabaseClient is not properly initialized')
      setLoading(false)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session: initialSession }, error } = await supabaseClient.auth.getSession()
        
        if (error) {
          console.error('Error getting initial session:', error)
          return
        }

        if (mounted) {
          setSession(initialSession)
          setUser(initialSession?.user ?? null)
          
          if (initialSession?.user) {
            const roles = await fetchUserRole(initialSession.user.id)
            if (mounted) {
              setUserRole(roles)
            }
          }
          
          setLoading(false)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Set up auth state change listener - ONLY ONE for the entire app
    const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id)
        
        if (!mounted) return

        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          // Only fetch roles on significant auth events
          if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
            const roles = await fetchUserRole(session.user.id)
            if (mounted) {
              setUserRole(roles)
            }
          }
        } else {
          // User signed out
          setUserRole({ isAdmin: false, isPelatis: false, roles: [], pelatisId: null })
        }

        if (mounted) {
          setLoading(false)
        }
      }
    )

    // Cleanup function
    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, []) // Empty dependency array - this effect should only run once

  const value: AuthContextType = {
    user,
    session,
    userRole,
    loading,
    signOut,
    refreshUserRole,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}