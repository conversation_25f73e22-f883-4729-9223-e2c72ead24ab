{"name": "liftapp", "version": "0.1.0", "private": true, "scripts": {"update-types": "supabase gen types typescript --linked > types/supabase.ts", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@edge-runtime/cookies": "^5.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.3", "@react-pdf/renderer": "^4.3.0", "@sentry/nextjs": "^9.14.0", "@shadcn/ui": "^0.0.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.47.10", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.62.11", "@tanstack/react-virtual": "^3.13.8", "@types/mermaid": "^9.2.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-signature-canvas": "^1.0.6", "add": "^2.0.6", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "easymde": "^2.18.0", "framer-motion": "^12.6.3", "init": "^0.1.2", "libphonenumber-js": "^1.11.17", "lodash": "^4.17.21", "lucide-react": "^0.439.0", "marked": "^15.0.7", "mermaid": "^11.4.1", "mjml": "^4.15.3", "next": "14.2.8", "papaparse": "^5.4.1", "pdfkit": "^0.17.1", "pg": "^8.13.1", "qrcode": "^1.5.4", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-onesignal": "^3.0.1", "react-select": "^5.8.0", "react-signature-canvas": "^1.0.6", "react-simplemde-editor": "^5.2.0", "react-to-pdf": "^1.0.1", "recharts": "^2.13.0", "remark-gfm": "^4.0.0", "resend": "^4.1.2", "shadcn-ui": "^0.9.0", "sonner": "^1.7.1", "table": "^6.8.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "xml2js": "^0.6.2", "zod": "^3.24.2"}, "devDependencies": {"@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.7", "@types/marked": "^6.0.0", "@types/node": "^20", "@types/pdfkit": "^0.13.9", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.8", "fast-xml-parser": "^5.2.2", "postcss": "^8", "supabase": "^2.1.1", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"node": ">=20.0.0"}}