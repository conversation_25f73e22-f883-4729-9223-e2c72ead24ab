# yek.yaml configuration for feature extraction
ignore_patterns:
  - "node_modules/**"
  - ".next/**"
  - "dist/**"
  - ".git/**"
  - ".vercel/**"
  - "**/*.test.tsx"
  - "**/*.spec.ts"
  
# Give priority to important files
priority_rules:
  - score: 100
    pattern: "^app/"
  - score: 90
    pattern: "^components/"
  - score: 80
    pattern: "^hooks/"
  - score: 70
    pattern: "^lib/"
  - score: 60
    pattern: "^types/"
  - score: 50
    pattern: "^utils/"

# Define output format
output_template: "/* FILE: FILE_PATH */\n\nFILE_CONTENT\n\n/* END FILE */\n"

# Add any specific binary file extensions your project might have
binary_extensions:
  - ".woff"
  - ".woff2"
  - ".ttf"
  - ".eot"