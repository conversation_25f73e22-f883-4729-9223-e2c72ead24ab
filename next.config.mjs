import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'eebyxmqdzvutxakzijbu.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    // Add error handling for failed image loads
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  webpack: (config, { isServer }) => {
    // Existing aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': resolve(__dirname, './'),
      '@components': resolve(__dirname, './components'),
      '@hooks': resolve(__dirname, './hooks'),
      '@types': resolve(__dirname, './types'),
      '@utils': resolve(__dirname, './utils'),
      '@lib': resolve(__dirname, './lib'),
    };

    // Add fallbacks for Node.js modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    return config;
  },
  // Explicitly mark MJML as external to avoid bundling issues
  experimental: {
    serverComponentsExternalPackages: ['mjml'],
  },
};

export default nextConfig;