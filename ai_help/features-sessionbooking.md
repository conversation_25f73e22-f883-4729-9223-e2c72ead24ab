Based on the provided code excerpts, I'll detail the Session Booking System:

**Core Components**

1. **Session Management**
   - Session creation
   - Availability tracking
   - Capacity management
   - Program association

2. **Booking Flow**
   - User bookings
   - Admin bookings
   - Cancellations
   - Check-ins

**Database Schema**

```sql
-- Sessions table
sessions (
  id uuid PRIMARY KEY,
  start_time timestamptz NOT NULL,
  max_participants integer NOT NULL,
  program_id uuid REFERENCES programs(id),
  duration integer NOT NULL,
  created_at timestamptz DEFAULT now()
)

-- Bookings table
bookings (
  id uuid PRIMARY KEY,
  booked_session_id uuid REFERENCES sessions(id),
  pelatis_id uuid REFERENCES pelates(id),
  created_at timestamptz DEFAULT now(),
  UNIQUE(booked_session_id, pelatis_id)
)

-- Check-ins table
check_ins (
  id uuid PRIMARY KEY,
  session_id uuid REFERENCES sessions(id),
  pelatis_id uuid REFERENCES pelates(id),
  check_in_time timestamptz DEFAULT now(),
  UNIQUE(session_id, pelatis_id)
)

-- View for session booking counts
CREATE VIEW session_booking_counts AS
SELECT 
  s.id as session_id,
  COUNT(b.id) as total_bookings
FROM sessions s
LEFT JOIN bookings b ON s.id = b.booked_session_id
GROUP BY s.id;
```

**Key Implementation Features**

1. **Session Fetching**

````typescript path=app/user/session-book/page.tsx mode=EDIT
const fetchSessionsForWeek = useCallback(async (startDate: Date) => {
  try {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 7);

    const { data: sessionsData, error: sessionsError } = await supabase
      .from('sessions')
      .select(`
        *,
        programs (name),
        bookings (id),
        session_booking_counts!left (total_bookings)
      `)
      .gte('start_time', startDate.toISOString())
      .lt('start_time', endDate.toISOString());

    if (sessionsError) throw sessionsError;

    const formattedSessions = sessionsData.map(session => ({
      ...session,
      bookings: session.bookings || [],
      program_name: session.programs?.name || 'N/A',
      total_bookings: session.session_booking_counts?.[0]?.total_bookings || 0,
      available_slots: session.max_participants - (session.session_booking_counts?.[0]?.total_bookings || 0)
    }));

    setSessions(formattedSessions);
  } catch (err) {
    console.error('Error:', err);
    setError('Failed to fetch sessions');
  }
}, [supabase]);
````

2. **Booking Creation**

````typescript path=app/user/session-book/page.tsx mode=EDIT
const bookSession = async () => {
  if (!selectedSession || !userProfile) return;
  
  setLoading(true);
  try {
    const { error } = await supabase.rpc('create_booking', {
      p_booked_session_id: selectedSession.id,
      p_pelatis_id: userProfile.id
    });

    if (error) throw error;

    setShowModal(false);
    toast.success('Η κράτηση σας ολοκληρώθηκε με επιτυχία!');
    await Promise.all([
      fetchSessionsForWeek(currentWeekStart),
      fetchUserBookings()
    ]);

  } catch (err) {
    console.error('Error in bookSession:', err);
    toast.error('Αποτυχία κράτησης');
  } finally {
    setLoading(false);
  }
};
````

3. **Booking Cancellation**

````typescript path=app/user/sessions/bookings/page.tsx mode=EDIT
const cancelBooking = useCallback(async (bookingId: string) => {
  try {
    const { error } = await supabase
      .from('bookings')
      .delete()
      .eq('id', bookingId);

    if (error) throw error;
    
    setBookings(prevBookings => 
      prevBookings.filter(booking => booking.id !== bookingId)
    );
    toast.success('Booking cancelled successfully');
  } catch (error) {
    console.error('Error cancelling booking:', error);
    toast.error('Failed to cancel booking');
  }
}, [supabase]);
````

4. **Check-in System**

````typescript path=components/SessionsAdmin.tsx mode=EDIT
async function createCheckInFromBooking(
  bookingId: string, 
  pelatis_id: string, 
  session_id: string
) {
  try {
    const { error } = await supabase
      .from('check_ins')
      .insert({
        session_id: session_id,
        pelatis_id: pelatis_id,
        check_in_time: new Date().toISOString()
      })
      .select();

    if (error) throw error;

    setDialogMessages(prev => [
      ...prev, 
      { type: 'success', content: 'Check-in created successfully.' }
    ]);
    
    // Remove the booking and create check-in
    await Promise.all([
      setTodayBookings(prev => 
        prev.filter(booking => booking.value !== bookingId)
      ),
      deleteBooking(bookingId)
    ]);
  } catch (error) {
    console.error('Error creating check-in:', error);
    setDialogMessages(prev => [
      ...prev, 
      { type: 'error', content: 'Failed to create check-in.' }
    ]);
  }
}
````

**Business Rules**

1. **Booking Validation**
   - Check session capacity
   - Prevent double bookings
   - Validate user eligibility
   - Time-based restrictions

2. **Cancellation Rules**
   - Time window for cancellation
   - Refund/credit policies
   - Notification system

3. **Check-in Process**
   - Attendance tracking
   - Late arrival handling
   - No-show policies

**Database Functions**

````sql path=supabase/migrations/booking_functions.sql mode=EDIT
-- Create booking with validation
CREATE OR REPLACE FUNCTION create_booking(
  p_booked_session_id UUID,
  p_pelatis_id UUID
) RETURNS void AS $$
BEGIN
  -- Check if session is full
  IF (
    SELECT count(*) >= max_participants 
    FROM sessions s 
    LEFT JOIN bookings b ON s.id = b.booked_session_id 
    WHERE s.id = p_booked_session_id
  ) THEN
    RAISE EXCEPTION 'Session is full';
  END IF;

  -- Check for existing booking
  IF EXISTS (
    SELECT 1 FROM bookings 
    WHERE booked_session_id = p_booked_session_id 
    AND pelatis_id = p_pelatis_id
  ) THEN
    RAISE EXCEPTION 'Booking already exists';
  END IF;

  -- Create booking
  INSERT INTO bookings (booked_session_id, pelatis_id)
  VALUES (p_booked_session_id, p_pelatis_id);
END;
$$ LANGUAGE plpgsql;
````

**Error Handling**

1. **Common Scenarios**
   - Session full
   - Double booking
   - Invalid session
   - Expired session
   - Network issues

2. **User Feedback**
   - Toast notifications
   - Error messages
   - Success confirmations
   - Loading states

Would you like me to elaborate on any specific aspect of the booking system?
