Based on the provided code excerpts, I'll detail the User Authentication and Role-Based Access System:

**Authentication System Components**

1. **User Types & Roles**
   - Admin
   - Regular User (Pelatis)
   - Guest (unauthenticated)

2. **Authentication States**
   - Unauthenticated
   - Authenticated
   - Session Expired
   - Email Verification Pending

**Required Files Structure**

```plaintext
/app
  /auth
    /callback
      /route.ts              # Auth callback handler
    /lookup
      /route.ts             # User lookup endpoint
    /page.tsx               # Main auth page
  /admin
    /check-role
      /page.tsx             # Role verification
  /api
    /admin
      /create-user
        /route.ts           # Admin user creation

/components
  /auth
    /LoginForm.tsx          # Login form
    /SignupForm.tsx         # Registration form
    /ResetPassword.tsx      # Password reset
    /EmailVerification.tsx  # Email verification
    /AuthGuard.tsx         # Route protection
  /admin
    /UserManagement.tsx     # User administration
    /RoleManagement.tsx     # Role management

/hooks
  /useUserRole.ts           # Role checking hook
  /useSupabase.ts          # Supabase client hook
  /useAuth.ts              # Auth state hook

/utils
  /auth.server.ts          # Server-side auth utilities
  /supabase-admin.ts       # Admin utilities

/lib
  /jwt.ts                  # JWT handling

/middleware.ts             # Auth middleware
```

**Database Schema**

```sql
-- User Roles
roles (
  id integer PRIMARY KEY,
  name text NOT NULL UNIQUE,
  description text,
  created_at timestamptz DEFAULT now()
)

-- User Role Assignments
user_roles (
  id uuid PRIMARY KEY,
  auth_user_id uuid REFERENCES auth.users(id),
  role_id integer REFERENCES roles(id),
  created_at timestamptz DEFAULT now(),
  UNIQUE(auth_user_id, role_id)
)

-- User Profiles
pelates (
  id uuid PRIMARY KEY,
  auth_user_id uuid REFERENCES auth.users(id) UNIQUE,
  name text NOT NULL,
  email text NOT NULL UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Role Permissions
role_permissions (
  id uuid PRIMARY KEY,
  role_id integer REFERENCES roles(id),
  permission text NOT NULL,
  created_at timestamptz DEFAULT now(),
  UNIQUE(role_id, permission)
)
```

**Key Implementation Features**

1. **Server-Side Authentication**

````typescript path=utils/auth.server.ts mode=EXCERPT
export const getServerUser = cache(async () => {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    redirect('/auth');
  }

  const { data: pelatesData, error: pelatesError } = await supabase
    .from('pelates')
    .select('id')
    .eq('auth_user_id', user.id)
    .single();

  const { data: roles } = await supabase.rpc('getUserRoles', { 
    p_user_id: user.id 
  });
  
  const isAdmin = Array.isArray(roles) && roles.includes('admin');

  return {
    userId: user.id,
    clientId: pelatesData.id,
    isAdmin,
  };
});
````

2. **Client-Side Role Checking**

````typescript path=hooks/useUserRole.ts mode=EXCERPT
export const useUserRole = (): UserRoleHook => {
  const { user } = useSupabase();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isPelatis, setIsPelatis] = useState(false);
  
  useEffect(() => {
    const checkUserRole = async () => {
      if (!user) return;

      const { data: roles } = await supabase
        .rpc('getUserRoles', {
          p_user_id: user.id
        });

      setIsAdmin(Array.isArray(roles) && roles.includes('admin'));
    };
    
    checkUserRole();
  }, [user]);
  
  return { isAdmin, isPelatis, loading, error };
};
````

3. **Route Protection (Middleware)**

````typescript path=app/middleware.ts mode=EXCERPT
export async function middleware(req: NextRequest) {
  if (req.nextUrl.pathname.startsWith('/_next') || 
      req.nextUrl.pathname.startsWith('/api')) {
    return NextResponse.next()
  }
  
  const res = NextResponse.next()
  const supabase = createMiddlewareClient<Database>({ req, res })

  const { data: { session } } = await supabase.auth.getSession()
  
  if (session?.expires_at) {
    const timeUntilExpiry = new Date(session.expires_at).getTime() - Date.now()
    if (timeUntilExpiry < 30 * 60 * 1000) {
      await supabase.auth.refreshSession()
    }
  }
  
  return res
}
````

4. **Admin User Creation**

````typescript path=app/api/admin/create-user/route.ts mode=EXCERPT
export async function POST(request: Request) {
  const { pelatiId, email } = await request.json();
  
  const supabase = createRouteHandlerClient<Database>({ 
    cookies 
  }, {
    options: {
      global: {
        headers: {
          Authorization: `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      }
    }
  });

  const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
    email,
    email_confirm: true,
    user_metadata: { pelatiId }
  });

  await supabase
    .from('user_roles')
    .insert({
      auth_user_id: authUser.user.id,
      role_id: 4
    });

  return NextResponse.json({ 
    success: true, 
    userId: authUser.user.id 
  });
}
````

**Security Features**

1. **JWT Management**
   - Token generation
   - Validation
   - Refresh handling
   - Expiration management

2. **Role-Based Access Control**
   - Admin privileges
   - User permissions
   - Route protection
   - API endpoint security

3. **Session Management**
   - Auto refresh
   - Secure storage
   - Expiration handling
   - Multi-device support

Would you like me to elaborate on any specific aspect of the authentication system?
