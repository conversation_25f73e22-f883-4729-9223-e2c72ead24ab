Here's a detailed breakdown of the Notifications System Workflow:

**Event Categories & Triggers**

1. **Session-Related**
   - Booking confirmed → "Your session for {date} at {time} is confirmed"
   - Session cancelled → "Your session on {date} has been cancelled"
   - Session reminder → "Reminder: Session tomorrow at {time}"
   - Waitlist status → "Spot available for {date} session"
   - Schedule changes → "Your session on {date} has been rescheduled"

2. **Achievement-Related**
   - Badge earned → "Congratulations! You've earned the {badge_name} badge!"
   - Goal reached → "You've reached your {goal_type} goal!"
   - Streak milestone → "Amazing! You've maintained a {number} day streak!"
   - PR achieved → "New personal record in {exercise}!"

3. **Administrative**
   - Announcements → "Important: {announcement_text}"
   - Membership updates → "Your membership will expire in {days} days"
   - Payment reminders → "Payment due for {month} membership"
   - Policy updates → "Our terms have been updated"

4. **Support System**
   - Ticket updates → "New reply to your support ticket #{ticket_id}"
   - Ticket status → "Your ticket #{ticket_id} has been resolved"

5. **WOD-Related**
   - New WOD posted → "Today's WOD is now available"
   - WOD completion → "Great job completing today's WOD!"
   - Coach feedback → "Coach left feedback on your WOD performance"

**Notification Processing Flow**

1. **Creation**
   - Event triggers notification creation
   - System generates notification record with:
     - Client ID
     - Message
     - Type (info/warning/error)
     - Metadata (relevant IDs, additional info)
     - Expiration date
     - Link (if applicable)

2. **Delivery**
   - Real-time push via Supabase Realtime
   - Update notification count badge
   - Optional email delivery for important notifications
   - Mobile push notification (if implemented)

3. **Lifecycle**
   - Unread status until user interaction
   - Mark as read on click/view
   - Auto-expire after 30 days
   - Archive/delete options
   - Batch operations for multiple notifications

**Required Files Structure**

```plaintext
/lib
  /notifications.ts              # Core notification service
  /notificationTypes.ts         # Type definitions
  /notificationTemplates.ts     # Message templates

/hooks
  /useNotifications.ts          # Main notifications hook
  /useNotificationsStream.ts    # Real-time updates hook
  /useDismissNotification.ts    # Dismiss functionality
  /useFetchNotifications.ts     # Fetch notifications

/components
  /notifications
    /NotificationsPopover.tsx   # Dropdown notifications
    /NotificationBadge.tsx      # Unread count badge
    /NotificationItem.tsx       # Individual notification
    /NotificationList.tsx       # List component
    /NotificationFilters.tsx    # Filter/sort options

/app
  /notifications
    /page.tsx                   # Full notifications page
    /layout.tsx                 # Notifications layout
  /admin
    /notifications
      /dashboard
        /page.tsx              # Admin notification management
        /NotificationStats.tsx  # Analytics component
        /BulkActions.tsx       # Bulk operations

/api
  /notifications
    /create.ts                 # Create notification
    /dismiss.ts               # Mark as read/dismiss
    /delete.ts               # Delete notification
    /bulk.ts                # Bulk operations

/types
  /notifications.d.ts         # TypeScript definitions

/utils
  /notificationHelpers.ts    # Helper functions
  /notificationFormatters.ts # Format notification text

/config
  /notificationConfig.ts     # Configuration options

/database
  /migrations
    /notifications.sql       # Database schema
```

**Database Schema (notifications table)**
```sql
notifications (
  id uuid PRIMARY KEY,
  client_id uuid REFERENCES pelates(id),
  message text NOT NULL,
  type text CHECK (type IN ('info', 'warning', 'error')),
  metadata jsonb,
  created_at timestamptz DEFAULT now(),
  read boolean DEFAULT false,
  expires_at timestamptz,
  link text,
  FOREIGN KEY (client_id) REFERENCES auth.users(id)
)
```

Would you like me to elaborate on any specific aspect of this system?
