Here's a detailed breakdown of the Badges and Goals System:

**Badge System Workflow**

1. **Badge Categories & Types**
   - **Attendance**
     - First Check-in
     - 10/25/50/100 Check-ins
     - Perfect Month
     - Consistency Streaks (7/30/90 days)

   - **Exercise Performance**
     - First PR
     - Weight Milestones
     - Movement Mastery
     - Skill Achievements

   - **Progress**
     - Weight Loss Goals
     - Strength Gains
     - Skills Acquired
     - Program Completion

   - **Community**
     - Referrals
     - Event Participation
     - Social Engagement
     - Feedback Contribution

2. **Badge Progression Levels**
   - Bronze → Entry level
   - Silver → Intermediate
   - Gold → Advanced
   - Platinum → Elite

**Goals System Workflow**

1. **Goal Categories**
   - Monthly Check-ins
   - Weight/Body Composition
   - Performance Metrics
   - Skill Acquisition
   - Attendance Streaks

2. **Goal States**
   - Pending (newly created)
   - Active (in progress)
   - Completed (achieved)
   - Failed (not achieved by deadline)
   - Abandoned (manually cancelled)

**Achievement Flow**

1. **Automated Triggers**
   - System monitors relevant metrics
   - Checks achievement conditions
   - Awards badge/updates goal progress
   - Creates celebration notification
   - Updates user profile

2. **Manual Awards**
   - Coach/Admin reviews performance
   - Selects achievement to award
   - Adds custom message
   - Triggers celebration

**Required Files Structure**

```plaintext
/lib
  /badges
    /badgeService.ts           # Core badge service
    /badgeTypes.ts             # Badge definitions
    /badgeConditions.ts        # Achievement conditions
    /badgeProgression.ts       # Level progression logic
  /goals
    /goalService.ts            # Core goals service
    /goalTypes.ts              # Goal definitions
    /goalCalculations.ts       # Progress calculations
    /goalValidation.ts         # Validation rules

/hooks
  /badges
    /useBadges.ts             # Main badges hook
    /useBadgeProgress.ts      # Progress tracking
    /useBadgeUnlock.ts        # Achievement detection
  /goals
    /useGoals.ts              # Main goals hook
    /useGoalProgress.ts       # Progress tracking
    /useGoalUpdates.ts        # Goal updates

/components
  /badges
    /BadgeDisplay.tsx         # Badge showcase
    /BadgeGrid.tsx            # Grid display
    /BadgeCard.tsx            # Individual badge
    /BadgeProgress.tsx        # Progress indicator
    /BadgeUnlock.tsx         # Achievement animation
    /admin
      /BadgeManagement.tsx    # Admin badge management
      /BadgeAnalytics.tsx     # Badge statistics
  /goals
    /GoalsManager.tsx         # Goals management
    /GoalCard.tsx            # Individual goal
    /GoalProgress.tsx        # Progress tracking
    /GoalForm.tsx            # Goal creation/edit
    /admin
      /GoalTemplates.tsx     # Goal templates
      /GoalAssignment.tsx    # Assign goals

/app
  /achievements
    /page.tsx                # Achievement dashboard
    /badges/page.tsx         # Badges page
    /goals/page.tsx          # Goals page
  /admin
    /badges/page.tsx         # Badge administration
    /goals/page.tsx         # Goal administration

/api
  /badges
    /award.ts               # Award badge
    /progress.ts           # Update progress
    /revoke.ts            # Revoke badge
  /goals
    /create.ts            # Create goal
    /update.ts           # Update goal
    /complete.ts         # Complete goal
    /assign.ts          # Assign goal

/types
  /badges.d.ts           # Badge type definitions
  /goals.d.ts           # Goal type definitions

/utils
  /badgeHelpers.ts      # Badge helper functions
  /goalHelpers.ts       # Goal helper functions
  /progressCalculator.ts # Progress calculations

/config
  /badgeConfig.ts       # Badge configuration
  /goalConfig.ts        # Goal configuration
```

**Database Schema**

```sql
-- Badges tables
badges (
  id uuid PRIMARY KEY,
  name text NOT NULL,
  description text,
  category badge_category NOT NULL,
  level badge_level NOT NULL,
  icon_content text,
  requirements jsonb,
  created_at timestamptz DEFAULT now()
)

user_badges (
  id uuid PRIMARY KEY,
  pelatis_id uuid REFERENCES pelates(id),
  badge_id uuid REFERENCES badges(id),
  achieved_at timestamptz DEFAULT now(),
  progress jsonb,
  UNIQUE(pelatis_id, badge_id)
)

-- Goals tables
goals (
  id uuid PRIMARY KEY,
  title text NOT NULL,
  description text,
  category goal_category NOT NULL,
  target_value numeric,
  duration_days integer,
  created_at timestamptz DEFAULT now()
)

assigned_goals (
  id uuid PRIMARY KEY,
  pelatis_id uuid REFERENCES pelates(id),
  goal_id uuid REFERENCES goals(id),
  assigned_by uuid REFERENCES auth.users(id),
  start_date timestamptz DEFAULT now(),
  due_date timestamptz,
  current_value numeric DEFAULT 0,
  status goal_status DEFAULT 'pending',
  progress_history jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Enums
CREATE TYPE badge_category AS ENUM (
  'attendance', 
  'exercise', 
  'progression', 
  'consistency', 
  'milestone'
);

CREATE TYPE badge_level AS ENUM (
  'bronze',
  'silver',
  'gold',
  'platinum'
);

CREATE TYPE goal_status AS ENUM (
  'pending',
  'active',
  'completed',
  'failed',
  'abandoned'
);
```

Would you like me to elaborate on any specific aspect of these systems?
