Here's a detailed breakdown of the Workout (WOD) Tracking System:

**WOD System Core Features**

1. **WOD Management**
   - Creation/scheduling
   - Publishing/unpublishing
   - Template management
   - Exercise linking
   - Warmup sections
   - Programming cycles

2. **User Interaction**
   - View daily WOD
   - Track completion
   - Log performance
   - Record modifications
   - Save favorites

3. **Coach Features**
   - Batch WOD creation
   - Movement substitutions
   - Scaling options
   - Performance tracking
   - Feedback system

**Workflow States**

1. **WOD States**
   - Draft
   - Scheduled
   - Published
   - Completed
   - Archived

2. **User Progress States**
   - Not Started
   - In Progress
   - Completed
   - Scaled
   - DNF (Did Not Finish)

**Required Files Structure**

```plaintext
/components
  /wods
    /creation
      /WodBuilder.tsx          # Main WOD creation interface
      /ExerciseSelector.tsx    # Exercise selection component
      /TemplateSelector.tsx    # WOD templates
      /WodFormModal.tsx        # WOD form modal
      /WodPreview.tsx          # Preview component
    /display
      /WodCard.tsx            # Individual WOD display
      /WodList.tsx            # List of WODs
      /WodCalendar.tsx        # Calendar view
      /DailyWod.tsx           # Today's WOD
    /tracking
      /WodTimer.tsx           # Workout timer
      /ScoreInput.tsx         # Performance input
      /ResultsLog.tsx         # Results logging
      /ProgressGraph.tsx      # Progress visualization
    /admin
      /WodDashboard.tsx       # Admin dashboard
      /BatchEditor.tsx        # Batch WOD editing
      /ProgrammingCycles.tsx  # Programming cycles
    /user
      /UserWodView.tsx        # User WOD interface
      /CompletionForm.tsx     # Completion form
      /HistoryView.tsx        # Personal history
      /FavoriteWods.tsx       # Saved WODs

/app
  /wods
    /today
      /page.tsx               # Today's WOD page
      /client.tsx             # Client component
    /history
      /page.tsx              # History page
    /templates
      /page.tsx              # Templates page
  /admin
    /wods
      /page.tsx              # Admin WOD management
      /templates/page.tsx     # Template management
      /analytics/page.tsx     # WOD analytics

/lib
  /wods
    /wodService.ts           # Core WOD service
    /wodTypes.ts            # Type definitions
    /wodValidation.ts       # Validation rules
    /wodCalculations.ts     # Score calculations
    /wodFormatting.ts       # Text formatting

/hooks
  /wods
    /useWodCreation.ts      # WOD creation hook
    /useWodTracking.ts      # Tracking hook
    /useWodHistory.ts       # History hook
    /useWodTemplates.ts     # Templates hook
    /useWodTimer.ts         # Timer hook

/utils
  /wodHelpers.ts            # Helper functions
  /wodScoring.ts           # Scoring utilities
  /wodExport.ts           # Export functions
  /wodImport.ts          # Import functions

/types
  /wod.d.ts               # WOD type definitions
```

**Database Schema**

```sql
-- Main WOD table
wod (
  id uuid PRIMARY KEY,
  date date NOT NULL,
  content text NOT NULL,
  warmup text,
  exercises uuid[] REFERENCES exercise_movements(id)[],
  is_published boolean DEFAULT false,
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  created_by uuid REFERENCES auth.users(id),
  template_id uuid REFERENCES wod_templates(id),
  programming_cycle_id uuid REFERENCES programming_cycles(id),
  scaling_options jsonb,
  metadata jsonb
)

-- WOD Templates
wod_templates (
  id uuid PRIMARY KEY,
  name text NOT NULL,
  description text,
  content text NOT NULL,
  category text,
  default_scaling jsonb,
  created_at timestamptz DEFAULT now(),
  created_by uuid REFERENCES auth.users(id)
)

-- Programming Cycles
programming_cycles (
  id uuid PRIMARY KEY,
  name text NOT NULL,
  start_date date,
  end_date date,
  description text,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now()
)

-- User WOD Completions
user_wods (
  id uuid PRIMARY KEY,
  pelatis_id uuid REFERENCES pelates(id),
  wod_id uuid REFERENCES wod(id),
  completion_status completion_status_type,
  score jsonb,
  scaling_used jsonb,
  notes text,
  completion_time interval,
  completed_at timestamptz DEFAULT now(),
  modified_movements jsonb,
  rpe integer CHECK (rpe BETWEEN 1 AND 10),
  UNIQUE(pelatis_id, wod_id)
)

-- User WOD Favorites
wod_favorites (
  id uuid PRIMARY KEY,
  pelatis_id uuid REFERENCES pelates(id),
  wod_id uuid REFERENCES wod(id),
  created_at timestamptz DEFAULT now(),
  UNIQUE(pelatis_id, wod_id)
)

-- WOD Comments/Feedback
wod_comments (
  id uuid PRIMARY KEY,
  wod_id uuid REFERENCES wod(id),
  user_wod_id uuid REFERENCES user_wods(id),
  user_id uuid REFERENCES auth.users(id),
  content text NOT NULL,
  created_at timestamptz DEFAULT now()
)

-- Custom types
CREATE TYPE completion_status_type AS ENUM (
  'not_started',
  'in_progress',
  'completed',
  'scaled',
  'dnf'
);
```

**Key Features Implementation**

1. **WOD Creation**
   - Rich text editor for content
   - Exercise movement linking
   - Scaling options definition
   - Template-based creation
   - Batch scheduling

2. **WOD Display**
   - Markdown rendering
   - Movement demonstrations
   - Scaling suggestions
   - Timer integration
   - Progress tracking

3. **Performance Tracking**
   - Score validation
   - PR detection
   - History comparison
   - Analytics generation
   - Export capabilities

4. **Admin Features**
   - Programming cycles
   - Template management
   - Batch operations
   - Analytics dashboard
   - User progress tracking

Would you like me to elaborate on any specific aspect of the WOD system?
