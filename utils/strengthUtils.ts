// utils/strengthUtils.ts
import type { Exercise } from '@/types/strength-program';

/**
 * Calculates warmup sets for a given exercise based on Starting Strength principles
 * @param workWeight The weight for the work sets (in kg)
 * @param barWeight The weight of the bar (defaults to 20kg)
 * @param reps The number of reps for work sets (defaults to 5)
 * @returns Array of warmup sets with weight, reps, and percentage of work weight
 */
export function calculateWarmupSets(workWeight: number, barWeight: number = 20, reps: number = 5) {
  // Don't create warmup sets if workWeight is just the bar
  if (workWeight <= barWeight) {
    return [];
  }

  // Create warmup progression
  // Starting Strength typically uses 5 reps for first sets, then decreases
  const warmupSets = [
    { percentage: 0, reps: 5, description: 'Empty Bar' },
    { percentage: 0.4, reps: 5, description: '40%' },
    { percentage: 0.6, reps: 3, description: '60%' },
    { percentage: 0.8, reps: 2, description: '80%' },
    { percentage: 0.9, reps: 1, description: '90%' }
  ];

  // For lighter weights (<40kg), reduce the number of warmup sets
  let adjustedWarmups = [...warmupSets];
  if (workWeight < 60) {
    // Remove the 90% set for lighter weights
    adjustedWarmups.pop();
    
    if (workWeight < 40) {
      // Remove the 80% set for very light weights
      adjustedWarmups.pop();
    }
  }

  // Calculate actual weights for each set
  return adjustedWarmups.map(set => {
    let weight = set.percentage === 0 
      ? barWeight 
      : Math.round((workWeight * set.percentage) / 2.5) * 2.5;
      
    // Ensure weight is at least the bar weight
    weight = Math.max(weight, barWeight);
    
    // Make sure weight doesn't exceed work weight
    weight = Math.min(weight, workWeight);
    
    return {
      weight,
      reps: set.reps,
      percentage: set.percentage,
      description: set.description,
      isWarmup: true
    };
  });
}

/**
 * Available plates in the gym (in kg)
 * Each entry represents a pair of plates
 */
export const availablePlates = [20, 15, 10, 5, 2.5, 1.25];

/**
 * Calculates which plates to put on each side of the bar
 * @param targetWeight The total weight to achieve (in kg)
 * @param barWeight The weight of the bar (defaults to 20kg)
 * @returns Array of plates to put on each side
 */
export function calculatePlates(targetWeight: number, barWeight: number = 20): number[] {
  // Weight must be achievable with available plates
  const plateWeight = targetWeight - barWeight;
  
  // If we're just using the bar or less, return empty array
  if (plateWeight <= 0) return [];
  
  // Weight per side
  const sideWeight = plateWeight / 2;
  
  const result: number[] = [];
  let remainingWeight = sideWeight;
  
  // Start with largest plates and work down
  for (const plate of availablePlates) {
    while (remainingWeight >= plate) {
      result.push(plate);
      remainingWeight -= plate;
    }
  }
  
  // Check if we couldn't exactly achieve the weight with available plates
  if (remainingWeight > 0) {
    console.warn(`Could not exactly achieve ${targetWeight}kg with available plates. Off by ${remainingWeight * 2}kg.`);
  }
  
  return result;
}

/**
 * Format the plate arrangement to a readable string
 * @param plates Array of plates per side
 * @returns String representation (e.g. "20kg × 2, 5kg × 2 per side")
 */
export function formatPlateArrangement(plates: number[]): string {
  if (plates.length === 0) {
    return "just the bar";
  }
  
  // Count occurrences of each plate
  const plateCounts: Record<number, number> = {};
  plates.forEach(plate => {
    plateCounts[plate] = (plateCounts[plate] || 0) + 1;
  });
  
  // Format as string
  const plateStrings = Object.entries(plateCounts)
    .map(([plate, count]) => `${plate}kg × ${count}`)
    .join(', ');
    
  return `${plateStrings} per side`;
}

/**
 * Calculates an efficient plate loading strategy for an entire workout
 * Aims to minimize plate changes between exercises
 * @param exercises Array of exercises with their work weights
 * @param barWeight The weight of the bar (defaults to 20kg)
 * @returns Optimal plate arrangement for the workout
 */
export function calculateEfficientPlateArrangement(exercises: { weight: number, exercise_id?: string, exercise_name?: string }[], barWeight: number = 20) {
  // Group exercises by weight to see which plates are needed
  // Use Array.reduce to create a unique sorted array instead of spreading a Set
  const uniqueWeights = exercises
    .reduce<number[]>((unique, exercise) => {
      if (!unique.includes(exercise.weight)) {
        unique.push(exercise.weight);
      }
      return unique;
    }, [])
    .sort((a, b) => a - b);
  
  // Calculate plates needed for each unique weight
  const platesByWeight = uniqueWeights.map(weight => ({
    weight,
    plates: calculatePlates(weight, barWeight)
  }));
  
  return {
    uniqueWeights,
    platesByWeight,
    allPlatesNeeded: getAllRequiredPlates(platesByWeight)
  };
}

/**
 * Gets all unique plates required for a workout
 * @param platesByWeight Array of weights and their plate arrangements
 * @returns Record with counts of each plate needed
 */
function getAllRequiredPlates(platesByWeight: { weight: number, plates: number[] }[]): Record<number, number> {
  const allPlatesNeeded: Record<number, number> = {};
  
  platesByWeight.forEach(({ plates }) => {
    plates.forEach(plate => {
      allPlatesNeeded[plate] = Math.max(
        (allPlatesNeeded[plate] || 0),
        plates.filter(p => p === plate).length
      );
    });
  });
  
  return allPlatesNeeded;
}

/**
 * Format a descriptive plate loading guide
 * @param allPlatesNeeded Record with counts of each plate needed
 * @returns Readable string of plates to gather for the workout
 */
export function formatPlateLoadingGuide(allPlatesNeeded: Record<number, number>): string {
  if (Object.keys(allPlatesNeeded).length === 0) {
    return "No plates needed for this workout, just use the empty bar.";
  }
  
  const plateList = Object.entries(allPlatesNeeded)
    .sort(([a], [b]) => Number(b) - Number(a)) // Sort by plate weight (descending)
    .map(([plate, count]) => `${count} pairs of ${plate}kg plates`)
    .join(', ');
    
  return `For this workout, you'll need: ${plateList}.`;
}