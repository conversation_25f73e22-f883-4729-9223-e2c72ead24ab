import { cache } from 'react'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase'

export const getServerUser = cache(async () => {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    console.error('Auth error:', userError);
    redirect('/auth');
  }

  // Cache key includes user ID and timestamp (refreshed every hour)
  const cacheKey = `${user.id}_${Math.floor(Date.now() / 3600000)}`
  
  const { data: pelatesData, error: pelatesError } = await supabase
    .from('pelates')
    .select('id')
    .eq('auth_user_id', user.id)
    .single();

  if (pelatesError || !pelatesData) {
    console.error('Pelates error:', pelatesError);
    redirect('/auth');
  }

  // Check if user is admin
  const { data: roles } = await supabase.rpc('getUserRoles', { 
    p_user_id: user.id 
  });
  
  const isAdmin = Array.isArray(roles) && roles.includes('admin');

  return {
    userId: user.id,
    clientId: pelatesData.id,
    isAdmin,
  };
})