import type { AssessmentData } from '@/types/fitness';

export const calculateFitnessMetrics = (data: AssessmentData) => {
  const heightInMeters = data.height / 100;
  const heightInInches = data.height / 2.54;
    
  const bmi = data.weight / (heightInMeters ** 2);
    
  const idealWeight = data.sex === 'male'
    ? 48 + 2.7 * (heightInInches - 60)
    : 45.5 + 2.2 * (heightInInches - 60);
      
  const bmr = data.sex === 'male'
    ? 88.362 + (13.397 * data.weight) + (4.799 * data.height) - (5.677 * data.age)
    : 447.593 + (9.247 * data.weight) + (3.098 * data.height) - (4.330 * data.age);
      
  const additionalCalories = data.sex === 'male' ? 207.86 : 178.50;
    
  return {
    bmi: Number(bmi.toFixed(2)),
    idealWeight: Number(idealWeight.toFixed(2)),
    overweight: Number((data.weight - idealWeight).toFixed(2)),
    bmr: Number(bmr.toFixed(2)),
    additionalCalories: Number(additionalCalories.toFixed(2))
  };
};
  
export const getBMICategory = (bmi: number): string => {
  if (bmi < 18.5) return 'Underweight';
  if (bmi < 24.9) return 'Normal weight';
  if (bmi < 29.9) return 'Overweight';
  return 'Obese';
};