#!/bin/bash
# extract-feature.sh - Extract files for a specific feature
# Usage: ./extract-feature.sh feature-name

# Define color codes for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if a feature name was provided
if [ -z "$1" ]; then
  echo -e "${RED}Error: Please provide a feature name${NC}"
  echo "Available features:"
  echo "  auth - Authentication system"
  echo "  notifications - Notification system"
  echo "  bookings - Session booking system"
  echo "  checkins - Check-in tracking"
  echo "  wods - Workout of the day"
  echo "  goals - Goals and achievements"
  echo "  exercise - Exercise tracking"
  echo "  payments - Payments and subscriptions"
  echo "  support - Support system"
  exit 1
fi

FEATURE=$1
OUTPUT_FILE="feature-${FEATURE}.txt"

# Handle different features
case $FEATURE in
  auth)
    echo -e "${GREEN}Extracting Authentication System files...${NC}"
    yek "app/auth/page.tsx" "hooks/useServerUser.ts" "hooks/useUserRole.ts" "utils/auth.server.ts" "lib/jwt.ts" "components/AuthForm.tsx" > $OUTPUT_FILE
    ;;
    
  notifications)
    echo -e "${GREEN}Extracting Notification System files...${NC}"
    yek "app/user/notifications/page.tsx" "components/NotificationsPopover.tsx" "hooks/useNotifications.ts" "hooks/useFetchNotifications.ts" "lib/notifications.ts" > $OUTPUT_FILE
    ;;
    
  bookings)
    echo -e "${GREEN}Extracting Booking System files...${NC}"
    yek "app/user/session-book/page.tsx" "app/user/bookings/page.tsx" "app/admin/sessions/add/page.tsx" "components/SessionCreationForm.tsx" "components/SessionsUserPerseas.tsx" > $OUTPUT_FILE
    ;;
    
  checkins)
    echo -e "${GREEN}Extracting Check-in System files...${NC}"
    yek "app/admin/check-ins/page.tsx" "components/CheckinsModal.tsx" > $OUTPUT_FILE
    ;;
    
  wods)
    echo -e "${GREEN}Extracting WOD System files...${NC}"
    yek "app/admin/wods/add/page.tsx" "app/user/wods/page.tsx" "components/wods/WodForm.tsx" "components/wods/UserWODs.tsx" > $OUTPUT_FILE
    ;;
    
  goals)
    echo -e "${GREEN}Extracting Goals and Badges System files...${NC}"
    yek "app/user/achievements/page.tsx" "components/goals_badges/BadgeDisplay.tsx" "components/goals_badges/GoalsManager.tsx" > $OUTPUT_FILE
    ;;
    
  exercise)
    echo -e "${GREEN}Extracting Exercise Tracking System files...${NC}"
    yek "app/user/exercise-records/page.tsx" "app/user/exercise-records/QuickAddForm.tsx" "components/ExerciseRecordForm.tsx" > $OUTPUT_FILE
    ;;
    
  payments)
    echo -e "${GREEN}Extracting Payments System files...${NC}"
    yek "app/admin/payments/add/page.tsx" "app/admin/subscriptions/active/page.tsx" "components/PaymentHistory.tsx" "components/MembershipStatus.tsx" > $OUTPUT_FILE
    ;;
    
  support)
    echo -e "${GREEN}Extracting Support System files...${NC}"
    yek "app/user/support/page.tsx" "app/admin/support/page.tsx" "lib/services/support.ts" > $OUTPUT_FILE
    ;;
    
  *)
    echo -e "${RED}Unknown feature: $FEATURE${NC}"
    exit 1
    ;;
esac

echo -e "${GREEN}Extraction complete! Contents saved to $OUTPUT_FILE${NC}"
echo "You can now copy the contents to clipboard with: cat $OUTPUT_FILE | pbcopy"