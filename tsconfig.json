{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "types": ["mermaid"], "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@components/*": ["components/*"], "@hooks/*": ["hooks/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"], "@lib/*": ["lib/*"]}, "forceConsistentCasingInFileNames": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}