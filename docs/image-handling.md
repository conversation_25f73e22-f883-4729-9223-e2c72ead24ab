# Image Handling in LiftApp

This document explains how images are stored and accessed in the LiftApp application.

## Storage Structure

Images in LiftApp are stored in Supabase Storage buckets. The primary bucket for exercise images is named `exercises`.

## Image URL Construction

To access images stored in Supabase Storage, you need to construct the URL using the following pattern:

```
{SUPABASE_URL}/storage/v1/object/public/{BUCKET_NAME}/{IMAGE_ID}
```

Where:
- `SUPABASE_URL` is your Supabase project URL (from environment variable `NEXT_PUBLIC_SUPABASE_URL`)
- `BUCKET_NAME` is the name of the storage bucket (e.g., `exercises`)
- `IMAGE_ID` is the unique identifier or path of the image in the bucket

## Using the Image Utility Functions

We've created utility functions to simplify image URL handling in the application. These functions are located in `lib/utils/imageUtils.ts`.

### getImageUrl

```typescript
function getImageUrl(imageId: string, bucket: string = 'exercises'): string
```

This function constructs the full URL for an image stored in Supabase Storage.

**Example usage:**

```typescript
import { getImageUrl } from '@/lib/utils/imageUtils';

// In your component
const imageUrl = getImageUrl('example-image.jpg');
// Result: https://your-project.supabase.co/storage/v1/object/public/exercises/example-image.jpg

// With a custom bucket
const profileImageUrl = getImageUrl('avatar.png', 'profiles');
// Result: https://your-project.supabase.co/storage/v1/object/public/profiles/avatar.png
```

### getFallbackImageUrl

```typescript
function getFallbackImageUrl(text: string = 'No+Image'): string
```

This function provides a fallback placeholder image URL when an image fails to load.

**Example usage:**

```typescript
import { getFallbackImageUrl } from '@/lib/utils/imageUtils';

// In your component's error handler
<Image
  src={getImageUrl(image.image_url)}
  onError={(e) => {
    const imgElement = e.currentTarget as HTMLImageElement;
    imgElement.src = getFallbackImageUrl();
  }}
/>
```

## Best Practices

1. **Always use the utility functions** instead of constructing URLs manually to ensure consistency.

2. **Handle image loading errors** by providing fallback images.

3. **Store only the image ID or path** in your database, not the full URL. This makes it easier to change storage providers or URL structures in the future.

4. **Validate image IDs** before attempting to load them to prevent unnecessary network requests.

## Updating Image IDs

If you need to update image IDs in the database (e.g., when migrating images), you can use SQL queries like:

```sql
-- Create a mapping table for image renames
CREATE TABLE IF NOT EXISTS public.images_rename (
  oldname TEXT PRIMARY KEY,
  newname TEXT
);

-- Insert mappings
INSERT INTO public.images_rename (oldname, newname) VALUES
('old-image-1.jpg', 'new-image-1.jpg'),
('old-image-2.jpg', 'new-image-2.jpg');

-- Update image_id values in exercise_movements table
UPDATE public.exercise_movements em
SET image_id = ir.newname
FROM public.images_rename ir
WHERE em.image_id = ir.oldname
AND ir.newname IS NOT NULL;

-- Check if any image_id values weren't updated
SELECT 
  COUNT(*) AS remaining_old_ids
FROM 
  public.exercise_movements em
JOIN 
  public.images_rename ir ON em.image_id = ir.oldname
WHERE 
  ir.newname IS NOT NULL;
```
