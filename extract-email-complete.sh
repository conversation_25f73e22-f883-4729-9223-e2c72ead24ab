#!/bin/bash
# extract-email-complete.sh - Extract email feature with auth and API dependencies
# Usage: bash extract-email-complete.sh

# Define color codes for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Extracting Email Feature with Auth and API dependencies...${NC}"

OUTPUT_FILE="email-feature-complete.txt"

# List of files to extract
FILES=(
  # Email composer and related components
  "components/EmailComposer.tsx"
  "app/api/send-email/route.ts"
  
  # Auth dependencies
  "app/auth/page.tsx"
  "hooks/useServerUser.ts"
  "hooks/useUserRole.ts"
  "utils/auth.server.ts"
  "lib/jwt.ts"
  "components/AuthForm.tsx"
  
  # API routes
  "app/api/auth/callback/route.ts"
  
  # Members view
  "app/admin/users/view/page.tsx"
  
  # Support services (likely related to email)
  "lib/services/support.ts"
  "services/support.ts"
)

# Extract files using yek
echo -e "${BLUE}Running yek to extract files...${NC}"
yek "${FILES[@]}" > $OUTPUT_FILE

# Check if extraction was successful
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Extraction complete! Contents saved to $OUTPUT_FILE${NC}"
  
  # Get file size
  FILE_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
  LINE_COUNT=$(wc -l < "$OUTPUT_FILE")
  
  echo -e "${YELLOW}File size: $FILE_SIZE${NC}"
  echo -e "${YELLOW}Line count: $LINE_COUNT${NC}"
  
  echo "You can now copy the contents to clipboard with: cat $OUTPUT_FILE | pbcopy"
  
  # Optional: automatically copy to clipboard on macOS
  if [ "$(uname)" == "Darwin" ]; then
    cat $OUTPUT_FILE | pbcopy
    echo -e "${GREEN}Content copied to clipboard!${NC}"
  fi
else
  echo -e "${RED}Error: Extraction failed${NC}"
  exit 1
fi