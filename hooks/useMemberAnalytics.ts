// hooks/useMemberAnalytics.ts
import { useState, useEffect, useMemo } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { format, subMonths, parseISO, startOfMonth, endOfMonth } from 'date-fns';
import type { Database } from '@/types/supabase';
import { Users, UserPlus, UserMinus, Calendar, Activity } from 'lucide-react';

export type TimeframeOption = '30' | '90' | '180' | '365';

export type MemberMetric = {
  title: string;
  value: number | string;
  change: number;
  icon: React.ElementRef<any>;
  description: string;
};

export type CohortData = {
  cohort: string;
  initialCount: number;
  [key: string]: number | string;
};

export type AtRiskMemberData = {
  id: string;
  name: string;
  checkins: number;
  lastCheckIn: string | null;
  subscription_end?: string | null;
  days_remaining?: number | null;
};

export type MemberSegmentData = {
  name: string;
  value: number;
  color?: string;
  avgMonths: number;
  memberCount: number;
  totalRevenue: number;
};

export type CheckInDistributionData = {
  range: string;
  count: number;
  percentage: number;
};

export type LifetimeValueData = {
  range: string;
  value: number;
  avgSpend: number;
  totalRevenue: number;
};

export type MemberGrowthData = {
  month: string;
  date: string;
  newMembers: number;
  churnedMembers: number;
  netChange: number;
};

export interface MemberAnalyticsData {
  metrics: MemberMetric[];
  memberGrowth: MemberGrowthData[];
  retentionCohorts: CohortData[];
  atRiskMembers: AtRiskMemberData[];
  checkInDistribution: CheckInDistributionData[];
  memberSegments: MemberSegmentData[];
  lifetimeValues: LifetimeValueData[];
  isLoading: boolean;
  error: Error | null;
}

export function useMemberAnalytics(timeframe: TimeframeOption): MemberAnalyticsData {
  const [metrics, setMetrics] = useState<MemberMetric[]>([]);
  const [memberGrowth, setMemberGrowth] = useState<MemberGrowthData[]>([]);
  const [retentionCohorts, setRetentionCohorts] = useState<CohortData[]>([]);
  const [atRiskMembers, setAtRiskMembers] = useState<AtRiskMemberData[]>([]);
  const [checkInDistribution, setCheckInDistribution] = useState<CheckInDistributionData[]>([]);
  const [memberSegments, setMemberSegments] = useState<MemberSegmentData[]>([]);
  const [lifetimeValues, setLifetimeValues] = useState<LifetimeValueData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const supabase = createClientComponentClient<Database>();

  // Date range calculation based on timeframe
  const dateRange = useMemo(() => {
    const endDate = new Date();
    let startDate: Date;

    switch (timeframe) {
      case '30':
        startDate = subMonths(endDate, 1);
        break;
      case '90':
        startDate = subMonths(endDate, 3);
        break;
      case '180':
        startDate = subMonths(endDate, 6);
        break;
      case '365':
        startDate = subMonths(endDate, 12);
        break;
      default:
        startDate = subMonths(endDate, 3);
    }

    return {
      start: startDate.toISOString(),
      end: endDate.toISOString(),
      previousStart: subMonths(startDate, parseInt(timeframe) / 30).toISOString(),
      previousEnd: startDate.toISOString(),
    };
  }, [timeframe]);

  // Helper function to calculate percent change
  function calculatePercentChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  useEffect(() => {
    async function fetchMemberData() {
      setIsLoading(true);
      setError(null);

      try {
        // 1. Fetch key metrics
        const [
          activeMembers,
          previousActiveMembers,
          newMembers,
          previousNewMembers,
          churned,
          previousChurned,
          totalCheckIns,
          previousCheckIns
        ] = await Promise.all([
          // Active members count - current period
          supabase.from('active_subscriptions')
            .select('client_id', { count: 'exact', head: true }),

          // Active members count - previous period
          supabase.from('pliromes')
            .select('pelates_id', { count: 'exact', head: true })
            .lte('start_program', dateRange.previousEnd)
            .gt('end_date', dateRange.previousEnd),

          // New members count - current period
          supabase.from('pliromes')
            .select('pelates_id', { count: 'exact', head: true })
            .gte('start_program', dateRange.start)
            .lte('start_program', dateRange.end),

          // New members count - previous period
          supabase.from('pliromes')
            .select('pelates_id', { count: 'exact', head: true })
            .gte('start_program', dateRange.previousStart)
            .lte('start_program', dateRange.previousEnd),

          // Churned members count - current period
          supabase.from('pliromes')
            .select('pelates_id', { count: 'exact', head: true })
            .gte('end_date', dateRange.start)
            .lte('end_date', dateRange.end),

          // Churned members count - previous period
          supabase.from('pliromes')
            .select('pelates_id', { count: 'exact', head: true })
            .gte('end_date', dateRange.previousStart)
            .lte('end_date', dateRange.previousEnd),

          // Check-ins count - current period
          supabase.from('check_ins')
            .select('id', { count: 'exact', head: true })
            .gte('check_in_time', dateRange.start)
            .lte('check_in_time', dateRange.end),

          // Check-ins count - previous period
          supabase.from('check_ins')
            .select('id', { count: 'exact', head: true })
            .gte('check_in_time', dateRange.previousStart)
            .lte('check_in_time', dateRange.previousEnd)
        ]);

        const activeMembersCount = activeMembers.count || 0;
        const previousActiveMembersCount = previousActiveMembers.count || 0;
        const activeChange = calculatePercentChange(activeMembersCount, previousActiveMembersCount);

        const newMembersCount = newMembers.count || 0;
        const previousNewMembersCount = previousNewMembers.count || 0;
        const newMembersChange = calculatePercentChange(newMembersCount, previousNewMembersCount);

        const churnedCount = churned.count || 0;
        const previousChurnedCount = previousChurned.count || 0;
        const churnedChange = calculatePercentChange(churnedCount, previousChurnedCount);

        const checkInsCount = totalCheckIns.count || 0;
        const previousCheckInsCount = previousCheckIns.count || 0;
        const checkInsChange = calculatePercentChange(checkInsCount, previousCheckInsCount);

        // Calculate retention rate
        const retentionRate = previousActiveMembersCount > 0
          ? ((previousActiveMembersCount - previousChurnedCount) / previousActiveMembersCount) * 100
          : 0;

        const currentRetentionRate = activeMembersCount > 0
          ? ((activeMembersCount - churnedCount) / activeMembersCount) * 100
          : 0;

        const retentionChange = calculatePercentChange(currentRetentionRate, retentionRate);

        // Update metrics with all the data needed for UI display
        setMetrics([
          {
            title: 'Active Members',
            value: activeMembersCount,
            change: activeChange,
            icon: Users, // This will be mapped to an actual component in the UI
            description: 'Total active paid memberships'
          },
          {
            title: 'New Members',
            value: newMembersCount,
            change: newMembersChange,
            icon: UserPlus,
            description: `New sign-ups in the selected period`
          },
          {
            title: 'Churn Rate',
            value: activeMembersCount > 0 ? `${((churnedCount / activeMembersCount) * 100).toFixed(1)}%` : '0%',
            change: -churnedChange, // Invert change (negative change in churn is positive)
            icon: UserMinus,
            description: 'Percentage of members who cancelled'
          },
          {
            title: 'Retention Rate',
            value: `${currentRetentionRate.toFixed(1)}%`,
            change: retentionChange,
            icon: Calendar,
            description: 'Percentage of members who stayed'
          },
          {
            title: 'Check-ins',
            value: checkInsCount,
            change: checkInsChange,
            icon: Activity,
            description: 'Total check-ins during period'
          }
        ]);

        // 2. Fetch monthly growth data for chart
        const monthsToFetch = Math.max(12, parseInt(timeframe) / 30);
        const monthlyData = [];

        for (let i = 0; i < monthsToFetch; i++) {
          const monthStart = startOfMonth(subMonths(new Date(), i));
          const monthEnd = endOfMonth(monthStart);

          const [newMembers, churnedMembers] = await Promise.all([
            supabase.from('pliromes')
              .select('pelates_id', { count: 'exact', head: true })
              .gte('start_program', monthStart.toISOString())
              .lte('start_program', monthEnd.toISOString()),

            supabase.from('pliromes')
              .select('pelates_id', { count: 'exact', head: true })
              .gte('end_date', monthStart.toISOString())
              .lte('end_date', monthEnd.toISOString())
          ]);

          monthlyData.push({
            month: format(monthStart, 'MMM yyyy'),
            date: monthStart.toISOString(),
            newMembers: newMembers.count || 0,
            churnedMembers: churnedMembers.count || 0,
            netChange: (newMembers.count || 0) - (churnedMembers.count || 0)
          });
        }

        setMemberGrowth(monthlyData.reverse());

        // 3. Fetch retention cohorts data
        const cohorts = [];
        for (let i = 0; i < 6; i++) { // Last 6 cohorts
          const cohortStart = startOfMonth(subMonths(new Date(), i + 1));
          const cohortEnd = endOfMonth(cohortStart);

          // Get members who joined in this cohort
          const { data: cohortMembers, error: cohortError } = await supabase
            .from('pliromes')
            .select('pelates_id, start_program, end_date')
            .gte('start_program', cohortStart.toISOString())
            .lte('start_program', cohortEnd.toISOString());

          if (cohortError) continue;
          if (!cohortMembers || cohortMembers.length === 0) continue;

          const initialCount = cohortMembers.length;
          const cohortData: CohortData = {
            cohort: format(cohortStart, 'MMM yyyy'),
            initialCount
          };

          // Calculate retention for months 1-6
          for (let month = 1; month <= 6; month++) {
            const checkDate = new Date(cohortEnd);
            checkDate.setMonth(checkDate.getMonth() + month);

            // Skip future months
            if (checkDate > new Date()) break;

            // Count members still active at this point
            const stillActiveCount = cohortMembers.filter(member => {
              return !member.end_date || new Date(member.end_date) >= checkDate;
            }).length;

            cohortData[`month_${month}`] = Math.round((stillActiveCount / initialCount) * 100);
          }

          cohorts.push(cohortData);
        }

        setRetentionCohorts(cohorts.reverse());

        // 4. Fetch at-risk members
        const { data: checkInStats } = await supabase
          .from('user_checkin_stats')
          .select('*')
          .order('total_checkins', { ascending: true })
          .limit(20);

        if (checkInStats) {
          // Also get subscription end dates for these members
          const pelatesIds = checkInStats.map(stat => stat.pelatis_id).filter(Boolean);

          // Get last check-in dates for these members
          const { data: lastCheckIns } = await supabase
            .from('check_ins')
            .select('pelatis_id, check_in_time')
            .in('pelatis_id', pelatesIds)
            .order('check_in_time', { ascending: false });

          // Create a map of last check-in dates
          const lastCheckInMap = new Map();
          if (lastCheckIns) {
            lastCheckIns.forEach(checkIn => {
              if (!lastCheckInMap.has(checkIn.pelatis_id) && checkIn.check_in_time) {
                lastCheckInMap.set(checkIn.pelatis_id, checkIn.check_in_time);
              }
            });
          }

          const { data: subscriptionData } = await supabase
            .from('pliromes')
            .select('pelates_id, end_date')
            .in('pelates_id', pelatesIds)
            .order('end_date', { ascending: true });

          const subscriptionMap = new Map();

          if (subscriptionData) {
            subscriptionData.forEach(sub => {
              if (!subscriptionMap.has(sub.pelates_id) && sub.end_date) {
                const endDate = new Date(sub.end_date);
                const remainingDays = Math.ceil((endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

                subscriptionMap.set(sub.pelates_id, {
                  end_date: sub.end_date,
                  days_remaining: remainingDays
                });
              }
            });
          }

          const atRisk = checkInStats
            .filter(stat =>
              stat.pelatis_id !== null && // Ensure we have a valid ID
              ((stat.total_checkins || 0) < 10 || // Low check-in threshold
              (subscriptionMap.has(stat.pelatis_id) && subscriptionMap.get(stat.pelatis_id).days_remaining < 14)) // Soon expiring
            )
            .map(stat => ({
              id: stat.pelatis_id!, // Non-null assertion since we filtered out nulls
              name: `${stat.name || ''} ${stat.last_name || ''}`.trim() || 'Unknown Member',
              checkins: stat.total_checkins || 0,
              lastCheckIn: lastCheckInMap.get(stat.pelatis_id!) || null,
              subscription_end: subscriptionMap.has(stat.pelatis_id!) ? subscriptionMap.get(stat.pelatis_id!).end_date : null,
              days_remaining: subscriptionMap.has(stat.pelatis_id!) ? subscriptionMap.get(stat.pelatis_id!).days_remaining : null
            }))
            .sort((a, b) => {
              // Sort by days remaining first (if available for both)
              if (a.days_remaining !== null && b.days_remaining !== null) {
                return a.days_remaining - b.days_remaining;
              }
              // Then sort by check-ins
              return a.checkins - b.checkins;
            })
            .slice(0, 10);

          setAtRiskMembers(atRisk);
        }

        // 5. Check-in distribution
        const { data: allCheckinStats } = await supabase
          .from('user_checkin_stats')
          .select('*');

        if (allCheckinStats) {
          const distribution = [
            { range: '0 check-ins', count: 0, percentage: 0 },
            { range: '1-3 check-ins', count: 0, percentage: 0 },
            { range: '4-7 check-ins', count: 0, percentage: 0 },
            { range: '8-11 check-ins', count: 0, percentage: 0 },
            { range: '12+ check-ins', count: 0, percentage: 0 }
          ];

          allCheckinStats.forEach(stat => {
            const checkins = stat.total_checkins || 0;

            if (checkins === 0) {
              distribution[0].count++;
            } else if (checkins <= 3) {
              distribution[1].count++;
            } else if (checkins <= 7) {
              distribution[2].count++;
            } else if (checkins <= 11) {
              distribution[3].count++;
            } else {
              distribution[4].count++;
            }
          });

          // Calculate percentages
          const totalMembers = allCheckinStats.length;
          distribution.forEach(item => {
            item.percentage = Math.round((item.count / totalMembers) * 100);
          });

          setCheckInDistribution(distribution);
        }

        // 6. Member lifetime value and segmentation
        const { data: customerData } = await supabase
          .from('pliromes')
          .select(`
            pelates_id,
            start_program,
            end_date,
            money_gave
          `);

        if (customerData) {
          const lifetimeMap = new Map();

          customerData.forEach(payment => {
            if (!lifetimeMap.has(payment.pelates_id)) {
              lifetimeMap.set(payment.pelates_id, {
                total_spent: 0,
                first_payment: payment.start_program,
                last_payment: payment.start_program,
                payments_count: 0,
                membership_months: 0
              });
            }

            const customer = lifetimeMap.get(payment.pelates_id);
            customer.total_spent += payment.money_gave || 0;
            customer.payments_count++;

            if (payment.start_program && new Date(payment.start_program) < new Date(customer.first_payment)) {
              customer.first_payment = payment.start_program;
            }

            if (payment.start_program && new Date(payment.start_program) > new Date(customer.last_payment)) {
              customer.last_payment = payment.start_program;
            }

            // Calculate membership duration
            if (payment.start_program && payment.end_date) {
              const start = new Date(payment.start_program);
              const end = new Date(payment.end_date);
              const months = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth();
              customer.membership_months += months > 0 ? months : 0;
            }
          });

          // Calculate different segments based on total spent
          const segmentRanges = [
            { min: 0, max: 200, name: '€0-200', color: '#FF8042' },
            { min: 200, max: 500, name: '€200-500', color: '#FFBB28' },
            { min: 500, max: 1000, name: '€500-1000', color: '#00C49F' },
            { min: 1000, max: Infinity, name: '€1000+', color: '#0088FE' }
          ];

          const segments = segmentRanges.map(range => ({
            name: range.name,
            value: 0,
            color: range.color,
            avgMonths: 0,
            memberCount: 0,
            totalRevenue: 0
          }));

          lifetimeMap.forEach(customer => {
            const segmentIndex = segmentRanges.findIndex(
              range => customer.total_spent >= range.min && customer.total_spent < range.max
            );

            if (segmentIndex >= 0) {
              segments[segmentIndex].value++;
              segments[segmentIndex].memberCount++;
              segments[segmentIndex].totalRevenue += customer.total_spent;
              segments[segmentIndex].avgMonths += customer.membership_months;
            }
          });

          // Calculate averages
          segments.forEach(segment => {
            if (segment.memberCount > 0) {
              segment.avgMonths = Math.round(segment.avgMonths / segment.memberCount);
            }
          });

          // Filter out empty segments
          const filteredSegments = segments.filter(segment => segment.value > 0);

          setMemberSegments(filteredSegments);

          // Create lifetime value buckets
          const lifetimeValueBuckets = [
            { range: '0-3 months', value: 0, avgSpend: 0, totalRevenue: 0 },
            { range: '4-6 months', value: 0, avgSpend: 0, totalRevenue: 0 },
            { range: '7-12 months', value: 0, avgSpend: 0, totalRevenue: 0 },
            { range: '1-2 years', value: 0, avgSpend: 0, totalRevenue: 0 },
            { range: '2+ years', value: 0, avgSpend: 0, totalRevenue: 0 }
          ];

          lifetimeMap.forEach(customer => {
            let bucketIndex;

            if (customer.membership_months <= 3) {
              bucketIndex = 0;
            } else if (customer.membership_months <= 6) {
              bucketIndex = 1;
            } else if (customer.membership_months <= 12) {
              bucketIndex = 2;
            } else if (customer.membership_months <= 24) {
              bucketIndex = 3;
            } else {
              bucketIndex = 4;
            }

            lifetimeValueBuckets[bucketIndex].value++;
            lifetimeValueBuckets[bucketIndex].totalRevenue += customer.total_spent;
          });

          // Calculate average spend
          lifetimeValueBuckets.forEach(bucket => {
            if (bucket.value > 0) {
              bucket.avgSpend = Math.round(bucket.totalRevenue / bucket.value);
            }
          });

          setLifetimeValues(lifetimeValueBuckets);
        }

      } catch (err) {
        console.error('Error fetching member data:', err);
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    }

    fetchMemberData();
  }, [supabase, dateRange, timeframe]);

  return {
    metrics,
    memberGrowth,
    retentionCohorts,
    atRiskMembers,
    checkInDistribution,
    memberSegments,
    lifetimeValues,
    isLoading,
    error
  };
}
