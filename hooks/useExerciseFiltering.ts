// Custom hook for efficient exercise filtering

import { useMemo } from 'react';
import { ExerciseMovement, Filters } from '@/components/exercises/ExercisesClient';

export function useExerciseFiltering(
  exercises: ExerciseMovement[],
  filters: Filters,
  searchTerm: string = '',
  sortConfig: { key: string; direction: 'asc' | 'desc' } | null = null
) {
  // Step 1: Apply filtering
  const filteredExercises = useMemo(() => {
    if (!exercises.length) return [];

    let result = [...exercises];

    // Apply search term if any
    if (searchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(exercise => {
        // Search in name
        if (exercise.exercise_name.toLowerCase().includes(lowerSearch)) return true;

        // Search in description
        if (exercise.description && exercise.description.toLowerCase().includes(lowerSearch))
          return true;

        // Search in categories
        if (exercise.categories?.some(cat =>
          cat.category_value.toLowerCase().includes(lowerSearch)
        )) return true;

        return false;
      });
    }

    // Apply category filters
    if (filters.body_part !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'body_part' && cat.category_value === filters.body_part
        )
      );
    }

    if (filters.equipment !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'equipment' && cat.category_value === filters.equipment
        )
      );
    }

    if (filters.expertise_level !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'expertise_level' && cat.category_value === filters.expertise_level
        )
      );
    }

    if (filters.movement_category !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'movement_category' && cat.category_value === filters.movement_category
        )
      );
    }

    // Apply media filters
    if (filters.has_video !== 'all') {
      result = result.filter(exercise =>
        filters.has_video === 'yes'
          ? (exercise.videos && exercise.videos.length > 0)
          : (!exercise.videos || exercise.videos.length === 0)
      );
    }

    if (filters.has_image !== 'all') {
      result = result.filter(exercise =>
        filters.has_image === 'yes'
          ? (exercise.images && exercise.images.length > 0)
          : (!exercise.images || exercise.images.length === 0)
      );
    }

    if (filters.has_records !== 'all') {
      result = result.filter(exercise =>
        filters.has_records === 'yes' ? exercise.hasRecords : !exercise.hasRecords
      );
    }

    if (filters.video_source !== 'all') {
      result = result.filter(exercise =>
        exercise.videos?.some(video =>
          video.video_source === filters.video_source
        )
      );
    }

    return result;
  }, [exercises, filters, searchTerm]);

  // Step 2: Apply sorting
  const sortedExercises = useMemo(() => {
    if (!sortConfig) return filteredExercises;

    return [...filteredExercises].sort((a, b) => {
      const getSortValue = (exercise: ExerciseMovement, key: string) => {
        if (key === 'exercise_name') return exercise.exercise_name;

        // Get value from primary category
        if (['body_part', 'equipment', 'expertise_level', 'movement_category'].includes(key)) {
          return exercise.categories?.find(cat =>
            cat.category_type === key && cat.is_primary
          )?.category_value || '';
        }

        return '';
      };

      const aValue = getSortValue(a, sortConfig.key);
      const bValue = getSortValue(b, sortConfig.key);

      if (!aValue) return 1;
      if (!bValue) return -1;

      const comparison = aValue.localeCompare(bValue);
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [filteredExercises, sortConfig]);

  return {
    filteredExercises,
    sortedExercises,
    totalFiltered: filteredExercises.length
  };
}