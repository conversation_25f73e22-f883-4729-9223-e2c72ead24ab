// hooks/useStrengthProgram.tsx
import { useState } from 'react';
import { useSupabase } from './useSupabase';
import type { PlanData, TodaysWorkout, Exercise, WorkoutStatus } from '@/types/strength-program';
import type { Database } from '@/types/supabase'; // Add this import

export function useStrengthProgram() {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [todaysWorkout, setTodaysWorkout] = useState<TodaysWorkout | null>(null);
  const [activePlan, setActivePlan] = useState<PlanData | null>(null);

  /**
   * Creates a new strength training plan for a user
   */
  const createStrengthTrainingPlan = async (
    pelatiId: string,
    startingWeights: Record<string, number>,
    incrementSettings: Record<string, number>,
    startDate: string
  ): Promise<string | null> => {
    try {
      setLoading(true);
      
      // Get the Starting Strength program ID
      const { data: ssProgram } = await supabase
        .from('programs')
        .select('id')
        .eq('name', 'Starting Strength')
        .maybeSingle();
      
      if (!ssProgram) {
        throw new Error('Starting Strength program not found');
      }
      
      // Deactivate any existing active plans
      await supabase
        .from('strength_training_plans')
        .update({ is_active: false })
        .eq('pelatis_id', pelatiId)
        .eq('is_active', true);
      
      // Create a new plan
      const { data: newPlan, error: planError } = await supabase
        .from('strength_training_plans')
        .insert({
          pelatis_id: pelatiId,
          program_id: ssProgram.id,
          starting_weights: startingWeights,
          increment_settings: incrementSettings,
          start_date: startDate,
          is_active: true
        })
        .select('id')
        .single();
      
      if (planError || !newPlan) {
        throw new Error(planError?.message || 'Failed to create training plan');
      }
      
      return newPlan.id;
    } catch (err) {
      console.error('Error in createStrengthTrainingPlan:', err);
      setError(err as Error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gets the active strength training plan for a user
   */
  const getActivePlan = async (pelatiId: string): Promise<PlanData | null> => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('strength_training_plans')
        .select('*')
        .eq('pelatis_id', pelatiId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .maybeSingle();
      
      if (error) {
        throw new Error(error.message);
      }
      
      if (data) {
        setActivePlan(data as PlanData);
        return data as PlanData;
      }
      
      return null;
    } catch (err) {
      console.error('Error in getActivePlan:', err);
      setError(err as Error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gets today's scheduled workout
   */
  const getTodaysWorkout = async (pelatiId: string): Promise<TodaysWorkout | null> => {
    try {
      setLoading(true);
      
      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];
      
      // Get active plan
      const plan = await getActivePlan(pelatiId);
      
      if (!plan) {
        return null;
      }
      
      // Get today's workout
      const { data: workoutData, error: workoutError } = await supabase
        .from('strength_workouts')
        .select('id, week_number, workout_type, status')
        .eq('plan_id', plan.id)
        .eq('scheduled_date', today)
        .eq('status', 'scheduled')
        .maybeSingle();
      
      if (workoutError) {
        throw new Error(workoutError.message);
      }
      
      if (!workoutData) {
        return null;
      }
      
      // Now we need to calculate the exercise details based on the workout type and week
      // This would ideally come from a database, but for this example, we'll generate it
      
      const exercises = generateWorkoutExercises(
        workoutData.workout_type,
        workoutData.week_number,
        plan.starting_weights,
        plan.increment_settings
      );
      
      const workout: TodaysWorkout = {
        workout_id: workoutData.id,
        week_number: workoutData.week_number,
        workout_type: workoutData.workout_type,
        exercises,
        scheduled_date: today // Add the today date we already have
      };
      
      setTodaysWorkout(workout);
      return workout;
    } catch (err) {
      console.error('Error in getTodaysWorkout:', err);
      setError(err as Error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Generate workout exercises based on plan parameters
   */
/**
 * Generate workout exercises based on plan parameters
 */
const generateWorkoutExercises = (
  workoutType: string,
  weekNumber: number,
  startingWeights: Record<string, number> | null,
  incrementSettings: Record<string, number> | null
): Exercise[] => {
  const exercises: Exercise[] = [];
  
  // Use empty objects as fallbacks if null is provided
  const safeStartingWeights = startingWeights || {};
  const safeIncrementSettings = incrementSettings || {};
  
  // Get exercise IDs
  const getExerciseInfo = async (name: string) => {
    const { data } = await supabase
      .from('exercise_movements')
      .select('id')
      .eq('exercise_name', name)
      .single();
    
    return data ? { id: data.id, name } : { id: name.toLowerCase().replace(/\s/g, ''), name };
  };
  
  // Define the exercises for each workout type
  let exerciseNames: string[] = [];
  
  if (workoutType === 'A') {
    exerciseNames = ['Squat', 'Bench Press', 'Deadlift'];
  } else if (workoutType === 'B') {
    exerciseNames = ['Squat', 'Press', 'Power Clean'];
  }
  
  // For each exercise, calculate the weight based on starting weight and increments
  exerciseNames.forEach(async (exerciseName) => {
    const exerciseInfo = await getExerciseInfo(exerciseName);
    
    // Map exercise name to key in starting weights
    const keyMapping: Record<string, string> = {
      'Squat': 'squat',
      'Bench Press': 'bench',
      'Press': 'press',
      'Deadlift': 'deadlift',
      'Power Clean': 'powerClean'
    };
    
    const key = keyMapping[exerciseName];
    
    if (!key) return;
    
    const startWeight = safeStartingWeights[key] || 20; // Default to bar weight if not found
    const increment = safeIncrementSettings[key] || 2.5; // Default to 2.5kg increment
    
    // Calculate current weight based on week number
    // Weeks start at 1, so subtract 1 for the calculation
    const incrementWeeks = weekNumber - 1;
    let currentWeight = startWeight + (increment * incrementWeeks);
    
    // Round to nearest 2.5kg
    currentWeight = Math.round(currentWeight / 2.5) * 2.5;
    
    // Add the exercise to our list
    exercises.push({
      exercise_id: exerciseInfo.id,
      exercise_name: exerciseInfo.name,
      weight: currentWeight,
      sets: 3, // Standard for Starting Strength
      reps: 5, // Standard for Starting Strength
      is_warmup: false
    });
  });
  
  return exercises;
};

  /**
   * Generates and stores a complete workout plan for a user
   */
/**
 * Generates and stores a complete workout plan for a user
 */
const generateAndStoreWorkoutPlan = async (
  planId: string,
  pelatiId: string,
  startingWeights: Record<string, number>,
  incrementSettings: Record<string, number>,
  startDate: Date,
  weeks: number = 12
) => {
  try {
    setLoading(true);
    
    // Ensure start date is a Monday
    const dayOfWeek = startDate.getDay();
    const daysToAdd = (dayOfWeek === 0 ? 1 : dayOfWeek === 1 ? 0 : 8 - dayOfWeek);
    const adjustedStartDate = new Date(startDate);
    adjustedStartDate.setDate(adjustedStartDate.getDate() + daysToAdd);
    
    // Important: Define the workouts array with the correct type
// Update the workouts array type
const workouts: {
  plan_id: string;
  workout_type: string;
  scheduled_date: string;
  day_of_week: string;
  week_number: number;
  status: WorkoutStatus;
}[] = [];
    
    // Generate workouts for the specified number of weeks
    // Starting Strength is typically Mon/Wed/Fri alternating A/B workouts
    for (let week = 1; week <= weeks; week++) {
      const weekStartDate = new Date(adjustedStartDate);
      weekStartDate.setDate(weekStartDate.getDate() + (week - 1) * 7);
      
      // Monday - Workout A
      const mondayDate = new Date(weekStartDate);
      workouts.push({
        plan_id: planId,
        workout_type: 'A',
        scheduled_date: mondayDate.toISOString().split('T')[0],
        day_of_week: 'Monday',
        week_number: week,
        status: 'scheduled' // This is now guaranteed to be of the correct type
      });
      
      // Wednesday - Workout B
      const wednesdayDate = new Date(weekStartDate);
      wednesdayDate.setDate(wednesdayDate.getDate() + 2);
      workouts.push({
        plan_id: planId,
        workout_type: 'B',
        scheduled_date: wednesdayDate.toISOString().split('T')[0],
        day_of_week: 'Wednesday',
        week_number: week,
        status: 'scheduled'
      });
      
      // Friday - Workout A (alternating)
      const fridayDate = new Date(weekStartDate);
      fridayDate.setDate(fridayDate.getDate() + 4);
      workouts.push({
        plan_id: planId,
        workout_type: 'A',
        scheduled_date: fridayDate.toISOString().split('T')[0],
        day_of_week: 'Friday',
        week_number: week,
        status: 'scheduled'
      });
    }
    
    // Store workouts in database
    const { error } = await supabase
      .from('strength_workouts')
      .insert(workouts);
    
    if (error) {
      throw new Error(error.message);
    }
  } catch (err) {
    console.error('Error in generateAndStoreWorkoutPlan:', err);
    setError(err as Error);
  } finally {
    setLoading(false);
  }
};

  /**
   * Marks a workout as completed and records exercise data
   */
  const completeWorkout = async (workoutId: string, completedExercises: Record<string, any>[] = []) => {
    try {
      setLoading(true);
      
      // First, update the workout status
      const { error: workoutError } = await supabase
      .from('strength_workouts')
      .update({ 
        status: 'completed' as WorkoutStatus,
        completed_date: new Date().toISOString()
      })
      .eq('id', workoutId);
      
      if (workoutError) {
        throw new Error(workoutError.message);
      }
      
      // If we have completed exercises data, record it
      if (completedExercises.length > 0) {
        // Get user ID from auth
        const { data: authData } = await supabase.auth.getSession();
        if (!authData.session) {
          throw new Error('Not authenticated');
        }
        
        // Get pelatis_id for the user
        const { data: userData } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', authData.session.user.id)
          .single();
        
        if (!userData) {
          throw new Error('User profile not found');
        }
        
        // Format exercise records
        const exerciseRecords = completedExercises.map(exercise => ({
          workout_id: workoutId,
          pelatis_id: userData.id,
          exercise_id: exercise.exercise_id,
          weight: exercise.weight,
          sets: exercise.sets,
          reps: exercise.reps,
          date_performed: new Date().toISOString(),
          is_pr: exercise.is_pr,
          is_warmup: exercise.is_warmup
        }));
        
        // Insert exercise records
        const { error: recordsError } = await supabase
          .from('strength_exercise_records')
          .insert(exerciseRecords);
        
        if (recordsError) {
          throw new Error(recordsError.message);
        }
      }
      
      return true;
    } catch (err) {
      console.error('Error in completeWorkout:', err);
      setError(err as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Resets a user's strength program
   */
  const resetUserProgram = async (pelatiId: string) => {
    try {
      console.log("Resetting user program for pelatiId:", pelatiId);
      setLoading(true);
      
      // Step 1: Delete all exercise records for this user
      const { error: recordsError } = await supabase
        .from('strength_exercise_records')
        .delete()
        .eq('pelatis_id', pelatiId);
        
      if (recordsError) console.error("Error deleting exercise records:", recordsError);
      
      // Step 2: Get all plans for this user
      const { data: plans } = await supabase
        .from('strength_training_plans')
        .select('id')
        .eq('pelatis_id', pelatiId);
        
      if (plans && plans.length > 0) {
        // Step 3: Delete all workouts for these plans
        const planIds = plans.map(p => p.id);
        
        const { error: workoutsError } = await supabase
          .from('strength_workouts')
          .delete()
          .in('plan_id', planIds);
          
        if (workoutsError) console.error("Error deleting workouts:", workoutsError);
        
        // Step 4: Delete all plans
        const { error: plansError } = await supabase
          .from('strength_training_plans')
          .delete()
          .eq('pelatis_id', pelatiId);
          
        if (plansError) console.error("Error deleting plans:", plansError);
      }
      
      console.log("Reset complete");
      return true;
    } catch (err) {
      console.error("Error in resetUserProgram:", err);
      setError(err as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };


  /**
 * Completes a custom workout and records exercise data
 */
  const completeCustomWorkout = async (workoutId: string, pelatiId: string, completedExercises: Record<string, any>[] = []) => {
    try {
      setLoading(true);
      
      // First, update the workout status
      const { error: workoutError } = await supabase
      .from('strength_workouts')
      .update({ 
        status: 'completed' as WorkoutStatus,
        completed_date: new Date().toISOString()
      })
      .eq('id', workoutId);
    if (workoutError) {
      throw new Error(workoutError.message);
    }
    
    // If we have completed exercises data, record it
    if (completedExercises.length > 0) {
      // Format exercise records
      const exerciseRecords = completedExercises.map(exercise => ({
        workout_id: workoutId,
        pelatis_id: pelatiId,
        exercise_id: exercise.exercise_id,
        weight: exercise.weight,
        sets: exercise.sets,
        reps: exercise.reps,
        date_performed: new Date().toISOString(),
        is_pr: exercise.is_pr,
        is_warmup: exercise.is_warmup
      }));
      
      // Insert exercise records
      const { error: recordsError } = await supabase
        .from('strength_exercise_records')
        .insert(exerciseRecords);
      
      if (recordsError) {
        throw new Error(recordsError.message);
      }
    }
    
    return true;
  } catch (err) {
    console.error('Error in completeCustomWorkout:', err);
    setError(err as Error);
    return false;
  } finally {
    setLoading(false);
  }
};

  return {
    loading,
    error,
    todaysWorkout,
    activePlan,
    createStrengthTrainingPlan,
    getActivePlan,
    getTodaysWorkout,
    generateAndStoreWorkoutPlan,
    completeWorkout,
    resetUserProgram,
    completeCustomWorkout
  };
}