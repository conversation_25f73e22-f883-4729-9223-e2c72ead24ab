// hooks/useReconciliationRecords.ts
import { useState, useEffect, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

type ReconciliationRecord = Database['public']['Tables']['reconciliation_records']['Row'];

export function useReconciliationRecords() {
  const [records, setRecords] = useState<ReconciliationRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    const fetchRecords = async () => {
      try {
        setLoading(true);
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();
        
        const { data, error } = await supabase
          .from('reconciliation_records')
          .select('*')
          .order('reconciliation_date', { ascending: false });
          
        if (error) throw error;
        
        if (isMountedRef.current && data) {
          setRecords(data);
        }
      } catch (err) {
        if (isMountedRef.current) {
          console.error('Error fetching reconciliation records:', err);
          setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };
    
    fetchRecords();
    
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  return { records, loading, error };
}