import { useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';
import { SupabaseClient } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";

type NotificationType = Database['public']['Tables']['notifications']['Row'];

interface UseNotificationsStreamProps {
 onNotifications: (notifications: NotificationType[]) => void;
 clientId: string;
 enabled: boolean;
}

export function useNotificationsStream({
 onNotifications,
 clientId,
 enabled
}: UseNotificationsStreamProps) {
 const supabase = createClientComponentClient<Database>();

 const handler = useRef(
   debounce((notifications: NotificationType[]) => {
     onNotifications(notifications);
   }, 1000)
 ).current;

 const { data: subscription } = useQuery({
   enabled,
   queryKey: ['realtime-notifications', clientId],
   queryFn: () => {
     const channel = supabase.channel('notifications-channel');
     return channel
       .on('postgres_changes',
         {
           event: 'INSERT',
           schema: 'public',
           filter: `client_id=eq.${clientId}`,
           table: 'notifications',
         },
         (payload) => handler([payload.new as NotificationType])
       )
       .subscribe();
   },
 });

 useEffect(() => {
   return () => {
     subscription?.unsubscribe();
   };
 }, [subscription]);
}