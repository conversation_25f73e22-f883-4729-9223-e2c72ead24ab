// hooks/useNotificationDebug.ts
import { useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

import type { Database } from "@/types/supabase";

export function useNotificationDebug(userId: string) {
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function debugNotifications() {
      console.group('Notification Debug Info');
      console.log('Debugging notifications for userId:', userId);

      try {
        // 1. Check if user exists in pelates table
        const { data: pelatesData, error: pelatesError } = await supabase
          .from('pelates')
          .select('id, auth_user_id')
          .eq('auth_user_id', userId)
          .single();

        console.log('Pelates check:', { pelatesData, pelatesError });

        if (pelatesData) {
          // 2. Check notifications for this pelati
          const { data: notificationsData, error: notificationsError } = await supabase
            .from('notifications')
            .select('*')
            .eq('client_id', pelatesData.id);

          console.log('Notifications check:', {
            count: notificationsData?.length,
            notifications: notificationsData,
            error: notificationsError
          });

          // 3. Check realtime subscription
          const channel = supabase
            .channel('debug-notifications')
            .on('postgres_changes', {
              event: '*',
              schema: 'public',
              table: 'notifications',
              filter: `client_id=eq.${pelatesData.id}`
            },
            payload => {
              console.log('Realtime notification received:', payload);
            })
            .subscribe(status => {
              console.log('Realtime subscription status:', status);
            });

          // Cleanup after 10 seconds
          setTimeout(() => {
            supabase.removeChannel(channel);
          }, 10000);
        }

      } catch (error) {
        console.error('Debug error:', error);
      }

      console.groupEnd();
    }

    debugNotifications();
  }, [userId, supabase]);
}