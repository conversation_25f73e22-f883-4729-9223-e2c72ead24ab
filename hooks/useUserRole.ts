import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useSupabase } from './useSupabase';

interface UserRoleHook {
  isAdmin: boolean;
  isPelatis: boolean;
  loading: boolean;
  error: Error | null;
  pelatisId: string | null; // Add this
}

export const useUserRole = (): UserRoleHook => {
  const { user } = useSupabase();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isPelatis, setIsPelatis] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [pelatisId, setPelatisId] = useState<string | null>(null); // Add this
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    const checkUserRole = async () => {
      if (!user) {
        console.log('No user found');
        setLoading(false);
        return;
      }

      try {
        // Check for pelatis profile first since we need the ID
        const { data: pelatesData, error: pelatesError } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', user.id)
          .single();

        if (pelatesError && pelatesError.code !== 'PGRST116') {
          throw pelatesError;
        }

        setIsPelatis(!!pelatesData);
        setPelatisId(pelatesData?.id || null); // Store the pelatis ID

        // Rest of your role checking code...
        const { data: roles, error: rolesError } = await supabase
          .rpc('getUserRoles', {
            p_user_id: user.id
          });

        if (!rolesError) {
          setIsAdmin(Array.isArray(roles) && roles.includes('admin'));
        }

      } catch (err) {
        console.error('Error in useUserRole:', err);
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    checkUserRole();
  }, [user, supabase]);

  return { isAdmin, isPelatis, loading, error, pelatisId };
};