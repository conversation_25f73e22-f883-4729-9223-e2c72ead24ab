/**
 * @file useFetchNotifications.ts
 * @description Custom React hook for fetching and managing notifications
 * 
 * @overview
 * Provides a comprehensive solution for:
 * - Fetching initial unread notifications
 * - Setting up real-time notification streams
 * - Managing notification lifecycle
 * 
 * @dependencies
 * - React Query (useQuery)
 * - Custom Supabase hook
 * - Real-time notifications stream hook
 * 
 * @supabase_interactions
 * - Queries notifications table
 * - Filters unread, non-expired notifications
 * - Sets up real-time subscription
 * 
 * @usage_example
 * ```typescript
 * useFetchNotifications({
 *   onNotifications: (notifications) => {
 *     // Handle incoming notifications
 *   },
 *   clientId: user.id,
 *   realtime: true
 * });
 * ```
 * 
 * @performance_considerations
 * - Limits initial fetch to 10 notifications
 * - Disables unnecessary refetching
 * - Efficient real-time updates
 * 
 * @error_handling
 * - Throws errors for failed data fetching
 * - Allows custom error management
 * 
 * @query_optimization
 * - Uses React Query for caching and state management
 * - Minimal network requests
 * 
 * @security_considerations
 * - Filters notifications by client ID
 * - Checks notification expiration
 * 
 * <AUTHOR> Name]
 * @created 2024-01-15
 * @version 1.0.0
 */
import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@/hooks/useSupabase';
import { useNotificationsStream } from './useNotificationsStream';
import type { Database } from '@/types/supabase';

// Type definition for notifications
type NotificationRow = Database['public']['Tables']['notifications']['Row'];

/**
 * Props interface for useFetchNotifications hook
 * Defines the configuration for notification fetching
 */
interface UseFetchNotificationsProps {
  /** Callback function to handle incoming notifications */
  onNotifications: (notifications: NotificationRow[]) => unknown;
  /** Client/User ID to filter notifications */
  clientId: string;
  /** Flag to enable/disable real-time updates */
  realtime: boolean;
}

/**
 * Primary hook for fetching and managing notifications
 * 
 * @param {UseFetchNotificationsProps} props - Configuration for notification fetching
 * 
 * @description
 * - Fetches initial set of unread notifications
 * - Sets up real-time notification stream
 * - Triggers callback with initial and real-time notifications
 */
export function useFetchNotifications({
  onNotifications,
  clientId,
  realtime,
}: UseFetchNotificationsProps) {
  // Fetch initial notifications
  const { data: initialNotifications } = useFetchInitialNotifications({
    clientId,
  });

  // Set up real-time notification stream
  useNotificationsStream({
    onNotifications,
    clientId,
    enabled: realtime,
  });

  // Trigger callback with initial notifications
  useEffect(() => {
    if (initialNotifications) {
      onNotifications(initialNotifications);
    }
  }, [initialNotifications, onNotifications]);
}

/**
 * Props for initial notifications fetch
 */
interface UseFetchInitialNotificationsProps {
  /** Client/User ID to filter notifications */
  clientId: string;
}

/**
 * Hook to fetch initial set of notifications
 * 
 * @param {UseFetchInitialNotificationsProps} props - Configuration for initial fetch
 * @returns {Object} React Query result with initial notifications
 * 
 * @description
 * - Fetches up to 10 unread, non-expired notifications
 * - Ordered by creation date (most recent first)
 * - Uses React Query for efficient data management
 */
function useFetchInitialNotifications({ clientId }: UseFetchInitialNotificationsProps) {
  // Access Supabase client
  const { supabase } = useSupabase();
  
  // Get current timestamp
  const now = new Date().toISOString();

  // Use React Query for notification fetching
  return useQuery({
    // Unique query key for caching
    queryKey: ['notifications', clientId],
    
    // Async function to fetch notifications
    queryFn: async () => {
      // Supabase query to fetch notifications
      const { data, error } = await supabase
        .from('notifications')
        .select()
        .eq('client_id', clientId)    // Filter by client ID
        .eq('read', false)             // Only unread notifications
        .gt('expires_at', now)         // Not expired
        .order('created_at', { ascending: false }) // Most recent first
        .limit(10);                    // Limit to 10 notifications
      
      // Throw error if query fails
      if (error) throw error;
      
      // Type assertion to ensure correct return type
      return data as NotificationRow[];
    },
    
    // Optimization flags
    refetchOnMount: false,             // Prevent unnecessary refetches
    refetchOnWindowFocus: false,       // Prevent background refetches
  });
}