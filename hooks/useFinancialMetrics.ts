// hooks/useFinancialMetrics.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { PeriodRange } from '@/types/dashboard';
import { format, parseISO } from 'date-fns';

// Define types for our financial data structure
export interface FinancialMetric {
  current: number;
  previous: number;
  percentChange: number;
}

export interface FinancialMetrics {
  mrr: FinancialMetric;
  totalRevenue: FinancialMetric;
  avgRevenuePerMember: FinancialMetric;
  totalExpenses: FinancialMetric;
  profitMargin: FinancialMetric;
  netProfit: FinancialMetric;
}

export interface RevenueItem {
  name: string;
  value: number;
}

export interface ExpenseItem {
  name: string;
  value: number;
}

export interface MerchandiseItem {
  item: string;
  quantity: number;
  revenue: number;
}

export interface MonthlyRevenueData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
}

export interface FinancialData {
  metrics: FinancialMetrics;
  monthlyRevenue: MonthlyRevenueData[];
  revenueByType: RevenueItem[];
  expensesByCategory: ExpenseItem[];
  merchandiseRevenue: MerchandiseItem[];
  isLoading: boolean;
  error: Error | null;
}

export function useFinancialMetrics(periodRange: PeriodRange): FinancialData {
  const [data, setData] = useState<FinancialData>({
    metrics: {
      mrr: { current: 0, previous: 0, percentChange: 0 },
      totalRevenue: { current: 0, previous: 0, percentChange: 0 },
      avgRevenuePerMember: { current: 0, previous: 0, percentChange: 0 },
      totalExpenses: { current: 0, previous: 0, percentChange: 0 },
      profitMargin: { current: 0, previous: 0, percentChange: 0 },
      netProfit: { current: 0, previous: 0, percentChange: 0 }
    },
    monthlyRevenue: [],
    revenueByType: [],
    expensesByCategory: [],
    merchandiseRevenue: [],
    isLoading: true,
    error: null
  });

  const supabase = createClientComponentClient<Database>();

  // Helper function to calculate percent change
  const calculatePercentChange = (current: number, previous: number): number => {
    if (previous === 0) return 0;
    return Number(((current - previous) / previous * 100).toFixed(1));
  };

  useEffect(() => {
    const fetchFinancialData = async () => {
      setData(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        // 1. FETCH CURRENT PERIOD PAYMENT DATA
        const { data: currentPayments, error: paymentsError } = await supabase
          .from('pliromes')
          .select(`
            id,
            money_gave,
            date_money_gave,
            course,
            pelates_id,
            way_of_payment
          `)
          .gte('date_money_gave', periodRange.startDate)
          .lte('date_money_gave', periodRange.endDate);

        if (paymentsError) throw paymentsError;

        // 2. FETCH PREVIOUS PERIOD PAYMENT DATA (for comparison)
        const { data: previousPayments, error: prevPaymentsError } = await supabase
          .from('pliromes')
          .select(`
            id,
            money_gave,
            date_money_gave,
            course,
            pelates_id
          `)
          .gte('date_money_gave', periodRange.previousStartDate)
          .lte('date_money_gave', periodRange.previousEndDate);

        if (prevPaymentsError) throw prevPaymentsError;

        // 3. FETCH EXPENSE DATA
        const { data: currentExpenses, error: expensesError } = await supabase
          .from('admin_expenses')
          .select(`
            id,
            amount,
            category,
            date
          `)
          .gte('date', periodRange.startDate)
          .lte('date', periodRange.endDate);

        if (expensesError) throw expensesError;

        // 4. FETCH PREVIOUS PERIOD EXPENSE DATA
        const { data: previousExpenses, error: prevExpensesError } = await supabase
          .from('admin_expenses')
          .select(`
            id,
            amount,
            category,
            date
          `)
          .gte('date', periodRange.previousStartDate)
          .lte('date', periodRange.previousEndDate);

        if (prevExpensesError) throw prevExpensesError;

        // 5. FETCH ACTIVE MEMBERS COUNT
        const { data: activeMembers, error: membersError } = await supabase
          .from('active_subscriptions')
          .select('client_id')
          .eq('subscription_status', 'active');

        if (membersError) throw membersError;

        // 6. FETCH MERCHANDISE SALES
        const { data: merchandiseSalesRaw, error: merchError } = await supabase
          .from('merchandise_sales')
          .select(`
            id,
            item_type,
            price,
            sale_date
          `)
          .gte('sale_date', periodRange.startDate)
          .lte('sale_date', periodRange.endDate);

        if (merchError) throw merchError;

        // Helper function to process merchandise sales data
        const processMerchandiseSales = (salesData: any[]) => {
          const groupedByType: Record<string, { count: number, price: number }> = {};

          salesData.forEach(sale => {
            const itemType = sale.item_type || 'Other';
            if (!groupedByType[itemType]) {
              groupedByType[itemType] = { count: 0, price: sale.price || 0 };
            }
            groupedByType[itemType].count += 1;
          });

          return Object.entries(groupedByType).map(([item_type, data]) => ({
            id: item_type,
            item_type,
            quantity: data.count,
            price: data.price
          }));
        };

        // Process the raw data to group by item_type and calculate quantities
        const merchandiseSales = merchandiseSalesRaw ? processMerchandiseSales(merchandiseSalesRaw) : [];

        // 7. FETCH MONTHLY REVENUE DATA FOR CHARTS
        // We'll get 6 months of data for charts
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const { data: monthlyPayments, error: monthlyPaymentsError } = await supabase
          .from('pliromes')
          .select(`
            date_money_gave,
            money_gave
          `)
          .gte('date_money_gave', sixMonthsAgo.toISOString())
          .lte('date_money_gave', periodRange.endDate);

        if (monthlyPaymentsError) throw monthlyPaymentsError;

        const { data: monthlyExpenses, error: monthlyExpensesError } = await supabase
          .from('admin_expenses')
          .select(`
            date,
            amount
          `)
          .gte('date', sixMonthsAgo.toISOString())
          .lte('date', periodRange.endDate);

        if (monthlyExpensesError) throw monthlyExpensesError;

        // CALCULATE FINANCIAL METRICS

        // 1. Calculate total revenue for current and previous periods
        const currentTotalRevenue = currentPayments?.reduce((sum, payment) =>
          sum + (payment.money_gave || 0), 0) || 0;

        const previousTotalRevenue = previousPayments?.reduce((sum, payment) =>
          sum + (payment.money_gave || 0), 0) || 0;

        // 2. Calculate total expenses
        const currentTotalExpenses = currentExpenses?.reduce((sum, expense) =>
          sum + (expense.amount || 0), 0) || 0;

        const previousTotalExpenses = previousExpenses?.reduce((sum, expense) =>
          sum + (expense.amount || 0), 0) || 0;

        // 3. Calculate profit and margin
        const currentNetProfit = currentTotalRevenue - currentTotalExpenses;
        const previousNetProfit = previousTotalRevenue - previousTotalExpenses;

        const currentProfitMargin = currentTotalRevenue > 0
          ? (currentNetProfit / currentTotalRevenue) * 100
          : 0;

        const previousProfitMargin = previousTotalRevenue > 0
          ? (previousNetProfit / previousTotalRevenue) * 100
          : 0;

        // 4. Calculate Monthly Recurring Revenue (MRR)
        // This assumes a standard 30-day period for normalization
        const daysInPeriod =
          (new Date(periodRange.endDate).getTime() -
           new Date(periodRange.startDate).getTime()) / (1000 * 60 * 60 * 24);

        const currentMRR = (currentTotalRevenue / daysInPeriod) * 30;

        const daysInPreviousPeriod =
          (new Date(periodRange.previousEndDate).getTime() -
           new Date(periodRange.previousStartDate).getTime()) / (1000 * 60 * 60 * 24);

        const previousMRR = (previousTotalRevenue / daysInPreviousPeriod) * 30;

        // 5. Calculate Average Revenue Per Member
        const activeCount = activeMembers?.length || 1; // Prevent division by zero
        const currentAvgRevenuePerMember = currentTotalRevenue / activeCount;
        const previousAvgRevenuePerMember = previousTotalRevenue / activeCount;

        // 6. Process revenue by type
        // Here we'll use the course/program type to categorize revenue
        const revenueByType: { [key: string]: number } = {};

        // Add membership revenue
        currentPayments?.forEach(payment => {
          const programName = payment.course || 'Other';
          if (!revenueByType[programName]) {
            revenueByType[programName] = 0;
          }
          revenueByType[programName] += payment.money_gave || 0;
        });

        // Add merchandise revenue as a separate category
        const merchandiseTotal = merchandiseSales?.reduce(
          (sum, sale) => sum + ((sale.price || 0) * (sale.quantity || 0)), 0) || 0;

        if (merchandiseTotal > 0) {
          revenueByType['Merchandise'] = merchandiseTotal;
        }

        // 7. Process expenses by category
        const expensesByCategory: { [key: string]: number } = {};

        currentExpenses?.forEach(expense => {
          const category = expense.category || 'Other';
          if (!expensesByCategory[category]) {
            expensesByCategory[category] = 0;
          }
          expensesByCategory[category] += expense.amount || 0;
        });

        // 8. Process merchandise sales
        const merchandiseByItem: { [key: string]: { quantity: number, revenue: number } } = {};

        merchandiseSales?.forEach(sale => {
          const item = sale.item_type || 'Other';
          if (!merchandiseByItem[item]) {
            merchandiseByItem[item] = { quantity: 0, revenue: 0 };
          }
          merchandiseByItem[item].quantity += sale.quantity || 0;
          merchandiseByItem[item].revenue += (sale.price || 0) * (sale.quantity || 0);
        });

        // 9. Process monthly revenue data for charts
        // Group payments and expenses by month
        const monthlyData: { [key: string]: { revenue: number, expenses: number } } = {};

        // Initialize with the last 6 months
        for (let i = 0; i < 6; i++) {
          const date = new Date();
          date.setMonth(date.getMonth() - i);
          const monthKey = format(date, 'yyyy-MM');
          monthlyData[monthKey] = { revenue: 0, expenses: 0 };
        }

        // Add revenue data
        monthlyPayments?.forEach(payment => {
          if (!payment.date_money_gave) return;
          const monthKey = format(parseISO(payment.date_money_gave), 'yyyy-MM');
          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { revenue: 0, expenses: 0 };
          }
          monthlyData[monthKey].revenue += payment.money_gave || 0;
        });

        // Add expense data
        monthlyExpenses?.forEach(expense => {
          if (!expense.date) return;
          const monthKey = format(parseISO(expense.date), 'yyyy-MM');
          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { revenue: 0, expenses: 0 };
          }
          monthlyData[monthKey].expenses += expense.amount || 0;
        });

        // Build the final data object
        const financialData: FinancialData = {
          metrics: {
            mrr: {
              current: currentMRR,
              previous: previousMRR,
              percentChange: calculatePercentChange(currentMRR, previousMRR)
            },
            totalRevenue: {
              current: currentTotalRevenue,
              previous: previousTotalRevenue,
              percentChange: calculatePercentChange(currentTotalRevenue, previousTotalRevenue)
            },
            avgRevenuePerMember: {
              current: currentAvgRevenuePerMember,
              previous: previousAvgRevenuePerMember,
              percentChange: calculatePercentChange(currentAvgRevenuePerMember, previousAvgRevenuePerMember)
            },
            totalExpenses: {
              current: currentTotalExpenses,
              previous: previousTotalExpenses,
              percentChange: calculatePercentChange(currentTotalExpenses, previousTotalExpenses)
            },
            profitMargin: {
              current: currentProfitMargin,
              previous: previousProfitMargin,
              percentChange: calculatePercentChange(currentProfitMargin, previousProfitMargin)
            },
            netProfit: {
              current: currentNetProfit,
              previous: previousNetProfit,
              percentChange: calculatePercentChange(currentNetProfit, previousNetProfit)
            }
          },
          monthlyRevenue: Object.entries(monthlyData)
            .map(([month, data]) => ({
              month,
              revenue: data.revenue,
              expenses: data.expenses,
              profit: data.revenue - data.expenses
            }))
            .sort((a, b) => a.month.localeCompare(b.month)), // Sort by month

          revenueByType: Object.entries(revenueByType)
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value), // Sort by value descending

          expensesByCategory: Object.entries(expensesByCategory)
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value), // Sort by value descending

          merchandiseRevenue: Object.entries(merchandiseByItem)
            .map(([item, data]) => ({
              item,
              quantity: data.quantity,
              revenue: data.revenue
            }))
            .sort((a, b) => b.revenue - a.revenue), // Sort by revenue descending

          isLoading: false,
          error: null
        };

        setData(financialData);
      } catch (error) {
        console.error('Error fetching financial data:', error);
        setData(prev => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error : new Error('Unknown error occurred')
        }));
      }
    };

    fetchFinancialData();
  }, [periodRange, supabase]);

  return data;
}
