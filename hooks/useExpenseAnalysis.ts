// hooks/useExpenseAnalysis.ts
import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

export interface ExpenseAnalysisDatum {
  category: string;
  total: number;
}

export function useExpenseAnalysis() {
  const [data, setData] = useState<ExpenseAnalysisDatum[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();
        
        const since = new Date();
        since.setMonth(since.getMonth() - 11);
        const sinceStr = since.toISOString().slice(0, 10);
        
        const { data: rows, error: fetchError } = await supabase
          .from('admin_expenses')
          .select('category, amount, date')
          .gte('date', sinceStr);
          
        if (fetchError) {
          throw fetchError;
        }
        
        const map: Record<string, number> = {};
        rows?.forEach(row => {
          const cat = row.category || 'Unknown';
          map[cat] = (map[cat] || 0) + (Number(row.amount) || 0);
        });
        
        const result = Object.entries(map).map(([category, total]) => ({ category, total }));
        
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching expense analysis:', err);
          setError(err instanceof Error ? err.message : 'Unknown error occurred');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }
    
    fetchData();
    
    return () => { isMounted = false; };
  }, []);

  return { data, loading, error };
}