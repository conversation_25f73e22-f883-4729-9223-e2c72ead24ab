import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import type { Database } from '@/types/supabase';
import type { NotificationMetadata } from '@/types/notifications';

type Tables = Database['public']['Tables']
type UserGoal = Tables['user_goals']['Row']

// Fix the type syntax
type CreateGoalInput = Omit<Tables['user_goals']['Insert'], 'id' | 'pelatis_id' | 'created_at' | 'updated_at'>;

export function useGoals(userId: string) {
  const { supabase } = useSupabase();
  const [goals, setGoals] = useState<UserGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchGoals = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('user_goals')
          .select('*')
          .eq('pelatis_id', userId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setGoals(data as UserGoal[]);
      } catch (err) {
        console.error('Error fetching goals:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch goals'));
      } finally {
        setLoading(false);
      }
    };

    fetchGoals();

    const subscription = supabase
      .channel('goals-channel')
      .on<UserGoal>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_goals',
          filter: `pelatis_id=eq.${userId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setGoals((prev) => [payload.new as UserGoal, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setGoals((prev) =>
              prev.map((goal) =>
                goal.id === payload.new.id ? (payload.new as UserGoal) : goal
              )
            );
          } else if (payload.eventType === 'DELETE') {
            setGoals((prev) => prev.filter((goal) => goal.id !== payload.old.id));
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [userId, supabase]);

  const createGoal = async (goal: CreateGoalInput) => {
    try {
      const { data, error } = await supabase
        .from('user_goals')
        .insert([{
          ...goal,
          pelatis_id: userId,
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error creating goal:', err);
      throw err;
    }
  };

  const updateGoal = async (goalId: string, updates: Partial<UserGoal>) => {
    try {
      const { data, error } = await supabase
        .from('user_goals')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', goalId)
        .eq('pelatis_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error updating goal:', err);
      throw err;
    }
  };

  const deleteGoal = async (goalId: string) => {
    try {
      const { error } = await supabase
        .from('user_goals')
        .delete()
        .eq('id', goalId)
        .eq('pelatis_id', userId);

      if (error) throw error;
    } catch (err) {
      console.error('Error deleting goal:', err);
      throw err;
    }
  };

  const updateProgress = async (goalId: string, newValue: number) => {
    try {
      const goal = goals.find((g) => g.id === goalId);
      if (!goal) throw new Error('Goal not found');

      const updates: Partial<UserGoal> = {
        current_value: newValue,
      };

      // Check if goal is completed
      if (newValue >= goal.target_value && !goal.completed_at) {
        updates.completed_at = new Date().toISOString();
        
        // Create notification with proper metadata structure
        // Create metadata conforming to the expected structure
        const metadata: NotificationMetadata = {
          transactionId: goalId, // Store goal ID in transactionId field
          amount: newValue, // Store current value in amount field
          eventType: 'payment_success' // Use one of the allowed eventType values as a proxy for success
        };

        await supabase.from('notifications').insert({
          client_id: userId,
          message: `Congratulations! You've achieved your goal: ${goal.title}`,
          type: 'info',
          metadata: metadata,
          read: false,
          created_at: new Date().toISOString()
        });
      }

      return await updateGoal(goalId, updates);
    } catch (err) {
      console.error('Error updating progress:', err);
      throw err;
    }
  };

  const getActiveGoals = () => goals.filter((goal) => !goal.completed_at);
  const getCompletedGoals = () => goals.filter((goal) => goal.completed_at);

  return {
    goals,
    loading,
    error,
    createGoal,
    updateGoal,
    deleteGoal,
    updateProgress,
    getActiveGoals,
    getCompletedGoals,
  };
}
