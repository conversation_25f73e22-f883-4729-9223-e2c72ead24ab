/**
 * @file useLocalStorage.ts
 * @description Custom React hook for managing localStorage with type-safe, reactive storage
 * 
 * @overview
 * A robust localStorage hook that provides:
 * - Type-safe localStorage interactions
 * - Reactive state management
 * - Server-side rendering compatibility
 * - Error handling for localStorage operations
 * 
 * @features
 * - Generic type support
 * - Fallback to initial value
 * - Safe parsing and storage
 * - Automatic synchronization between state and localStorage
 * 
 * @dependencies
 * - React (useState, useEffect)
 * 
 * @browserApis
 * - localStorage
 * 
 * @performanceConsiderations
 * - Lazy initialization of localStorage value
 * - Minimal rerenders
 * - Safe server-side rendering handling
 * 
 * @errorHandling
 * - Graceful error catching
 * - Fallback to initial value
 * - Console logging for debugging
 * 
 * @usageExample
 * ```typescript
 * const [name, setName] = useLocalStorage('username', 'Guest');
 * setName('John'); // Updates both state and localStorage
 * ```
 * 
 * @securityNote
 * - Avoid storing sensitive information
 * - JSON serialization limits storage capabilities
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */

import { useState, useEffect } from 'react';

export function useLocalStorage<T>(
  // Generic type T allows flexible storage of any serializable type
  key: string,           // Unique key for localStorage
  initialValue: T        // Fallback value if no stored data exists
): [T, (value: T | ((val: T) => T)) => void] {
  // Lazy state initialization to handle SSR and performance
  const [storedValue, setStoredValue] = useState<T>(() => {
    // Early return for server-side rendering prevents window access errors
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      // Attempt to retrieve and parse stored item
      const item = window.localStorage.getItem(key);
      
      // Return parsed item or fallback to initial value
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // Defensive error handling
      // Logs error and returns initial value to maintain app stability
      console.log(error);
      return initialValue;
    }
  });

  // Synchronize state changes with localStorage
  useEffect(() => {
    // Guard against server-side rendering
    if (typeof window !== "undefined") {
      try {
        // Stringify and store the current state
        window.localStorage.setItem(key, JSON.stringify(storedValue));
      } catch (error) {
        // Log any storage errors
        console.log(error);
      }
    }
  }, [key, storedValue]); // Re-run effect on key or value changes

  // Custom setter function with additional logic
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Support both direct values and functional updates
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      
      // Update local state
      setStoredValue(valueToStore);
      
      // Update localStorage (with SSR protection)
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // Robust error handling
      console.log(error);
    }
  };

  // Return tuple for convenient destructuring
  return [storedValue, setValue];
}