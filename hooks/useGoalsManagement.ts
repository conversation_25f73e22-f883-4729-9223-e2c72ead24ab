import { useState, useEffect } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import { Goal, GoalFormData } from '@/types/goals';
import { toast } from 'react-hot-toast';

export function useGoalsManagement(userId?: string) {
  const { supabase } = useSupabase();
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchGoals = async (pelatisId?: string) => {
    try {
      const query = supabase
        .from('assigned_goals')
        .select('*')
        .order('created_at', { ascending: false });

      if (pelatisId) {
        query.eq('pelatis_id', pelatisId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setGoals(data as Goal[]);
    } catch (error) {
      console.error('Error fetching goals:', error);
      toast.error('Αποτυχία φόρτωσης στόχων');
    } finally {
      setLoading(false);
    }
  };

  const createGoal = async (formData: GoalFormData, pelatisId: string) => {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .insert({
          title: formData.title,
          assigned_by: userId || '',
          category: formData.category,
          current_value: formData.current_value,
          description: formData.description,
          due_date: formData.due_date,
          pelatis_id: pelatisId,
          start_date: new Date().toISOString(),
          status: 'pending' as const,
          target_value: formData.target_value,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
      toast.success('Ο στόχος δημιουργήθηκε επιτυχώς');
      await fetchGoals(pelatisId);
    } catch (error) {
      console.error('Error creating goal:', error);
      toast.error('Αποτυχία δημιουργίας στόχου');
      throw error;
    }
  };

  const updateGoal = async (goalId: string, updates: Partial<Goal>) => {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', goalId);

      if (error) throw error;
      toast.success('Ο στόχος ενημερώθηκε επιτυχώς');
      await fetchGoals(userId);
    } catch (error) {
      console.error('Error updating goal:', error);
      toast.error('Αποτυχία ενημέρωσης στόχου');
      throw error;
    }
  };

  const deleteGoal = async (goalId: string) => {
    try {
      const { error } = await supabase
        .from('assigned_goals')
        .delete()
        .eq('id', goalId);

      if (error) throw error;
      toast.success('Ο στόχος διαγράφηκε επιτυχώς');
      await fetchGoals(userId);
    } catch (error) {
      console.error('Error deleting goal:', error);
      toast.error('Αποτυχία διαγραφής στόχου');
      throw error;
    }
  };

  useEffect(() => {
    if (userId) {
      fetchGoals(userId);
    }
  }, [userId]);

  return {
    goals,
    loading,
    fetchGoals,
    createGoal,
    updateGoal,
    deleteGoal
  };
}