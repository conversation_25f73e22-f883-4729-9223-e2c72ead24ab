// hooks/useServerUser.ts
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { redirect } from 'next/navigation';

export async function useServerUser() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  // Get the authenticated user
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/auth');
  }

  // Get the pelates record for the user
  const { data: pelatesData, error } = await supabase
    .from('pelates')
    .select('id')
    .eq('auth_user_id', user.id)
    .single();

  if (error || !pelatesData) {
    console.error('Error fetching pelates data:', error);
    redirect('/error');
  }

  return {
    userId: user.id,
    clientId: pelatesData.id,
  };
}