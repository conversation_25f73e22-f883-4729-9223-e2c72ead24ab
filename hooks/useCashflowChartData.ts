// hooks/useCashflowChartData.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

// Export this type so it can be imported in other files
export type CashflowChartDatum = {
  date: string;
  income: number;
  expense: number;
  balance: number;
};

type TimeRange = '30days' | '90days' | '6months' | '1year' | 'all';

export function useCashflowChartData(timeRange: TimeRange = '30days') {
  const [chartData, setChartData] = useState<CashflowChartDatum[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();
        
        // Calculate date range
        const today = new Date();
        let sinceDate = new Date();
        
        switch(timeRange) {
          case '30days':
            sinceDate.setDate(today.getDate() - 30);
            break;
          case '90days':
            sinceDate.setDate(today.getDate() - 90);
            break;
          case '6months':
            sinceDate.setMonth(today.getMonth() - 6);
            break;
          case '1year':
            sinceDate.setFullYear(today.getFullYear() - 1);
            break;
          case 'all':
            sinceDate = new Date(2000, 0, 1); // Far back enough for "all"
            break;
        }
        
        const sinceStr = sinceDate.toISOString().split('T')[0];
        
        // Fetch income
        const { data: incomeRows, error: incomeError } = await supabase
          .from('pliromes')
          .select('date_money_gave, money_gave')
          .gte('date_money_gave', sinceStr);
        if (incomeError) {
          throw incomeError;
        }
        
        // Fetch expenses
        const { data: expenseRows, error: expenseError } = await supabase
          .from('admin_expenses')
          .select('date, amount')
          .gte('date', sinceStr);
        if (expenseError) {
          throw expenseError;
        }
        
        // Process data for chart
        const dateMap = new Map<string, { income: number; expense: number }>();
        
        // Initialize with all dates in the range
        let currentDate = new Date(sinceDate);
        while (currentDate <= today) {
          const dateStr = currentDate.toISOString().split('T')[0];
          dateMap.set(dateStr, { income: 0, expense: 0 });
          currentDate.setDate(currentDate.getDate() + 1);
        }
        
        // Add income data
        incomeRows?.forEach(row => {
          if (row.date_money_gave) {
            const dateStr = new Date(row.date_money_gave).toISOString().split('T')[0];
            const existing = dateMap.get(dateStr) || { income: 0, expense: 0 };
            dateMap.set(dateStr, {
              ...existing,
              income: existing.income + (row.money_gave || 0)
            });
          }
        });
        
        // Add expense data
        expenseRows?.forEach(row => {
          if (row.date) {
            const dateStr = new Date(row.date).toISOString().split('T')[0];
            const existing = dateMap.get(dateStr) || { income: 0, expense: 0 };
            dateMap.set(dateStr, {
              ...existing,
              expense: existing.expense + (row.amount || 0)
            });
          }
        });
        
        // Convert to array and calculate running balance
        let balance = 0;
        const data = Array.from(dateMap.entries())
          .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
          .map(([date, values]) => {
            balance += values.income - values.expense;
            return {
              date,
              income: values.income,
              expense: values.expense,
              balance
            };
          });
        
        setChartData(data);
        
      } catch (err) {
        console.error('Error fetching cashflow data:', err);
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [timeRange]);
  
  return { chartData, loading, error };
}