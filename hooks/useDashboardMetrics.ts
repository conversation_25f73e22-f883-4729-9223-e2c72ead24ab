// hooks/useDashboardMetrics.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { PeriodRange, DashboardMetrics, ChartDataPoint, MemberSegment } from '@/types/dashboard';

interface DashboardData {
  metrics: DashboardMetrics;
  memberTrend: ChartDataPoint[];
  revenueTrend: ChartDataPoint[];
  memberSegments: MemberSegment[];
  isLoading: boolean;
  error: Error | null;
}

export function useDashboardMetrics(periodRange: PeriodRange): DashboardData {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    activeMembers: { current: 0, previous: 0, percentChange: 0 },
    mrr: { current: 0, previous: 0, percentChange: 0 },
    newMembers: { current: 0, previous: 0, percentChange: 0 },
    retentionRate: { current: 0, previous: 0, percentChange: 0 },
  });
  const [memberTrend, setMemberTrend] = useState<ChartDataPoint[]>([]);
  const [revenueTrend, setRevenueTrend] = useState<ChartDataPoint[]>([]);
  const [memberSegments, setMemberSegments] = useState<MemberSegment[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch active members count
        const { data: currentActiveMembers, error: currentMembersError } = await supabase
          .from('active_subscriptions')
          .select('client_id')
          .lte('end_date', periodRange.endDate)
          .gte('end_date', periodRange.startDate);

        if (currentMembersError) throw new Error(`Error fetching current members: ${currentMembersError.message}`);

        const { data: previousActiveMembers, error: previousMembersError } = await supabase
          .from('active_subscriptions')
          .select('client_id')
          .lte('end_date', periodRange.previousEndDate)
          .gte('end_date', periodRange.previousStartDate);

        if (previousMembersError) throw new Error(`Error fetching previous members: ${previousMembersError.message}`);

        const currentMemberCount = currentActiveMembers?.length || 0;
        const previousMemberCount = previousActiveMembers?.length || 0;
        const memberPercentChange = previousMemberCount > 0
          ? ((currentMemberCount - previousMemberCount) / previousMemberCount) * 100
          : 0;

        // Fetch MRR data
        const { data: currentMRR, error: currentMRRError } = await supabase.rpc(
          'get_monthly_recurring_revenue',
          { month_date: new Date().toISOString() }
        );

        if (currentMRRError) throw new Error(`Error fetching current MRR: ${currentMRRError.message}`);

        const { data: previousMRR, error: previousMRRError } = await supabase.rpc(
          'get_monthly_recurring_revenue',
          { month_date: periodRange.previousEndDate }
        );

        if (previousMRRError) throw new Error(`Error fetching previous MRR: ${previousMRRError.message}`);

        const mrrValue = currentMRR || 0;
        const previousMrrValue = previousMRR || 0;
        const mrrPercentChange = previousMrrValue > 0
          ? ((mrrValue - previousMrrValue) / previousMrrValue) * 100
          : 0;

        // Fetch new members data
        const { data: currentNewMembers, error: currentNewError } = await supabase
          .from('pliromes')
          .select('pelates_id')
          .gte('start_program', periodRange.startDate)
          .lte('start_program', periodRange.endDate)
          .is('nodebt', true);

        if (currentNewError) throw new Error(`Error fetching current new members: ${currentNewError.message}`);

        const { data: previousNewMembers, error: previousNewError } = await supabase
          .from('pliromes')
          .select('pelates_id')
          .gte('start_program', periodRange.previousStartDate)
          .lte('start_program', periodRange.previousEndDate)
          .is('nodebt', true);

        if (previousNewError) throw new Error(`Error fetching previous new members: ${previousNewError.message}`);

        const newMemberCount = currentNewMembers?.length || 0;
        const previousNewMemberCount = previousNewMembers?.length || 0;
        const newMemberPercentChange = previousNewMemberCount > 0
          ? ((newMemberCount - previousNewMemberCount) / previousNewMemberCount) * 100
          : 0;

        // Fetch retention rate
      // Get total members at start of period
  const { data: membersAtStartData, error: membersAtStartError } = await supabase
  .from('pliromes')
  .select('pelates_id')
  .lte('start_program', periodRange.startDate);

if (membersAtStartError) throw new Error(`Error fetching members at start: ${membersAtStartError.message}`);

// Get members who were still active at the end of period
const { data: membersStillActiveData, error: membersStillActiveError } = await supabase
  .from('pliromes')
  .select('pelates_id')
  .lte('start_program', periodRange.startDate)
  .or(`end_date.is.null, end_date.gte.${periodRange.endDate}`);

if (membersStillActiveError) throw new Error(`Error fetching members still active: ${membersStillActiveError.message}`);

// Calculate retention rate manually
const membersAtStart = membersAtStartData?.length || 1; // Avoid division by zero
const membersStillActive = membersStillActiveData?.length || 0;

const retentionValue = (membersStillActive / membersAtStart) * 100;

// Similarly for previous period
const { data: prevMembersAtStartData } = await supabase
  .from('pliromes')
  .select('pelates_id')
  .lte('start_program', periodRange.previousStartDate);

const { data: prevMembersStillActiveData } = await supabase
  .from('pliromes')
  .select('pelates_id')
  .lte('start_program', periodRange.previousStartDate)
  .or(`end_date.is.null, end_date.gte.${periodRange.previousEndDate}`);

const prevMembersAtStart = prevMembersAtStartData?.length || 1;
const prevMembersStillActive = prevMembersStillActiveData?.length || 0;

const previousRetentionValue = (prevMembersStillActive / prevMembersAtStart) * 100;

// Use these calculated values instead of the RPC results
const retentionPercentChange = previousRetentionValue > 0
  ? ((retentionValue - previousRetentionValue) / previousRetentionValue) * 100
  : 0;

        // Update metrics
        setMetrics({
          activeMembers: {
            current: currentMemberCount,
            previous: previousMemberCount,
            percentChange: memberPercentChange
          },
          mrr: {
            current: mrrValue,
            previous: previousMrrValue,
            percentChange: mrrPercentChange
          },
          newMembers: {
            current: newMemberCount,
            previous: previousNewMemberCount,
            percentChange: newMemberPercentChange
          },
          retentionRate: {
            current: retentionValue,
            previous: previousRetentionValue,
            percentChange: retentionPercentChange
          }
        });

        // Fetch data for charts (simplified for this example)
        // In a real implementation, you would add additional queries for charts

      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : 'Unknown error occurred';
        setError(new Error(errorMessage));
        console.error('Error fetching dashboard data:', e);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [supabase, periodRange]);

  return {
    metrics,
    memberTrend,
    revenueTrend,
    memberSegments,
    isLoading,
    error
  };
}