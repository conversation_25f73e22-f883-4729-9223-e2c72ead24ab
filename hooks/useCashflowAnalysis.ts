// hooks/useCashflowAnalysis.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { format, parseISO, subDays, subMonths, isAfter, isBefore, isEqual } from 'date-fns';

type PaymentMethod = Database['public']['Enums']['payment_method'];

interface CashflowSummary {
  // Account balances
  accountBalances: {
    [key in PaymentMethod]: number;
  };

  // Income by payment method
  income: {
    total: number;
    byMethod: {
      [key in PaymentMethod]?: number;
    };
  };

  // Expenses by payment method
  expenses: {
    total: number;
    byMethod: {
      [key in PaymentMethod]?: number;
    };
  };

  // Transfers between accounts
  transfers: {
    total: number;
    byAccount: {
      [key in PaymentMethod]?: {
        incoming: number;
        outgoing: number;
        net: number;
      };
    };
  };

  // Expected balances based on transactions
  expectedBalances: {
    [key in PaymentMethod]?: number;
  };

  // Difference between actual and expected balances
  balanceDifferences: {
    [key in PaymentMethod]?: number;
  };

  // Winbank balance records
  winbankBalances: Array<Database['public']['Tables']['winbank_balances']['Row']>;

  // Winbank reconciliation
  winbankReconciliation: {
    systemBalance: number;
    lastRecordedBalance: number | null;
    lastRecordedDate: string | null;
    difference: number | null;
    isReconciled: boolean;
  };

  // Recent transactions
  recentTransactions: Array<{
    id: string;
    date: string;
    type: 'income' | 'expense' | 'transfer';
    description: string;
    amount: number;
    method: PaymentMethod | 'Transfer';
  }>;
}

export function useCashflowAnalysis(period: 'week' | 'month' | 'quarter' | 'year' = 'month') {
  const [data, setData] = useState<CashflowSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchCashflowData = async () => {
      try {
        setLoading(true);
        const supabase = createClientComponentClient<Database>();

        // Calculate date range based on period
        const endDate = new Date();
        let startDate: Date;

        switch (period) {
          case 'week':
            startDate = subDays(endDate, 7);
            break;
          case 'month':
            startDate = subMonths(endDate, 1);
            break;
          case 'quarter':
            startDate = subMonths(endDate, 3);
            break;
          case 'year':
            startDate = subMonths(endDate, 12);
            break;
          default:
            startDate = subMonths(endDate, 1);
        }

        const startDateStr = startDate.toISOString().split('T')[0];

        // 1. Fetch account balances
        const { data: accountBalances, error: balancesError } = await supabase
          .from('account_balances')
          .select('*');

        if (balancesError) throw balancesError;

        // 2. Fetch income (payments)
        const { data: payments, error: paymentsError } = await supabase
          .from('pliromes')
          .select('id, money_gave, date_money_gave, way_of_payment, comments')
          .gte('date_money_gave', startDateStr);

        if (paymentsError) throw paymentsError;

        // 3. Fetch expenses
        const { data: expenses, error: expensesError } = await supabase
          .from('admin_expenses')
          .select('id, amount, date, payment_method, description, category')
          .gte('date', startDateStr);

        if (expensesError) throw expensesError;

        // 4. Fetch transfers
        const { data: transfers, error: transfersError } = await supabase
          .from('account_transfers')
          .select('*')
          .gte('initiated_date', startDateStr);

        if (transfersError) throw transfersError;

        // 5. Fetch Winbank balance records
        const { data: winbankBalances, error: winbankError } = await supabase
          .from('winbank_balances')
          .select('*')
          .order('transaction_date', { ascending: false });

        if (winbankError) throw winbankError;

        // Process account balances
        const actualBalances = {} as {[key in PaymentMethod]: number};
        accountBalances?.forEach(account => {
          actualBalances[account.account_name] = account.current_balance;
        });

        // Process income by payment method
        const incomeByMethod = {} as {[key in PaymentMethod]?: number};
        let totalIncome = 0;

        payments?.forEach(payment => {
          if (payment.way_of_payment) {
            const method = payment.way_of_payment as PaymentMethod;
            incomeByMethod[method] = (incomeByMethod[method] || 0) + (payment.money_gave || 0);
            totalIncome += payment.money_gave || 0;
          }
        });

        // Process expenses by payment method
        const expensesByMethod = {} as {[key in PaymentMethod]?: number};
        let totalExpenses = 0;

        expenses?.forEach(expense => {
          if (expense.payment_method) {
            const method = expense.payment_method as PaymentMethod;
            expensesByMethod[method] = (expensesByMethod[method] || 0) + (expense.amount || 0);
            totalExpenses += expense.amount || 0;
          }
        });

        // Process transfers
        const transfersByAccount = {} as {[key in PaymentMethod]?: {incoming: number, outgoing: number, net: number}};
        let totalTransfers = 0;

        transfers?.forEach(transfer => {
          const fromAccount = transfer.from_account;
          const toAccount = transfer.to_account;
          const amount = transfer.amount || 0;

          // Initialize if needed
          if (!transfersByAccount[fromAccount]) {
            transfersByAccount[fromAccount] = { incoming: 0, outgoing: 0, net: 0 };
          }
          if (!transfersByAccount[toAccount]) {
            transfersByAccount[toAccount] = { incoming: 0, outgoing: 0, net: 0 };
          }

          // Update transfer amounts
          transfersByAccount[fromAccount]!.outgoing += amount;
          transfersByAccount[fromAccount]!.net -= amount;
          transfersByAccount[toAccount]!.incoming += amount;
          transfersByAccount[toAccount]!.net += amount;

          totalTransfers += amount;
        });

        // Calculate expected balances
        const expectedBalances = {} as {[key in PaymentMethod]?: number};
        const balanceDifferences = {} as {[key in PaymentMethod]?: number};

        // Start with initial balances for each account
        accountBalances?.forEach(account => {
          // For each account, calculate expected balance based on:
          // 1. Income received through this payment method
          // 2. Expenses paid from this account
          // 3. Transfers in/out of this account
          const accountName = account.account_name;
          const initialBalance = account.initial_balance || 0;

          // Income for this account (if payment method matches account name)
          const accountIncome = incomeByMethod[accountName] || 0;

          // Expenses from this account
          const accountExpenses = expensesByMethod[accountName] || 0;

          // Net transfers
          const accountTransfers = transfersByAccount[accountName]?.net || 0;

          // Calculate expected balance
          expectedBalances[accountName] = initialBalance + accountIncome - accountExpenses + accountTransfers;

          // Calculate difference between actual and expected
          balanceDifferences[accountName] = actualBalances[accountName] - expectedBalances[accountName];
        });

        // Prepare recent transactions list (combined and sorted)
        const recentTransactions: Array<{
          id: string;
          date: string;
          type: 'income' | 'expense' | 'transfer';
          description: string;
          amount: number;
          method: PaymentMethod | 'Transfer';
        }> = [];

        // Add income transactions
        payments?.forEach(payment => {
          if (payment.way_of_payment) {
            recentTransactions.push({
              id: payment.id,
              date: payment.date_money_gave || '',
              type: 'income',
              description: payment.comments || 'Payment received',
              amount: payment.money_gave || 0,
              method: payment.way_of_payment as PaymentMethod
            });
          }
        });

        // Add expense transactions
        expenses?.forEach(expense => {
          if (expense.payment_method) {
            recentTransactions.push({
              id: expense.id,
              date: expense.date || '',
              type: 'expense',
              description: expense.description || `Expense: ${expense.category || 'Uncategorized'}`,
              amount: expense.amount || 0,
              method: expense.payment_method as PaymentMethod
            });
          }
        });

        // Add transfer transactions
        transfers?.forEach(transfer => {
          recentTransactions.push({
            id: transfer.id,
            date: transfer.initiated_date || '',
            type: 'transfer',
            description: `Transfer from ${transfer.from_account} to ${transfer.to_account}`,
            amount: transfer.amount || 0,
            method: 'Transfer'
          });
        });

        // Sort by date (most recent first)
        recentTransactions.sort((a, b) => {
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        });

        // Limit to most recent 50 transactions
        const limitedTransactions = recentTransactions.slice(0, 50);

        // Process Winbank balance records and reconciliation
        let lastRecordedBalance: number | null = null;
        let lastRecordedDate: string | null = null;

        if (winbankBalances && winbankBalances.length > 0) {
          // Get the most recent Winbank balance record
          const mostRecentRecord = winbankBalances[0]; // Already sorted by date descending
          lastRecordedBalance = mostRecentRecord.balance;
          lastRecordedDate = mostRecentRecord.transaction_date;
        }

        // Get the system's calculated Winbank balance
        const systemWinbankBalance = expectedBalances['WINBANK'] || 0;

        // Calculate difference and reconciliation status
        const winbankDifference = lastRecordedBalance !== null
          ? lastRecordedBalance - systemWinbankBalance
          : null;

        const isWinbankReconciled = winbankDifference !== null
          ? Math.abs(winbankDifference) < 0.01 // Consider reconciled if difference is less than 1 cent
          : false;

        // Set the final data
        setData({
          accountBalances: actualBalances,
          income: {
            total: totalIncome,
            byMethod: incomeByMethod
          },
          expenses: {
            total: totalExpenses,
            byMethod: expensesByMethod
          },
          transfers: {
            total: totalTransfers,
            byAccount: transfersByAccount
          },
          expectedBalances,
          balanceDifferences,
          winbankBalances: winbankBalances || [],
          winbankReconciliation: {
            systemBalance: systemWinbankBalance,
            lastRecordedBalance,
            lastRecordedDate,
            difference: winbankDifference,
            isReconciled: isWinbankReconciled
          },
          recentTransactions: limitedTransactions
        });

      } catch (err) {
        console.error('Error fetching cashflow analysis:', err);
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchCashflowData();
  }, [period]);

  return { data, loading, error };
}
