/**
 * @file useDismissNotification.ts
 * @description Custom React hook for dismissing notifications
 * 
 * @overview
 * Provides a reusable function to mark notifications as read
 * Encapsulates Supabase notification dismissal logic
 * 
 * @dependencies
 * - React (useCallback)
 * - Custom useSupabase hook
 * 
 * @supabase_interactions
 * - Updates 'notifications' table
 * - Sets 'read' status to true
 * - Filters by specific notification ID
 * 
 * @usage_example
 * ```typescript
 * const dismissNotification = useDismissNotification();
 * await dismissNotification(notificationId);
 * ```
 * 
 * @performance
 * - Memoized callback to prevent unnecessary re-renders
 * - Lightweight single database update operation
 * 
 * @error_handling
 * - Throws error if Supabase update fails
 * - Allows caller to handle potential errors
 * 
 * @accessibility
 * - Provides clean, reusable interface for notification dismissal
 * 
 * @security_considerations
 * - Relies on Row Level Security for data access
 * - Scoped to authenticated user's notifications
 * 
 * <AUTHOR> Name]
 * @created 2024-01-15
 * @version 1.0.0
 */
import { useCallback } from 'react';
import { useSupabase } from '@/hooks/useSupabase';

/**
 * Custom hook to dismiss a notification by marking it as read
 * 
 * @returns {Function} Async function to dismiss a notification
 * @throws {Error} If notification update fails
 * 
 * @description
 * - Creates a memoized callback for dismissing notifications
 * - Uses Supabase to update notification read status
 * - Prevents unnecessary re-renders with useCallback
 */
export function useDismissNotification() {
  // Access Supabase client from custom hook
  const { supabase } = useSupabase();
  
  // Memoized callback to prevent unnecessary recreations
  return useCallback(
    async (notificationId: string) => {
      // Attempt to update notification read status
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      // Throw error if update fails
      if (error) {
        // Log error for debugging
        console.error('Failed to dismiss notification:', error);
        throw error;
      }
    },
    [supabase], // Dependency array ensures stable reference
  );
}