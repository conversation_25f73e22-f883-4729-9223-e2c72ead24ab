/**
 * @file supabase-admin.ts
 * @description Server-side Supabase admin utilities for user management
 * 
 * @overview
 * Provides administrative functions for Supabase authentication:
 * - Creating users programmatically
 * - Using service role for elevated permissions
 * 
 * @dependencies
 * - @supabase/supabase-js
 * 
 * @supabase_interactions
 * - Creates Supabase client with service role key
 * - Uses admin authentication methods
 * 
 * @security_considerations
 * - Uses service role key (highest privilege)
 * - Disables auto token refresh and session persistence
 * - Allows programmatic user creation
 * 
 * @usage_warning
 * CRITICAL: Service role key provides unrestricted access
 * - Never expose this key on the client-side
 * - Only use in secure server-side environments
 * 
 * @error_handling
 * - Throws errors for user creation failures
 * - Provides detailed error information
 * 
 * @performance
 * - Lightweight client creation
 * - Minimal overhead for user management
 * 
 * <AUTHOR> Name]
 * @created 2024-01-15
 * @version 1.0.0
 */
import { createClient } from '@supabase/supabase-js';

/**
 * Create a Supabase admin client with elevated permissions
 * 
 * @description
 * Initializes a Supabase client using the service role key
 * Configured with minimal session management for security
 */
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // High-privilege service role key
  {
    auth: {
      autoRefreshToken: false, // Disable automatic token refresh
      persistSession: false    // Prevent session persistence
    }
  }
);

/**
 * Create a new user programmatically
 * 
 * @param {string} email - User's email address
 * @param {string} password - User's password
 * @returns {Promise} User creation result
 * 
 * @throws {Error} If user creation fails
 * 
 * @security Critical method requiring careful usage
 * @description 
 * - Creates a new user with admin privileges
 * - Automatically confirms email to bypass verification
 * - Provides full control over user creation process
 */
export const createUser = async (email: string, password: string) => {
  // Attempt to create user using admin authentication method
  const { data, error } = await supabaseAdmin.auth.admin.createUser({
    email,
    password,
    email_confirm: true // Automatically confirm email
  });

  // Throw error if user creation fails
  if (error) {
    throw error;
  }

  return data;
};