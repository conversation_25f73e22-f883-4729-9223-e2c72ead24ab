// hooks/useMonthlySummary.ts
import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

export interface MonthlySummaryDatum {
  month: string;
  income: number;
  expenses: number;
}

export function useMonthlySummary() {
  const [data, setData] = useState<MonthlySummaryDatum[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();
        
        // Fetch income
        const { data: incomeRows, error: incomeError } = await supabase
          .from('pliromes')
          .select('date_money_gave, money_gave');
          
        if (incomeError) throw incomeError;
        
        // Fetch expenses
        const { data: expenseRows, error: expenseError } = await supabase
          .from('admin_expenses')
          .select('date, amount');
          
        if (expenseError) throw expenseError;
        
        // Aggregate by month
        const map: Record<string, MonthlySummaryDatum> = {};
        const now = new Date();
        for (let i = 0; i < 12; ++i) {
          const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
          const monthStr = d.toISOString().slice(0, 7);
          map[monthStr] = { month: monthStr, income: 0, expenses: 0 };
        }
        
        incomeRows?.forEach(row => {
          const date = row.date_money_gave?.slice(0, 7);
          if (date && map[date]) {
            map[date].income += Number(row.money_gave) || 0;
          }
        });
        
        expenseRows?.forEach(row => {
          const date = row.date?.slice(0, 7);
          if (date && map[date]) {
            map[date].expenses += Number(row.amount) || 0;
          }
        });
        
        const result = Object.values(map).sort((a, b) => a.month.localeCompare(b.month));
        
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching monthly summary:', err);
          setError(err instanceof Error ? err.message : 'Unknown error occurred');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }
    
    fetchData();
    
    return () => { isMounted = false; };
  }, []); // Removed supabase from dependencies

  return { data, loading, error };
}