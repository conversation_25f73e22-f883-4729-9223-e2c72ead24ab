// hooks/useBusinessMetrics.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/supabase';
import { PeriodRange, DashboardMetrics, ChartDataPoint, MemberSegment } from '@/types/dashboard';

interface BusinessMetricsResult {
  dashboardMetrics: DashboardMetrics;
  memberTrend: ChartDataPoint[];
  revenueTrend: ChartDataPoint[];
  memberSegments: MemberSegment[];
  checkInTrend: ChartDataPoint[];
  isLoading: boolean;
  error: Error | null;
}

export function useBusinessMetrics(periodRange: PeriodRange): BusinessMetricsResult {
  const [dashboardMetrics, setDashboardMetrics] = useState<DashboardMetrics>({
    activeMembers: { current: 0, previous: 0, percentChange: 0 },
    mrr: { current: 0, previous: 0, percentChange: 0 },
    newMembers: { current: 0, previous: 0, percentChange: 0 },
    retentionRate: { current: 0, previous: 0, percentChange: 0 },
    churnRate: { current: 0, previous: 0, percentChange: 0 },
    avgCheckins: { current: 0, previous: 0, percentChange: 0 }
  });
  const [memberTrend, setMemberTrend] = useState<ChartDataPoint[]>([]);
  const [revenueTrend, setRevenueTrend] = useState<ChartDataPoint[]>([]);
  const [memberSegments, setMemberSegments] = useState<MemberSegment[]>([]);
  const [checkInTrend, setCheckInTrend] = useState<ChartDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const supabase = createClientComponentClient<Database>();
  
  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      setError(null);
      
      try {
        // Extract period length from date range to use for thresholds
        const startDate = new Date(periodRange.startDate);
        const endDate = new Date(periodRange.endDate);
        const daysDifference = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Determine period based on days difference
        const periodValue = daysDifference <= 30 ? '30' : 
                           daysDifference <= 90 ? '90' : 
                           daysDifference <= 180 ? '180' : '365';
        
        // 1. Fetch active members
        const { data: currentActiveMembers, error: currentMembersError } = await supabase
          .from('active_subscriptions')
          .select('client_id')
          .gte('end_date', periodRange.startDate)
          .lte('end_date', periodRange.endDate);
        
        if (currentMembersError) throw new Error(`Error fetching active members: ${currentMembersError.message}`);
        
        const { data: previousActiveMembers, error: previousMembersError } = await supabase
          .from('active_subscriptions')
          .select('client_id')
          .gte('end_date', periodRange.previousStartDate)
          .lte('end_date', periodRange.previousEndDate);
          
        if (previousMembersError) throw new Error(`Error fetching previous active members: ${previousMembersError.message}`);
        
        const currentMemberCount = currentActiveMembers?.length || 0;
        const previousMemberCount = previousActiveMembers?.length || 0;
        const memberPercentChange = previousMemberCount > 0 
          ? ((currentMemberCount - previousMemberCount) / previousMemberCount) * 100
          : 0;
        
        // 2. Fetch MRR data
        const { data: currentMRR, error: currentMRRError } = await supabase.rpc(
          'get_monthly_recurring_revenue',
          { month_date: new Date().toISOString() }
        );
        
        if (currentMRRError) throw new Error(`Error fetching current MRR: ${currentMRRError.message}`);
        
        const { data: previousMRR, error: previousMRRError } = await supabase.rpc(
          'get_monthly_recurring_revenue',
          { month_date: periodRange.previousEndDate }
        );
        
        if (previousMRRError) throw new Error(`Error fetching previous MRR: ${previousMRRError.message}`);
        
        const mrrValue = currentMRR || 0;
        const previousMrrValue = previousMRR || 0;
        const mrrPercentChange = previousMrrValue > 0
          ? ((mrrValue - previousMrrValue) / previousMrrValue) * 100
          : 0;
        
        // 3. Fetch new members
        const { data: currentNewMembers, error: currentNewError } = await supabase
          .from('pliromes')
          .select('pelates_id')
          .gte('start_program', periodRange.startDate)
          .lte('start_program', periodRange.endDate)
          .is('nodebt', true);
        
        if (currentNewError) throw new Error(`Error fetching new members: ${currentNewError.message}`);
        
        const { data: previousNewMembers, error: previousNewError } = await supabase
          .from('pliromes')
          .select('pelates_id')
          .gte('start_program', periodRange.previousStartDate)
          .lte('start_program', periodRange.previousEndDate)
          .is('nodebt', true);
        
        if (previousNewError) throw new Error(`Error fetching previous new members: ${previousNewError.message}`);
        
        const newMemberCount = currentNewMembers?.length || 0;
        const previousNewMemberCount = previousNewMembers?.length || 0;
        const newMemberPercentChange = previousNewMemberCount > 0
          ? ((newMemberCount - previousNewMemberCount) / previousNewMemberCount) * 100
          : 0;
        
        // 4. Fetch retention rate
        const { data: currentRetention, error: currentRetentionError } = await supabase.rpc(
          'get_member_retention_rate',
          { 
            start_date: periodRange.startDate,
            end_date: periodRange.endDate
          }
        );
        
        if (currentRetentionError) throw new Error(`Error fetching retention rate: ${currentRetentionError.message}`);
        
        const { data: previousRetention, error: previousRetentionError } = await supabase.rpc(
          'get_member_retention_rate',
          { 
            start_date: periodRange.previousStartDate,
            end_date: periodRange.previousEndDate
          }
        );
        
        if (previousRetentionError) throw new Error(`Error fetching previous retention rate: ${previousRetentionError.message}`);
        
        const retentionValue = currentRetention || 0;
        const previousRetentionValue = previousRetention || 0;
        const retentionPercentChange = previousRetentionValue > 0
          ? ((retentionValue - previousRetentionValue) / previousRetentionValue) * 100
          : 0;
        
        // 5. Fetch monthly trends data
        const { data: trends, error: trendsError } = await supabase
          .from('monthly_kpi_metrics')
          .select('*')
          .order('month', { ascending: false })
          .limit(12);
        
        if (trendsError) throw new Error(`Error fetching trend data: ${trendsError.message}`);
        
        if (trends) {
          // Process for member and revenue trends
          const processedTrends = trends
            .sort((a, b) => {
              const dateA = a.month ? new Date(a.month).getTime() : 0;
              const dateB = b.month ? new Date(b.month).getTime() : 0;
              return dateA - dateB;
            })
            .map(t => ({
              month: t.month,
              members: t.total_members || 0,
              revenue: t.mrr || 0,
              checkins: t.total_checkins || 0
            }));
            
          setMemberTrend(processedTrends);
          setRevenueTrend(processedTrends);
          setCheckInTrend(processedTrends);
        }
        
        // 6. Fetch check-in data for member segmentation
        const { data: checkInStats, error: checkInError } = await supabase
          .from('user_checkin_stats')
          .select('*');
        
        if (checkInError) throw new Error(`Error fetching check-in stats: ${checkInError.message}`);
        
        // Process member segments based on check-in frequency
        if (checkInStats) {
          // Define segments
          const segments: MemberSegment[] = [
            { name: 'Power Users (High Activity)', value: 0, color: '#0088FE' },
            { name: 'Regular (Moderate Activity)', value: 0, color: '#00C49F' },
            { name: 'Occasional (Low Activity)', value: 0, color: '#FFBB28' },
            { name: 'Inactive (Zero Check-ins)', value: 0, color: '#FF8042' }
          ];
          
          // Threshold values depend on period length
          const highThreshold = periodValue === '30' ? 8 : periodValue === '90' ? 24 : 100;
          const mediumThreshold = periodValue === '30' ? 4 : periodValue === '90' ? 12 : 50;
          const lowThreshold = periodValue === '30' ? 1 : periodValue === '90' ? 3 : 12;
          
          // Categorize members
          checkInStats.forEach(stat => {
            const totalCheckins = stat.total_checkins || 0;
            
            if (totalCheckins >= highThreshold) {
              segments[0].value++;
            } else if (totalCheckins >= mediumThreshold) {
              segments[1].value++;
            } else if (totalCheckins >= lowThreshold) {
              segments[2].value++;
            } else {
              segments[3].value++;
            }
          });
          
          // Filter out empty segments
          const filteredSegments = segments.filter(s => s.value > 0);
          setMemberSegments(filteredSegments);
        }
        
        // 7. Calculate churn rate
        // For simplicity, using 100 - retention rate
        const churnRate = 100 - retentionValue;
        const previousChurnRate = 100 - previousRetentionValue;
        const churnPercentChange = previousChurnRate > 0
          ? ((churnRate - previousChurnRate) / previousChurnRate) * 100
          : 0;
        
        // 8. Calculate average check-ins per member
        // This would typically be done with a dedicated query
        // For now, using a simple calculation based on available data
        let avgCheckinsCurrent = 0;
        let avgCheckinsPrevious = 0;
        
        if (checkInStats && checkInStats.length > 0) {
          const totalCheckins = checkInStats.reduce((sum, stat) => sum + (stat.total_checkins || 0), 0);
          avgCheckinsCurrent = totalCheckins / checkInStats.length;
          
          // For previous, we'll use a placeholder calculation
          // In a real implementation, you'd query historical data
          avgCheckinsPrevious = avgCheckinsCurrent * 0.95; // Assume 5% difference
        }
        
        const avgCheckinsPercentChange = avgCheckinsPrevious > 0
          ? ((avgCheckinsCurrent - avgCheckinsPrevious) / avgCheckinsPrevious) * 100
          : 0;
        
        // Update dashboard metrics
        setDashboardMetrics({
          activeMembers: {
            current: currentMemberCount,
            previous: previousMemberCount,
            percentChange: memberPercentChange
          },
          mrr: {
            current: mrrValue,
            previous: previousMrrValue,
            percentChange: mrrPercentChange
          },
          newMembers: {
            current: newMemberCount,
            previous: previousNewMemberCount,
            percentChange: newMemberPercentChange
          },
          retentionRate: {
            current: retentionValue,
            previous: previousRetentionValue,
            percentChange: retentionPercentChange
          },
          churnRate: {
            current: churnRate,
            previous: previousChurnRate,
            percentChange: churnPercentChange
          },
          avgCheckins: {
            current: avgCheckinsCurrent,
            previous: avgCheckinsPrevious,
            percentChange: avgCheckinsPercentChange
          }
        });
        
      } catch (e) {
        console.error('Error fetching dashboard data:', e);
        setError(e instanceof Error ? e : new Error('Unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchData();
  }, [supabase, periodRange]);
  
  return {
    dashboardMetrics,
    memberTrend,
    revenueTrend,
    memberSegments,
    checkInTrend,
    isLoading,
    error
  };
}
