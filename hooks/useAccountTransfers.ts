// hooks/useAccountTransfers.ts
import { useState, useEffect, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";

// Use the exact type from the Database definition to ensure compatibility
export type AccountTransfer = Database['public']['Tables']['account_transfers']['Row'];

export function useAccountTransfers() {
  const [transfers, setTransfers] = useState<AccountTransfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    const fetchTransfers = async () => {
      try {
        setLoading(true);
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();

        const { data, error } = await supabase
          .from('account_transfers')
          .select('*')
          .order('initiated_date', { ascending: false });

        if (error) throw error;

        if (isMountedRef.current && data) {
          setTransfers(data);
        }
      } catch (err) {
        if (isMountedRef.current) {
          console.error('Error fetching account transfers:', err);
          setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    fetchTransfers();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return { transfers, loading, error };
}