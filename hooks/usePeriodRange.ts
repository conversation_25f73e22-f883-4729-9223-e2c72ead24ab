// hooks/usePeriodRange.ts (full implementation)
import { useMemo } from 'react';
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { PeriodOption, PeriodRange } from '@/types/dashboard';

export function usePeriodRange(period: PeriodOption): PeriodRange {
  return useMemo(() => {
    const today = new Date();
    const endDate = today;
    let startDate: Date;
    let previousEndDate: Date;
    let previousStartDate: Date;

    // Calculate date ranges based on period
    if (period === '30') {
      startDate = subDays(today, 30);
      previousEndDate = subDays(startDate, 1);
      previousStartDate = subDays(previousEndDate, 30);
    } else if (period === '90') {
      startDate = subDays(today, 90);
      previousEndDate = subDays(startDate, 1);
      previousStartDate = subDays(previousEndDate, 90);
    } else if (period === '180') {
      startDate = subDays(today, 180);
      previousEndDate = subDays(startDate, 1);
      previousStartDate = subDays(previousEndDate, 180);
    } else {
      // 365 days
      startDate = subDays(today, 365);
      previousEndDate = subDays(startDate, 1);
      previousStartDate = subDays(previousEndDate, 365);
    }

    // Format dates for SQL queries
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      previousStartDate: previousStartDate.toISOString(),
      previousEndDate: previousEndDate.toISOString(),
    };
  }, [period]);
}