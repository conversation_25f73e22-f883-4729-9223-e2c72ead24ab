
import { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/useSupabase';
import type { Database, Json } from "@/types/supabase";
import type { NotificationMetadata } from '@/types/notifications';

type Tables = Database['public']['Tables'];
type Badge = Tables['badges']['Row'];
type UserBadge = Tables['user_badges']['Row'] & {
  badge?: Badge; // For joined queries
};
type NotificationInsert = Tables['notifications']['Insert'];
type BadgeCategory = Database['public']['Enums']['badge_category'];
type BadgeLevel = Database['public']['Enums']['badge_level'];

// Map badge levels to numbers for notification metadata
const BADGE_LEVEL_TO_NUMBER: Record<BadgeLevel, number> = {
  'bronze': 1,
  'silver': 2,
  'gold': 3,
  'platinum': 4
};

export function useBadges(userId: string) {
  const { supabase } = useSupabase();
  const [badges, setBadges] = useState<UserBadge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchBadges = async () => {
      try {
        setLoading(true);

        // Fetch user badges with related badge information
        const { data, error } = await supabase
          .from('user_badges')
          .select(`
            *,
            badge:badges (*)
          `)
          .eq('pelatis_id', userId);

        if (error) throw error;

        setBadges(data || []);
      } catch (err) {
        console.error('Error fetching badges:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch badges'));
      } finally {
        setLoading(false);
      }
    };

    // Set up real-time subscription for new badges
    const subscription = supabase
      .channel('badges-channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'user_badges',
          filter: `pelatis_id=eq.${userId}`,
        },
        async (payload) => {
          // Fetch the complete badge data including the badge details
          const { data } = await supabase
            .from('user_badges')
            .select(`
              *,
              badge:badges (*)
            `)
            .eq('id', payload.new.id)
            .single();

          if (data) {
            setBadges((prev) => [...prev, data]);
            // Trigger notification or celebration animation
            onNewBadge(data);
          }
        }
      )
      .subscribe();

    fetchBadges();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [userId, supabase]);

  // Handle new badge achievement
  const onNewBadge = async (badge: UserBadge) => {
    if (!badge.badge) return;

    // Convert badge level from string enum to number
    const badgeLevelNumber = badge.badge.level ? BADGE_LEVEL_TO_NUMBER[badge.badge.level] : undefined;

    // Create metadata object conforming to the expected structure
    const metadata: NotificationMetadata = {
      badge_id: badge.badge_id,
      badge_name: badge.badge.name,
      badge_level: badgeLevelNumber, // Using the numeric representation
      eventType: 'badge_earned' as any // Using existing eventType with appropriate value
    };

    const notification: NotificationInsert = {
      client_id: userId,
      message: `Congratulations! You've earned the ${badge.badge.name} badge!`,
      type: 'info',
      metadata: metadata as Json, // Cast to Json type
      created_at: new Date().toISOString(),
      read: false,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    };

    try {
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notification);

      if (notificationError) throw notificationError;
    } catch (err) {
      console.error('Error creating badge notification:', err);
    }
  };

  // Get badges by category
  const getBadgesByCategory = (category: BadgeCategory) => {
    return badges.filter((badge) => badge.badge?.category === category);
  };

  // Get latest badges
  const getLatestBadges = (limit: number = 5) => {
    return [...badges]
      .sort((a, b) => new Date(b.achieved_at).getTime() - new Date(a.achieved_at).getTime())
      .slice(0, limit);
  };

  // Calculate progress for a specific badge type
  const getBadgeProgress = async (badgeId: string) => {
    const { data: badge } = await supabase
      .from('badges')
      .select('*')
      .eq('id', badgeId)
      .single();

    if (!badge) return null;

    // Calculate progress based on badge category
    switch (badge.category) {
      case 'attendance': {
        const { data } = await supabase
          .from('check_ins')
          .select('*')
          .eq('pelatis_id', userId);

        return {
          total_checkins: data?.length || 0,
          // Add more attendance-related progress metrics
        };
      }
      case 'exercise': {
        const { data } = await supabase
          .from('exercise_records')
          .select('*')
          .eq('pelatis_id', userId);

        return {
          total_exercises: data?.length || 0,
          prs: data?.filter(record => record.is_pr)?.length || 0,
          // Add more exercise-related progress metrics
        };
      }
      // Add more categories as needed
      default:
        return null;
    }
  };

  return {
    badges,
    loading,
    error,
    getBadgesByCategory,
    getLatestBadges,
    getBadgeProgress,
  };
}
