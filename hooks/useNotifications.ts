import { useState, useEffect, useCallback } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useToast } from "@/hooks/use-toast";

type Notification = Database['public']['Tables']['notifications']['Row'];

// Type guard for metadata
function isPaymentMetadata(metadata: Notification['metadata']): metadata is { eventType: string } {
  return !!metadata && typeof metadata === 'object' && 'eventType' in metadata;
}

export function useNotifications(userId: string) {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [pelatiId, setPelatiId] = useState<string | null>(null);
  
  const supabase = createClientComponentClient<Database>();

  // First, check if user is admin and get pelati_id
  useEffect(() => {
    async function initializeUser() {
      try {
        // Check if user is admin
        const { data: roles } = await supabase.rpc('getUserRoles', {
          p_user_id: userId
        });
        const userIsAdmin = roles?.includes('admin') ?? false;
        setIsAdmin(userIsAdmin);
        console.log('User is admin:', userIsAdmin);

        // Get pelati_id regardless of admin status
        const { data: pelatiData } = await supabase
          .from('pelates')
          .select('id')
          .eq('auth_user_id', userId)
          .single();

        console.log('Found pelati_id:', pelatiData?.id);
        setPelatiId(pelatiData?.id || null);
      } catch (error) {
        console.error('Error initializing user:', error);
      }
    }

    initializeUser();
  }, [userId, supabase]);

  const handlePaymentNotification = useCallback((notification: Notification) => {
    if (!isPaymentMetadata(notification.metadata)) return;
    
    if (notification.metadata.eventType === 'payment_success') {
      toast({
        description: notification.message,
        variant: "default",
      });
    } else if (notification.metadata.eventType === 'payment_failed') {
      toast({
        description: notification.message,
        variant: "destructive",
      });
    } else if (notification.metadata.eventType === 'refund_processed') {
      toast({
        description: notification.message,
        variant: "default",
      });
    }
  }, [toast]);

  useEffect(() => {
    if (notifications.length > 0) {
      notifications.forEach(notification => {
        if (isPaymentMetadata(notification.metadata) && 
            notification.metadata.eventType.startsWith('payment_')) {
          handlePaymentNotification(notification)
        }
      })
    }
  }, [notifications, handlePaymentNotification])

  // Fetch notifications based on user role
  useEffect(() => {
    if (!isAdmin && !pelatiId) return; // Wait for initialization

    let channel: RealtimeChannel;
    let mounted = true;

    const fetchNotifications = async () => {
      try {
        setLoading(true);
        console.log('Fetching notifications for:', isAdmin ? 'admin' : `pelati_id: ${pelatiId}`);
    
        let query = supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false });

        if (isAdmin) {
          // Admins see notifications where either:
          // 1. They are the client_id (their personal notifications)
          // 2. admin_impersonation is true (notifications meant for all admins)
          if (pelatiId) {
            query = query.or(`client_id.eq.${pelatiId},admin_impersonation.eq.true`);
          } else {
            query = query.eq('admin_impersonation', true);
          }
        } else if (pelatiId) {
          // Regular users only see their own notifications
          query = query.eq('client_id', pelatiId);
        } else {
          // Fallback - shouldn't happen due to the guard at the top of the effect
          console.warn('No pelatiId found for regular user, showing no notifications');
          query = query.eq('client_id', '00000000-0000-0000-0000-000000000000'); // A dummy ID that won't match anything
        }

        const { data, error } = await query;
        if (error) throw error;
        
        console.log('Fetched notifications:', data);
        
        if (mounted) {
          setNotifications(data ?? []);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    const setupSubscription = () => {
      // Make sure we have a valid filter
      let filterString: string;
      
      if (isAdmin) {
        // Admin either sees their own notifications or admin notifications
        filterString = pelatiId 
          ? `client_id=eq.${pelatiId},admin_impersonation=eq.true` 
          : `admin_impersonation=eq.true`;
      } else if (pelatiId) {
        // Regular user only sees their own notifications
        filterString = `client_id=eq.${pelatiId}`;
      } else {
        // Fallback - shouldn't happen
        filterString = `client_id=eq.00000000-0000-0000-0000-000000000000`;
      }

      const channelId = `notifications-${isAdmin ? 'admin' : (pelatiId || 'unknown')}`;
      
      channel = supabase
        .channel(channelId)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'notifications',
            filter: filterString,
          },
          (payload) => {
            console.log('Realtime notification received:', payload);
            if (!mounted) return;

            if (payload.eventType === 'INSERT') {
              const newNotification = payload.new as Notification;
              setNotifications((current) => [newNotification, ...current]);
            } else if (payload.eventType === 'UPDATE') {
              setNotifications((current) =>
                current.map((n) =>
                  n.id === payload.new.id ? { ...n, ...payload.new } : n
                )
              );
            } else if (payload.eventType === 'DELETE') {
              setNotifications((current) =>
                current.filter((n) => n.id !== payload.old.id)
              );
            }
          }
        )
        .subscribe((status) => {
          console.log('Realtime subscription status:', status);
        });
    };

    void fetchNotifications();
    setupSubscription();

    return () => {
      mounted = false;
      if (channel) {
        void supabase.removeChannel(channel);
      }
    };
  }, [isAdmin, pelatiId, supabase]);

  const markAsRead = async (notificationId: string) => {
    try {
      let query = supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);
      
      // If not admin, also filter by client_id
      if (!isAdmin && pelatiId) {
        query = query.eq('client_id', pelatiId);
      }
      
      const { error } = await query;
      if (error) throw error;

      setNotifications((current) =>
        current.map((n) =>
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  };

  const markAllAsRead = async () => {
    try {
      let query = supabase
        .from('notifications')
        .update({ read: true })
        .is('read', false);

      // If not admin, filter by client_id
      if (!isAdmin && pelatiId) {
        query = query.eq('client_id', pelatiId);
      }

      const { error } = await query;
      if (error) throw error;

      setNotifications((current) =>
        current.map((n) => ({ ...n, read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  };

  return {
    notifications,
    loading,
    isAdmin,
    markAsRead,
    markAllAsRead,
  };
}
