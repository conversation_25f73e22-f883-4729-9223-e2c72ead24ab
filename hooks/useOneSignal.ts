import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useToast } from "@/hooks/use-toast";
import type { OneSignalType } from '@/types/onesignal';

interface OneSignalHook {
  isInitialized: boolean;
  isSubscribed: boolean;
  playerId: string | null;
  requestSubscription: () => Promise<boolean>;
}

export function useOneSignal(): OneSignalHook {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();
  const { toast } = useToast();

  useEffect(() => {
    const initializeOneSignal = async () => {
      if (typeof window === 'undefined') return;

      try {
        // Initialize OneSignal array
        window.OneSignal = window.OneSignal || [];

        // Wait for OneSignal to be loaded
        await new Promise<void>((resolve) => {
          const script = document.createElement('script');
          script.src = 'https://cdn.onesignal.com/sdks/OneSignalSDK.js';
          script.async = true;
          script.onload = () => resolve();
          document.head.appendChild(script);
        });

        // Initialize OneSignal
        window.OneSignal.push(function() {
          (window.OneSignal[0] as OneSignalType).init({
            appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID || '',
            allowLocalhostAsSecureOrigin: process.env.NODE_ENV === 'development',
            notifyButton: {
              enable: false,
            },
            welcomeNotification: {
              disable: true
            }
          }).then(() => {
            setIsInitialized(true);
            checkSubscriptionStatus();
          });
        });

      } catch (error) {
        console.error('OneSignal initialization error:', error);
        toast({
          title: "Error",
          description: "Failed to initialize push notifications",
          variant: "destructive",
        });
      }
    };

    const checkSubscriptionStatus = async () => {
      try {
        const oneSignal = window.OneSignal[0] as OneSignalType;
        const state = await oneSignal.getNotificationPermission();
        setIsSubscribed(state === 'granted');

        if (state === 'granted') {
          const id = await oneSignal.getUserId();
          setPlayerId(id);
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
      }
    };

    initializeOneSignal();

    // Cleanup
    return () => {
      const script = document.querySelector('script[src*="OneSignalSDK.js"]');
      if (script) {
        script.remove();
      }
    };
  }, [toast]);

  const requestSubscription = async (): Promise<boolean> => {
    if (!window.OneSignal?.[0]) {
      toast({
        title: "Error",
        description: "OneSignal is not available",
        variant: "destructive",
      });
      return false;
    }

    try {
      const oneSignal = window.OneSignal[0] as OneSignalType;
      const result = await oneSignal.showNativePrompt();
      
      if (result) {
        const id = await oneSignal.getUserId();
        setPlayerId(id);
        setIsSubscribed(true);

        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.id) {
          throw new Error('No authenticated user found');
        }

        const { error } = await supabase
          .from('admin_onesignal_subscriptions')
          .upsert({
            auth_user_id: session.user.id,
            player_id: id,
            enabled: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }, {
            onConflict: 'auth_user_id,player_id'
          });

        if (error) throw error;

        toast({
          title: "Success",
          description: "Push notifications enabled successfully",
        });

        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error requesting push notification permission:', error);
      toast({
        title: "Error",
        description: "Failed to enable push notifications",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    isInitialized,
    isSubscribed,
    playerId,
    requestSubscription,
  };
}
