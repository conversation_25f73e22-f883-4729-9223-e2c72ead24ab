// hooks/useFinancialOperations.ts
import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";

type PaymentMethod = Database['public']['Enums']['payment_method'];
type AccountBalance = Database['public']['Tables']['account_balances']['Row'];
type AdminExpense = Database['public']['Tables']['admin_expenses']['Row'];
type AccountTransfer = Database['public']['Tables']['account_transfers']['Row'];
type ReconciliationRecord = Database['public']['Tables']['reconciliation_records']['Row'];

export function useFinancialOperations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Function to create an expense
  const createExpense = async (expense: Omit<AdminExpense, 'id' | 'created_at'>) => {
    // ... (unchanged)
  };

  // Function to create an account transfer
  const createTransfer = async (transfer: Omit<AccountTransfer, 'id' | 'created_at'>) => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClientComponentClient<Database>();

      // Insert the transfer
      const { data, error: insertError } = await supabase
        .from('account_transfers')
        .insert(transfer)
        .select()
        .single();

      if (insertError) throw insertError;

      // Update account balances
      if (data) {
        // Get the current balances first
        const { data: fromAccount, error: fromFetchError } = await supabase
          .from('account_balances')
          .select('current_balance')
          .eq('account_name', data.from_account)
          .single();

        if (fromFetchError) throw fromFetchError;

        const { data: toAccount, error: toFetchError } = await supabase
          .from('account_balances')
          .select('current_balance')
          .eq('account_name', data.to_account)
          .single();

        if (toFetchError) throw toFetchError;

        // Calculate new balances
        const fromNewBalance = fromAccount.current_balance - data.amount;
        const toNewBalance = toAccount.current_balance + data.amount;

        // Update from_account balance
        const { error: fromError } = await supabase
          .from('account_balances')
          .update({
            current_balance: fromNewBalance,
            last_updated: new Date().toISOString()
          })
          .eq('account_name', data.from_account);

        if (fromError) throw fromError;

        // Update to_account balance
        const { error: toError } = await supabase
          .from('account_balances')
          .update({
            current_balance: toNewBalance,
            last_updated: new Date().toISOString()
          })
          .eq('account_name', data.to_account);

        if (toError) throw toError;
      }

      return data;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create transfer'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Function to reconcile account balance
  const reconcileAccount = async (reconciliation: Omit<ReconciliationRecord, 'id' | 'created_at'>) => {
    // ... (unchanged)
  };

  return {
    loading,
    error,
    createExpense,
    createTransfer,
    reconcileAccount
  };
}