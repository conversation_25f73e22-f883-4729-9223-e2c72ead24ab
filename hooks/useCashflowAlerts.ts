// hooks/useCashflowAlerts.ts
import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from "@/types/supabase";

type AccountBalance = Database['public']['Tables']['account_balances']['Row'];
type AccountTransfer = Database['public']['Tables']['account_transfers']['Row'];

export function useCashflowAlerts() {
  const [unreconciledAccounts, setUnreconciledAccounts] = useState<AccountBalance[]>([]);
  const [largeTransfers, setLargeTransfers] = useState<AccountTransfer[]>([]);
  const [lowBalanceAccounts, setLowBalanceAccounts] = useState<AccountBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();

        // Fetch all accounts
        const { data: accounts, error: accError } = await supabase
          .from('account_balances')
          .select('*');

        if (accError) throw accError;

        // Fetch large transfers
        const { data: transfers, error: trError } = await supabase
          .from('account_transfers')
          .select('*')
          .gt('amount', 1000)
          .eq('status', 'pending');

        if (trError) throw trError;

        // Process accounts
        if (accounts) {
          // Low balance accounts (less than 100)
          const lowBalAccounts = accounts.filter(acc => acc.current_balance < 100);
          setLowBalanceAccounts(lowBalAccounts);

          // Accounts not reconciled in last 30 days
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          const unrecAccounts = accounts.filter(acc =>
            !acc.last_reconciled || new Date(acc.last_reconciled) < thirtyDaysAgo
          );
          setUnreconciledAccounts(unrecAccounts);
        }

        // Set large transfers
        setLargeTransfers(transfers || []);
      } catch (err) {
        console.error('Error fetching cashflow alerts:', err);
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchAlerts();
  }, []);

  // Also provide counts for convenience
  const alerts = {
    unreconciledAccounts: unreconciledAccounts.length,
    largeTransfers: largeTransfers.length,
    lowBalanceAccounts: lowBalanceAccounts.length
  };

  return {
    unreconciledAccounts,
    largeTransfers,
    lowBalanceAccounts,
    alerts,
    loading,
    error
  };
}