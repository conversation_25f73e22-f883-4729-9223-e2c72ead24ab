// hooks/useAccountBalances.ts
import { useState, useEffect, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Import the Database type to use the correct enum type
import type { Database } from "@/types/supabase";

// Use the exact type from the Database definition to ensure compatibility
export type AccountBalance = Database['public']['Tables']['account_balances']['Row'];

export function useAccountBalances() {
  const [balances, setBalances] = useState<AccountBalance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    const fetchBalances = async () => {
      try {
        console.log("Fetching account balances...");
        setLoading(true);
        const supabase = createClientComponentClient();

        // Add a small delay to ensure loading state is visible for debugging
        await new Promise(resolve => setTimeout(resolve, 500));

        const { data, error } = await supabase
          .from('account_balances')
          .select('*');

        if (error) {
          console.error("Supabase error fetching balances:", error);
          throw error;
        }

        if (isMountedRef.current) {
          if (data && data.length > 0) {
            console.log("Account balances fetched successfully:", data.length, "accounts");
            // Ensure the data is properly formatted
            const formattedData = data.map(item => ({
              id: item.id,
              account_name: item.account_name,
              current_balance: typeof item.current_balance === 'number' ? item.current_balance : parseFloat(item.current_balance),
              initial_balance: typeof item.initial_balance === 'number' ? item.initial_balance : parseFloat(item.initial_balance),
              last_reconciled: item.last_reconciled,
              last_updated: item.last_updated,
              created_at: item.created_at || null // Ensure created_at is never undefined
            }));

            setBalances(formattedData);
          } else {
            console.warn("No account balances found or empty data array");
            // Set empty array instead of leaving previous state
            setBalances([]);
          }
        }
      } catch (err) {
        if (isMountedRef.current) {
          console.error('Error fetching account balances:', err);
          setError(err instanceof Error ? err : new Error('Unknown error occurred'));
          // Even on error, we should set balances to empty array to avoid using stale data
          setBalances([]);
        }
      } finally {
        if (isMountedRef.current) {
          console.log("Account balances loading complete");
          setLoading(false);
        }
      }
    };

    fetchBalances();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return { balances, loading, error };
}