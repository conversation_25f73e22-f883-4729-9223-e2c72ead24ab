// hooks/useIncomeBreakdown.ts
import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

export interface IncomeBreakdownDatum {
  payment_method: string;
  total: number;
}

export function useIncomeBreakdown() {
  const [data, setData] = useState<IncomeBreakdownDatum[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        
        // Create a properly typed Supabase client
        const supabase = createClientComponentClient<Database>();
        
        const since = new Date();
        since.setMonth(since.getMonth() - 11);
        const sinceStr = since.toISOString().slice(0, 10);
        
        const { data: rows, error: fetchError } = await supabase
          .from('pliromes')
          .select('way_of_payment, money_gave, date_money_gave')
          .gte('date_money_gave', sinceStr);
          
        if (fetchError) {
          throw fetchError;
        }
        
        const map: Record<string, number> = {};
        rows?.forEach(row => {
          const method = row.way_of_payment || 'Unknown';
          map[method] = (map[method] || 0) + (Number(row.money_gave) || 0);
        });
        
        const result = Object.entries(map).map(([payment_method, total]) => ({ payment_method, total }));
        
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching income breakdown:', err);
          setError(err instanceof Error ? err.message : 'Unknown error occurred');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }
    
    fetchData();
    
    return () => { isMounted = false; };
  }, []); // Removed supabase from dependencies since we're creating it inside

  return { data, loading, error };
}