const { Client } = require("pg");
const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");
require("dotenv").config({ path: ".env.local" });

const DB_URL = process.env.DB_URL;

// Output directory
const OUTPUT_DIR = path.join(__dirname, "supabase_exports");
if (!fs.existsSync(OUTPUT_DIR)) {
  console.log("Creating output directory:", OUTPUT_DIR);
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Folder structure output file
const FOLDER_STRUCTURE_FILE = path.join(OUTPUT_DIR, "folder_structure.txt");

// Queries for each export category
const QUERIES = {
  functions: `
    SELECT pg_get_functiondef(p.oid) AS definition
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public';
  `,
  triggers: `
    SELECT pg_get_triggerdef(t.oid) AS definition
    FROM pg_trigger t
    JOIN pg_class c ON t.tgrelid = c.oid
    WHERE t.tgisinternal = false;
  `,
  types: `
    SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
    FROM 
        information_schema.columns
    WHERE 
        table_schema = 'public'
    ORDER BY 
        table_name, ordinal_position;
  `,
  policies: `
    SELECT
        p.tablename AS table_name,
        p.policyname AS policy_name,
        COALESCE(pg_get_expr(p.qual, p.polrelid), 'true') AS using_clause,
        COALESCE(pg_get_expr(p.with_check, p.polrelid), 'true') AS with_check_clause,
        p.permissive,
        array_to_string(p.roles, ', ') AS roles
    FROM
        pg_policies p
    WHERE
        p.schemaname = 'public'
    ORDER BY
        p.tablename, p.policyname;
  `,
};

async function exportCategory(client, category, query) {
  console.log(`Starting export for: ${category}`);
  try {
    const res = await client.query(query);
    console.log(`Query for ${category} executed successfully.`);

    const filePath = path.join(OUTPUT_DIR, `${category}.sql`);
    if (res.rows.length === 0) {
      console.log(`No ${category} found. Writing empty file.`);
      fs.writeFileSync(filePath, `-- No ${category} found`);
      console.log(`Empty ${category} file written to: ${filePath}`);
      return;
    }

    let content = "";
    if (category === "policies") {
      // Format policies
      const grouped = res.rows.reduce((acc, row) => {
        acc[row.table_name] = acc[row.table_name] || [];
        acc[row.table_name].push(row);
        return acc;
      }, {});

      for (const [table, policies] of Object.entries(grouped)) {
        content += `-- Policies for table: ${table}\n`;
        for (const policy of policies) {
          content += `  CREATE POLICY ${policy.policy_name} ON ${table}\n`;
          content += `    FOR ${policy.permissive ? "PERMISSIVE" : "RESTRICTIVE"}\n`;
          content += `    USING (${policy.using_clause})\n`;
          content += `    WITH CHECK (${policy.with_check_clause})\n`;
          content += `    TO ${policy.roles};\n\n`;
        }
      }
    } else {
      // Default formatting for other categories
      content = res.rows.map(row => row.definition).join(";\n\n") + ";\n";
    }

    fs.writeFileSync(filePath, content);
    console.log(`Exported ${category} to ${filePath}`);
  } catch (err) {
    console.error(`Error exporting ${category}:`, err.message);
  }
}

async function generateFolderStructure() {
  return new Promise((resolve, reject) => {
    console.log("Generating folder structure...");
    const excludePattern = "node_modules|.next|.git|.env*|coverage|.turbo|dist|build|out|*.lock|static|public|.vercel|.vscode|.next|.DS_Store";
    const command = `tree -I '${excludePattern}' --dirsfirst -a -F --noreport .`;

    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error("Error generating folder structure:", error.message);
        return reject(error);
      }
      if (stderr) {
        console.error("Error output from tree command:", stderr);
      }

      // Write folder structure to file
      fs.writeFileSync(FOLDER_STRUCTURE_FILE, stdout);
      console.log(`Folder structure saved to ${FOLDER_STRUCTURE_FILE}`);
      resolve();
    });
  });
}

async function main() {
  const client = new Client({
    connectionString: DB_URL,
    ssl: {
      rejectUnauthorized: false, // Allow SSL
    },
  });

  console.log("Connecting to the database...");
  try {
    await client.connect();
    console.log("Connected to the database.");

    // Export each category
    for (const [category, query] of Object.entries(QUERIES)) {
      console.log(`Processing category: ${category}`);
      await exportCategory(client, category, query);
    }

    // Generate folder structure
    await generateFolderStructure();
  } catch (err) {
    console.error("Error:", err.message);
  } finally {
    console.log("Closing database connection...");
    await client.end();
    console.log("Database connection closed.");
  }
}

main();
