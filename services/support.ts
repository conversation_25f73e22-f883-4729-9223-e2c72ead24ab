// 5. services/support.ts
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

type MessageCategory = Database['public']['Enums']['message_category'];
type MessageStatus = Database['public']['Enums']['message_status'];
type MessagePriority = Database['public']['Enums']['message_priority'];

interface CreateThreadParams {
  title: string;
  category: MessageCategory;
  message: string;
  clientId: string;
}

export const supportService = {
  async createThread(
    supabase: SupabaseClient<Database>,
    { title, category, message, clientId }: CreateThreadParams
  ) {
    const { data: thread, error: threadError } = await supabase
      .from('support_threads')
      .insert({
        title,
        category,
        client_id: clientId,
        priority: 'medium',
        status: 'open'
      })
      .select()
      .single();

    if (threadError) throw threadError;

    const { error: messageError } = await supabase
      .from('support_messages')
      .insert({
        thread_id: thread.id,
        content: message,
        is_coach_reply: false,
        sender_id: clientId
      });

    if (messageError) throw messageError;

    return thread;
  },

  async addReply(
    supabase: SupabaseClient<Database>,
    { threadId, content, senderId, isCoachReply }: {
      threadId: string;
      content: string;
      senderId: string;
      isCoachReply: boolean;
    }
  ) {
    const { error } = await supabase
      .from('support_messages')
      .insert({
        thread_id: threadId,
        content,
        sender_id: senderId,
        is_coach_reply: isCoachReply
      });

    if (error) throw error;
  }
};